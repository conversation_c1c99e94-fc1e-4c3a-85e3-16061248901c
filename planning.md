# Planning

You are an expert frontend developer working in a TypeScript + React + Tailwind + Storybook environment. Your task is to build a purely **presentational component** called `NegotiationCenter`.

This component will render a **scrollable timeline view** of negotiation-related events, primarily focusing on rate offers and messages. It will later plug into a larger Negotiation Cockpit.

---

## Requirements:

1. ✅ Purely Presentational (no state or API logic)
2. ✅ Designed for Storybook — with mock props and multiple stories
3. ✅ All styling via TailwindCSS
4. ✅ All inputs passed as props
5. ✅ Clear, accessible layout with semantic HTML
6. ✅ Responsively designed for mobile + desktop

---

## Component Name

`NegotiationCenter`

---

## Props Shape (Define Prop Types)

```ts
type NegotiationCenterProps = {
  items: TimelineBlock[];
  currentUserId: string;
};

type TimelineBlock =
  | { type: "message"; id: string; from: User; text: string; timestamp: string }
  | {
      type: "rate_offer";
      id: string;
      from: User;
      rate: number;
      timestamp: string;
    }
  | {
      type: "rate_agreed";
      id: string;
      by: User;
      rate: number;
      timestamp: string;
    }
  | {
      type: "milestone";
      id: string;
      label: string;
      timestamp: string;
      color?: string;
    };

type User = {
  id: string;
  name: string;
  avatarUrl?: string;
};
```

## Visual Behavior

    •	Render each block chronologically, grouped by day if possible
    •	Rate offers are cards with prominent values and optional action buttons (disabled by default in this component)
    •	Messages render as chat bubbles, right-aligned if from currentUserId
    •	Milestones are horizontal dividers with a label and optional color accent
    •	Rate agreements have a checkmark icon and muted style
    •	Use accessible timestamps (e.g., “2h ago” and aria-label full datetime)

⸻

## Storybook Stories

Create stories for:
• Default: Mixed types
• LongFeed: >10 items to test scrolling
• RateOnly: Just offers and agreements
• MessageOnly: Just messages
• DarkMode: Toggle tailwind dark class

⸻

## Bonus (Optional Enhancements)

• Group by date headers (e.g., “Today”, “Yesterday”)
• Add onBlockClick optional prop to test interactivity in Storybook

⸻

Output:

1. NegotiationCenter.tsx — React component
2. NegotiationCenter.stories.tsx — Storybook stories
3. Tailwind utility class usage throughout
4. Assume all strings are already localized
