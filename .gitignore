turbo/test
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
next-env.d.ts

# nitro
.nitro/
.output/

# expo
.expo/
expo-env.d.ts
apps/expo/.gitignore
apps/expo/ios
apps/expo/android

# production
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
dist/

# turbo
.turbo

# prisma
**/prisma/databases
**/prisma/generated

*storybook.log

# Mock Service Worker
public/apiMockServiceWorker.js
public/mockServiceWorker.js

# Added by Claude Task Master
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.roo/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Task files (top level)
/tasks/
