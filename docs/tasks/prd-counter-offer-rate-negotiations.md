# PRD: Counter Offer – Rate Negotiations

## Introduction/Overview

The Counter Offer – Rate Negotiations feature enables bidirectional price negotiation between healthcare providers and hiring organizations on the Med platform. Currently, the platform supports applications, offers, messaging, and contracts, but lacks the ability to negotiate compensation rates. This feature introduces a structured negotiation process where both parties can propose, counter, and finalize rates before contract execution.

The feature solves the problem of inflexible pricing by allowing market-driven rate determination while maintaining organizational control through configurable rate ranges and validation rules.

## Goals

1. Enable bidirectional rate negotiation between providers and hiring organizations
2. Provide hiring organizations with control over acceptable rate ranges
3. Maintain a complete audit trail of all negotiation activities
4. Integrate seamlessly with existing offer and contract workflows
5. Reduce time-to-hire by streamlining the compensation discussion process
6. Increase platform engagement through interactive negotiation features

## User Stories

### As a Healthcare Provider:
- I want to propose a rate when applying to a job so that I can communicate my compensation expectations upfront
- I want to counter-offer when I receive an offer with a rate I find unacceptable so that I can negotiate fair compensation
- I want to see the full negotiation history so that I can track the progression of rate discussions
- I want to know if my proposed rate is within the job's acceptable range so that I can make informed decisions

### As a Hiring Organization:
- I want to set a compensation range when posting a job so that I can guide provider expectations
- I want to choose whether to enforce strict range limits so that I can control negotiation boundaries
- I want to send offers with specific rates so that I can initiate compensation discussions
- I want to counter-offer provider rate requests so that I can negotiate within my budget constraints
- I want to see all negotiation activity so that I can make informed hiring decisions

### As a System Administrator:
- I want to audit negotiation histories so that I can monitor platform activity and resolve disputes
- I want to view rate trends across the platform so that I can provide insights to stakeholders

## Functional Requirements

1. **Rate Range Configuration**
   - Hiring organizations must be able to set a minimum and maximum rate when creating job posts
   - Hiring organizations must be able to toggle "Must be within range" enforcement per job post
   - Rate ranges must be displayed to providers viewing job posts

2. **Provider Rate Proposals**
   - Providers must be able to propose a rate when applying to jobs
   - System must validate proposed rates against job range settings (soft validation for flexible ranges, hard validation for strict ranges)
   - Providers must be able to propose rates within or outside the range based on job settings

3. **Organization Offer Enhancement**
   - Hiring organizations must be able to include specific rates when sending offers
   - System must validate organization-proposed rates against their own job range settings
   - Organizations must be able to send offers without rates (existing functionality preserved)

4. **Bidirectional Counter Offers**
   - Both providers and organizations must be able to counter-offer with new rates
   - System must track the complete negotiation thread with timestamps and user attribution
   - No limit on the number of counter-offer rounds

5. **Rate Validation System**
   - System must check all proposed rates against job range settings
   - For strict ranges: prevent submission of out-of-range rates with error messaging
   - For flexible ranges: allow out-of-range rates with warning notifications
   - System must validate that organization offers comply with their own range settings

6. **Negotiation Threading**
   - System must maintain chronological order of all rate proposals and counter-offers
   - Each negotiation entry must include: rate amount, timestamp, user ID, and status
   - System must preserve full negotiation history even after rate finalization

7. **Rate Finalization**
   - Either party must be able to accept a proposed rate to finalize negotiations
   - System must lock the negotiated rate upon acceptance
   - Finalized rate must automatically update the associated job post and contract terms
   - System must prevent further rate negotiations once a rate is accepted

8. **Rate Negotiation UI Panel**
   - System must provide a dedicated interface separate from messaging for rate negotiations
   - Panel must display: initial rate range, complete offer/counter timeline, current status, and finalized rate
   - Interface must include clear status indicators (Pending, Declined, Accepted)
   - Panel must be accessible from both job and application views

9. **Contract Integration**
   - System must make contract generation available only after rate finalization
   - Finalized rate must be automatically populated in contract terms
   - System must maintain link between negotiated rate and final contract

10. **Administrative Controls**
    - Administrators must be able to view complete negotiation histories for audit purposes
    - System must provide reporting capabilities for rate negotiation trends and outcomes
    - Administrators must be able to access negotiation data for dispute resolution

## Non-Goals (Out of Scope)

- Automated rate suggestions or AI-powered negotiation assistance
- Integration with external salary benchmarking services
- Complex approval workflows for rate negotiations
- Rate negotiation for existing contracts (only for new applications/offers)
- Bulk rate negotiation across multiple positions
- Time-limited negotiation windows or expiration dates
- Integration with payroll or billing systems

## Design Considerations

- Rate Negotiation Panel should be visually distinct from messaging interface
- Use clear visual indicators for rate status (pending, accepted, declined)
- Ensure mobile-responsive design for provider access
- Consider accessibility requirements for all negotiation interfaces
- Maintain consistent styling with existing platform components

## Technical Considerations

- Extend existing offer/application data models to include rate negotiation fields
- Implement proper database indexing for negotiation history queries
- Ensure rate validation logic is consistent across all entry points
- Consider caching strategies for frequently accessed negotiation data
- Implement proper audit logging for all rate negotiation activities
- Ensure secure handling of financial data in compliance with platform standards

## Success Metrics

- Increase in successful job placements by 15% within 3 months of launch
- Reduction in time-to-hire by 20% for positions with active rate negotiations
- 80% of job posts utilize the rate range configuration feature
- 60% of applications include initial rate proposals
- Less than 5% of negotiations require administrative intervention
- User satisfaction score of 4.0+ for the negotiation experience

## Open Questions

1. Should there be any notification system for new rate proposals/counter-offers?
2. How should the system handle rate negotiations when multiple providers are being considered for the same position?
3. Should there be any analytics dashboard for hiring organizations to track their negotiation patterns?
4. What happens to ongoing negotiations if a job post is modified or closed?
5. Should there be any integration with the existing messaging system to notify about rate changes?
