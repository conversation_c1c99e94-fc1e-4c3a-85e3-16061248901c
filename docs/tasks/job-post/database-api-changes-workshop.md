# Database & API Changes Workshop - Job Post Creator

**Date:** December 28, 2024  
**Purpose:** Essential database schema and API changes needed for Job Post Creator feature  
**Status:** Workshop Document - Work in Progress

## Current JobPost Model Analysis

### Existing Fields (Keep)
```prisma
model JobPost {
  // Core identification
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Current structured fields
  status      JobPostStatus   @default(DRAFT)
  mode        JobPostMode     @default(INDEPENDENT) 
  type        JobPostType     @default(PER_DIEM)
  priority    JobPostPriority @default(MEDIUM)
  
  // Basic content (enhance these)
  summary     String    // Job title
  scope       String    // Job description
  role        String    // Medical role
  
  // Existing relationships (keep)
  organizationId String
  locationId     String?
  departmentId   String?
  specialties    Specialty[]
}
```

## Essential Database Changes

### 1. Add Structured Data Fields to JobPost

```prisma
model JobPost {
  // ... existing fields ...
  
  // NEW: Enhanced structured data
  facilityType     String?           // "Hospital", "Clinic", "Long-term Care"
  experienceLevel  ExperienceLevel?  // ENTRY, MID, SENIOR, EXPERT
  urgencyLevel     UrgencyLevel?     // LOW, MEDIUM, HIGH, URGENT
  skillTags        String[]          // ["ICU", "Pediatrics", "Emergency"]
  
  // NEW: LLM enrichment data (JSON)
  enrichedData     Json?             // Store LLM-processed structured data
  
  // NEW: Template reference
  templateId       String?
  template         JobPostTemplate?  @relation(fields: [templateId], references: [id])
}
```

### 2. New Enums

```prisma
enum ExperienceLevel {
  ENTRY_LEVEL    // 0-2 years
  MID_LEVEL      // 2-5 years  
  SENIOR_LEVEL   // 5-10 years
  EXPERT_LEVEL   // 10+ years
  
  @@schema("public")
}

enum UrgencyLevel {
  LOW       // Standard posting
  MEDIUM    // Preferred fill
  HIGH      // Priority fill
  URGENT    // Immediate need
  
  @@schema("public")
}
```

### 3. New JobPostTemplate Model

```prisma
model JobPostTemplate {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  name        String    // "ICU Nurse Template"
  description String?   // Template description
  role        String    // Medical role this template is for
  
  // Template content
  summaryTemplate String  // Template for job title
  scopeTemplate   String  // Template for job description
  
  // Default structured data
  defaultFacilityType     String?
  defaultExperienceLevel  ExperienceLevel?
  defaultUrgencyLevel     UrgencyLevel?
  defaultSkillTags        String[]
  
  // Usage tracking
  usageCount  Int @default(0)
  isActive    Boolean @default(true)
  
  // Relationships
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  jobPosts       JobPost[]
  
  @@index([role, isActive])
  @@schema("public")
}
```

### 4. New JobEnrichmentData Model

```prisma
model JobEnrichmentData {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Source data
  originalText      String    // Original job description
  
  // LLM processing results
  extractedSkills   String[]  // Skills identified by LLM
  suggestedTags     String[]  // Suggested skill tags
  licenseRequirements String[] // Suggested licensing requirements
  
  // Processing metadata
  llmModel         String    // Which LLM model was used
  processingTime   Int?      // Processing time in ms
  confidence       Float?    // Confidence score 0-1
  
  // Relationships
  jobPostId String @unique
  jobPost   JobPost @relation(fields: [jobPostId], references: [id], onDelete: Cascade)
  
  @@schema("public")
}
```

## Essential API Changes

### 1. Update Job Creation Schema

```typescript
// Extend existing zCoreJobPost schema
const zEnhancedJobPost = z.object({
  // ... existing fields ...
  
  // NEW: Structured data fields
  facilityType: z.string().optional(),
  experienceLevel: z.nativeEnum(ExperienceLevel).optional(),
  urgencyLevel: z.nativeEnum(UrgencyLevel).optional(),
  skillTags: z.array(z.string()).optional(),
  
  // NEW: Template usage
  templateId: z.string().optional(),
  
  // NEW: Enrichment request
  requestEnrichment: z.boolean().default(false),
});
```

### 2. New API Endpoints

```typescript
// Job Templates
export const jobTemplatesRouter = createTRPCRouter({
  // Get templates for a role
  getByRole: publicProcedure
    .input(z.object({ role: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.jobPostTemplate.findMany({
        where: { role: input.role, isActive: true },
        orderBy: { usageCount: 'desc' }
      });
    }),
    
  // Create template from job post
  createFromJob: protectedProcedure
    .input(z.object({ 
      jobPostId: z.string(),
      name: z.string(),
      description: z.string().optional()
    }))
    .mutation(async ({ ctx, input }) => {
      // Implementation
    }),
});

// Job Enrichment
export const jobEnrichmentRouter = createTRPCRouter({
  // Enrich job description
  enrich: protectedProcedure
    .input(z.object({
      jobPostId: z.string(),
      text: z.string()
    }))
    .mutation(async ({ ctx, input }) => {
      // Call LLM service and store results
    }),
    
  // Get enrichment results
  getResults: protectedProcedure
    .input(z.object({ jobPostId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.jobEnrichmentData.findUnique({
        where: { jobPostId: input.jobPostId }
      });
    }),
});
```

### 3. Enhanced Search Capabilities

```typescript
// Update existing job search to include new fields
export const jobSearchRouter = createTRPCRouter({
  enhanced: publicProcedure
    .input(z.object({
      // ... existing search params ...
      
      // NEW: Structured search
      facilityTypes: z.array(z.string()).optional(),
      experienceLevels: z.array(z.nativeEnum(ExperienceLevel)).optional(),
      urgencyLevels: z.array(z.nativeEnum(UrgencyLevel)).optional(),
      skillTags: z.array(z.string()).optional(),
    }))
    .query(async ({ ctx, input }) => {
      // Enhanced search implementation
    }),
});
```

## Database Migration Strategy

### Phase 1: Add New Fields (Non-breaking)
```sql
-- Add new optional fields to JobPost
ALTER TABLE "JobPost" 
ADD COLUMN "facilityType" TEXT,
ADD COLUMN "experienceLevel" "ExperienceLevel",
ADD COLUMN "urgencyLevel" "UrgencyLevel", 
ADD COLUMN "skillTags" TEXT[],
ADD COLUMN "enrichedData" JSONB,
ADD COLUMN "templateId" TEXT;

-- Create new enums
CREATE TYPE "ExperienceLevel" AS ENUM ('ENTRY_LEVEL', 'MID_LEVEL', 'SENIOR_LEVEL', 'EXPERT_LEVEL');
CREATE TYPE "UrgencyLevel" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');
```

### Phase 2: Create New Tables
```sql
-- Create JobPostTemplate table
CREATE TABLE "JobPostTemplate" (
  "id" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "role" TEXT NOT NULL,
  "summaryTemplate" TEXT NOT NULL,
  "scopeTemplate" TEXT NOT NULL,
  "defaultFacilityType" TEXT,
  "defaultExperienceLevel" "ExperienceLevel",
  "defaultUrgencyLevel" "UrgencyLevel",
  "defaultSkillTags" TEXT[],
  "usageCount" INTEGER NOT NULL DEFAULT 0,
  "isActive" BOOLEAN NOT NULL DEFAULT true,
  "organizationId" TEXT,
  
  CONSTRAINT "JobPostTemplate_pkey" PRIMARY KEY ("id")
);

-- Create JobEnrichmentData table  
CREATE TABLE "JobEnrichmentData" (
  "id" TEXT NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  "originalText" TEXT NOT NULL,
  "extractedSkills" TEXT[],
  "suggestedTags" TEXT[],
  "licenseRequirements" TEXT[],
  "llmModel" TEXT NOT NULL,
  "processingTime" INTEGER,
  "confidence" DOUBLE PRECISION,
  "jobPostId" TEXT NOT NULL,
  
  CONSTRAINT "JobEnrichmentData_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "JobEnrichmentData_jobPostId_key" UNIQUE ("jobPostId")
);
```

### Phase 3: Add Indexes
```sql
-- Add indexes for performance
CREATE INDEX "JobPost_structured_idx" ON "JobPost"("facilityType", "experienceLevel", "urgencyLevel");
CREATE INDEX "JobPost_skillTags_idx" ON "JobPost" USING GIN("skillTags");
CREATE INDEX "JobPostTemplate_role_active_idx" ON "JobPostTemplate"("role", "isActive");
```

## API Integration Points

### 1. LLM Service Integration
- **Endpoint**: `/api/jobs/enrich`
- **Input**: Job description text
- **Output**: Structured data (skills, tags, requirements)
- **Error Handling**: Fallback to manual entry

### 2. Rate Guidance Service  
- **Endpoint**: `/api/jobs/rate-guidance`
- **Input**: Role, location, experience level, facility type
- **Output**: Suggested rate ranges with confidence scores

### 3. Template Management
- **Endpoints**: `/api/templates/*`
- **Features**: CRUD operations, usage tracking, organization-specific templates

## Next Steps

1. **Review & Validate**: Confirm these changes meet PRD requirements
2. **Detailed Migration Plan**: Create step-by-step migration scripts
3. **API Contract Design**: Finalize API endpoint specifications  
4. **LLM Integration Design**: Define LLM service integration architecture
5. **Testing Strategy**: Plan database and API testing approach
