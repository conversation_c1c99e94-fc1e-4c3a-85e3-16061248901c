# Job Posts Module Survey

**Date:** December 28, 2024  
**Purpose:** Comprehensive analysis of the job post module to understand current architecture and impact of proposed changes  
**Related PRDs:** 
- [Counter Offer – Rate Negotiations](../prd-counter-offer-rate-negotiations.md)
- [Verification](../prd-verification.md) 
- [Job Post Creator](../prd-job-post-creator.md)
- [Portable Onboarding](./prd-portable-onboarding.md)

## Executive Summary

The job post module is a core component of the AXA Med platform that handles the complete lifecycle of healthcare job postings, from creation to completion. It encompasses job creation, publishing, search, application management, offer processing, and provider matching. This survey identifies the current architecture, key components, and potential impact areas for upcoming feature implementations.

## Current Architecture Overview

### Core Components Structure

```
Job Post Module
├── Database Models (Prisma)
│   ├── JobPost (primary entity)
│   ├── JobPosition (individual positions)
│   ├── Application (provider applications)
│   ├── Offer (organization offers)
│   └── Related entities (Location, Department, Specialty, etc.)
├── API Layer (tRPC)
│   ├── Job Management (/jobs/)
│   ├── Applications (/applications/)
│   ├── Offers (/offers/)
│   ├── Search (/job-search/)
│   └── Positions (/positions/)
├── Frontend Components
│   ├── Forms (JobPostForm, ApplicationForm, OfferForm)
│   ├── Display Components (JobPost, JobCard, JobListing)
│   ├── Management Views (Organization, Provider dashboards)
│   └── Search & Filtering interfaces
└── Background Jobs
    ├── Provider notifications
    ├── Job matching
    └── Status updates
```

## Database Schema Analysis

### Primary Models

**JobPost Model** (`packages/db-medical/prisma/schema.prisma`)
- **Core Fields**: id, summary, scope, role, status, type, mode, priority
- **Timestamps**: createdAt, updatedAt, publishedAt, filledAt, etc.
- **Payment Fields**: paymentType, paymentAmount, paymentRate, nightRate, overtimeRate, holidayRate, bonusRate, billingRate
- **Relationships**: organization, location, department, specialties, applications, offers, shifts
- **Status Enum**: DRAFT, PUBLISHED, FILLED, COMPLETED, CANCELLED, EXPIRED
- **Type Enum**: PERMANENT, TEMPORARY, PER_DIEM
- **Mode Enum**: INDEPENDENT, ASSISTED
- **Priority Enum**: LOW, MEDIUM, HIGH

**Related Models**:
- **JobPosition**: Individual position instances with their own lifecycle
- **Application**: Provider applications to job posts
- **Offer**: Organization offers to providers
- **Location**: Facility/hospital information
- **Department**: Specific departments within facilities
- **Specialty**: Medical specialties and certifications

### Current Payment Structure
- Multiple rate types supported (base, night, overtime, holiday, bonus, billing)
- PayType enum: HOURLY, FLAT_RATE, PER_DIEM
- No current rate negotiation or range functionality

## API Architecture

### Main Router Structure (`packages/api-medical/src/router/jobs/`)

**Core Routers**:
- `jobs.ts` - Main job post CRUD operations
- `applications/` - Application management
- `offers/` - Offer management  
- `job-search.ts` - Search functionality
- `positions.ts` - Position management
- `job-internal.ts` - Internal admin actions
- `job-organization.ts` - Organization-specific actions

**Key Endpoints**:
- `jobs.get` - Retrieve single job with includes
- `jobs.getMany` - List jobs with filtering/pagination
- `jobs.create` - Create new job post
- `jobs.update` - Update existing job post
- `applications.provider.create` - Provider applies to job
- `offers.organization.create` - Organization sends offer

## Frontend Components

### Forms (`apps/web-med/src/components/forms/`)

**JobPostForm.tsx**
- Basic form with: organizationId, role, summary, description, type, mode
- Uses Zod validation schema
- Currently simple single-step form
- **Impact**: Will need major enhancement for guided workflow

**ApplicationForm.tsx** 
- Simple form with: jobId, providerId, message
- **Notable**: Contains TODO comment for counter offers
- **Impact**: Needs rate proposal functionality

**OfferForm.tsx**
- Form with: notes, expiresAt, providerId, jobId
- **Impact**: Needs rate specification capability

### Display Components (`apps/web-med/src/components/jobs/`)

**JobPost.tsx** - Main job display component
**JobApplicationCard.tsx** - Application status cards
**JobOfferCard.tsx** - Offer management cards

### Views and Workflows

**Provider Views**:
- `job-board/` - Browse and filter available jobs
- `job-listing/` - Individual job details and application
- Application submission and tracking

**Organization Views**:
- `jobs/` - Manage organization's job posts
- `job/` - Individual job management and provider prospecting
- Application review and offer management

## Current Workflows

### Job Creation Workflow
1. Organization creates job post (DRAFT status)
2. Fills basic information (summary, scope, role, type, mode)
3. Sets payment information
4. Publishes job (PUBLISHED status)
5. Providers can view and apply

### Application Workflow  
1. Provider views job listing
2. Submits application with message
3. Organization reviews applications
4. Organization sends offers to selected providers
5. Provider accepts/rejects offers
6. Contract generation and position creation

### Current Limitations
- **No rate negotiation** - Fixed rates only
- **Basic job creation** - Simple form, no guided workflow
- **Limited enrichment** - Manual data entry
- **No verification integration** - Separate from job flow
- **Simple search** - Basic filtering capabilities

## Integration Points

### Stripe Integration
- Payment processing for completed jobs
- Provider payout management
- **Gap**: No identity verification integration in job flow

### Messaging System
- Thread creation for applications and offers
- **Gap**: No dedicated rate negotiation interface

### Provider Verification
- Separate verification process
- **Gap**: Not integrated with application/offer flow

## Background Jobs

### Provider Notifications (`packages/api-medical/src/jobs/new-opportunities-provider.ts`)
- Notifies providers of new job opportunities matching their role
- Batched processing for performance
- Email notifications for relevant jobs

## Impact Analysis for Proposed Features

### 1. Counter Offer – Rate Negotiations

**High Impact Areas**:
- **Database**: JobPost, Application, Offer models need rate fields
- **API**: All job/application/offer endpoints need rate handling
- **Forms**: JobPostForm, ApplicationForm, OfferForm need rate inputs
- **UI**: New rate negotiation interface needed
- **Workflow**: Application and offer flows need rate negotiation steps

**New Components Needed**:
- RateNegotiationPanel component
- Rate validation logic
- Negotiation history tracking
- Rate finalization workflow

### 2. Verification Integration

**Medium Impact Areas**:
- **Application Flow**: Verification gates before application submission
- **Offer Flow**: Verification requirements before offer acceptance
- **Provider Display**: Verification status indicators
- **Organization Views**: Access to provider verification data

**Integration Points**:
- Stripe Identity verification status
- Application submission validation
- Offer acceptance validation

### 3. Job Post Creator Enhancement

**High Impact Areas**:
- **JobPostForm**: Complete replacement with guided workflow
- **Database**: New structured data fields needed
- **API**: Job creation endpoints need enrichment integration
- **Search**: Enhanced filtering and matching capabilities
- **Templates**: New template system for job types

**New Services Needed**:
- LLM integration for job enrichment
- Rate guidance calculation service
- Licensing requirement suggestion engine
- Template management system

### 4. Portable Onboarding Integration

**Medium Impact Areas**:
- **Application Flow**: Onboarding gates for incomplete providers
- **Job Listing**: Onboarding prompts for unverified providers
- **Provider Dashboard**: Onboarding progress indicators

## Risk Assessment

### High Risk Changes
1. **Rate Negotiation**: Affects core job/application/offer workflows
2. **Job Post Creator**: Major form replacement and workflow changes
3. **Database Schema**: Multiple model changes across features

### Medium Risk Changes
1. **Verification Integration**: New validation points in existing flows
2. **Search Enhancement**: Algorithm and indexing changes

### Low Risk Changes
1. **Onboarding Integration**: Mostly additive UI components
2. **Background Job Updates**: Isolated notification improvements

## Recommendations

### Development Approach
1. **Phase 1**: Database schema updates for all features
2. **Phase 2**: API enhancements for rate negotiation and verification
3. **Phase 3**: Frontend component updates and new interfaces
4. **Phase 4**: Job Post Creator guided workflow implementation
5. **Phase 5**: Integration testing and optimization

### Testing Strategy
- **Unit Tests**: All new API endpoints and validation logic
- **Integration Tests**: End-to-end job creation and application flows
- **User Acceptance Tests**: Complete workflows with real user scenarios
- **Performance Tests**: Search and filtering with new structured data

### Migration Considerations
- **Data Migration**: Existing jobs need default values for new fields
- **Backward Compatibility**: Ensure existing workflows continue during rollout
- **Feature Flags**: Gradual rollout of new functionality
- **User Training**: Documentation and training for new guided workflows

## Next Steps

1. **Detailed Technical Design**: Create detailed technical specifications for each feature
2. **Database Migration Planning**: Plan schema changes and data migration strategy  
3. **API Contract Definition**: Define new API endpoints and data structures
4. **Component Architecture**: Design new UI components and integration patterns
5. **Testing Framework**: Establish comprehensive testing strategy
6. **Rollout Planning**: Define feature flag strategy and gradual rollout plan
