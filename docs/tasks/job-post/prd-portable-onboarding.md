# Portable Provider Onboarding Experience PRD

**Status:** Work in Progress  
**Last Updated:** June 20, 2025  
**Owner:** Product Team

**TODO:**

- [ ] Survey the current on-boarding experience for providers and pinpoint the few spots that can be transferred over.
- [ ] Strategically place certain components for certain steps and banking setup at the earnings page and other things.

## Executive Summary

The Portable Provider Onboarding Experience aims to eliminate the "waiting in lobby" state by creating contextual onboarding flows that can be embedded throughout the AXA platform. This system will guide medical providers through essential verification steps while allowing them to explore the dashboard and apply for opportunities immediately.

## Strategic Vision

### Core Philosophy

- **Get providers into the dashboard immediately** - No more lobby waiting
- **Progressive disclosure** - Introduce features as providers progress
- **Portable components** - Onboarding moments work across dashboard, marketplace, and application flows
- **Non-blocking experience** - Providers can explore while completing verification

### Key Principles

1. **Contextual Onboarding** - Show relevant next steps based on current flow
2. **Portable UI Components** - Reusable across dashboard, marketplace, and application contexts
3. **AI-First Approach** - Streamline data entry with intelligent extraction
4. **Integration-Ready** - Stripe identity verification and banking setup
5. **Transparent Progress** - Clear indication of completion status and next steps

## Problem Statement

Currently, medical providers experience friction during onboarding:

- **Lobby Wait State** - Providers wait for approval before accessing core features
- **Disconnected Flows** - Onboarding happens separately from product exploration
- **Manual Data Entry** - Repetitive form filling instead of AI-assisted extraction
- **Unclear Progress** - Providers don't understand verification status or next steps

## Success Metrics

### Primary KPIs

- **Time to Dashboard Access** - Reduce from approval-gated to immediate
- **Onboarding Completion Rate** - % of providers completing all 3 steps
- **Application Submission Rate** - Increase in providers applying to opportunities
- **User Engagement** - Dashboard exploration during onboarding

### Secondary KPIs

- **AI Extraction Accuracy** - Resume data extraction success rate
- **Identity Verification Success** - Stripe verification completion rate
- **Bank Setup Completion** - Provider payment setup completion rate

## User Personas

### Primary: Medical Provider (New User)

- **Goal:** Get hired for medical opportunities quickly
- **Pain Points:** Complex onboarding, unclear requirements, waiting periods
- **Needs:** Clear progress tracking, fast data entry, immediate platform access

### Secondary: AXA MET Team

- **Goal:** Efficient provider approval with proper verification
- **Pain Points:** Incomplete provider profiles, verification delays
- **Needs:** Complete provider data, verified identities, compliant documentation

## User Scenarios & Flows

### Scenario 1: Dashboard-First Onboarding

**Context:** New provider signs up and immediately accesses dashboard

```
Provider Journey:
1. Sign up → Immediate dashboard access
2. Onboarding banner: "Complete your profile to apply for opportunities"
3. Step 1: Upload Professional Profile
   - AI resume import OR manual form
   - Progress: 1/3 complete
4. Step 2: Verify Identity
   - Stripe identity verification
   - Progress: 2/3 complete
5. Step 3: Setup Banking
   - Stripe banking setup
   - Progress: 3/3 complete → "Ready to get hired!"
```

**User Flow:**

```
[Sign Up] → [Dashboard Access] → [Onboarding Banner]
    ↓
[Profile Upload] → [AI Processing] → [Review & Confirm]
    ↓
[Identity Verification] → [Stripe KYC] → [Verification Complete]
    ↓
[Banking Setup] → [Stripe Connect] → [Account Complete]
    ↓
[Full Platform Access] → [Application Ready]
```

### Scenario 2: Application-Flow Onboarding

**Context:** Provider finds opportunity and needs to complete verification to apply

```
Provider Journey:
1. Browse marketplace → Find interesting opportunity
2. Click "Apply" → Onboarding gate appears
3. "Complete verification to apply" modal
4. Same 3-step process but with context:
   - "Complete profile to apply for [Job Title]"
   - "Verify identity to submit application"
   - "Setup banking to receive payments"
5. After completion → Return to application flow
```

**User Flow:**

```
[Browse Opportunities] → [Click Apply] → [Verification Gate]
    ↓
[Contextual Onboarding Modal] → [Complete Steps] → [Return to Application]
    ↓
[Submit Application] → [Enter Negotiation Flow]
```

### Scenario 3: Offer-Flow Onboarding

**Context:** Provider receives offer but needs verification to proceed

```
Provider Journey:
1. Receive offer notification → View offer details
2. Click "Accept" → Verification required
3. "Complete verification to accept offer" flow
4. Same 3-step process with urgency context
5. After completion → Proceed with offer acceptance
```

## Onboarding Steps Deep Dive

### Step 1: Professional Profile Upload

**Options:**

- **AI Resume Import** (Preferred)
  - Upload PDF/DOC resume
  - AI extracts: Name, credentials, experience, specializations
  - Provider reviews and confirms data
- **Manual Form** (Fallback)
  - Guided form with smart defaults
  - Progressive disclosure of fields

**Components Needed:**

- `ResumeUploadWidget`
- `AIExtractionReview`
- `ProfileFormFallback`

### Step 2: Identity Verification

**Stripe Integration:**

- Government ID upload
- Selfie verification
- Address verification
- Real-time status updates

**Components Needed:**

- `StripeIdentityWidget`
- `VerificationStatus`
- `DocumentUpload`

### Step 3: Banking Setup

**Stripe Connect Integration:**

- Bank account linking
- Tax information collection
- Payout preferences
- Compliance verification

**Components Needed:**

- `StripeBankingWidget`
- `PayoutPreferences`
- `TaxInformation`

## Component Architecture

### Core Onboarding Components

```typescript
// Main orchestrator component
<OnboardingFlow
  context="dashboard" | "application" | "offer"
  onComplete={(steps) => void}
  currentStep?: OnboardingStep
  providerId: string
/>

// Individual step components
<ProfileUploadStep
  onComplete={(profileData) => void}
  allowAIExtraction={true}
  context="application"
/>

<IdentityVerificationStep
  onComplete={(verificationId) => void}
  stripeConfig={stripeConfig}
/>

<BankingSetupStep
  onComplete={(accountId) => void}
  stripeConfig={stripeConfig}
/>
```

### Portable Wrapper Components

```typescript
// Dashboard integration
<DashboardOnboardingBanner
  steps={incompleteSteps}
  onStepClick={(step) => void}
/>

// Application gate
<ApplicationOnboardingGate
  jobId={jobId}
  requiredSteps={["profile", "identity", "banking"]}
  onComplete={() => proceedToApplication()}
/>

// Offer gate
<OfferOnboardingGate
  offerId={offerId}
  urgencyLevel="high"
  onComplete={() => proceedToAcceptance()}
/>
```

### State Management

```typescript
interface OnboardingState {
  providerId: string;
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  profileData?: ProfileData;
  verificationStatus?: VerificationStatus;
  bankingStatus?: BankingStatus;
  context: OnboardingContext;
}

enum OnboardingStep {
  PROFILE_UPLOAD = "profile",
  IDENTITY_VERIFICATION = "identity",
  BANKING_SETUP = "banking",
}

enum OnboardingContext {
  DASHBOARD = "dashboard",
  APPLICATION = "application",
  OFFER = "offer",
}
```

## Technical Implementation

### API Endpoints

```typescript
// Onboarding status
GET / api / providers / { id } / onboarding - status;
POST / api / providers / { id } / onboarding - steps / { step } / complete;

// AI Profile extraction
POST / api / providers / profile / extract - resume;
PUT / api / providers / { id } / profile / review - extraction;

// Stripe integrations
POST / api / providers / { id } / stripe / identity - session;
GET / api / providers / { id } / stripe / identity - status;
POST / api / providers / { id } / stripe / banking - session;
```

### Database Schema Extensions

```sql
-- Provider onboarding tracking
ALTER TABLE providers ADD COLUMN onboarding_completed_at TIMESTAMP;
ALTER TABLE providers ADD COLUMN profile_extraction_data JSONB;
ALTER TABLE providers ADD COLUMN stripe_identity_session_id VARCHAR;
ALTER TABLE providers ADD COLUMN stripe_account_id VARCHAR;

-- Onboarding events
CREATE TABLE onboarding_events (
  id UUID PRIMARY KEY,
  provider_id UUID REFERENCES providers(id),
  step VARCHAR NOT NULL,
  context VARCHAR NOT NULL,
  completed_at TIMESTAMP,
  metadata JSONB
);
```

### Service Layer

```typescript
class OnboardingService {
  async getProviderStatus(providerId: string): Promise<OnboardingStatus>;
  async startProfileExtraction(
    providerId: string,
    resumeFile: File,
  ): Promise<ExtractionResult>;
  async createStripeIdentitySession(providerId: string): Promise<StripeSession>;
  async createStripeBankingSession(providerId: string): Promise<StripeSession>;
  async markStepComplete(
    providerId: string,
    step: OnboardingStep,
  ): Promise<void>;
}
```

## Integration Points

### AI Resume Extraction

- **Service:** OpenAI/Perplexity integration
- **Input:** PDF/DOC resume files
- **Output:** Structured profile data
- **Fallback:** Manual form completion

### Stripe Identity Verification

- **Service:** Stripe Identity API
- **Features:** Government ID, selfie, address verification
- **Compliance:** KYC/AML requirements

### Stripe Banking Setup

- **Service:** Stripe Connect
- **Features:** Bank linking, tax forms, payout setup
- **Compliance:** Financial regulations

## Provider Status State Machine

```
SIGNED_UP → PROFILE_PENDING → PROFILE_COMPLETE
    ↓              ↓              ↓
IDENTITY_PENDING → IDENTITY_COMPLETE → BANKING_PENDING
    ↓              ↓              ↓
ONBOARDING_COMPLETE → MET_REVIEW → APPROVED/REJECTED
```

**States:**

- **PROFILE_PENDING** - Can browse, cannot apply
- **IDENTITY_PENDING** - Can browse, limited applications
- **BANKING_PENDING** - Can apply, cannot accept offers
- **ONBOARDING_COMPLETE** - Can apply and negotiate
- **APPROVED** - Full platform access

## UI/UX Guidelines

### Design Principles

1. **Progressive Disclosure** - Show steps as needed
2. **Context Awareness** - Tailor messaging to current flow
3. **Clear Progress** - Visual step indicators
4. **Non-Intrusive** - Don't block exploration
5. **Mobile-First** - Responsive design for all devices

### Component Variants

- **Banner** - Persistent dashboard reminder
- **Modal** - Application/offer gate
- **Sidebar** - Step-by-step guidance
- **Toast** - Completion confirmations

### Messaging Strategy

- **Dashboard Context:** "Complete your profile to unlock opportunities"
- **Application Context:** "Verify your identity to apply for [Job Title]"
- **Offer Context:** "Complete banking setup to accept this offer"

## Implementation Roadmap

### Phase 1: Core Infrastructure (2 weeks)

- [ ] Onboarding state management
- [ ] Database schema updates
- [ ] Basic UI components
- [ ] API endpoint scaffolding

### Phase 2: AI & Stripe Integration (3 weeks)

- [ ] Resume AI extraction service
- [ ] Stripe Identity integration
- [ ] Stripe Banking integration
- [ ] Error handling & fallbacks

### Phase 3: Portable Components (2 weeks)

- [ ] Dashboard onboarding banner
- [ ] Application onboarding gate
- [ ] Offer onboarding gate
- [ ] Mobile responsiveness

### Phase 4: Analytics & Optimization (1 week)

- [ ] Event tracking
- [ ] Completion rate monitoring
- [ ] A/B testing framework
- [ ] Performance optimization

## Success Criteria

### Launch Readiness

- [ ] All 3 onboarding steps functional
- [ ] Portable components work in dashboard, application, and offer contexts
- [ ] AI resume extraction >85% accuracy
- [ ] Stripe integrations pass compliance review
- [ ] Mobile experience optimized
- [ ] Analytics tracking implemented

### Post-Launch (30 days)

- [ ] > 80% onboarding completion rate
- [ ] <2 minute average completion time per step
- [ ] > 90% provider satisfaction score
- [ ] 50% increase in application submissions

## Open Questions & Risks

### Technical Risks

- **AI Extraction Accuracy** - Resume parsing may miss key details
- **Stripe Integration Complexity** - Identity/banking setup edge cases
- **Mobile Performance** - File uploads on mobile devices

### Product Risks

- **User Fatigue** - Too many onboarding prompts
- **Verification Friction** - Identity checks may frustrate users
- **Context Switching** - Interrupting application flow

### Mitigation Strategies

- Comprehensive testing with real resume samples
- Fallback flows for all Stripe integration points
- Progressive web app features for mobile uploads
- Smart suppression of onboarding prompts
- Skip options for later completion
- Save progress across context switches

---

**Next Steps:**

1. Technical feasibility review with engineering team
2. Design mockups for each onboarding context
3. Stripe integration proof of concept
4. AI resume extraction pilot testing
