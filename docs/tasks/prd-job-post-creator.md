# PRD: Job Post Creator

## Introduction/Overview

The Job Post Creator feature transforms the current basic job posting process into a comprehensive, guided workflow that helps hiring organizations create professional, detailed, and well-structured job posts. Currently, the platform has a simple form for basic job information, but lacks the sophistication needed to create compelling job posts that attract quality healthcare providers and provide all necessary details for informed applications.

This feature solves the problem of incomplete or poorly structured job posts by providing guided creation, automatic enrichment, structured data capture, and intelligent suggestions based on platform data and industry standards.

## Goals

1. Replace the current basic job posting form with a comprehensive guided workflow
2. Automatically enrich job posts with relevant industry information and requirements
3. Capture structured data to improve search and matching capabilities
4. Provide intelligent pay rate guidance based on historical platform data
5. Generate professional job descriptions that increase provider engagement
6. Standardize job post quality across the platform
7. Reduce time-to-post while improving post quality

## User Stories

### As a Hiring Organization:
- I want a step-by-step guide for creating job posts so that I don't miss important information
- I want automatic suggestions for licensing requirements so that I ensure compliance
- I want pay rate recommendations based on similar positions so that I can offer competitive compensation
- I want my job descriptions to be professional and comprehensive so that I attract quality candidates
- I want to easily set schedules, locations, and requirements so that providers have clear expectations

### As a Healthcare Provider:
- I want to see detailed, professional job posts so that I can make informed application decisions
- I want clear information about requirements and qualifications so that I know if I'm eligible
- I want structured job information so that I can easily filter and search for relevant positions
- I want comprehensive job descriptions so that I understand the role and expectations

### As a System Administrator:
- I want standardized job post data so that I can provide better analytics and insights
- I want improved search capabilities so that providers can find relevant positions more easily
- I want consistent job post quality so that the platform maintains professional standards

## Functional Requirements

1. **Guided Job Creation Workflow**
   - System must provide a multi-step interface for job post creation
   - System must guide users through: role definition, schedule creation, location setup, rate setting, requirements specification, and description creation
   - System must allow users to save progress and return to incomplete job posts
   - System must validate required fields at each step before allowing progression

2. **Enhanced Job Data Capture**
   - System must capture structured data for: job title, role type, facility type, department, required experience level, shift type (per diem, temporary, permanent), and urgency level
   - System must maintain existing functionality for: schedule creation, location/facility selection, contact information, and department assignment
   - System must capture rate information with the new rate range functionality
   - System must allow for multiple contact assignments per job post

3. **Automatic Job Enrichment**
   - System must process job descriptions through LLM-based enrichment to extract structured requirements
   - System must automatically identify and suggest state-specific licensing requirements based on role and location
   - System must generate role-based skill tags from job descriptions and requirements
   - System must suggest relevant perks, expectations, and work context based on role type and facility

4. **Licensing Requirements Integration**
   - System must access existing provider qualification database to suggest relevant licensing requirements
   - System must automatically populate common licensing requirements based on role type and state
   - System must allow manual override and addition of specific licensing requirements
   - System must validate suggested requirements against known provider qualifications in the system

5. **Pay Rate Guidance System**
   - System must analyze historical job post data to provide rate recommendations
   - System must consider factors: job type, experience requirements, location, facility type, and shift urgency
   - System must display rate ranges from similar positions posted on the platform
   - System must allow hiring organizations to accept, modify, or ignore rate suggestions

6. **Rich Text Description Editor**
   - System must provide markdown/WYSIWYG editor for job description creation
   - System must support formatting options: headers, lists, bold/italic text, and links
   - System must provide templates for common job description sections
   - System must allow preview of formatted job descriptions before publishing

7. **Structured Data Output**
   - System must convert all job information into standardized database fields
   - System must ensure all enriched data is properly categorized and tagged
   - System must maintain backward compatibility with existing job post data structure
   - System must generate searchable metadata from job content

8. **Enhanced Job Presentation**
   - System must display enriched job posts with improved formatting and organization
   - System must highlight key information: rate range, requirements, schedule, and location
   - System must present licensing requirements in a clear, scannable format
   - System must maintain consistent presentation across all job posts

9. **Improved Search and Filtering**
   - System must enable filtering by structured fields: role type, experience level, facility type, shift type, and requirements
   - System must support keyword search across enriched job content
   - System must provide advanced search capabilities for providers
   - System must improve job matching algorithms using structured data

10. **Job Post Management**
    - System must allow editing of published job posts with change tracking
    - System must provide preview functionality before publishing
    - System must maintain version history for job post modifications
    - System must allow duplication of job posts for similar positions

## Non-Goals (Out of Scope)

- AI Quick Mode functionality (removed per user request)
- Integration with external job boards or posting services
- Automated job posting scheduling or recurring posts
- Advanced analytics dashboard for job post performance
- Integration with external salary benchmarking services
- Bulk job post creation or management tools
- Custom branding or styling options for individual job posts

## Design Considerations

- Guided workflow should feel intuitive and not overwhelming
- Use progressive disclosure to show relevant options based on previous selections
- Provide clear visual indicators of completion progress
- Ensure mobile-responsive design for hiring managers who may post jobs on mobile devices
- Maintain consistent styling with existing platform components
- Consider accessibility requirements for all form elements and editors

## Technical Considerations

- Extend existing job post data model to accommodate new structured fields
- Implement LLM integration for job description enrichment with proper error handling
- Design efficient database queries for rate guidance calculations
- Implement proper caching for frequently accessed rate and requirement data
- Ensure rich text editor security to prevent XSS attacks
- Consider performance implications of real-time enrichment vs. background processing
- Implement proper validation for all structured data inputs

## Success Metrics

- 90% of new job posts use the guided workflow within 2 months of launch
- 50% improvement in job post completeness (measured by filled structured fields)
- 25% increase in provider applications per job post
- 80% of hiring organizations use suggested licensing requirements
- 70% of hiring organizations use pay rate guidance
- Reduction in job post creation time by 30% despite increased detail capture
- User satisfaction score of 4.2+ for the job creation experience
- 40% improvement in job search result relevance based on structured data

## Open Questions

1. Should the system automatically save draft job posts as users progress through the workflow?
2. How should the system handle conflicts between suggested licensing requirements and organization preferences?
3. Should there be approval workflows for job posts before they go live?
4. How should the system handle job posts that fail LLM enrichment processing?
5. Should hiring organizations be able to customize which enrichment suggestions they want to see?
6. How should the system handle rate guidance when there's insufficient historical data for comparison?
