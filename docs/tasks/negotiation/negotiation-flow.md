# Negotiation Flow: Kaleidoscope Perspective

## Overview

This document presents a unified view of the negotiation flow, combining three interconnected perspectives:

- **User Journey**: What the user sees and experiences
- **Data Journey**: How database state evolves
- **UI State**: What interface elements and values are displayed

Each stage shows the kaleidoscope of these three elements working together to create a seamless negotiation experience.

---

## Stage 1: Job Posting Creation

### 🏥 Organization Perspective

#### User Journey

- Organization creates job posting
- Optionally sets rate range ($45-65/hr)
- Chooses negotiation policy (strict/flexible)
- Publishes job to platform

#### Data Journey

```sql
-- JobPost table state
INSERT INTO "JobPost" (
  id: "job_123",
  title: "Emergency Room Nurse Practitioner",
  minRate: 45.00,
  maxRate: 65.00,
  rateUnit: "HOUR",
  allowNegotiation: true,
  strictRateRange: false,
  status: "ACTIVE"
)
```

#### UI State

```tsx
<JobPostForm>
  <RateRangeConfig
    minRate={45}
    maxRate={65}
    strictMode={false}
    period="hour"
    showHelp={true}
  />
  <PublishButton status="ready" />
</JobPostForm>
```

### 🩺 Provider Perspective

#### User Journey

- Provider browses available positions
- Sees job with transparent rate range
- Reviews requirements and compensation

#### Data Journey

```sql
-- No data changes, read-only queries
SELECT * FROM "JobPost"
WHERE status = 'ACTIVE'
  AND allowNegotiation = true
```

#### UI State

```tsx
<JobPostCard>
  <RateRange min={45} max={65} period="hour" variant="badge" />
  <NegotiationBadge text="Negotiable" variant="success" />
</JobPostCard>
```

---

## Stage 2A: Provider-Initiated Negotiation (Application Path)

### 🩺 Provider Action: Submit Application with Rate Proposal

#### User Journey

- Provider clicks "Apply"
- Proposes rate ($58/hr based on experience)
- Adds justification message
- Submits application

#### Data Journey

```sql
-- Step 1: Create Application
INSERT INTO "Application" (
  id: "app_456",
  jobId: "job_123",
  providerId: "provider_789",
  proposedRate: 58.00,
  status: "PENDING"
)

-- Step 2: Initialize negotiation steps
UPDATE "Application"
SET steps = '[
  {"type": "rate_proposal", "status": "in_progress"},
  {"type": "rate_negotiation", "status": "pending"},
  {"type": "rate_agreement", "status": "pending"},
  {"type": "contract_generation", "status": "pending"},
  {"type": "contract_terms", "status": "pending"},
  {"type": "signature_collection", "status": "pending"}
]'::jsonb
WHERE id = "app_456"

-- Step 3: Create Initial Rate Action
INSERT INTO "Action" (
  id: "action_001",
  type: "RATE_PROPOSAL_APPLICATION",
  actorId: "provider_789",
  jobId: "job_123",
  applicationId: "app_456",
  metadata: {
    "proposedRate": 58.00,
    "rateUnit": "HOUR",
    "message": "Based on my 8 years of ER experience",
    "round": 1,
    "proposerType": "PROVIDER",
    "withinRange": true,
    "status": "PENDING",
    "isCurrentProposal": true
  }
)
```

#### UI State

```tsx
<ApplicationForm>
  <RateInput
    value={58}
    period="hour"
    validationRange={{ min: 45, max: 65 }}
    showValidation={true}
    validationMessages={{
      withinRange: "✅ Your rate is within the posted range",
    }}
  />
  <MessageField
    value="Based on my 8 years of ER experience"
    placeholder="Optional: Explain your rate proposal"
  />
  <SubmitButton>Apply with Rate Proposal</SubmitButton>
</ApplicationForm>
```

### 🏥 Organization Response View

#### User Journey

- Organization receives application notification
- Reviews provider credentials and rate proposal
- Sees rate is within acceptable range
- Decides to counter with $55/hr

#### Data Journey

```sql
-- Read current negotiation state (simplified)
SELECT a.steps, act.metadata
FROM "Application" a
JOIN "Action" act ON act.applicationId = a.id
WHERE a.id = "app_456"
  AND act.metadata->>'isCurrentProposal' = 'true'

-- Result shows:
-- steps: [{"type": "rate_proposal", "status": "in_progress"}, ...]
-- proposedRate: 58.00
-- withinRange: true
-- round: 1
-- status: "PENDING"
```

#### UI State

```tsx
<ApplicationReviewCard>
  <ProviderSummary name="Dr. Sarah Chen" experience="8 years" />
  <NegotiationTimeline>
    <NegotiationEntry
      action={rateProposalAction}
      type="proposal"
      showActions={true}
      currentUserType="organization"
    />
  </NegotiationTimeline>
  <RateCounterCard
    currentRate={58}
    period="hour"
    suggestedRange={{ min: 45, max: 65 }}
    onCounter={handleCounter}
    onAccept={handleAccept}
  />
</ApplicationReviewCard>
```

---

## Stage 2B: Organization-Initiated Negotiation (Offer Path)

### 🏥 Organization Action: Send Competitive Offer

#### User Journey

- Organization browses provider profiles
- Selects preferred provider
- Sends competitive rate offer ($52/hr)
- Includes recruitment message

#### Data Journey

```sql
-- Step 1: Create Offer
INSERT INTO "Offer" (
  id: "offer_789",
  jobId: "job_123",
  providerId: "provider_456",
  organizationId: "org_321",
  proposedRate: 52.00,
  status: "PENDING"
)

-- Step 2: Create Negotiation Context
INSERT INTO "NegotiationContext" (
  id: "neg_002",
  jobId: "job_123",
  sourceType: "offer",
  sourceId: "offer_789",
  providerId: "provider_456",
  organizationId: "org_321",
  currentPhase: "rate",
  rateAgreed: false
)

-- Step 3: Create Initial Rate Action
INSERT INTO "Action" (
  id: "action_002",
  type: "RATE_PROPOSAL_OFFER",
  actorId: "org_321",
  jobId: "job_123",
  offerId: "offer_789",
  metadata: {
    "proposedRate": 52.00,
    "rateUnit": "HOUR",
    "message": "We'd love to have you join our team",
    "round": 1,
    "proposerType": "ORGANIZATION",
    "withinRange": true,
    "status": "PENDING",
    "isCurrentProposal": true
  }
)
```

#### UI State

```tsx
<OfferCreationForm>
  <ProviderSummary name="Dr. Alex Johnson" specialty="Emergency Medicine" />
  <RateInput
    value={52}
    period="hour"
    validationRange={{ min: 45, max: 65 }}
    showValidation={true}
  />
  <MessageField
    value="We'd love to have you join our team"
    placeholder="Optional: Personal recruitment message"
  />
  <SendOfferButton>Send Competitive Offer</SendOfferButton>
</OfferCreationForm>
```

### 🩺 Provider Response View

#### User Journey

- Provider receives offer notification
- Reviews organization and rate proposal
- Rate is lower than expected
- Decides to counter with $60/hr

#### Data Journey

```sql
-- Read current negotiation state
SELECT o.*, nc.*, act.metadata
FROM "Offer" o
JOIN "NegotiationContext" nc ON nc.sourceId = o.id
JOIN "Action" act ON act.offerId = o.id
WHERE o.id = "offer_789"
  AND act.metadata->>'isCurrentProposal' = 'true'
```

#### UI State

```tsx
<OfferReviewCard>
  <OrganizationSummary name="Regional Medical Center" rating={4.8} />
  <NegotiationTimeline>
    <NegotiationEntry
      action={offerAction}
      type="proposal"
      showActions={true}
      currentUserType="provider"
    />
  </NegotiationTimeline>
  <RateCounterCard
    currentRate={52}
    period="hour"
    onCounter={handleCounter}
    onAccept={handleAccept}
    onDecline={handleDecline}
  />
</OfferReviewCard>
```

---

## Stage 3: Rate Counter-Negotiation Rounds

### 🔄 Turn 1: Organization Counter (from Application path)

#### User Journey

- Organization counters provider's $58/hr with $55/hr
- Adds business justification
- Maintains professional tone

#### Data Journey

```sql
-- Step 1: Mark previous proposal as superseded
UPDATE "Action"
SET metadata = jsonb_set(metadata, '{status}', '"SUPERSEDED"')
WHERE applicationId = "app_456"
  AND metadata->>'isCurrentProposal' = 'true'

-- Step 2: Create counter action
INSERT INTO "Action" (
  id: "action_003",
  type: "RATE_COUNTER_ORGANIZATION",
  actorId: "org_321",
  jobId: "job_123",
  applicationId: "app_456",
  metadata: {
    "proposedRate": 55.00,
    "rateUnit": "HOUR",
    "message": "Great experience! This rate aligns with our budget",
    "round": 2,
    "proposerType": "ORGANIZATION",
    "withinRange": true,
    "status": "PENDING",
    "isCurrentProposal": true,
    "respondsToActionId": "action_001"
  }
)

-- Step 3: Phase remains "rate" (tracked via Actions count)
```

#### UI State

```tsx
<NegotiationPanel>
  <NegotiationTimeline>
    <NegotiationEntry
      action={providerProposal}
      type="proposal"
      status="superseded"
    />
    <NegotiationEntry
      action={orgCounter}
      type="counter"
      status="current"
      showActions={false} // Organization's turn complete
    />
  </NegotiationTimeline>
  <CurrentStatus>
    <StatusBadge status="pending" text="Waiting for provider response" />
    <NextAction user="provider" />
  </CurrentStatus>
</NegotiationPanel>
```

### 🔄 Turn 2: Provider Final Counter

#### User Journey

- Provider reviews $55/hr counter
- Decides to meet in middle at $56/hr
- Positions as final offer

#### Data Journey

```sql
-- Step 1: Mark organization counter as superseded
UPDATE "Action"
SET metadata = jsonb_set(metadata, '{status}', '"SUPERSEDED"')
WHERE id = "action_003"

-- Step 2: Create provider counter
INSERT INTO "Action" (
  id: "action_004",
  type: "RATE_COUNTER_PROVIDER",
  actorId: "provider_789",
  jobId: "job_123",
  applicationId: "app_456",
  metadata: {
    "proposedRate": 56.00,
    "rateUnit": "HOUR",
    "message": "Let's meet in the middle - final offer",
    "round": 3,
    "proposerType": "PROVIDER",
    "withinRange": true,
    "status": "PENDING",
    "isCurrentProposal": true,
    "respondsToActionId": "action_003"
  }
)

-- Step 3: Phase remains "rate" (round derived from action count)
```

#### UI State

```tsx
<NegotiationPanel>
  <NegotiationTimeline>
    <NegotiationEntry action={providerInitial} status="superseded" />
    <NegotiationEntry action={orgCounter} status="superseded" />
    <NegotiationEntry action={providerFinal} status="current" />
  </NegotiationTimeline>
  <RateProgression>
    <RateStep rate={58} status="initial" />
    <RateStep rate={55} status="counter" />
    <RateStep rate={56} status="current" />
  </RateProgression>
  <CurrentStatus>
    <StatusBadge status="pending" text="Waiting for organization decision" />
  </CurrentStatus>
</NegotiationPanel>
```

---

## Stage 4: Rate Agreement

### 🤝 Organization Accepts Rate

#### User Journey

- Organization reviews $56/hr proposal
- Determines it's acceptable within budget
- Clicks "Accept Rate" to finalize

#### Data Journey

```sql
-- Step 1: Create acceptance action
INSERT INTO "Action" (
  id: "action_005",
  type: "RATE_ACCEPTANCE",
  actorId: "org_321",
  jobId: "job_123",
  applicationId: "app_456",
  metadata: {
    "acceptedRate": 56.00,
    "rateUnit": "HOUR",
    "message": "Agreed! Looking forward to working together",
    "round": 3,
    "proposerType": "ORGANIZATION",
    "status": "ACCEPTED",
    "respondsToActionId": "action_004"
  }
)

-- Step 2: Progress to next step (contract generation)
UPDATE "Application"
SET steps = jsonb_set(
  jsonb_set(steps, '{0,status}', '"completed"'),
  '{3,status}', '"in_progress"'
)
WHERE id = "app_456"

-- Step 3: Create JobPosition (existing logic)
INSERT INTO "JobPosition" (
  id: "position_123",
  jobId: "job_123",
  providerId: "provider_789",
  organizationId: "org_321",
  agreedRate: 56.00,
  rateUnit: "HOUR",
  status: "PENDING_CONTRACT"
)

-- Step 4: Update application status
UPDATE "Application"
SET status = "RATE_AGREED"
WHERE id = "app_456"
```

#### UI State

```tsx
<NegotiationPanel>
  <RateAgreementBanner>
    <SuccessIcon />
    <Text>Rate Agreed: $56/hr</Text>
    <ProgressIndicator current="contract" />
  </RateAgreementBanner>

  <NegotiationTimeline>
    <NegotiationEntry action={providerFinal} status="superseded" />
    <NegotiationEntry action={acceptance} status="accepted" />
  </NegotiationTimeline>

  <NextSteps>
    <ContractPreview rate={56} />
    <ContinueButton>Proceed to Contract Terms</ContinueButton>
  </NextSteps>
</NegotiationPanel>
```

---

## Stage 5: Contract Generation & Terms Negotiation

### 🔄 Automatic Contract Generation

#### User Journey

- System automatically generates contract
- Pre-fills agreed rate and basic terms
- Presents contract for review

#### Data Journey

```sql
-- Step 1: Generate contract (using existing contract module)
INSERT INTO "Contract" (
  id: "contract_456",
  jobPositionId: "position_123",
  providerId: "provider_789",
  organizationId: "org_321",
  agreedRate: 56.00,
  rateUnit: "HOUR",
  status: "DRAFT",
  -- Contract terms from job posting defaults
  paymentTerms: "Net 30",
  cancellationPolicy: "30 days notice",
  startDate: "2024-02-01"
)

-- Step 2: Contract association handled via JobPosition relationship

-- Step 3: Create contract initiation action
INSERT INTO "Action" (
  id: "action_006",
  type: "CONTRACT_INITIATE",
  actorId: "system", -- System-generated
  jobId: "job_123",
  applicationId: "app_456",
  contractId: "contract_456",
  metadata: {
    "contractGenerated": true,
    "agreedRate": 56.00,
    "phase": "contract_terms",
    "status": "PENDING_REVIEW"
  }
)
```

#### UI State

```tsx
<ContractReviewPanel>
  <PhaseIndicator>
    <Phase name="Rate" status="completed" />
    <Phase name="Contract" status="active" />
    <Phase name="Finalize" status="pending" />
  </PhaseIndicator>

  <ContractPreview>
    <RateDisplay amount={56} period="hour" variant="locked" />
    <ContractTerms
      paymentTerms="Net 30"
      cancellationPolicy="30 days notice"
      startDate="2024-02-01"
    />
  </ContractPreview>

  <ContractActions>
    <AcceptButton>Accept Terms</AcceptButton>
    <NegotiateButton>Negotiate Terms</NegotiateButton>
  </ContractActions>
</ContractReviewPanel>
```

### 🔄 Contract Terms Negotiation

#### User Journey

- Provider wants to negotiate payment terms
- Requests "Net 15" instead of "Net 30"
- Adds justification for request

#### Data Journey

```sql
-- Step 1: Create contract terms negotiation action
INSERT INTO "Action" (
  id: "action_007",
  type: "CONTRACT_TERMS_PROPOSAL",
  actorId: "provider_789",
  jobId: "job_123",
  applicationId: "app_456",
  contractId: "contract_456",
  metadata: {
    "proposedTerms": {
      "paymentTerms": "Net 15",
      "cancellationPolicy": "30 days notice", -- unchanged
      "startDate": "2024-02-01" -- unchanged
    },
    "modifiedFields": ["paymentTerms"],
    "message": "Prefer faster payment for cash flow",
    "round": 1,
    "proposerType": "PROVIDER",
    "status": "PENDING",
    "isCurrentProposal": true
  }
)

-- Step 2: Round tracking via action count (no separate tracking needed)
```

#### UI State

```tsx
<ContractNegotiationPanel>
  <ContractComparison>
    <TermsComparison
      original={{ paymentTerms: "Net 30" }}
      proposed={{ paymentTerms: "Net 15" }}
      highlight={["paymentTerms"]}
    />
  </ContractComparison>

  <NegotiationTimeline>
    <NegotiationEntry
      action={contractProposal}
      type="contract_proposal"
      status="pending"
    />
  </NegotiationTimeline>

  <CurrentStatus>
    <StatusBadge status="pending" text="Waiting for organization response" />
  </CurrentStatus>
</ContractNegotiationPanel>
```

---

## Stage 6: Contract Agreement & Finalization

### 🤝 Organization Accepts Contract Terms

#### User Journey

- Organization reviews payment terms request
- Accepts "Net 15" payment terms
- Contract is finalized for signatures

#### Data Journey

```sql
-- Step 1: Create contract acceptance action
INSERT INTO "Action" (
  id: "action_008",
  type: "CONTRACT_TERMS_ACCEPTANCE",
  actorId: "org_321",
  jobId: "job_123",
  applicationId: "app_456",
  contractId: "contract_456",
  metadata: {
    "acceptedTerms": {
      "paymentTerms": "Net 15",
      "cancellationPolicy": "30 days notice",
      "startDate": "2024-02-01"
    },
    "message": "Agreed to Net 15 payment terms",
    "status": "ACCEPTED",
    "respondsToActionId": "action_007"
  }
)

-- Step 2: Update contract with final terms
UPDATE "Contract"
SET
  paymentTerms = "Net 15",
  status = "PENDING_SIGNATURE",
  finalizedAt = NOW()
WHERE id = "contract_456"

-- Step 3: Complete final step
UPDATE "Application"
SET steps = jsonb_set(steps, '{5,status}', '"completed"')
WHERE id = "app_456"

-- Step 4: Update job position status
UPDATE "JobPosition"
SET status = "PENDING_SIGNATURE"
WHERE id = "position_123"

-- Step 5: Create negotiation completion action
INSERT INTO "Action" (
  id: "action_009",
  type: "NEGOTIATION_COMPLETE",
  actorId: "system",
  jobId: "job_123",
  applicationId: "app_456",
  contractId: "contract_456",
  metadata: {
    "finalRate": 56.00,
    "finalTerms": {
      "paymentTerms": "Net 15",
      "cancellationPolicy": "30 days notice"
    },
    "totalRounds": 3,
    "negotiationDuration": "2 days",
    "status": "COMPLETED"
  }
)
```

#### UI State

```tsx
<NegotiationCompletionPanel>
  <SuccessBanner>
    <CheckIcon />
    <Title>Negotiation Complete!</Title>
    <Subtitle>Ready for contract signature</Subtitle>
  </SuccessBanner>

  <FinalSummary>
    <AgreedRate amount={56} period="hour" />
    <AgreedTerms
      paymentTerms="Net 15"
      cancellationPolicy="30 days notice"
      startDate="2024-02-01"
    />
  </FinalSummary>

  <NextSteps>
    <ContractSigningButton>Proceed to Contract Signing</ContractSigningButton>
  </NextSteps>

  <NegotiationSummary
    totalRounds={3}
    duration="2 days"
    participants={[provider, organization]}
  />
</NegotiationCompletionPanel>
```

---

## Data State Summary

### Final Database State After Complete Negotiation

```sql
-- JobPost: Original posting
JobPost {
  id: "job_123",
  minRate: 45.00,
  maxRate: 65.00,
  allowNegotiation: true
}

-- Application: Provider's application with negotiation steps
Application {
  id: "app_456",
  proposedRate: 58.00,
  status: "RATE_AGREED",
  steps: [
    {"type": "rate_proposal", "status": "completed"},
    {"type": "rate_negotiation", "status": "completed"},
    {"type": "rate_agreement", "status": "completed"},
    {"type": "contract_generation", "status": "completed"},
    {"type": "contract_terms", "status": "completed"},
    {"type": "signature_collection", "status": "completed"}
  ]
}

-- JobPosition: Created when rate agreed
JobPosition {
  id: "position_123",
  agreedRate: 56.00,
  status: "PENDING_SIGNATURE"
}

-- Contract: Generated with final terms
Contract {
  id: "contract_456",
  agreedRate: 56.00,
  paymentTerms: "Net 15",
  status: "PENDING_SIGNATURE"
}

-- Actions: Complete audit trail
Action[] {
  action_001: RATE_PROPOSAL_APPLICATION (58.00),
  action_003: RATE_COUNTER_ORGANIZATION (55.00),
  action_004: RATE_COUNTER_PROVIDER (56.00),
  action_005: RATE_ACCEPTANCE (56.00),
  action_006: CONTRACT_INITIATE,
  action_007: CONTRACT_TERMS_PROPOSAL (Net 15),
  action_008: CONTRACT_TERMS_ACCEPTANCE (Net 15),
  action_009: NEGOTIATION_COMPLETE
}
```

### Query Patterns for UI Data

```sql
-- Get complete negotiation timeline
SELECT a.*, p.name as actor_name
FROM "Action" a
JOIN "Person" p ON p.id = a.actorId
WHERE a.applicationId = "app_456"
  AND a.type LIKE '%RATE%' OR a.type LIKE '%CONTRACT%'
ORDER BY a.createdAt ASC

-- Get current negotiation status (simplified)
SELECT a.steps, jp.status as position_status, c.status as contract_status
FROM "Application" a
LEFT JOIN "JobPosition" jp ON jp.id = a.positionId
LEFT JOIN "Contract" c ON c.positionId = jp.id
WHERE a.id = "app_456"

-- Get active proposal for UI
SELECT a.* FROM "Action" a
WHERE a.applicationId = "app_456"
  AND a.metadata->>'isCurrentProposal' = 'true'
  AND a.metadata->>'status' = 'PENDING'
```

This kaleidoscope perspective shows how user experience, data state, and UI components work together seamlessly throughout the entire negotiation lifecycle, from initial rate proposals through final contract agreement.

## Benefits of Simplified Architecture

### ✅ **Unified Shape Maintained**

- Application and Offer models keep their existing structure
- Only one new field: `negotiationPhase`
- All negotiation context derived from existing relationships

### ✅ **Action Model Leverage**

- Complete audit trail via existing Action patterns
- Rich metadata support already proven
- Consistent with platform's event-driven architecture
- No new tables to maintain or query

### ✅ **Query Efficiency**

- Single join to get negotiation state: `Application -> Actions`
- Round counting via `COUNT()` on Actions
- Current proposal via metadata flag
- Timeline via `ORDER BY createdAt`

### ✅ **Future Extensibility**

- Adding benefits negotiation = just new action types
- AI integration via Action metadata
- Analytics via existing Action aggregations
- No schema changes for new negotiation types

This approach maintains the "unified shape" you mentioned while adding the minimal delta needed for negotiation tracking. The negotiation phase becomes the single state transition point, with everything else derivable from existing data structures.

## Configurable Negotiation Pipeline System

### Overview

To support different hiring scenarios and flexible negotiation paths, we'll implement a step-based system that allows organizations to define custom negotiation workflows while maintaining the Action-based architecture.

### Step Definition Framework

#### Core Step Types

```typescript
enum NegotiationStepType {
  // Rate negotiation steps
  RATE_PROPOSAL = "rate_proposal",
  RATE_NEGOTIATION = "rate_negotiation",
  RATE_AGREEMENT = "rate_agreement",

  // Contract steps
  CONTRACT_GENERATION = "contract_generation",
  CONTRACT_TERMS = "contract_terms",
  CONTRACT_REVIEW = "contract_review",

  // Benefits steps (future)
  BENEFITS_DISCUSSION = "benefits_discussion",
  BENEFITS_AGREEMENT = "benefits_agreement",

  // Scheduling steps (future)
  SCHEDULE_NEGOTIATION = "schedule_negotiation",
  SCHEDULE_CONFIRMATION = "schedule_confirmation",

  // Compliance & Background steps
  BACKGROUND_CHECK_INITIATION = "background_check_initiation",
  BACKGROUND_CHECK_REVIEW = "background_check_review",
  COMPLIANCE_VERIFICATION = "compliance_verification",
  CREDENTIAL_VERIFICATION = "credential_verification",

  // Finalization
  SIGNATURE_COLLECTION = "signature_collection",
  ONBOARDING_PREP = "onboarding_prep",
  ONBOARDING_COMPLETION = "onboarding_completion",
  COMPLETE = "complete",
}

interface NegotiationStep {
  id: string;
  type: NegotiationStepType;
  name: string;
  description: string;

  // Conditional logic
  required: boolean;
  skipConditions?: StepSkipCondition[];
  dependencies?: string[]; // IDs of prerequisite steps

  // Business rules
  timeoutHours?: number;
  allowedActors: ("provider" | "organization" | "system")[];
  autoAdvanceOn?: ActionType[];

  // UI configuration
  componentType: string; // Maps to UI component
  config?: Record<string, any>;
}

interface StepSkipCondition {
  condition:
    | "existing_employee"
    | "quick_hire"
    | "rate_only"
    | "contract_exists";
  value?: any;
}
```

#### Predefined Workflow Templates

```typescript
// Standard full negotiation (default)
const STANDARD_WORKFLOW: Step[] = [
  { type: NegotiationStepType.RATE_PROPOSAL, status: StepStatus.PENDING },
  { type: NegotiationStepType.RATE_NEGOTIATION, status: StepStatus.PENDING },
  { type: NegotiationStepType.RATE_AGREEMENT, status: StepStatus.PENDING },
  { type: NegotiationStepType.CONTRACT_GENERATION, status: StepStatus.PENDING },
  { type: NegotiationStepType.CONTRACT_TERMS, status: StepStatus.PENDING },
  {
    type: NegotiationStepType.SIGNATURE_COLLECTION,
    status: StepStatus.PENDING,
  },
];

// Quick tele-hire workflow (rate-only)
const QUICK_HIRE_WORKFLOW: Step[] = [
  { type: NegotiationStepType.RATE_PROPOSAL, status: StepStatus.PENDING },
  { type: NegotiationStepType.RATE_AGREEMENT, status: StepStatus.PENDING },
  { type: NegotiationStepType.ONBOARDING_PREP, status: StepStatus.PENDING },
];

// Existing employee additional work
const EXISTING_EMPLOYEE_WORKFLOW: Step[] = [
  { type: NegotiationStepType.RATE_PROPOSAL, status: StepStatus.PENDING },
  {
    type: NegotiationStepType.SCHEDULE_NEGOTIATION,
    status: StepStatus.PENDING,
  },
  { type: NegotiationStepType.RATE_AGREEMENT, status: StepStatus.PENDING },
];

// Background check and compliance workflow (new providers needing full verification)
const BACKGROUND_CHECK_WORKFLOW: Step[] = [
  { type: NegotiationStepType.RATE_PROPOSAL, status: StepStatus.PENDING },
  { type: NegotiationStepType.RATE_AGREEMENT, status: StepStatus.PENDING },
  { type: NegotiationStepType.CONTRACT_GENERATION, status: StepStatus.PENDING },
  {
    type: NegotiationStepType.BACKGROUND_CHECK_INITIATION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.CREDENTIAL_VERIFICATION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.COMPLIANCE_VERIFICATION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.BACKGROUND_CHECK_REVIEW,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.SIGNATURE_COLLECTION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.ONBOARDING_COMPLETION,
    status: StepStatus.PENDING,
  },
];

// Post-signature compliance workflow (provider hired but needs background completion)
const POST_HIRE_COMPLIANCE_WORKFLOW: Step[] = [
  {
    type: NegotiationStepType.BACKGROUND_CHECK_INITIATION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.CREDENTIAL_VERIFICATION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.COMPLIANCE_VERIFICATION,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.BACKGROUND_CHECK_REVIEW,
    status: StepStatus.PENDING,
  },
  {
    type: NegotiationStepType.ONBOARDING_COMPLETION,
    status: StepStatus.PENDING,
  },
];

// Step metadata configuration (separate from step state)
const STEP_CONFIG = {
  [NegotiationStepType.RATE_PROPOSAL]: {
    name: "Rate Proposal",
    description: "Initial rate proposal from provider or organization",
    allowedActors: ["provider", "organization"],
    componentType: "RateProposalCard",
  },
  [NegotiationStepType.RATE_NEGOTIATION]: {
    name: "Rate Discussion",
    description: "Back-and-forth rate negotiation",
    allowedActors: ["provider", "organization"],
    timeoutHours: 48,
    componentType: "RateNegotiationPanel",
  },
  [NegotiationStepType.RATE_AGREEMENT]: {
    name: "Rate Finalization",
    description: "Final rate acceptance",
    allowedActors: ["provider", "organization"],
    componentType: "RateFinalizationCard",
  },
  [NegotiationStepType.CONTRACT_GENERATION]: {
    name: "Contract Creation",
    description: "Automatic contract generation",
    allowedActors: ["system"],
    componentType: "ContractGenerationStatus",
  },
  [NegotiationStepType.CONTRACT_TERMS]: {
    name: "Contract Terms",
    description: "Negotiate contract terms and conditions",
    allowedActors: ["provider", "organization"],
    timeoutHours: 72,
    componentType: "ContractTermsPanel",
  },
  [NegotiationStepType.SIGNATURE_COLLECTION]: {
    name: "Contract Signing",
    description: "Collect signatures from all parties",
    allowedActors: ["provider", "organization"],
    componentType: "SignatureCollectionCard",
  },
  [NegotiationStepType.ONBOARDING_PREP]: {
    name: "Onboarding Setup",
    allowedActors: ["organization"],
    componentType: "QuickOnboardingCard",
  },
  [NegotiationStepType.SCHEDULE_NEGOTIATION]: {
    name: "Schedule Coordination",
    allowedActors: ["provider", "organization"],
    componentType: "ScheduleNegotiationPanel",
  },
  [NegotiationStepType.BACKGROUND_CHECK_INITIATION]: {
    name: "Background Check Start",
    description: "Initiate background verification process",
    allowedActors: ["organization", "system"],
    componentType: "BackgroundCheckInitiationCard",
  },
  [NegotiationStepType.CREDENTIAL_VERIFICATION]: {
    name: "Credential Verification",
    description: "Verify medical licenses and certifications",
    allowedActors: ["organization", "system"],
    timeoutHours: 120, // 5 days for credential verification
    componentType: "CredentialVerificationCard",
  },
  [NegotiationStepType.COMPLIANCE_VERIFICATION]: {
    name: "Compliance Check",
    description: "Verify regulatory compliance requirements",
    allowedActors: ["organization", "system"],
    timeoutHours: 72,
    componentType: "ComplianceVerificationCard",
  },
  [NegotiationStepType.BACKGROUND_CHECK_REVIEW]: {
    name: "Background Review",
    description: "Review and approve background check results",
    allowedActors: ["organization"],
    componentType: "BackgroundCheckReviewCard",
  },
  [NegotiationStepType.ONBOARDING_COMPLETION]: {
    name: "Onboarding Complete",
    description: "Finalize onboarding and compliance verification",
    allowedActors: ["organization"],
    componentType: "OnboardingCompletionCard",
  },
};
```

### Dynamic Step Management

#### Application/Offer Schema Extension

```sql
-- Add steps configuration to Application/Offer models
ALTER TABLE "Application" ADD COLUMN "steps" JSONB;
ALTER TABLE "Offer" ADD COLUMN "steps" JSONB;
```

```typescript
// Step definition
type Step = {
  type: NegotiationStepType;
  status: StepStatus;
}

enum StepStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  SKIPPED = "skipped",
  FAILED = "failed"
}

// Model structure
model Application {
  // ... existing fields
  steps Json? // Step[]
}

model Offer {
  // ... existing fields
  steps Json? // Step[]
}
```

#### Step Progression Logic

```typescript
interface NegotiationWorkflow {
  id: string;
  applicationId?: string;
  offerId?: string;

  // Current state - simplified to just step array
  steps: Step[];

  // Metadata
  workflowType: "standard" | "quick_hire" | "existing_employee" | "custom";
  startedAt: Date;
  estimatedCompletionAt?: Date;
}

// Step progression via Actions
async function advanceNegotiationStep(
  applicationId: string,
  actionType: ActionType,
  metadata: any,
) {
  const application = await getApplicationWithSteps(applicationId);
  const currentStep = getCurrentStep(application.steps);

  // Check if action should advance step
  if (shouldAutoAdvanceStep(currentStep, actionType)) {
    const updatedSteps = progressToNextStep(application.steps);

    await updateApplication(applicationId, {
      steps: updatedSteps,
    });

    // Create step transition action
    await createAction({
      type: "NEGOTIATION_STEP_ADVANCE",
      applicationId,
      metadata: {
        fromStep: currentStep.type,
        toStep: getCurrentStep(updatedSteps).type,
        trigger: actionType,
        autoAdvanced: true,
      },
    });
  }
}

// Helper functions for step management
function getCurrentStep(steps: Step[]): Step | null {
  return (
    steps.find((step) => step.status === StepStatus.IN_PROGRESS) ||
    steps.find((step) => step.status === StepStatus.PENDING) ||
    null
  );
}

function progressToNextStep(steps: Step[]): Step[] {
  const currentIndex = steps.findIndex(
    (step) =>
      step.status === StepStatus.IN_PROGRESS ||
      step.status === StepStatus.PENDING,
  );

  if (currentIndex === -1) return steps;

  const updatedSteps = [...steps];

  // Mark current step as completed
  updatedSteps[currentIndex] = {
    ...updatedSteps[currentIndex],
    status: StepStatus.COMPLETED,
  };

  // Find next step to activate
  for (let i = currentIndex + 1; i < updatedSteps.length; i++) {
    if (updatedSteps[i].status === StepStatus.PENDING) {
      updatedSteps[i] = {
        ...updatedSteps[i],
        status: StepStatus.IN_PROGRESS,
      };
      break;
    }
  }

  return updatedSteps;
}

function shouldAutoAdvanceStep(
  step: Step | null,
  actionType: ActionType,
): boolean {
  if (!step) return false;

  // Define auto-advance conditions
  const AUTO_ADVANCE_MAP = {
    [NegotiationStepType.RATE_AGREEMENT]: [ActionType.ACCEPT],
    [NegotiationStepType.CONTRACT_GENERATION]: [ActionType.CREATE],
    [NegotiationStepType.BACKGROUND_CHECK_INITIATION]: [ActionType.INITIATE],
    [NegotiationStepType.CREDENTIAL_VERIFICATION]: [ActionType.VERIFY],
    [NegotiationStepType.COMPLIANCE_VERIFICATION]: [ActionType.VERIFY],
    [NegotiationStepType.BACKGROUND_CHECK_REVIEW]: [ActionType.APPROVE],
    [NegotiationStepType.ONBOARDING_COMPLETION]: [ActionType.COMPLETE],
    // Add more mappings as needed
  };

  return AUTO_ADVANCE_MAP[step.type]?.includes(actionType) ?? false;
}
```

### Workflow Selection Logic

#### Automatic Workflow Detection

```typescript
async function determineNegotiationWorkflow(
  jobId: string,
  providerId: string,
  organizationId: string,
  sourceType: "application" | "offer",
): Promise<Step[]> {
  // Check for existing employment relationship
  const existingPosition = await prisma.jobPosition.findFirst({
    where: {
      providerId,
      organizationId,
      status: { in: ["ACTIVE", "COMPLETED"] },
    },
  });

  if (existingPosition) {
    return initializeWorkflow(EXISTING_EMPLOYEE_WORKFLOW);
  }

  // Check job posting for quick hire flag
  const job = await prisma.jobPost.findUnique({
    where: { id: jobId },
    select: {
      type: true,
      metadata: true, // Could contain quickHire: true flag
    },
  });

  if (job?.metadata?.quickHire || job?.type === "PER_DIEM") {
    return initializeWorkflow(QUICK_HIRE_WORKFLOW);
  }

  // Check if provider needs background verification
  const providerProfile = await prisma.provider.findUnique({
    where: { id: providerId },
    select: {
      backgroundCheckStatus: true,
      complianceStatus: true,
      credentialsVerified: true,
    },
  });

  const needsBackgroundCheck =
    !providerProfile?.backgroundCheckStatus ||
    !providerProfile?.complianceStatus ||
    !providerProfile?.credentialsVerified;

  if (needsBackgroundCheck) {
    return initializeWorkflow(BACKGROUND_CHECK_WORKFLOW);
  }

  // Default to standard workflow
  return initializeWorkflow(STANDARD_WORKFLOW);
}

// Initialize workflow with first step active
function initializeWorkflow(workflow: Step[]): Step[] {
  const steps = [...workflow];
  if (steps.length > 0) {
    steps[0] = { ...steps[0], status: StepStatus.IN_PROGRESS };
  }
  return steps;
}

// Organization-specific workflow customization
async function getOrganizationWorkflow(
  organizationId: string,
  workflowType: string,
): Promise<Step[]> {
  const orgSettings = await prisma.organizationSetting.findUnique({
    where: { organizationId },
    select: {
      negotiationWorkflows: true, // JSONB field with custom workflows
    },
  });

  const customWorkflow = orgSettings?.negotiationWorkflows?.[workflowType];
  return customWorkflow
    ? initializeWorkflow(customWorkflow)
    : initializeWorkflow(STANDARD_WORKFLOW);
}
```

### UI Component Mapping

#### Dynamic Component Rendering

```typescript
// Map step types to UI components
const STEP_COMPONENT_MAP = {
  [NegotiationStepType.RATE_PROPOSAL]: RateProposalCard,
  [NegotiationStepType.RATE_NEGOTIATION]: RateNegotiationPanel,
  [NegotiationStepType.RATE_AGREEMENT]: RateFinalizationCard,
  [NegotiationStepType.CONTRACT_GENERATION]: ContractGenerationStatus,
  [NegotiationStepType.CONTRACT_TERMS]: ContractTermsPanel,
  [NegotiationStepType.SIGNATURE_COLLECTION]: SignatureCollectionCard,
  [NegotiationStepType.ONBOARDING_PREP]: OnboardingPrepCard,
  [NegotiationStepType.SCHEDULE_NEGOTIATION]: ScheduleNegotiationPanel,
  [NegotiationStepType.BACKGROUND_CHECK_INITIATION]: BackgroundCheckInitiationCard,
  [NegotiationStepType.CREDENTIAL_VERIFICATION]: CredentialVerificationCard,
  [NegotiationStepType.COMPLIANCE_VERIFICATION]: ComplianceVerificationCard,
  [NegotiationStepType.BACKGROUND_CHECK_REVIEW]: BackgroundCheckReviewCard,
  [NegotiationStepType.ONBOARDING_COMPLETION]: OnboardingCompletionCard,
};

// Dynamic negotiation interface
function NegotiationInterface({ applicationId }: { applicationId: string }) {
  const { steps, actions } = useNegotiationWorkflow(applicationId);
  const currentStep = getCurrentStep(steps);

  if (!currentStep) return <div>No active negotiation step</div>;

  const StepComponent = STEP_COMPONENT_MAP[currentStep.type];
  const stepConfig = STEP_CONFIG[currentStep.type];

  return (
    <div className="negotiation-interface">
      <NegotiationProgress steps={steps} />

      <StepComponent
        step={currentStep}
        config={stepConfig}
        actions={actions}
        onAction={handleStepAction}
      />

      <NegotiationTimeline actions={actions} />
    </div>
  );
}
```

### Example Workflow Scenarios

#### Scenario 1: Quick Tele-Hire

```typescript
// Job posting marked as quick hire
const jobPost = {
  type: "PER_DIEM",
  metadata: { quickHire: true },
  // ... other fields
};

// Automatically uses QUICK_HIRE_WORKFLOW:
// 1. Rate Proposal → 2. Rate Agreement → 3. Onboarding Prep
// Skips: Contract generation, terms negotiation, signature collection
```

#### Scenario 2: Existing Employee Additional Work

```typescript
// Provider already has active position with organization
const existingPosition = await prisma.jobPosition.findFirst({
  where: { providerId, organizationId, status: "ACTIVE" },
});

// Automatically uses EXISTING_EMPLOYEE_WORKFLOW:
// 1. Additional Work Rate → 2. Schedule Coordination → 3. Work Agreement
// Skips: Contract terms (uses existing), signature collection (framework exists)
```

#### Scenario 3: Standard Full Negotiation

```typescript
// New provider, permanent position
// Uses STANDARD_WORKFLOW:
// 1. Rate Proposal → 2. Rate Negotiation → 3. Rate Agreement →
// 4. Contract Generation → 5. Contract Terms → 6. Signature Collection
```

#### Scenario 4: Background Check & Compliance Workflow

```typescript
// New provider needing full verification
const providerProfile = {
  backgroundCheckStatus: null,
  complianceStatus: false,
  credentialsVerified: false,
};

// Automatically uses BACKGROUND_CHECK_WORKFLOW:
// 1. Rate Proposal → 2. Rate Agreement → 3. Contract Generation →
// 4. Background Check Initiation → 5. Credential Verification →
// 6. Compliance Verification → 7. Background Review →
// 8. Signature Collection → 9. Onboarding Completion
```

#### Scenario 5: Post-Hire Compliance Completion

```typescript
// Provider already hired but needs to complete pending background checks
const application = await prisma.application.update({
  where: { id: applicationId },
  data: {
    steps: POST_HIRE_COMPLIANCE_WORKFLOW,
  },
});

// Uses POST_HIRE_COMPLIANCE_WORKFLOW:
// 1. Background Check Initiation → 2. Credential Verification →
// 3. Compliance Verification → 4. Background Review → 5. Onboarding Completion
```

#### Scenario 6: Custom Organization Workflow

```typescript
// Organization with specific requirements
const customWorkflow = await getOrganizationWorkflow(orgId, "travel_nurse");

// Custom steps might include:
// 1. Rate Proposal → 2. Housing Negotiation → 3. Travel Allowance →
// 4. Rate Agreement → 5. Contract Generation → 6. Signature Collection
```

### Benefits of Configurable Pipeline System

#### ✅ **Flexibility Without Complexity**

- Standard workflows cover 90% of cases
- Custom workflows for special scenarios
- Step skipping based on business logic
- No architectural changes to Action system

#### ✅ **Future Extensibility**

- New step types = new UI components
- New workflows = new JSON configurations
- Benefits/scheduling steps easily added
- Organization-specific customization

#### ✅ **Maintained Simplicity**

- Action-based audit trail unchanged
- Single `steps` JSON field tracks all progression
- No additional tracking fields needed
- UI components map to step types

#### ✅ **Business Value**

- Quick hires complete in minutes vs. hours
- Existing employees avoid redundant paperwork
- Complex negotiations maintain full audit trails
- Organizations can customize to their processes

This configurable pipeline system provides the flexibility you need while maintaining the architectural simplicity of the Action-based approach. The step sequences become the "strings of arrays" you mentioned, with automatic progression logic and smart skipping capabilities.

---

## Real-World Use Case: Post-Hire Background Completion

### Scenario: Pending Provider Compliance

A healthcare provider has been hired and signed their contract, but their background check and compliance verification are still pending. The organization needs to track and manage the completion of these requirements before the provider can start working.

### 🔄 Stage 1: Initiating Post-Hire Compliance

#### User Journey

- Organization reviews hired providers with pending compliance
- Identifies Dr. Sarah Chen needs background check completion
- Initiates compliance workflow for completed hire

#### Data Journey

```sql
-- Update existing application with compliance steps
UPDATE "Application"
SET steps = '[
  {"type": "background_check_initiation", "status": "in_progress"},
  {"type": "credential_verification", "status": "pending"},
  {"type": "compliance_verification", "status": "pending"},
  {"type": "background_check_review", "status": "pending"},
  {"type": "onboarding_completion", "status": "pending"}
]'::jsonb
WHERE id = "app_456"

-- Create compliance initiation action
INSERT INTO "Action" (
  id: "action_010",
  type: "BACKGROUND_CHECK_INITIATE",
  actorId: "org_321",
  applicationId: "app_456",
  metadata: {
    "backgroundCheckProvider": "Sterling",
    "checkTypes": ["criminal", "employment", "education"],
    "urgency": "high",
    "status": "INITIATED"
  }
)
```

#### UI State

```tsx
<ComplianceTrackingPanel>
  <ProviderSummary
    name="Dr. Sarah Chen"
    status="Hired - Pending Compliance"
    startDate="2024-02-15"
  />

  <ComplianceProgress>
    <Step
      type="background_check_initiation"
      status="in_progress"
      estimatedCompletion="2024-02-01"
    />
    <Step type="credential_verification" status="pending" />
    <Step type="compliance_verification" status="pending" />
    <Step type="background_check_review" status="pending" />
    <Step type="onboarding_completion" status="pending" />
  </ComplianceProgress>

  <BackgroundCheckInitiationCard
    provider="Sterling"
    checkTypes={["criminal", "employment", "education"]}
    onInitiate={handleInitiate}
  />
</ComplianceTrackingPanel>
```

### 🔄 Stage 2: Automated Credential Verification

#### User Journey

- System automatically verifies provider's medical licenses
- Cross-references with state medical boards
- Provider receives notification of verification status

#### Data Journey

```sql
-- System completes background check initiation, advances to credential verification
UPDATE "Application"
SET steps = jsonb_set(
  jsonb_set(steps, '{0,status}', '"completed"'),
  '{1,status}', '"in_progress"'
)
WHERE id = "app_456"

-- Create credential verification action
INSERT INTO "Action" (
  id: "action_011",
  type: "CREDENTIAL_VERIFY",
  actorId: "system",
  applicationId: "app_456",
  metadata: {
    "licenseNumber": "RN123456789",
    "state": "CA",
    "verificationStatus": "VERIFIED",
    "expirationDate": "2025-12-31",
    "verificationSource": "CA Board of Nursing"
  }
)
```

#### UI State

```tsx
<CredentialVerificationCard>
  <VerificationStatus status="verified" />

  <LicenseDetails>
    <License
      type="Registered Nurse"
      number="RN123456789"
      state="California"
      status="Active"
      expires="2025-12-31"
    />
  </LicenseDetails>

  <VerificationSources>
    <Source name="CA Board of Nursing" status="verified" />
    <Source name="National Database" status="verified" />
  </VerificationSources>

  <NextAction>
    <AutoAdvanceIndicator text="Advancing to compliance verification..." />
  </NextAction>
</CredentialVerificationCard>
```

### 🔄 Stage 3: Compliance Verification Review

#### User Journey

- Organization compliance officer reviews verification results
- Checks for any flags or outstanding requirements
- Approves compliance status

#### Data Journey

```sql
-- Progress to compliance verification step
UPDATE "Application"
SET steps = jsonb_set(
  jsonb_set(steps, '{1,status}', '"completed"'),
  '{2,status}', '"in_progress"'
)
WHERE id = "app_456"

-- Create compliance verification action
INSERT INTO "Action" (
  id: "action_012",
  type: "COMPLIANCE_VERIFY",
  actorId: "compliance_officer_456",
  applicationId: "app_456",
  metadata: {
    "complianceChecks": {
      "malpracticeInsurance": "verified",
      "continuingEducation": "verified",
      "drugScreening": "verified"
    },
    "reviewedBy": "compliance_officer_456",
    "reviewNotes": "All requirements met",
    "status": "APPROVED"
  }
)
```

#### UI State

```tsx
<ComplianceVerificationCard>
  <ComplianceChecklist>
    <CheckItem
      item="Malpractice Insurance"
      status="verified"
      details="$2M coverage active"
    />
    <CheckItem
      item="Continuing Education"
      status="verified"
      details="40 CEUs completed 2023"
    />
    <CheckItem
      item="Drug Screening"
      status="verified"
      details="Passed - 2024-01-15"
    />
  </ComplianceChecklist>

  <ReviewSection>
    <ReviewerInfo officer="Jane Smith" date="2024-01-28" />
    <ReviewNotes text="All requirements met" />
    <ApprovalActions>
      <ApproveButton onClick={handleApprove} />
      <RequestMoreInfoButton onClick={handleRequest} />
    </ApprovalActions>
  </ReviewSection>
</ComplianceVerificationCard>
```

### 🔄 Stage 4: Final Background Review & Onboarding

#### User Journey

- Hiring manager reviews complete background package
- All verifications passed, provider cleared to start
- Onboarding completion triggers provider activation

#### Data Journey

```sql
-- Complete final steps
UPDATE "Application"
SET steps = '[
  {"type": "background_check_initiation", "status": "completed"},
  {"type": "credential_verification", "status": "completed"},
  {"type": "compliance_verification", "status": "completed"},
  {"type": "background_check_review", "status": "completed"},
  {"type": "onboarding_completion", "status": "completed"}
]'::jsonb
WHERE id = "app_456"

-- Create final completion action
INSERT INTO "Action" (
  id: "action_013",
  type: "ONBOARDING_COMPLETE",
  actorId: "hiring_manager_789",
  applicationId: "app_456",
  metadata: {
    "clearanceLevel": "full",
    "startDateConfirmed": "2024-02-15",
    "onboardingCompletedBy": "hiring_manager_789",
    "finalNotes": "Provider cleared for immediate start",
    "status": "ACTIVE"
  }
)

-- Update provider status
UPDATE "Provider"
SET
  backgroundCheckStatus = "COMPLETED",
  complianceStatus = "VERIFIED",
  credentialsVerified = true,
  status = "ACTIVE"
WHERE id = "provider_789"
```

#### UI State

```tsx
<OnboardingCompletionCard>
  <CompletionSummary>
    <SuccessIcon />
    <Title>Background Verification Complete!</Title>
    <ProviderName>Dr. Sarah Chen</ProviderName>
    <ClearanceLevel level="full" />
  </CompletionSummary>

  <CompletionDetails>
    <Timeline>
      <TimelineItem
        step="Background Check"
        date="2024-01-25"
        status="completed"
      />
      <TimelineItem
        step="Credential Verification"
        date="2024-01-26"
        status="completed"
      />
      <TimelineItem
        step="Compliance Review"
        date="2024-01-28"
        status="completed"
      />
      <TimelineItem step="Final Review" date="2024-01-30" status="completed" />
    </Timeline>
  </CompletionDetails>

  <NextSteps>
    <StartDateConfirmation date="2024-02-15" />
    <WelcomePackageLink />
    <ProviderActivationButton onClick={handleActivation} />
  </NextSteps>
</OnboardingCompletionCard>
```

### Benefits for Healthcare Compliance

#### ✅ **Regulatory Compliance**

- Complete audit trail for all verification steps
- Automated tracking of license expiration dates
- Compliance with healthcare staffing regulations
- Integration with third-party verification services

#### ✅ **Risk Management**

- No provider starts without completed background checks
- Real-time visibility into compliance status
- Automated alerts for missing requirements
- Clear escalation paths for compliance issues

#### ✅ **Operational Efficiency**

- Parallel processing of verification tasks
- Automated progression through verification steps
- Exception handling for failed verifications
- Integration with existing HRIS and compliance systems

#### ✅ **Provider Experience**

- Transparent process with clear status updates
- Minimal provider intervention required
- Proactive communication about requirements
- Quick resolution of compliance issues

This real-world use case demonstrates how the flexible step system handles complex compliance workflows that go far beyond simple rate negotiations, making it a truly comprehensive solution for healthcare staffing challenges.

---
