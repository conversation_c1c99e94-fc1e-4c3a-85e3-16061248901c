# Counter Offer - Rate Negotiations Data Architecture

## Overview

This guide outlines the data structures and architectural decisions for implementing rate negotiations in the medical platform. We're taking a phased approach, starting with core data models and building up to the complete negotiation system.

## Current Schema Analysis

### Existing Rate-Related Fields

From the current schema, we already have rate/payment fields in key models:

#### `JobPost` Model

```prisma
paymentType   PayType @default(HOURLY)  // HOURLY | FIXED
paymentAmount Float   @default(0.0)
paymentRate   Float   @default(1.0)
nightRate     Float   @default(1.25)
overtimeRate  Float   @default(1.5)
holidayRate   Float   @default(2.0)
bonusRate     Float   @default(1.0)
```

#### `Application` Model

```prisma
# Currently minimal - needs rate proposal fields
status ApplicationStatus @default(PENDING)
notes  String?
```

#### `Offer` Model

```prisma
# Currently minimal - needs rate proposal fields
status OfferStatus @default(PENDING)
notes  String?
```

## Phase 1: Core Data Structures

### 1.1 Rate Range Configuration

Extend the `JobPost` model to support rate ranges and negotiation policies:

```typescript
// New fields to add to JobPost
interface JobPostRateExtensions {
  // Rate Range Configuration
  minRate?: number;
  maxRate?: number;
  rateUnit: RateUnit; // 'HOUR' | 'DAY' | 'WEEK' | 'MONTH' | 'YEAR'

  // Negotiation Policy
  allowNegotiation: boolean;
  strictRateRange: boolean; // If true, proposals outside range are rejected

  // Metadata
  marketMedianRate?: number;
  lastUpdatedRates?: Date;
}

// New enum for rate units
enum RateUnit {
  HOUR
  DAY
  WEEK
  MONTH
  YEAR
}
```

### 1.2 Rate Proposals

Extend `Application` and `Offer` models to include rate proposals:

```typescript
// New fields to add to Application
interface ApplicationRateExtensions {
  proposedRate?: number;
  proposedRateUnit?: RateUnit;
  rateMessage?: string; // Optional justification message
  rateProposedAt?: Date;
}

// New fields to add to Offer
interface OfferRateExtensions {
  proposedRate?: number;
  proposedRateUnit?: RateUnit;
  rateMessage?: string;
  rateProposedAt?: Date;
}
```

### 1.3 Rate Negotiation History via Action Model

Instead of creating a separate rate negotiation table, we'll leverage the existing `Action` model to track negotiation timeline and history. This provides consistency with the platform's existing audit trail pattern.

````typescript
// Rate negotiation actions using existing Action model
interface RateNegotiationAction {
  // Uses existing Action fields
  id: string;
  createdAt: Date;
  type: RateActionType; // New action types for rate negotiations
  metadata: RateActionMetadata; // Rich metadata in JSON field

  // Existing relationships we'll use
  actorId: string; // Person who made this proposal
  jobId?: string;
  applicationId?: string;
  offerId?: string;
}

// New action types for rate negotiations
enum RateActionType {
  RATE_PROPOSAL_APPLICATION = 'RATE_PROPOSAL_APPLICATION'
  RATE_PROPOSAL_OFFER = 'RATE_PROPOSAL_OFFER'
  RATE_COUNTER_PROVIDER = 'RATE_COUNTER_PROVIDER'
  RATE_COUNTER_ORGANIZATION = 'RATE_COUNTER_ORGANIZATION'
  RATE_ACCEPTANCE = 'RATE_ACCEPTANCE'
  RATE_REJECTION = 'RATE_REJECTION'
  RATE_RANGE_UPDATE = 'RATE_RANGE_UPDATE'
}

// Metadata structure for rate negotiation actions
interface RateActionMetadata {
  // Rate details
  proposedRate: number;
  rateUnit: RateUnit;
  message?: string;

  // Negotiation context
  round: number; // Track negotiation round (1, 2, 3, etc.)
  proposerType: 'PROVIDER' | 'ORGANIZATION';

  // Validation results
  withinRange: boolean;
  rangeVariance?: number; // How far outside range (+/- percentage)
  jobMinRate?: number;
  jobMaxRate?: number;

  // Response tracking (for counter-offers)
  respondsToActionId?: string; // References previous action
  responseType?: 'ACCEPT' | 'DECLINE' | 'COUNTER';
  responseMessage?: string;

  // Status tracking
  status: 'PENDING' | 'ACTIVE' | 'ACCEPTED' | 'DECLINED' | 'SUPERSEDED';
  isCurrentProposal: boolean; // True for the latest active proposal
}

## Phase 2: Extended Data Models

### 2.1 Rate Negotiation Summary

Aggregate model to track overall negotiation state:

```typescript
interface RateNegotiationSummary {
  id: string;
  createdAt: Date;
  updatedAt: Date;

  // Identifiers
  jobId: string;
  applicationId?: string;
  offerId?: string;
  providerId: string;
  organizationId: string;

  // Current state
  status: NegotiationSummaryStatus;
  currentRound: number;
  totalRounds: number;

  // Rate progression
  initialJobRate?: number;
  initialProposedRate?: number;
  currentProposedRate?: number;
  finalAgreedRate?: number;
  rateUnit: RateUnit;

  // Timeline
  startedAt: Date;
  lastActivityAt: Date;
  agreedAt?: Date;
  expiredAt?: Date;

  // Job constraints
  jobMinRate?: number;
  jobMaxRate?: number;
  strictRateRange: boolean;

  // Metrics
  totalMessages: number;
  averageResponseTime?: number; // in hours
  finalVarianceFromInitial?: number; // percentage change
}

enum NegotiationSummaryStatus {
  DRAFT = 'DRAFT'
  ACTIVE = 'ACTIVE'
  AGREED = 'AGREED'
  DECLINED = 'DECLINED'
  EXPIRED = 'EXPIRED'
  CANCELLED = 'CANCELLED'
}
````

### 2.2 Rate Validation Rules

Model to store organization-specific rate policies:

```typescript
interface RatePolicy {
  id: string;
  createdAt: Date;
  updatedAt: Date;

  organizationId: string;

  // Default policies
  defaultAllowNegotiation: boolean;
  defaultStrictRange: boolean;
  defaultRateUnit: RateUnit;

  // Validation rules
  maxVarianceAboveRange: number; // percentage
  maxVarianceBelowRange: number; // percentage
  maxNegotiationRounds: number;
  negotiationTimeoutDays: number;

  // Rate boundaries
  organizationMinRate?: number;
  organizationMaxRate?: number;

  // Auto-approval thresholds
  autoApproveWithinRange: boolean;
  autoApproveThreshold?: number; // rate amount
  requiresApprovalAbove?: number; // rate amount
}
```

## Phase 3: Integration Models

### 3.1 Rate Notification Preferences

Track notification preferences for rate negotiations:

```typescript
interface RateNotificationSetting {
  id: string;
  personId: string;

  // Notification channels
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;

  // Event triggers
  newProposal: boolean;
  counterOffer: boolean;
  accepted: boolean;
  declined: boolean;
  expiring: boolean;

  // Timing
  instantNotification: boolean;
  digestFrequency?: "DAILY" | "WEEKLY";
}
```

### 3.2 Rate Analytics Tracking

Model for tracking negotiation patterns and analytics:

```typescript
interface RateNegotiationAnalytics {
  id: string;
  createdAt: Date;

  // Scope
  organizationId?: string;
  providerId?: string;
  jobType?: string;
  specialty?: string;

  // Time period
  periodStart: Date;
  periodEnd: Date;

  // Metrics
  totalNegotiations: number;
  successfulNegotiations: number;
  averageRounds: number;
  averageTimeToAgreement: number; // hours

  // Rate metrics
  averageInitialRate: number;
  averageFinalRate: number;
  averageRateIncrease: number; // percentage

  // Range analysis
  negotiationsWithinRange: number;
  negotiationsAboveRange: number;
  negotiationsBelowRange: number;

  rateUnit: RateUnit;
}
```

## Data Relationships

### Core Entity Relationships (Using Action Model)

```mermaid
---
title: Rate Negotiation via Action Model Relationships
---
erDiagram
    JobPost ||--o{ Action : "has rate actions"
    JobPost ||--|| RatePolicy : "follows policy"

    Application ||--o{ Action : "tracked by actions"
    Offer ||--o{ Action : "tracked by actions"

    Action }o--|| Person : "performed by actor"
    Action }o--|| RateNegotiationSummary : "summarized in"

    Organization ||--|| RatePolicy : "defines"
    Organization ||--o{ RateNegotiationAnalytics : "tracked for"

    Person ||--|| RateNotificationSetting : "configured"
    Provider ||--o{ RateNegotiationAnalytics : "analyzed"
```

## TypeScript Interfaces for API Layer

### 3.1 Request/Response Types (Action-Based)

```typescript
// Rate proposal requests (creates Action records)
interface CreateRateProposalRequest {
  rate: number;
  rateUnit: RateUnit;
  message?: string;
  jobId: string;
  applicationId?: string;
  offerId?: string;
}

interface RateProposalResponse {
  action: Action; // The created rate action
  summary: RateNegotiationSummary;
  validation: RateValidationResult;
}

// Rate validation
interface RateValidationResult {
  isValid: boolean;
  withinRange: boolean;
  variance?: number;
  messages: string[];
  severity: "INFO" | "WARNING" | "ERROR";
}

// Negotiation timeline (built from Action records)
interface NegotiationTimelineResponse {
  summary: RateNegotiationSummary;
  actions: Action[]; // All rate-related actions for this job/application/offer
  jobPolicy: RatePolicy;
  canNegotiate: boolean;
  suggestedActions: string[];
}

// Helper for extracting rate data from action metadata
interface RateActionView {
  action: Action;
  rateData: RateActionMetadata;
  actor: Person;
  isCurrentProposal: boolean;
  respondedToBy?: RateActionView; // Link to response action
}
```

### 3.2 Business Logic Types

```typescript
// Rate calculation utilities
interface RateConversion {
  rate: number;
  fromUnit: RateUnit;
  toUnit: RateUnit;
  convertedRate: number;
  annualEquivalent: number;
}

// Negotiation actions
interface NegotiationAction {
  type: "PROPOSE" | "ACCEPT" | "DECLINE" | "COUNTER";
  rate?: number;
  rateUnit?: RateUnit;
  message?: string;
  automaticResponse?: boolean;
}

// Market data
interface MarketRateData {
  specialty: string;
  location: string;
  rateUnit: RateUnit;
  percentile25: number;
  percentile50: number;
  percentile75: number;
  averageRate: number;
  sampleSize: number;
  lastUpdated: Date;
}
```

## Implementation Phases

### Phase 1: Foundation (Week 1)

- [ ] Extend JobPost model with rate range fields
- [ ] Extend Application/Offer models with rate proposals
- [ ] Create RateNegotiation model
- [ ] Create basic enums and validation types

### Phase 2: Business Logic (Week 2)

- [ ] Create RateNegotiationSummary model
- [ ] Implement RatePolicy model
- [ ] Add rate validation logic
- [ ] Create TypeScript interfaces for API

### Phase 3: Analytics & Notifications (Week 3)

- [ ] Add RateNotificationSetting model
- [ ] Create RateNegotiationAnalytics model
- [ ] Implement market data structures
- [ ] Add audit trail capabilities

### Phase 4: Integration (Week 4)

- [ ] Connect to existing Contract generation
- [ ] Integrate with notification system
- [ ] Add proper indexing and queries
- [ ] Performance optimization

## Database Migration Strategy

### Migration 1: Core Extensions

```sql
-- Add rate negotiation fields to existing tables
ALTER TABLE "JobPost" ADD COLUMN "minRate" DOUBLE PRECISION;
ALTER TABLE "JobPost" ADD COLUMN "maxRate" DOUBLE PRECISION;
ALTER TABLE "JobPost" ADD COLUMN "rateUnit" "RateUnit" DEFAULT 'HOUR';
ALTER TABLE "JobPost" ADD COLUMN "allowNegotiation" BOOLEAN DEFAULT true;
ALTER TABLE "JobPost" ADD COLUMN "strictRateRange" BOOLEAN DEFAULT false;

-- Add rate proposal fields
ALTER TABLE "Application" ADD COLUMN "proposedRate" DOUBLE PRECISION;
ALTER TABLE "Application" ADD COLUMN "proposedRateUnit" "RateUnit";
ALTER TABLE "Application" ADD COLUMN "rateMessage" TEXT;

ALTER TABLE "Offer" ADD COLUMN "proposedRate" DOUBLE PRECISION;
ALTER TABLE "Offer" ADD COLUMN "proposedRateUnit" "RateUnit";
ALTER TABLE "Offer" ADD COLUMN "rateMessage" TEXT;
```

### Migration 2: Action Types & Summary Table

```sql
-- No new core tables needed! The Action model already supports our use case.
-- We just need to add the summary table and ensure action types are available.

CREATE TABLE "RateNegotiationSummary" (
  "id" TEXT PRIMARY KEY,
  "jobId" TEXT REFERENCES "JobPost"("id"),
  "applicationId" TEXT REFERENCES "Application"("id"),
  "offerId" TEXT REFERENCES "Offer"("id"),
  "status" "NegotiationSummaryStatus" DEFAULT 'DRAFT',
  "currentRound" INTEGER DEFAULT 1,
  "finalAgreedRate" DOUBLE PRECISION,
  "rateUnit" "RateUnit" DEFAULT 'HOUR',
  -- ... additional summary fields
);

-- Add index for efficient rate action queries
CREATE INDEX "action_rate_negotiation_idx" ON "Action" ("type", "jobId", "applicationId", "offerId", "createdAt")
WHERE "type" IN (
  'RATE_PROPOSAL_APPLICATION',
  'RATE_PROPOSAL_OFFER',
  'RATE_COUNTER_PROVIDER',
  'RATE_COUNTER_ORGANIZATION',
  'RATE_ACCEPTANCE',
  'RATE_REJECTION'
);
```

## Action-Based Rate Negotiation Queries

### Common Query Patterns

```typescript
// Get all rate actions for a specific application
async function getRateNegotiationHistory(applicationId: string) {
  return await prisma.action.findMany({
    where: {
      applicationId,
      type: {
        in: [
          "RATE_PROPOSAL_APPLICATION",
          "RATE_COUNTER_PROVIDER",
          "RATE_COUNTER_ORGANIZATION",
          "RATE_ACCEPTANCE",
          "RATE_REJECTION",
        ],
      },
    },
    include: {
      actor: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });
}

// Get current active rate proposal
async function getCurrentRateProposal(applicationId: string) {
  return await prisma.action.findFirst({
    where: {
      applicationId,
      type: {
        in: [
          "RATE_PROPOSAL_APPLICATION",
          "RATE_COUNTER_PROVIDER",
          "RATE_COUNTER_ORGANIZATION",
        ],
      },
      metadata: {
        path: ["isCurrentProposal"],
        equals: true,
      },
    },
    include: {
      actor: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
}

// Create rate proposal action
async function createRateProposal(
  data: CreateRateProposalRequest & { actorId: string },
) {
  // First, mark any existing proposals as superseded
  await prisma.action.updateMany({
    where: {
      applicationId: data.applicationId,
      type: {
        in: ["RATE_PROPOSAL_APPLICATION", "RATE_COUNTER_PROVIDER"],
      },
      metadata: {
        path: ["isCurrentProposal"],
        equals: true,
      },
    },
    data: {
      metadata: {
        // Update metadata to mark as superseded
        ...{}, // existing metadata
        status: "SUPERSEDED",
        isCurrentProposal: false,
      },
    },
  });

  // Create new rate proposal action
  return await prisma.action.create({
    data: {
      type: "RATE_PROPOSAL_APPLICATION",
      actorId: data.actorId,
      jobId: data.jobId,
      applicationId: data.applicationId,
      metadata: {
        proposedRate: data.rate,
        rateUnit: data.rateUnit,
        message: data.message,
        round: await getNextNegotiationRound(data.applicationId),
        proposerType: "PROVIDER",
        withinRange: validateRateWithinRange(data.rate, data.jobId),
        status: "PENDING",
        isCurrentProposal: true,
      },
    },
    include: {
      actor: true,
    },
  });
}

// Helper to build negotiation timeline for UI
function buildNegotiationTimeline(
  actions: Action[],
): NegotiationTimelineEntry[] {
  return actions.map((action) => ({
    id: action.id,
    type: mapActionTypeToTimelineType(action.type),
    user: action.actor.name,
    rate: (action.metadata as RateActionMetadata).proposedRate,
    rateUnit: (action.metadata as RateActionMetadata).rateUnit,
    message: (action.metadata as RateActionMetadata).message,
    timestamp: action.createdAt,
    status: (action.metadata as RateActionMetadata).status,
    round: (action.metadata as RateActionMetadata).round,
    withinRange: (action.metadata as RateActionMetadata).withinRange,
  }));
}
```

### Benefits of Action-Based Approach

1. **Consistency**: Uses the existing audit trail pattern
2. **Scalability**: Leverages existing indexes and performance optimizations
3. **Flexibility**: Rich JSON metadata can evolve without schema changes
4. **Integration**: Seamlessly integrates with existing action-based features
5. **Audit Trail**: Complete history is automatically maintained
6. **No Additional Tables**: Minimal schema impact

## Success Metrics

### Data Quality Metrics

- **Validation Accuracy**: 99%+ rate proposals validated correctly
- **Data Consistency**: 100% referential integrity maintained
- **Performance**: <100ms query response time for negotiation timelines
- **Action Integration**: 100% rate negotiations tracked via Action model

### Business Metrics

- **Adoption Rate**: 70%+ of job posts use rate ranges
- **Negotiation Success**: 60%+ negotiations result in agreement
- **Data Completeness**: 95%+ negotiations have complete audit trail
- **Action Efficiency**: <50ms average action creation time

This action-based data architecture provides a solid foundation for implementing the rate negotiation feature while maintaining consistency with existing platform patterns and maximizing code reuse.
