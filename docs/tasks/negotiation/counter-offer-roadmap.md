# Counter Offer - Rate Negotiations UI Roadmap

## Overview

This roadmap outlines the development of UI components for the Counter Offer - Rate Negotiations feature, leveraging the existing Action model for data persistence and focusing on user experience insights from our journey mapping. We're taking a bottom-up approach, starting with atomic presentational components and building up to complex compositional interfaces. All components will be purely presentational with ergonomic APIs, designed for reuse across the medical platform.

## Key Architectural Decisions

### Action-Based Data Strategy

- **No new database tables** - leverages existing `Action` model with rich JSON metadata
- **Consistent audit trails** - follows platform patterns for tracking user activity
- **Efficient queries** - uses existing indexes and performance optimizations
- **Rich metadata** - stores negotiation context in flexible JSON structure

### User Experience Priorities

Based on our persona analysis and emotional journey mapping:

- **Provider Focus**: Mobile-first, confidence-building validation, transparency
- **Organization Focus**: Efficient workflows, budget compliance, quick decisions
- **Key Friction Points**: Rate proposal anxiety, unclear next steps, waiting periods
- **Success Factors**: Clear status indicators, real-time validation, progress transparency

## Phase 1: Atomic UI Components

### 1.1 Rate Display Foundation

**Priority: Critical** | **Complexity: Low** | **Timeline: 1-2 days**

#### `RateDisplay` Component

Core component for formatting and displaying monetary rates consistently across the platform.

```typescript
interface RateDisplayProps {
  amount: number;
  period?: 'hour' | 'day' | 'week' | 'month' | 'year';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'compact' | 'bold' | 'muted';
  showCurrency?: boolean;
  precision?: number;
  className?: string;
}

// Usage Examples:
<RateDisplay amount={75} period="hour" size="lg" variant="bold" />
// Renders: "$75/hr"

<RateDisplay amount={2400} period="month" variant="compact" />
// Renders: "$2.4k/mo"

// Action-based data integration:
const rateFromAction = (action.metadata as RateActionMetadata).proposedRate;
<RateDisplay
  amount={rateFromAction}
  period={(action.metadata as RateActionMetadata).rateUnit}
/>
```

**Features:**

- Automatic number formatting with commas
- Intelligent abbreviations for large numbers (k, M)
- Responsive typography scaling
- Consistent period abbreviations
- Multiple display variants

#### `RateRange` Component

Displays minimum and maximum rate ranges with clear visual hierarchy.

```typescript
interface RateRangeProps {
  min: number;
  max: number;
  period?: 'hour' | 'day' | 'week' | 'month' | 'year';
  variant?: 'badge' | 'inline' | 'stacked' | 'compact';
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  separator?: string;
  className?: string;
}

// Usage Examples:
<RateRange min={60} max={90} period="hour" variant="badge" />
// Renders: Badge with "$60-$90/hr"

<RateRange min={60} max={90} variant="stacked" showLabels />
// Renders: Stacked layout with "Min: $60/hr" / "Max: $90/hr"
```

**Features:**

- Multiple layout variants (inline, stacked, badge)
- Optional min/max labels
- Customizable separators
- Consistent with RateDisplay formatting

### 1.2 Status and Validation Indicators

**Priority: Critical** | **Complexity: Low** | **Timeline: 1 day**

#### `RateStatus` Component

Visual status indicators for negotiation states with semantic colors and icons.

```typescript
interface RateStatusProps {
  status: 'pending' | 'accepted' | 'declined' | 'counter' | 'expired';
  variant?: 'badge' | 'dot' | 'icon' | 'pill';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showText?: boolean;
  animated?: boolean;
  className?: string;
}

// Usage Examples:
<RateStatus status="pending" variant="badge" showIcon />
<RateStatus status="accepted" variant="dot" size="sm" />
<RateStatus status="counter" variant="pill" animated />

// Action-based status mapping:
const actionStatus = (action.metadata as RateActionMetadata).status;
<RateStatus status={mapActionStatusToUI(actionStatus)} variant="badge" />
```

**Features:**

- Semantic color coding (green=accepted, red=declined, yellow=pending, blue=counter)
- Icon integration with Lucide icons
- Animated states for dynamic feedback
- Multiple visual variants

#### `RateValidationStatus` Component

Shows validation feedback for rate proposals against acceptable ranges.

```typescript
interface RateValidationStatusProps {
  status: 'within' | 'above' | 'below' | 'warning';
  message?: string;
  variant?: 'inline' | 'tooltip' | 'popover';
  severity?: 'info' | 'warning' | 'error';
  showIcon?: boolean;
  className?: string;
}

// Usage Examples:
<RateValidationStatus
  status="above"
  message="Rate exceeds maximum range by $15/hr"
  variant="inline"
  severity="warning"
/>
```

**Features:**

- Clear validation messaging
- Multiple display variants
- Severity levels with appropriate styling
- Tooltip integration for space-constrained layouts

### 1.3 Rate Input Components

**Priority: Critical** | **Complexity: Medium** | **Timeline: 2-3 days**

#### `RateInput` Component

Enhanced currency input with built-in validation and rate-specific features.

```typescript
interface RateInputProps {
  value?: number;
  onChange: (value: number | undefined) => void;
  period?: 'hour' | 'day' | 'week' | 'month' | 'year';
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  disabled?: boolean;
  showValidation?: boolean;
  validationRange?: { min: number; max: number };
  strictValidation?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// Usage Examples:
<RateInput
  value={75}
  onChange={setValue}
  period="hour"
  validationRange={{ min: 60, max: 90 }}
  showValidation
/>

// UX-focused validation messaging (from journey mapping):
<RateInput
  value={62}
  onChange={setValue}
  period="hour"
  validationRange={{ min: 45, max: 65 }}
  showValidation
  validationMessages={{
    withinRange: "✅ Your rate is within the posted range",
    aboveRange: "⚠️ Above posted range - organization may negotiate",
    belowRange: "💡 Below range - you may be able to ask for more"
  }}
  helpText="Based on your 8 years of experience in family medicine"
/>
```

**Features:**

- Extends existing Currency component
- Real-time validation feedback
- Rate-specific masking and formatting
- Period selection integration
- Validation range highlighting

#### `RateRangeInput` Component

Dual input for setting minimum and maximum rate ranges.

```typescript
interface RateRangeInputProps {
  minValue?: number;
  maxValue?: number;
  onMinChange: (value: number | undefined) => void;
  onMaxChange: (value: number | undefined) => void;
  period?: 'hour' | 'day' | 'week' | 'month' | 'year';
  disabled?: boolean;
  minLimit?: number;
  maxLimit?: number;
  strictMode?: boolean;
  gap?: number; // Minimum gap between min and max
  layout?: 'horizontal' | 'vertical';
  className?: string;
}

// Usage Examples:
<RateRangeInput
  minValue={60}
  maxValue={90}
  onMinChange={setMin}
  onMaxChange={setMax}
  period="hour"
  gap={10}
  strictMode
/>
```

**Features:**

- Linked validation (min cannot exceed max)
- Enforced gap between values
- Strict mode for organizational controls
- Flexible layouts

## Phase 2: Interactive Components

### 2.1 Rate Configuration

**Priority: High** | **Complexity: Medium** | **Timeline: 2-3 days**

#### `RateRangeConfig` Component

Complete rate range configuration with strict mode toggle.

```typescript
interface RateRangeConfigProps {
  minRate?: number;
  maxRate?: number;
  strictMode?: boolean;
  onRangeChange: (min: number | undefined, max: number | undefined) => void;
  onStrictModeChange: (strict: boolean) => void;
  period?: 'hour' | 'day' | 'week' | 'month' | 'year';
  disabled?: boolean;
  showHelp?: boolean;
  marketData?: { min: number; max: number; median: number };
  className?: string;
}

// Usage Examples:
<RateRangeConfig
  minRate={60}
  maxRate={90}
  strictMode={false}
  onRangeChange={handleRangeChange}
  onStrictModeChange={handleStrictMode}
  period="hour"
  showHelp
  marketData={{ min: 55, max: 95, median: 75 }}
/>
```

**Features:**

- Integrated range input with strict mode toggle
- Help text and tooltips
- Market data comparison overlay
- Validation messaging

#### `RatePeriodSelector` Component

Dropdown for selecting rate periods with automatic conversions.

```typescript
interface RatePeriodSelectorProps {
  value: 'hour' | 'day' | 'week' | 'month' | 'year';
  onChange: (period: string) => void;
  rate?: number;
  showConversions?: boolean;
  allowedPeriods?: string[];
  size?: 'sm' | 'md' | 'lg';
  variant?: 'select' | 'tabs' | 'radio';
  className?: string;
}

// Usage Examples:
<RatePeriodSelector
  value="hour"
  onChange={setPeriod}
  rate={75}
  showConversions
  variant="tabs"
/>
```

**Features:**

- Automatic rate conversions display
- Multiple UI variants
- Filtered period options
- Conversion previews

### 2.2 Negotiation Timeline

**Priority: High** | **Complexity: Medium** | **Timeline: 3-4 days**

#### `NegotiationEntry` Component

Single entry in a negotiation timeline built from Action model data.

```typescript
interface NegotiationEntryProps {
  // Built from Action model
  action: Action;
  rateData: RateActionMetadata;

  // UI-specific props
  type: 'initial' | 'proposal' | 'counter' | 'acceptance' | 'decline';
  showActions?: boolean;
  isCurrentUser?: boolean;
  currentUserType?: 'provider' | 'organization';
  onAccept?: (actionId: string) => void;
  onDecline?: (actionId: string) => void;
  onCounter?: (actionId: string) => void;
  className?: string;
}

// Usage Examples:
<NegotiationEntry
  action={rateAction}
  rateData={rateAction.metadata as RateActionMetadata}
  type="proposal"
  showActions
  isCurrentUser={false}
  currentUserType="organization"
  onAccept={(actionId) => handleAcceptRate(actionId)}
  onCounter={(actionId) => handleCounterOffer(actionId)}
/>

// Based on Dr. Sarah Chen persona (provider emotional journey):
// Shows: "$62/hr - Based on my 8 years in family medicine"
// Status: "⚠️ Above range - pending organization response"
// Actions: [Accept] [Counter] for organization user
```

**Features:**

- Rich timeline entry with metadata
- Author attribution with avatars
- Inline actions (accept/decline/counter)
- Message support
- Visual status indicators

#### `NegotiationTimeline` Component

Complete timeline built from Action model queries.

```typescript
interface NegotiationTimelineProps {
  // Action-based data
  actions: Action[]; // Rate-related actions from API
  currentUserId: string;
  currentUserType: 'provider' | 'organization';

  // UI configuration
  showActions?: boolean;
  maxHeight?: string;
  onActionTaken?: (actionId: string, actionType: string, data?: any) => void;
  loading?: boolean;
  emptyMessage?: string;
  optimisticActions?: Action[]; // For immediate UI updates
  className?: string;
}

// Usage Examples:
<NegotiationTimeline
  actions={rateActions}
  currentUserId="user-123"
  currentUserType="provider"
  showActions
  maxHeight="400px"
  onActionTaken={handleNegotiationAction}
/>

// Based on user journey emotional mapping:
// Provider sees: Timeline with confidence-building validation
// Organization sees: Efficient decision-making interface
// Both see: Clear "who needs to act next" indicators
```

**Features:**

- Scrollable timeline with proper spacing
- User-specific action visibility
- Loading states
- Empty state handling
- Auto-scroll to latest entry

## Phase 3: Compositional Components

### 3.1 Rate Proposal Interface

**Priority: High** | **Complexity: High** | **Timeline: 4-5 days**

#### `RateProposalCard` Component

Complete interface for making initial rate proposals.

```typescript
interface RateProposalCardProps {
  suggestedRange?: { min: number; max: number };
  strictMode?: boolean;
  currentRate?: number;
  period?: string;
  onPropose: (rate: number, message?: string) => void;
  onCancel?: () => void;
  loading?: boolean;
  disabled?: boolean;
  showMarketData?: boolean;
  marketData?: { min: number; max: number; median: number };
  maxMessageLength?: number;
  className?: string;
}

// Usage Examples:
<RateProposalCard
  suggestedRange={{ min: 60, max: 90 }}
  strictMode={false}
  period="hour"
  onPropose={handlePropose}
  onCancel={handleCancel}
  showMarketData
  marketData={{ min: 55, max: 95, median: 75 }}
/>
```

**Features:**

- Integrated rate input with validation
- Optional message field
- Market data comparison
- Range validation with visual feedback
- Loading and disabled states

#### `RateCounterCard` Component

Interface for responding to rate proposals with counter offers.

```typescript
interface RateCounterCardProps {
  currentRate: number;
  period: string;
  suggestedRange?: { min: number; max: number };
  strictMode?: boolean;
  onCounter: (rate: number, message?: string) => void;
  onAccept: () => void;
  onDecline: (reason?: string) => void;
  loading?: boolean;
  disabled?: boolean;
  showComparison?: boolean;
  maxMessageLength?: number;
  className?: string;
}

// Usage Examples:
<RateCounterCard
  currentRate={75}
  period="hour"
  suggestedRange={{ min: 60, max: 90 }}
  onCounter={handleCounter}
  onAccept={handleAccept}
  onDecline={handleDecline}
  showComparison
/>
```

**Features:**

- Three-action interface (accept/decline/counter)
- Rate comparison display
- Optional decline reasoning
- Counter proposal form
- Visual rate differences

### 3.2 Summary and Overview Components

**Priority: Medium** | **Complexity: Medium** | **Timeline: 2-3 days**

#### `RateNegotiationSummary` Component

High-level overview of negotiation status and progress.

```typescript
interface RateNegotiationSummaryProps {
  initialRange?: { min: number; max: number };
  currentProposal?: number;
  finalRate?: number;
  period: string;
  status: 'draft' | 'negotiating' | 'agreed' | 'declined' | 'expired';
  rounds: number;
  startDate: Date;
  lastActivity?: Date;
  participants: Array<{
    id: string;
    name: string;
    type: 'provider' | 'organization';
    avatar?: string;
  }>;
  onViewDetails?: () => void;
  className?: string;
}

// Usage Examples:
<RateNegotiationSummary
  initialRange={{ min: 60, max: 90 }}
  currentProposal={75}
  finalRate={80}
  period="hour"
  status="agreed"
  rounds={3}
  startDate={new Date('2024-01-15')}
  participants={[...]}
  onViewDetails={handleViewDetails}
/>
```

**Features:**

- Status overview with visual indicators
- Participant avatars and roles
- Timeline information
- Progress tracking
- Quick actions

#### `RateComparisonCard` Component

Side-by-side comparison of different rate proposals.

```typescript
interface RateComparisonCardProps {
  originalRate?: number;
  proposedRate: number;
  marketRange?: { min: number; max: number; median: number };
  period: string;
  showRecommendation?: boolean;
  showPercentageDiff?: boolean;
  variant?: 'side-by-side' | 'stacked' | 'minimal';
  className?: string;
}

// Usage Examples:
<RateComparisonCard
  originalRate={70}
  proposedRate={80}
  marketRange={{ min: 65, max: 95, median: 75 }}
  period="hour"
  showRecommendation
  showPercentageDiff
  variant="side-by-side"
/>
```

**Features:**

- Visual rate comparison
- Market positioning indicators
- Percentage difference calculations
- Recommendation system
- Multiple layout variants

## Phase 4: Advanced Compositional Components

### 4.1 Complete Negotiation Interfaces

**Priority: Medium** | **Complexity: High** | **Timeline: 5-6 days**

#### `RateNegotiationPanel` Component

Complete negotiation interface leveraging Action model and user journey insights.

```typescript
interface RateNegotiationPanelProps {
  // Data context
  jobId: string;
  applicationId?: string;
  offerId?: string;

  // Job/offer configuration
  jobRange?: { min: number; max: number };
  strictMode?: boolean;
  period: string;

  // Action-based negotiation data
  actions: Action[]; // All rate-related actions
  summary?: RateNegotiationSummary;

  // User context
  currentUser: {
    id: string;
    type: 'provider' | 'organization';
    name: string;
  };

  // Callbacks aligned with Action creation
  onCreateRateAction: (actionType: RateActionType, metadata: RateActionMetadata) => Promise<void>;

  // UI state
  loading?: boolean;
  disabled?: boolean;
  mobileOptimized?: boolean; // Based on provider persona device usage
  className?: string;
}

// Usage Examples:
<RateNegotiationPanel
  jobId="job-123"
  applicationId="app-456"
  jobRange={{ min: 45, max: 65 }}
  strictMode={false}
  period="hour"
  actions={rateActions}
  summary={negotiationSummary}
  currentUser={{ id: 'user-123', type: 'provider', name: 'Dr. Sarah Chen' }}
  onCreateRateAction={async (type, metadata) => {
    await createAction({ type, metadata, actorId: 'user-123', applicationId: 'app-456' });
  }}
  mobileOptimized={true} // Based on provider persona (70% mobile usage)
/>

// Emotional journey integration:
// - Shows confidence-building validation for providers
// - Displays efficient workflow for organizations
// - Clear "next action" indicators reduce anxiety
// - Real-time status updates prevent confusion
```

**Features:**

- Complete negotiation interface
- Context-aware action buttons
- Integrated timeline and summary
- Real-time status updates
- Permission-based UI

#### `RateFinalizationCard` Component

Final confirmation interface for agreed rates.

```typescript
interface RateFinalizationCardProps {
  agreedRate: number;
  period: string;
  negotiationRounds: number;
  finalizedDate: Date;
  participants: Array<{
    id: string;
    name: string;
    type: 'provider' | 'organization';
    confirmed?: boolean;
  }>;
  terms?: string;
  onConfirm: () => void;
  onRenegotiate?: () => void;
  showContractPreview?: boolean;
  loading?: boolean;
  className?: string;
}

// Usage Examples:
<RateFinalizationCard
  agreedRate={80}
  period="hour"
  negotiationRounds={3}
  finalizedDate={new Date()}
  participants={[...]}
  terms="Standard healthcare rate agreement with 30-day terms"
  onConfirm={handleConfirm}
  onRenegotiate={handleRenegotiate}
  showContractPreview
/>
```

**Features:**

- Final rate confirmation
- Participant confirmation status
- Contract preview integration
- Renegotiation option
- Terms display

### 4.2 Administrative and Analytics Components

**Priority: Low** | **Complexity: Medium** | **Timeline: 3-4 days**

#### `RateAnalyticsCard` Component

Analytics and insights for rate negotiation patterns.

```typescript
interface RateAnalyticsCardProps {
  averageRounds: number;
  successRate: number;
  averageTimeToAgreement: number; // in hours
  rateDistribution: Array<{ range: string; count: number }>;
  trendData: Array<{ period: string; averageRate: number }>;
  period: string;
  variant?: 'summary' | 'detailed' | 'chart';
  className?: string;
}

// Usage Examples:
<RateAnalyticsCard
  averageRounds={2.3}
  successRate={85}
  averageTimeToAgreement={24}
  rateDistribution={[...]}
  trendData={[...]}
  period="hour"
  variant="detailed"
/>
```

**Features:**

- Key metrics display
- Rate distribution charts
- Trend analysis
- Success rate indicators
- Time-to-agreement metrics

## Implementation Strategy

### Revised Development Phases (Action-Based)

#### **Week 1: Foundation & Core Actions**

- Action types and metadata interfaces
- `RateDisplay`, `RateRange`, `RateStatus` components
- `RateInput` with validation messaging from UX research
- API integration for creating/querying rate actions

#### **Week 2: Interactive Components & Timeline**

- `NegotiationEntry` component consuming Action data
- `NegotiationTimeline` with action-based queries
- `RateProposalCard` creating rate actions
- Mobile-first responsive design (provider persona priority)

#### **Week 3: Compositional Interfaces**

- `RateNegotiationPanel` complete interface
- `RateCounterCard` for bidirectional negotiation
- Real-time action updates and optimistic UI
- Organization-focused efficiency workflows

#### **Week 4: Advanced Features & Integration**

- `RateFinalizationCard` for completed negotiations
- Contract integration with agreed rates
- Analytics and reporting components
- Performance optimization and caching

#### **Week 5: Polish & Launch**

- Comprehensive testing across user personas
- Accessibility compliance (WCAG 2.1 AA)
- Storybook documentation
- Production deployment and monitoring

### Component Package Structure

```
packages/ui/src/
├── rates/
│   ├── display/
│   │   ├── RateDisplay.tsx
│   │   ├── RateRange.tsx
│   │   └── RateComparison.tsx
│   ├── inputs/
│   │   ├── RateInput.tsx
│   │   ├── RateRangeInput.tsx
│   │   └── RatePeriodSelector.tsx
│   ├── status/
│   │   ├── RateStatus.tsx
│   │   └── RateValidationStatus.tsx
│   ├── negotiation/
│   │   ├── NegotiationEntry.tsx
│   │   ├── NegotiationTimeline.tsx
│   │   ├── RateProposalCard.tsx
│   │   └── RateCounterCard.tsx
│   ├── config/
│   │   ├── RateRangeConfig.tsx
│   │   └── RateFinalizationCard.tsx
│   ├── panels/
│   │   ├── RateNegotiationPanel.tsx
│   │   └── RateNegotiationSummary.tsx
│   ├── analytics/
│   │   └── RateAnalyticsCard.tsx
│   └── index.ts
```

### Export Strategy

```typescript
// packages/ui/src/rates/index.ts
export * from "./display";
export * from "./inputs";
export * from "./status";
export * from "./negotiation";
export * from "./config";
export * from "./panels";
export * from "./analytics";

// packages/ui/src/index.ts
export * from "./rates";
```

### Testing Strategy

- **Unit Tests**: Each atomic component with comprehensive prop testing
- **Integration Tests**: Compositional components with user interaction flows
- **Visual Tests**: Storybook stories for all components with variants
- **Accessibility Tests**: ARIA compliance and keyboard navigation

### Documentation Strategy

- **Storybook Stories**: Interactive examples for all components
- **API Documentation**: TypeScript interfaces with JSDoc comments
- **Usage Guides**: Best practices and common patterns
- **Design System**: Integration with existing design tokens

## Success Metrics

### Technical Metrics

- **Action Integration**: 100% rate negotiations tracked via Action model
- **Performance**: <100ms action creation, <50ms timeline queries
- **Reusability**: 80%+ of components used in multiple contexts
- **API Ergonomics**: Developer satisfaction score 4.5+/5
- **Test Coverage**: >90% unit test coverage
- **Accessibility**: 100% WCAG 2.1 AA compliance

### User Experience Metrics (From Journey Mapping)

- **Provider Confidence**: >4.0/5.0 satisfaction with rate proposal experience
- **Organization Efficiency**: <2.5 average negotiation rounds
- **Mobile Experience**: >4.0/5.0 satisfaction on mobile devices
- **Time to Resolution**: <48 hours from application to rate agreement
- **Negotiation Success Rate**: >75% completion rate

### Business Impact Metrics

- **Adoption Rate**: >60% of applications include rate proposals
- **Platform Engagement**: 15% increase in successful job placements
- **Time-to-Hire**: 20% reduction for positions with active negotiations
- **User Retention**: Improved satisfaction scores for both user types

This revised roadmap leverages the existing Action model architecture while prioritizing user experience insights from our comprehensive persona and journey analysis. The 5-week timeline is more realistic and focused on delivering maximum value with minimal architectural complexity.
