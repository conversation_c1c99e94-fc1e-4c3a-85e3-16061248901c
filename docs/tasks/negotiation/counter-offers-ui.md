# Counter Offer Rate Negotiations - UI Architecture

## Overview

This document outlines the UI architecture for implementing the counter offer rate negotiations feature using our existing component ecosystem. We'll leverage our established primitives, shared utilities, and patterns while defining clear boundaries for new components.

## Existing Component Analysis

### 🎯 Direct Reuse Opportunities

From our existing catalog, these components can be used directly without modification:

#### **Primitives (Perfect Fit)**

```tsx
// Form & Input Components
// Layout & Structure
import {
  Accordion,
  Alert,
  Badge,
  Button,
  Card,
  Dialog,
  Input,
  Popover,
  Progress,
  ScrollArea,
  Select,
  Separator,
  Skeleton,
  Switch,
  Table,
  Tabs,
  Toast,
} from "@/ui/primitives";
```

#### **Fields (Rate-Specific)**

```tsx
// Perfect for rate negotiations
import { Select as FieldSelect } from "@/ui/fields";
import { Currency, Number } from "@/ui/fields/numeric";
```

#### **Shared Utilities (Business Logic)**

```tsx
// Display & Formatting
// Interactive Components
import {
  Currency as CurrencyDisplay,
  DateTime,
  DialogConfirmation,
  Empty<PERSON>ist,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ated<PERSON>ard,
  TimeAgo,
} from "@/ui/shared";
```

### 🔧 Composition Opportunities

These existing components need minor configuration for our use case:

#### **Table Components**

```tsx
// Negotiation timeline display
import { DataTable } from "@/ui/tables";

// Configure with custom columns for negotiation history
```

#### **Search Components**

```tsx
// For filtering negotiations
import { SearchFilter, SearchParams } from "@/ui/search";

// Configure for negotiation status, date ranges, rate ranges
```

## UI Architecture Boundaries

### Layer 1: Enhanced Primitives

**Purpose**: Extend existing primitives with rate-specific behavior
**Dependencies**: Only existing primitives
**Examples**: Rate-aware input validation, status-specific badges

```tsx
// Building on existing Currency field
interface RateInputProps extends CurrencyFieldProps {
  validationRange?: { min: number; max: number };
  period?: "hour" | "day" | "week" | "month" | "year";
  showValidation?: boolean;
}

// Enhanced Badge for negotiation status
interface NegotiationStatusBadgeProps extends BadgeProps {
  status: "pending" | "accepted" | "declined" | "counter";
  animated?: boolean;
}
```

### Layer 2: Business Components

**Purpose**: Combine primitives into rate negotiation-specific components
**Dependencies**: Layer 1 + existing shared utilities
**Examples**: Rate comparison cards, negotiation entries

```tsx
// Compose existing Card + Currency + Badge + DateTime
function NegotiationEntry({
  amount,
  period,
  author,
  timestamp,
  status,
  message,
}: NegotiationEntryProps) {
  return (
    <Card>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CurrencyDisplay amount={amount} period={period} />
          <NegotiationStatusBadge status={status} />
        </div>
        <TimeAgo timestamp={timestamp} />
      </div>
      {message && <p className="text-sm text-muted-foreground">{message}</p>}
    </Card>
  );
}
```

### Layer 3: Feature Workflows

**Purpose**: Complete user workflows using business components
**Dependencies**: Layers 1-2 + form handling + API integration
**Examples**: Complete negotiation panel, rate proposal wizard

```tsx
// Compose multiple business components into complete workflow
function RateNegotiationWorkflow({
  jobId,
  applicationId,
  currentUser,
  onAction,
}: RateNegotiationWorkflowProps) {
  return (
    <Tabs defaultValue="timeline">
      <TabsList>
        <TabsTrigger value="timeline">Timeline</TabsTrigger>
        <TabsTrigger value="propose">Make Offer</TabsTrigger>
        <TabsTrigger value="summary">Summary</TabsTrigger>
      </TabsList>

      <TabsContent value="timeline">
        <NegotiationTimeline negotiations={negotiations} />
      </TabsContent>

      <TabsContent value="propose">
        <RateProposalForm onSubmit={onAction} />
      </TabsContent>

      <TabsContent value="summary">
        <NegotiationSummary summary={summary} />
      </TabsContent>
    </Tabs>
  );
}
```

### Layer 4: Integration Components

**Purpose**: Connect feature workflows to data and state management
**Dependencies**: All layers + API hooks + routing
**Examples**: Connected negotiation panels with real-time updates

## Component Implementation Strategy

### Phase 1: Enhanced Primitives (1-2 days)

#### 1.1 Rate Display Components

```tsx
// Extend existing Currency display with period support
interface RateDisplayProps {
  amount: number;
  period: "hour" | "day" | "week" | "month" | "year";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  variant?: "default" | "compact" | "bold" | "muted";
}

// Reuse: CurrencyDisplay + custom formatting
// New: Period abbreviation logic
```

#### 1.2 Enhanced Input Components

```tsx
// Extend existing Currency field with validation
interface RateInputProps extends CurrencyFieldProps {
  validationRange?: { min: number; max: number };
  showValidation?: boolean;
  period?: string;
}

// Reuse: Currency field component
// New: Validation overlay using Alert primitive
```

#### 1.3 Status Indicators

```tsx
// Extend existing Badge with semantic colors
interface RateStatusProps extends BadgeProps {
  status: NegotiationStatus;
  animated?: boolean;
}

// Reuse: Badge primitive
// New: Status-to-color mapping, animation classes
```

### Phase 2: Business Components (2-3 days)

#### 2.1 Negotiation Timeline

```tsx
// Compose Table + Cards for timeline display
function NegotiationTimeline({ negotiations }: TimelineProps) {
  // Reuse: DataTable or custom composition
  // Reuse: Card for each entry
  // Reuse: DateTime, TimeAgo, Badge
  // New: Timeline-specific layout and interactions
}
```

#### 2.2 Rate Comparison

```tsx
// Compose multiple RateDisplay components
function RateComparisonCard({
  original,
  proposed,
  marketRange,
}: ComparisonProps) {
  // Reuse: Card, RateDisplay, Badge
  // Reuse: Progress for market positioning
  // New: Comparison layout and difference calculations
}
```

#### 2.3 Rate Proposal Forms

```tsx
// Compose form primitives with validation
function RateProposalForm({ onSubmit }: ProposalFormProps) {
  // Reuse: Form, RateInput, Button, Select
  // Reuse: Textarea for messages
  // New: Form validation logic and submission handling
}
```

### Phase 3: Feature Workflows (3-4 days)

#### 3.1 Complete Negotiation Panel

```tsx
// Orchestrate all business components
function RateNegotiationPanel(props: NegotiationPanelProps) {
  // Reuse: Tabs, Dialog, Card, ScrollArea
  // Reuse: All business components from Phase 2
  // New: State management, user permissions, workflow logic
}
```

#### 3.2 Job Configuration

```tsx
// Extend existing job forms with rate ranges
function JobRateConfiguration({ onUpdate }: RateConfigProps) {
  // Reuse: Form primitives, Switch, RateInput
  // Reuse: Card, Popover for help text
  // New: Rate range validation, policy configuration
}
```

### Phase 4: Integration & Polish (1-2 days)

#### 4.1 Connected Components

```tsx
// Connect to API and state management
function ConnectedNegotiationPanel({ jobId }: ConnectedProps) {
  // Reuse: All previous layers
  // New: API integration, real-time updates, error handling
}
```

## Architectural Patterns

### 🔄 Composition Over Extension

**Pattern**: Build complex components by composing existing primitives rather than extending them.

```tsx
// ✅ GOOD: Compose existing components
function RateRangeInput({ min, max, onChange }: RangeProps) {
  return (
    <div className="flex gap-2">
      <RateInput
        value={min}
        onChange={(value) => onChange(value, max)}
        placeholder="Minimum rate"
      />
      <Separator orientation="vertical" />
      <RateInput
        value={max}
        onChange={(value) => onChange(min, value)}
        placeholder="Maximum rate"
      />
    </div>
  );
}

// ❌ AVOID: Creating entirely new primitive components
```

### 🎯 Single Responsibility Boundaries

**Pattern**: Each component layer has a specific responsibility and dependency scope.

```tsx
// Layer 1: Pure presentation, no business logic
function RateDisplay({ amount, period }: DisplayProps) {
  return (
    <span>
      {formatCurrency(amount)}/{abbreviatePeriod(period)}
    </span>
  );
}

// Layer 2: Business logic, no data fetching
function NegotiationEntry({ negotiation }: EntryProps) {
  return (
    <Card>
      <RateDisplay amount={negotiation.amount} period={negotiation.period} />
      <NegotiationStatusBadge status={negotiation.status} />
    </Card>
  );
}

// Layer 3: Workflow orchestration, no direct API calls
function NegotiationWorkflow({ negotiations, onAction }: WorkflowProps) {
  return (
    <div>
      {negotiations.map((negotiation) => (
        <NegotiationEntry key={negotiation.id} negotiation={negotiation} />
      ))}
    </div>
  );
}

// Layer 4: Data integration and side effects
function ConnectedNegotiation({ jobId }: ConnectedProps) {
  const { data, mutate } = useNegotiations(jobId);
  return <NegotiationWorkflow negotiations={data} onAction={mutate} />;
}
```

### 🔀 Flexible Configuration

**Pattern**: Use configuration props to adapt existing components rather than creating variants.

```tsx
// ✅ GOOD: Configure existing DataTable
function NegotiationHistoryTable({ negotiations }: HistoryProps) {
  const columns = [
    { key: "amount", label: "Rate", render: (row) => <RateDisplay {...row} /> },
    {
      key: "status",
      label: "Status",
      render: (row) => <RateStatusBadge {...row} />,
    },
    { key: "timestamp", label: "Date", render: (row) => <TimeAgo {...row} /> },
  ];

  return (
    <DataTable
      data={negotiations}
      columns={columns}
      emptyState={<EmptyList message="No negotiations yet" />}
    />
  );
}
```

## Integration with Existing Patterns

### 🔍 Search Integration

```tsx
// Leverage existing search components for filtering
function NegotiationSearch() {
  return (
    <div className="flex gap-2">
      <SearchSelect
        param="status"
        options={[
          { value: "pending", label: "Pending" },
          { value: "active", label: "Active" },
          { value: "completed", label: "Completed" },
        ]}
      />
      <SearchDate param="dateRange" />
    </div>
  );
}
```

### 📝 Form Integration

```tsx
// Extend existing form patterns
function RateNegotiationForm() {
  return (
    <Form>
      <FormField
        name="proposedRate"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Proposed Rate</FormLabel>
            <FormControl>
              <RateInput {...field} validationRange={{ min: 60, max: 90 }} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </Form>
  );
}
```

### 🎨 Theme Integration

```tsx
// Use existing design tokens
const negotiationTheme = {
  colors: {
    pending: "hsl(var(--warning))",
    accepted: "hsl(var(--success))",
    declined: "hsl(var(--destructive))",
    counter: "hsl(var(--primary))",
  },
  spacing: {
    negotiationGap: "var(--space-4)",
    timelineIndent: "var(--space-6)",
  },
};
```

## Component Organization

### 📁 Directory Structure

```
packages/ui/src/
├── rates/                          # New domain directory
│   ├── display/                    # Layer 1: Enhanced primitives
│   │   ├── RateDisplay.tsx
│   │   ├── RateRange.tsx
│   │   └── RateStatus.tsx
│   ├── inputs/                     # Layer 1: Enhanced inputs
│   │   ├── RateInput.tsx
│   │   ├── RateRangeInput.tsx
│   │   └── RatePeriodSelector.tsx
│   ├── business/                   # Layer 2: Business components
│   │   ├── NegotiationEntry.tsx
│   │   ├── NegotiationTimeline.tsx
│   │   ├── RateComparison.tsx
│   │   └── RateProposal.tsx
│   ├── workflows/                  # Layer 3: Complete workflows
│   │   ├── NegotiationPanel.tsx
│   │   ├── JobRateConfig.tsx
│   │   └── RateAnalytics.tsx
│   ├── connected/                  # Layer 4: Integration components
│   │   ├── ConnectedNegotiation.tsx
│   │   └── ConnectedRateConfig.tsx
│   └── index.ts                    # Clean exports
```

### 📦 Export Strategy

```tsx
// packages/ui/src/rates/index.ts
// Layer 1: Enhanced primitives
export { RateDisplay, RateRange, RateStatus } from "./display";
export { RateInput, RateRangeInput, RatePeriodSelector } from "./inputs";

// Layer 2: Business components
export {
  NegotiationEntry,
  NegotiationTimeline,
  RateComparison,
  RateProposal,
} from "./business";

// Layer 3: Complete workflows
export { NegotiationPanel, JobRateConfig, RateAnalytics } from "./workflows";

// Layer 4: Connected components (optional - may live in apps)
export { ConnectedNegotiation, ConnectedRateConfig } from "./connected";
```

## Success Metrics

### 🎯 Reuse Metrics

- **80%+ Component Reuse**: Most functionality built by composing existing components
- **Minimal New Primitives**: <5 new base components needed
- **Clean Boundaries**: Each layer depends only on lower layers
- **Fast Development**: Complete feature in <2 weeks using composition

### 🚀 Integration Quality

- **Design Consistency**: All components follow existing design tokens
- **Accessibility**: Inherit accessibility from existing primitives
- **Performance**: No performance regressions from composition
- **API Ergonomics**: Intuitive prop interfaces consistent with existing patterns

This architecture leverages our existing component ecosystem while maintaining clear boundaries and enabling rapid development of the rate negotiation feature through composition rather than recreation.
