---
description: Guidelines for API workflows and component development, relevant for Storybook.
globs: **/__stories__/**
alwaysApply: false
---

TITLE: General API Workflow for Components
DESCRIPTION:
Key steps to follow when building components that interact with the API:
1.  **Understand the Data Model**: Check the database schema (`@axa/db-[domain]`), identify the primary entity, relationships, required fields, defaults, and constraints.
2.  **Explore the API Layer**: Locate the relevant router (`@axa/api-[domain]/src/router/`), review procedures (get, getMany, create, update), and understand input validation schemas. **Verify routes before proceeding.**
3.  **Analyze Selection Patterns**: Check what relations can be included via the `include` parameter, determine necessary includes for your component, and note any nested includes.
4.  **Plan Component Structure**: Decide on data/presentation layer separation, define prop types based on API return types with specific includes, and plan for loading, error, and empty states.
5.  **Implement Data Layer**: Create data fetching logic, set up error handling, and implement caching if needed.
6.  **Implement Presentation Layer**: Build UI components receiving data as props, ensure correct typing based on API selection, and create variations for different data shapes.
7.  **Test Implementation**: Test with different include combinations, verify error handling, and test loading states.
----------------------------------------

TITLE: API Type Import Conventions
DESCRIPTION:
Standard way to import types from the API layer:
- Import types for API responses, requests, and errors: `import type { RouterError, RouterInputs, RouterOutputs } from "@/api";`
- Import enums directly from the API (sourced from Prisma), which can also be used as types: `import { ContractType, PaymentMethod, UserStatus } from "@/api";`
----------------------------------------

TITLE: Using API Types in Components
DESCRIPTION:
Guidelines for applying API-derived types in components. Explicit type definitions are best for presentational components; data-fetching components should prefer type inference from procedure calls.
- **Get Specific API Response Type**: `type User = RouterOutputs["users"]["get"];`
- **Get Specific API Input Type**: `type CreateUserInput = RouterInputs["users"]["create"];`
- **Using Enums**: Define mappings or utility functions outside component functions for clarity (e.g., `statusColorMap`, `getStatusColor` function).
  ```typescript
  // Define color mapping outside the component function
  const statusColorMap = {
    [UserStatus.ACTIVE]: "green",
    [UserStatus.PENDING]: "yellow",
  };

  function getStatusColor(userStatus: UserStatus) {
    return statusColorMap[userStatus] || "gray";
  }
  // Use enums in your component with named function
  function UserStatusBadge({ status }: { status: UserStatus }) {
    return <Badge color={getStatusColor(status)}>{status}</Badge>;
  }
  ```
- **Type Inference in Data Fetching**: Types are often automatically inferred from tRPC procedure calls.
  ```typescript
  function UserProfile({ userId }: { userId: string }) {
    const { data } = api.users.get.useQuery({
      id: userId,
      include: { profile: true },
    });
    // data is automatically typed
    if (!data) return null;
    return <UserCard user={data} />;
  }
  ```
----------------------------------------

TITLE: Data Layer Responsibilities
DESCRIPTION:
The data layer is responsible for:
- **Data Fetching**: Making API calls, handling pagination/filtering, managing loading states.
- **Data Transformation**: Formatting data for presentation, combining data sources, computing derived values.
- **State Management**: Managing cache invalidation, optimistic updates, tracking loading/error states.
- **Error Handling**: Catching API errors, providing meaningful messages, implementing retry logic.
----------------------------------------

TITLE: Presentation Layer Responsibilities
DESCRIPTION:
The presentation layer is responsible for:
- **Rendering UI**: Displaying data, implementing responsive layouts, applying styles/animations.
- **User Interaction**: Handling inputs, triggering callbacks from the data layer, managing local UI state.
- **Accessibility**: Ensuring semantic markup, keyboard navigation, screen reader support.
- **Variations**: Handling different data shapes, conditional rendering, supporting visual states.
----------------------------------------

TITLE: API Selection Workflow for Components
DESCRIPTION:
Steps for determining and using data selections from the API:
1.  **Identify Required Data**: Determine all fields and relationships the component needs.
2.  **Check Available Includes**: Review API documentation/code for available `include` options, noting limitations or performance considerations.
3.  **Define Selection Pattern**: Specify `include` parameters based on requirements, considering nested includes and avoiding over-fetching.
4.  **Type Your Component**: Use TypeScript to derive types from the selection pattern, ensuring type safety and documenting required includes in component props.
5.  **Implement Data Fetching**: Use the defined selection pattern in API calls, handle loading/error states, and consider caching/refetching.
----------------------------------------

TITLE: Client-Side API Consumption: Basic Query with Includes
DESCRIPTION:
Example of a basic tRPC query with an `include` statement:
```typescript
const { data, isLoading } = api.users.get.useQuery({
  id: userId,
  include: { 
    profile: true // Example: include the user's profile
  } 
});
```
----------------------------------------

TITLE: Client-Side API Consumption: Query with Filtering and Pagination
DESCRIPTION:
Example of a tRPC query for a list with filtering and pagination options:
```typescript
const { data, fetchNextPage } = api.posts.getMany.useInfiniteQuery(
  {
    limit: 10,
    filter: { status: "published" }, // Example filter
  },
  {
    getNextPageParam: (lastPage) => lastPage.nextCursor,
  }
);
```
----------------------------------------

TITLE: Client-Side API Consumption: Mutation
DESCRIPTION:
Example of a tRPC mutation for creating data:
```typescript
const createUser = api.users.create.useMutation({
  onSuccess: () => {
    // Invalidate cache or handle success
  },
  onError: (error) => {
    // Handle error
  }
});

createUser.mutate({ name: "New User", email: "<EMAIL>" });
```
----------------------------------------

TITLE: Client-Side API Consumption: Error Handling
DESCRIPTION:
Basic try-catch block for handling errors when making direct API calls (less common with tRPC hooks, which have `onError`):
```typescript
try {
  const data = await api.users.getMany({ // Assuming a direct client call, not a hook
    pageSize: 10,
    include: { profile: true },
  });
  // Process data
} catch (error) {
  // Handle error (e.g., display a message, log it)
}
```
----------------------------------------

TITLE: Type Safety Best Practices with API Data
DESCRIPTION:
Guidelines for ensuring type safety when working with API data:
1.  **Derive Types from API Selection**: Use `RouterOutputs` and `RouterInputs` to get exact types. Let TypeScript infer the shape based on `include` statements.
2.  **Document Required Includes**: Note required `include`s in component documentation (e.g., JSDoc comments) to explain data requirements for props.
3.  **Use Discriminated Unions**: Create union types for components that can handle different data shapes based on API responses. Use type narrowing for conditional rendering.
4.  **Avoid Type Assertions**: Do not use `as` to force types. Rely on the type system and correct `include` patterns to validate data access.
----------------------------------------
