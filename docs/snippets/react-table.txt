TITLE: Defining Table Columns (columns)
DESCRIPTION: The `columns` option is an array of `ColumnDef<TData>` used to define the table's columns. Refer to the Column Def Guide for detailed information on creating column definitions.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/table.md#_snippet_2

LANGUAGE: tsx
CODE:
```
type columns = ColumnDef<TData>[]
```

----------------------------------------

TITLE: Initializing TanStack Table with createAngularTable in Angular
DESCRIPTION: This TypeScript snippet demonstrates how to use createAngularTable to initialize a table instance within an Angular component. It accepts a function that returns table options, including reactive data (using Angular signals), column definitions, and the core row model getter.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/angular/angular-table.md#_snippet_0

LANGUAGE: typescript
CODE:
```
import {createAngularTable} from '@tanstack/angular-table'

export class AppComponent {
  data = signal<Person[]>([])

  table = createAngularTable(() => ({
    data: this.data(),
    columns: defaultColumns,
    getCoreRowModel: getCoreRowModel(),
  }))
}
```

----------------------------------------

TITLE: Configuring Server-Side Pagination with TanStack Table (JSX)
DESCRIPTION: This snippet demonstrates how to configure TanStack Table for server-side pagination. It involves setting `manualPagination` to `true` to disable client-side pagination and providing the total `rowCount` (or `pageCount`) from the backend so the table can calculate the total number of pages.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/pagination.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import { useReactTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table';
//...
const table = useReactTable({
  columns,
  data,
  getCoreRowModel: getCoreRowModel(),
  // getPaginationRowModel: getPaginationRowModel(), //not needed for server-side pagination
  manualPagination: true, //turn off client-side pagination
  rowCount: dataQuery.data?.rowCount, //pass in the total row count so the table knows how many pages there are (pageCount calculated internally if not provided)
  // pageCount: dataQuery.data?.pageCount, //alternatively directly pass in pageCount instead of rowCount
});
```

----------------------------------------

TITLE: Configuring Custom Row IDs in TanStack Table (TypeScript)
DESCRIPTION: This snippet demonstrates how to use the `getRowId` table option to assign a custom, unique identifier to each row, such as a UUID from a database. This ensures that the row selection state is keyed by a stable and meaningful ID rather than just the row index, which is crucial for consistent selection across data changes.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/row-selection.md#_snippet_2

LANGUAGE: TypeScript
CODE:
```
const table = useReactTable({
  //...
  getRowId: row => row.uuid //use the row's uuid from your database as the row id
})
```

----------------------------------------

TITLE: Creating Unstable Columns and Data in React with TanStack Table
DESCRIPTION: This snippet demonstrates a common pitfall where `columns` and `data` arrays are redefined on every render within a React component. This leads to an infinite re-rendering loop because React detects new references for these props on each render, triggering continuous updates in TanStack Table.
SOURCE: https://github.com/tanstack/table/blob/main/docs/faq.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
export default function MyComponent() {
  //😵 BAD: This will cause an infinite loop of re-renders because `columns` is redefined as a new array on every render!
  const columns = [
    // ...
  ];

  //😵 BAD: This will cause an infinite loop of re-renders because `data` is redefined as a new array on every render!
  const data = [
    // ...
  ];

  //❌ Columns and data are defined in the same scope as `useReactTable` without a stable reference, will cause infinite loop!
  const table = useReactTable({
    columns,
    data,
  });

  return <table>...</table>;
}
```

----------------------------------------

TITLE: Setting Default Column Options (defaultColumn)
DESCRIPTION: The `defaultColumn` option allows providing partial `ColumnDef<TData>` options that are merged with all column definitions supplied to the table. This is useful for setting default renderers or options for sorting, filtering, and grouping across all columns.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/table.md#_snippet_3

LANGUAGE: tsx
CODE:
```
defaultColumn?: Partial<ColumnDef<TData>>
```

----------------------------------------

TITLE: Defining Columns with createColumnHelper in TanStack Table (TSX)
DESCRIPTION: This snippet demonstrates how to use `createColumnHelper` to define a comprehensive set of columns for a TanStack Table, including display columns for actions, grouping columns for logical organization, and accessor columns for data extraction. It showcases type-safe column creation for a `Person` data shape, illustrating various header, cell, and footer configurations.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/column-defs.md#_snippet_0

LANGUAGE: tsx
CODE:
```
// Define your row shape
type Person = {
  firstName: string
  lastName: string
  age: number
  visits: number
  status: string
  progress: number
}

const columnHelper = createColumnHelper<Person>()

// Make some columns!
const defaultColumns = [
  // Display Column
  columnHelper.display({
    id: 'actions',
    cell: props => <RowActions row={props.row} />,
  }),
  // Grouping Column
  columnHelper.group({
    header: 'Name',
    footer: props => props.column.id,
    columns: [
      // Accessor Column
      columnHelper.accessor('firstName', {
        cell: info => info.getValue(),
        footer: props => props.column.id,
      }),
      // Accessor Column
      columnHelper.accessor(row => row.lastName, {
        id: 'lastName',
        cell: info => info.getValue(),
        header: () => <span>Last Name</span>,
        footer: props => props.column.id,
      }),
    ],
  }),
  // Grouping Column
  columnHelper.group({
    header: 'Info',
    footer: props => props.column.id,
    columns: [
      // Accessor Column
      columnHelper.accessor('age', {
        header: () => 'Age',
        footer: props => props.column.id,
      }),
      // Grouping Column
      columnHelper.group({
        header: 'More Info',
        columns: [
          // Accessor Column
          columnHelper.accessor('visits', {
            header: () => <span>Visits</span>,
            footer: props => props.column.id,
          }),
          // Accessor Column
          columnHelper.accessor('status', {
            header: 'Status',
            footer: props => props.column.id,
          }),
          // Accessor Column
          columnHelper.accessor('progress', {
            header: 'Profile Progress',
            footer: props => props.column.id,
          }),
        ],
      }),
    ],
  }),
]
```

----------------------------------------

TITLE: Achieving Stable References for TanStack Table Props in React
DESCRIPTION: This solution demonstrates how to provide stable references for `columns` and `data` in React components using `useMemo` or `useState`, or by defining them outside the component. This prevents infinite re-rendering loops by ensuring that TanStack Table receives the same reference for its props across renders, optimizing performance.
SOURCE: https://github.com/tanstack/table/blob/main/docs/faq.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
//✅ OK: Define columns outside of the component
const columns = [
  // ...
];

//✅ OK: Define data outside of the component
const data = [
  // ...
];

// Usually it's more practical to define columns and data inside the component, so use `useMemo` or `useState` to give them stable references
export default function MyComponent() {
  //✅ GOOD: This will not cause an infinite loop of re-renders because `columns` is a stable reference
  const columns = useMemo(() => [
    // ...
  ], []);

  //✅ GOOD: This will not cause an infinite loop of re-renders because `data` is a stable reference
  const [data, setData] = useState(() => [
    // ...
  ]);

  // Columns and data are defined in a stable reference, will not cause infinite loop!
  const table = useReactTable({
    columns,
    data,
  });

  return <table>...</table>;
}
```

----------------------------------------

TITLE: Passing Custom Table Context (meta)
DESCRIPTION: The `meta` option allows passing any arbitrary object to `table.options.meta`, serving as a custom context for the table. This type is globally extensible via declaration merging, useful for passing data like locale objects or update functions.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/table.md#_snippet_6

LANGUAGE: tsx
CODE:
```
meta?: TableMeta // This interface is extensible via declaration merging. See below!
```

----------------------------------------

TITLE: Accessing and Mutating Table State
DESCRIPTION: Demonstrates how to interact with the table's internal state using the `table.getState()` API to read, and feature-specific setters like `table.setRowSelection()` and `table.resetRowSelection()` to modify or reset state. This pattern applies to all features that register state within the table instance.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/tables.md#_snippet_5

LANGUAGE: javascript
CODE:
```
table.getState().rowSelection //read the row selection state
table.setRowSelection((old) => ({...old})) //set the row selection state
table.resetRowSelection() //reset the row selection state
```

----------------------------------------

TITLE: Updating Table State in TanStack Table (TypeScript)
DESCRIPTION: This method updates the table's state. It is recommended to pass an updater function `(prevState) => newState` for state transitions, though a direct object can also be provided. If `options.onStateChange` is configured, it will be triggered with the new state.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/table.md#_snippet_24

LANGUAGE: TypeScript
CODE:
```
setState: (updater: Updater<TableState>) => void
```

----------------------------------------

TITLE: Mutating TanStack Table Data In-Place Leading to Infinite Loops
DESCRIPTION: This example illustrates a pitfall where even with initially stable `columns` and `data` references, mutating the `data` array directly (e.g., using `filter` inline) within the `useReactTable` hook causes an infinite re-render loop. This happens because the inline mutation creates a new array reference on every render, invalidating the stability.
SOURCE: https://github.com/tanstack/table/blob/main/docs/faq.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
export default function MyComponent() {
  //✅ GOOD
  const columns = useMemo(() => [
    // ...
  ], []);

  //✅ GOOD (React Query provides stable references to data automatically)
  const { data, isLoading } = useQuery({
    //...
  });

  const table = useReactTable({
    columns,
    //❌ BAD: This will cause an infinite loop of re-renders because `data` is mutated in place (destroys stable reference)
    data: data?.filter(d => d.isActive) ?? [],
  });

  return <table>...</table>;
}
```

----------------------------------------

TITLE: Creating a Basic Table Instance Across Frameworks
DESCRIPTION: Illustrates the creation of a fundamental TanStack Table instance by providing the required `columns` and `data` options. This snippet demonstrates the framework-specific adapter functions (`createTable`, `useReactTable`, `createSolidTable`, `createSvelteTable`, `useVueTable`) used to instantiate the table.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/tables.md#_snippet_4

LANGUAGE: javascript
CODE:
```
//vanilla js
const table = createTable({ columns, data })

//react
const table = useReactTable({ columns, data })

//solid
const table = createSolidTable({ columns, data })

//svelte
const table = createSvelteTable({ columns, data })

//vue
const table = useVueTable({ columns, data })
```

----------------------------------------

TITLE: Initializing Data Array with TData Type for Stability
DESCRIPTION: Demonstrates various methods to initialize a data array (`User[]`) while ensuring a 'stable' reference, which is critical for preventing infinite re-renders, particularly in React. Examples include direct declaration, React's `useState`, and Vue's `ref` for managing data state.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/tables.md#_snippet_2

LANGUAGE: typescript
CODE:
```
//note: data needs a "stable" reference in order to prevent infinite re-renders
const data: User[] = []
//or
const [data, setData] = React.useState<User[]>([])
//or
const data = ref<User[]>([])
//etc...
```

----------------------------------------

TITLE: Connecting Row Selection APIs to Checkbox Inputs in TanStack Table (TSX)
DESCRIPTION: This TSX snippet illustrates how to integrate TanStack Table's row selection APIs with checkbox inputs within column definitions. It shows how to use `table.getToggleAllRowsSelectedHandler()` for a 'select all' checkbox in the header and `row.getToggleSelectedHandler()` for individual row selection checkboxes in cells. It also uses `table.getIsAllRowsSelected()`, `table.getIsSomeRowsSelected()`, `row.getIsSelected()`, and `row.getCanSelect()` for managing checkbox state.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/row-selection.md#_snippet_8

LANGUAGE: TSX
CODE:
```
const columns = [
  {
    id: 'select-col',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllRowsSelected()}
        indeterminate={table.getIsSomeRowsSelected()}
        onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        onChange={row.getToggleSelectedHandler()}
      />
    ),
  },
  //... more column definitions...
]
```

----------------------------------------

TITLE: Getting Selected Row Model in TanStack Table (TypeScript)
DESCRIPTION: Defines the `getSelectedRowModel` method, which returns a `RowModel<TData>` containing all currently selected rows, regardless of filtering or grouping.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/features/row-selection.md#_snippet_16

LANGUAGE: TypeScript
CODE:
```
getSelectedRowModel: () => RowModel<TData>
```

----------------------------------------

TITLE: Implementing TanStack Table Pagination Controls in JSX
DESCRIPTION: This snippet demonstrates how to implement common pagination controls using TanStack Table's APIs. It includes buttons for navigating to the first, previous, next, and last pages, along with a select input for dynamically changing the page size. The `getCanPreviousPage` and `getCanNextPage` methods are used to disable navigation buttons when appropriate, and `setPageSize` updates the table's page size based on user selection.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/pagination.md#_snippet_5

LANGUAGE: jsx
CODE:
```
<Button
  onClick={() => table.firstPage()}
  disabled={!table.getCanPreviousPage()}
>
  {'<<'}
</Button>
<Button
  onClick={() => table.previousPage()}
  disabled={!table.getCanPreviousPage()}
>
  {'<'}
</Button>
<Button
  onClick={() => table.nextPage()}
  disabled={!table.getCanNextPage()}
>
  {'>'}
</Button>
<Button
  onClick={() => table.lastPage()}
  disabled={!table.getCanNextPage()}
>
  {'>>'}
</Button>
<select
  value={table.getState().pagination.pageSize}
  onChange={e => {
    table.setPageSize(Number(e.target.value))
  }}
>
  {[10, 20, 30, 40, 50].map(pageSize => (
    <option key={pageSize} value={pageSize}>
      {pageSize}
    </option>
  ))}
</select>
```

----------------------------------------

TITLE: Implementing Custom Expanding UI with getRowCanExpand in TanStack Table (React/TypeScript)
DESCRIPTION: This snippet demonstrates how to implement a custom expanding UI by overriding `getRowCanExpand` to control which rows can be expanded, independent of `subRows`. It includes a React JSX example for rendering a detail panel as a separate row that spans the table's columns when a row is expanded, allowing for flexible custom content.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/expanding.md#_snippet_3

LANGUAGE: TypeScript
CODE:
```
//...
const table = useReactTable({
  // other options...
  getRowCanExpand: (row) => true, // Add your logic to determine if a row can be expanded. True means all rows include expanded data
  getCoreRowModel: getCoreRowModel(),
  getExpandedRowModel: getExpandedRowModel(),
})
//...
<tbody>
  {table.getRowModel().rows.map((row) => (
    <React.Fragment key={row.id}>
     {/* Normal row UI */}
      <tr>
        {row.getVisibleCells().map((cell) => (
          <td key={cell.id}>
            <FlexRender
              render={cell.column.columnDef.cell}
              props={cell.getContext()}
            />
          </td>
        ))}
      </tr>
      {/* If the row is expanded, render the expanded UI as a separate row with a single cell that spans the width of the table */}
      {row.getIsExpanded() && (
        <tr>
          <td colSpan={row.getAllCells().length}> // The number of columns you wish to span for the expanded data if it is not a row that shares the same columns as the parent row
            // Your custom UI goes here
          </td>
        </tr>
      )}
    </React.Fragment>
  ))}
</tbody>
//...
```

----------------------------------------

TITLE: Rendering Header Content with flexRender in TanStack Table (TSX)
DESCRIPTION: Demonstrates how to use the `flexRender` utility with the header's context to render the header content defined in `header.column.columnDef.header`. This approach allows for framework-agnostic rendering of header components.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/header.md#_snippet_12

LANGUAGE: tsx
CODE:
```
flexRender(header.column.columnDef.header, header.getContext())
```

----------------------------------------

TITLE: Installing Project Dependencies (Bash)
DESCRIPTION: This command installs all necessary project dependencies listed in the `package.json` file. Users can choose between `npm install`, `pnpm install`, or `yarn install` based on their preferred package manager to set up the project environment.
SOURCE: https://github.com/tanstack/table/blob/main/examples/qwik/basic/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
$ npm install # or pnpm install or yarn install
```

----------------------------------------

TITLE: Initializing TanStack Table with Core Row Model (TypeScript)
DESCRIPTION: This snippet demonstrates the basic initialization of a TanStack Table instance using the `useReactTable` hook. It highlights the essential `getCoreRowModel` function, which is required to process and display the table's data. This function serves as the foundational row model for the table.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/row-models.md#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import { getCoreRowModel, useReactTable } from '@tanstack/react-table'

function Component() {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(), //row model
  })
}
```

----------------------------------------

TITLE: Defining Columns with createColumnHelper - TanStack Table TSX
DESCRIPTION: This snippet demonstrates the recommended way to define columns using `createColumnHelper` for improved TypeScript support in TanStack Table. It shows both string-based (`accessorKey`) and function-based (`accessorFn`) accessors, along with the updated `header` property, replacing the older `accessor` and `Header` properties.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/migrating.md#_snippet_3

LANGUAGE: tsx
CODE:
```
columnHelper.accessor('firstName', { //accessorKey
  header: 'First Name',
}),
columnHelper.accessor(row => row.lastName, { //accessorFn
  header: () => <span>Last Name</span>,
}),
```

----------------------------------------

TITLE: Rendering Table Cells with flexRender in Qwik JSX
DESCRIPTION: This example illustrates the use of `flexRender` to dynamically render cell content within a Qwik table. It iterates through rows and visible cells, applying `flexRender` with the cell definition and context to display the appropriate template, enabling flexible and dynamic table rendering.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/qwik/qwik-table.md#_snippet_1

LANGUAGE: JSX
CODE:
```
import { flexRender } from '@tanstack/qwik-table'
//...
return (
  <tbody>
    {table.getRowModel().rows.map(row => {
      return (
        <tr key={row.id}>
          {row.getVisibleCells().map(cell => (
            <td key={cell.id}>
              {flexRender(cell.column.columnDef.cell, cell.getContext())}
            </td>
          ))}
        </tr>
      )
    })}
  </tbody>
);
```

----------------------------------------

TITLE: Avoiding Unstable Data and Column References in React (Bad Practice)
DESCRIPTION: Illustrates an incorrect approach where `columns` and `data` arrays are defined directly within the component's render scope without stable references. This leads to new array instances on every render, causing infinite re-renders when passed to `useReactTable`.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/data.md#_snippet_9

LANGUAGE: tsx
CODE:
```
export default function MyComponent() {
  //😵 BAD: This will cause an infinite loop of re-renders because `columns` is redefined as a new array on every render!
  const columns = [
    // ...
  ];

  //😵 BAD: This will cause an infinite loop of re-renders because `data` is redefined as a new array on every render!
  const data = [
    // ...
  ];

  //❌ Columns and data are defined in the same scope as `useReactTable` without a stable reference, will cause infinite loop!
  const table = useReactTable({
    columns,
    data ?? [], //❌ Also bad because the fallback array is re-created on every render
  });

  return <table>...</table>;
}
```

----------------------------------------

TITLE: Rendering Cell Content with flexRender (Angular HTML)
DESCRIPTION: This Angular HTML snippet demonstrates how to use `*flexRender` to dynamically render content within a table cell, header, or footer using a `TemplateRef`. It shows how to access the `$implicit` context and provides examples for rendering simple strings or HTML strings using `[innerHTML]`.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/angular/angular-table.md#_snippet_8

LANGUAGE: angular-html
CODE:
```
<ng-container
  *flexRender="
              cell.column.columnDef.cell;
              props: cell.getContext();
              let cell
            "
>
  <!-- if you want to render a simple string -->
  {{ cell }}
  <!-- if you want to render an html string -->
  <div [innerHTML]="cell"></div>
</ng-container>

<ng-template #myCell let-context>
  <!-- render something with context -->
</ng-template>
```

----------------------------------------

TITLE: Installing TanStack Angular Table Adapter (Bash)
DESCRIPTION: Installs the @tanstack/angular-table package using npm. This adapter is designed for Angular 17 and leverages Angular's new Signal implementation for efficient table management.
SOURCE: https://github.com/tanstack/table/blob/main/docs/installation.md#_snippet_5

LANGUAGE: bash
CODE:
```
npm install @tanstack/angular-table
```

----------------------------------------

TITLE: Importing and Applying Multiple TanStack Table Row Models (TypeScript)
DESCRIPTION: This example illustrates how to import and configure various row models in TanStack Table to enable advanced data grid features. It shows the comprehensive list of available row models and how to pass them to the `useReactTable` hook, allowing the table to handle features like expansion, filtering, grouping, pagination, and sorting.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/row-models.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
//only import the row models you need
import {
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getGroupedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
}
//...
const table = useReactTable({
  columns,
  data,
  getCoreRowModel: getCoreRowModel(),
  getExpandedRowModel: getExpandedRowModel(),
  getFacetedMinMaxValues: getFacetedMinMaxValues(),
  getFacetedRowModel: getFacetedRowModel(),
  getFacetedUniqueValues: getFacetedUniqueValues(),
  getFilteredRowModel: getFilteredRowModel(),
  getGroupedRowModel: getGroupedRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  getSortedRowModel: getSortedRowModel(),
})
```

----------------------------------------

TITLE: Customizing Row IDs with getRowId Option in TanStack Table (JavaScript)
DESCRIPTION: This snippet demonstrates how to customize the unique `id` for each row using the `getRowId` option during table initialization. By default, `row.id` is the row index, but this allows using a unique identifier from the original row data, such as a `uuid`.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/rows.md#_snippet_3

LANGUAGE: js
CODE:
```
const table = useReactTable({
  columns,
  data,
  getRowId: originalRow => originalRow.uuid //override the row.id with the uuid from the original row's data
})
```

----------------------------------------

TITLE: Managing Row Selection State with React and TanStack Table (TypeScript)
DESCRIPTION: This example illustrates how to manage the row selection state externally using React's `useState` hook. It shows how to hoist the state out of the table instance via `onRowSelectionChange` and then pass it back in through the `state` option, enabling easier access and manipulation of selected row IDs.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/row-selection.md#_snippet_1

LANGUAGE: TypeScript
CODE:
```
const [rowSelection, setRowSelection] = useState<RowSelectionState>({}) //manage your own row selection state

const table = useReactTable({
  //...
  onRowSelectionChange: setRowSelection, //hoist up the row selection state to your own scope
  state: {
    rowSelection //pass the row selection state back to the table instance
  }
})
```

----------------------------------------

TITLE: Initializing Solid Table with createSolidTable (TSX)
DESCRIPTION: This snippet demonstrates how to initialize a table using the `createSolidTable` function from `@tanstack/solid-table`. It takes an `options` object to configure the table and returns a table instance, which can then be used for rendering.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/solid/solid-table.md#_snippet_0

LANGUAGE: TSX
CODE:
```
import { createSolidTable } from '@tanstack/solid-table'

function App() {
  const table = createSolidTable(options)

  // ...render your table
}
```

----------------------------------------

TITLE: Iterating Rows and Cells in TanStack Table (Angular Template)
DESCRIPTION: This snippet shows how to iterate through the table's rows and their visible cells using Angular's `@for` loop. This is fundamental for rendering the table body and displaying cell content.
SOURCE: https://github.com/tanstack/table/blob/main/examples/angular/row-selection-signal/src/app/app.component.html#_snippet_1

LANGUAGE: Angular Template
CODE:
```
@for (row of table.getRowModel().rows; track row.id) { @for (cell of row.getVisibleCells(); track cell.id) { } }
```

----------------------------------------

TITLE: Handling Updater Functions in TanStack Table Callbacks (JSX)
DESCRIPTION: Shows how `on[State]Change` callbacks can receive either a direct value or an updater function, similar to React's `setState`. It provides examples of how to correctly process the `updater` argument, allowing for custom logic before updating the state, by checking if `updater` is a function.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/react/guide/table-state.md#_snippet_5

LANGUAGE: jsx
CODE:
```
const [sorting, setSorting] = React.useState([])
const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 })

const table = useReactTable({
  columns,
  data,
  //...
  state: {
    pagination,
    sorting,
  }
  //syntax 1
  onPaginationChange: (updater) => {
    setPagination(old => {
      const newPaginationValue = updater instanceof Function ? updater(old) : updater
      //do something with the new pagination value
      //...
      return newPaginationValue
    })
  },
  //syntax 2
  onSortingChange: (updater) => {
    const newSortingValue = updater instanceof Function ? updater(sorting) : updater
    //do something with the new sorting value
    //...
    setSorting(updater) //normal state update
  }
})
```

----------------------------------------

TITLE: Rendering Custom Cell Content with flexRender in TanStack Table (React)
DESCRIPTION: This snippet demonstrates using `flexRender` from `@tanstack/react-table` to render custom cell content, especially when `cell: () => JSX` is used in column definitions. It ensures correct rendering of JSX and calls the callback function with appropriate parameters, allowing for rich UI elements within table cells.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/cells.md#_snippet_3

LANGUAGE: JSX
CODE:
```
import { flexRender } from '@tanstack/react-table'

const columns = [
  {
    accessorKey: 'fullName',
    cell: ({ cell, row }) => {
      return <div><strong>{row.original.firstName}</strong> {row.original.lastName}</div>
    }
    //...
  }
]
//...
<tr>
  {row.getVisibleCells().map(cell => {
    return <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
  })}
</tr>
```

----------------------------------------

TITLE: Rendering Table Cells with FlexRenderDirective in Angular HTML
DESCRIPTION: This Angular HTML snippet illustrates the use of the *flexRender structural directive to dynamically render table cell content. It binds cell.column.columnDef.cell as the template and cell.getContext() as props, demonstrating how to display simple string values or render HTML strings using [innerHTML].
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/angular/angular-table.md#_snippet_2

LANGUAGE: angular-html
CODE:
```
<tbody>
@for (row of table.getRowModel().rows; track row.id) {
  <tr>
    @for (cell of row.getVisibleCells(); track cell.id) {
      <td>
        <ng-container
          *flexRender=
              "cell.column.columnDef.cell;
              props: cell.getContext();
              let cell
            "
        >
          <!-- if you want to render a simple string -->
          {{ cell }}
          <!-- if you want to render an html string -->
          <div [innerHTML]="cell"></div>
        </ng-container>
      </td>
    }
  </tr>
}
</tbody>
```

----------------------------------------

TITLE: Controlling Individual State (Sorting) with onSortingChange in TypeScript
DESCRIPTION: This example illustrates how to control a specific part of the table state, such as `sorting`, using an `on[State]Change` callback (`onSortingChange`). It highlights the crucial requirement that the corresponding state value (`sorting`) must be explicitly provided in the `state` option when using an `on[State]Change` callback, otherwise, the state will be "frozen". The `setSorting` function handles updates, similar to React's `setState`.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/svelte/guide/table-state.md#_snippet_4

LANGUAGE: ts
CODE:
```
let sorting = []
const setSorting = updater => {
  if (updater instanceof Function) {
    sorting = updater(sorting)
  } else {
    sorting = updater
  }
  options.update(old => ({
    ...old,
    state: {
      ...old.state,
      sorting,
    },
  }))
}
//...
const options = writable({
  columns,
  data,
  //...
  state: {
    sorting, //required because we are using `onSortingChange`
  },
  onSortingChange: setSorting, //makes the `state.sorting` controlled
})
const table = createSvelteTable(options)
```

----------------------------------------

TITLE: Installing Project Dependencies (npm/yarn)
DESCRIPTION: This command installs all necessary project dependencies listed in the `package.json` file. Users can choose between npm or yarn for package management.
SOURCE: https://github.com/tanstack/table/blob/main/examples/react/virtualized-infinite-scrolling/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install
```

LANGUAGE: bash
CODE:
```
yarn
```

----------------------------------------

TITLE: Installing Dependencies (npm/yarn) - Bash
DESCRIPTION: This snippet shows how to install project dependencies using either npm or yarn. It's a prerequisite for running the application.
SOURCE: https://github.com/tanstack/table/blob/main/examples/svelte/column-groups/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install
```

LANGUAGE: bash
CODE:
```
yarn
```

----------------------------------------

TITLE: Installing Project Dependencies (npm/yarn) - Bash
DESCRIPTION: This snippet shows how to install the necessary project dependencies using either npm or yarn. Running one of these commands will download and set up all required packages defined in the project's package.json file.
SOURCE: https://github.com/tanstack/table/blob/main/examples/react/fully-controlled/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install
```

LANGUAGE: bash
CODE:
```
yarn
```

----------------------------------------

TITLE: Installing Project Dependencies - Shell
DESCRIPTION: These commands are used to install all required project dependencies. Choose 'npm install' if you are using npm, or 'yarn' if you prefer Yarn for package management.
SOURCE: https://github.com/tanstack/table/blob/main/examples/svelte/filtering/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install
```

LANGUAGE: Shell
CODE:
```
yarn
```

----------------------------------------

TITLE: Installing Project Dependencies (npm/yarn)
DESCRIPTION: These commands are used to install all required project dependencies. Users can choose between npm or yarn package managers.
SOURCE: https://github.com/tanstack/table/blob/main/examples/vue/sub-components/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install
```

LANGUAGE: Shell
CODE:
```
yarn
```

----------------------------------------

TITLE: Installing Project Dependencies (npm/yarn) - Shell
DESCRIPTION: This snippet shows how to install the necessary project dependencies using either npm or yarn. It's a prerequisite for running the application.
SOURCE: https://github.com/tanstack/table/blob/main/examples/react/column-pinning/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install
```

LANGUAGE: Shell
CODE:
```
yarn
```

----------------------------------------

TITLE: Controlling Specific Table States for Server-Side Data in Angular
DESCRIPTION: This comprehensive example demonstrates how to control specific TanStack Table states (filtering, sorting, pagination) externally using Angular signals for a server-side data fetching scenario. It shows how to pass controlled state values to the table via the state option and update external signals using the on[State]Change callbacks, allowing for integration with custom data fetching logic.
SOURCE: https://github.com/tanstack/table/blob/main/docs/framework/angular/guide/table-state.md#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import {signal} from '@angular/core';
import {SortingState, ColumnFiltersState, PaginationState} from '@tanstack/angular-table'
import {toObservable} from "@angular/core/rxjs-interop";
import {combineLatest, switchMap} from 'rxjs';

class TableComponent {
  readonly columnFilters = signal<ColumnFiltersState>([]) //no default filters
  readonly sorting = signal<SortingState>([
    {
      id: 'age',
      desc: true, //sort by age in descending order by default
    }
  ])
  readonly pagination = signal<PaginationState>({
    pageIndex: 0,
    pageSize: 15
  })

  //Use our controlled state values to fetch data
  readonly data$ = combineLatest({
    filters: toObservable(this.columnFilters),
    sorting: toObservable(this.sorting),
    pagination: toObservable(this.pagination)
  }).pipe(
    switchMap(({filters, sorting, pagination}) => fetchData(filters, sorting, pagination))
  )
  readonly data = toSignal(this.data$);

  readonly table = createAngularTable(() => ({
    columns: this.columns,
    data: this.data(),
    //...
    state: {
      columnFilters: this.columnFilters(), //pass controlled state back to the table (overrides internal state)
      sorting: this.sorting(),
      pagination: this.pagination(),
    },
    onColumnFiltersChange: updater => { //hoist columnFilters state into our own state management
      updater instanceof Function
        ? this.columnFilters.update(updater)
        : this.columnFilters.set(updater)
    },
    onSortingChange: updater => {
      updater instanceof Function
        ? this.sorting.update(updater)
        : this.sorting.set(updater)
    },
    onPaginationChange: updater => {
      updater instanceof Function
        ? this.pagination.update(updater)
        : this.pagination.set(updater)
    },
  }))
}

//...
```

----------------------------------------

TITLE: Creating a Table Instance (React, Solid, Qwik, Vue, Svelte)
DESCRIPTION: These functions are used to create a table instance, with the specific function depending on the framework adapter being used (React, Solid, Qwik, Vue, Svelte). They take `TableOptions<TData>` and return a `Table<TData>`.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/table.md#_snippet_0

LANGUAGE: tsx
CODE:
```
type useReactTable = <TData extends AnyData>(
  options: TableOptions<TData>
) => Table<TData>
```

----------------------------------------

TITLE: Providing Initial Table State (initialState)
DESCRIPTION: The `initialState` option allows passing an initial partial table state. This state is used when resetting various table states, either automatically or via reset functions like `table.resetRowSelection()`. The object does not need to be stable as state is not reset when it changes.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/core/table.md#_snippet_4

LANGUAGE: tsx
CODE:
```
initialState?: Partial<
  VisibilityTableState &
  ColumnOrderTableState &
  ColumnPinningTableState &
  FiltersTableState &
  SortingTableState &
  ExpandedTableState &
  GroupingTableState &
  ColumnSizingTableState &
  PaginationTableState &
  RowSelectionTableState
>
```

----------------------------------------

TITLE: Implementing Custom Fuzzy Filter and Sort Functions with TanStack Table (TypeScript)
DESCRIPTION: This example demonstrates how to create a custom `fuzzyFilter` using `match-sorter-utils` to rank items and store ranking metadata via `addMeta`. It also provides a corresponding `fuzzySort` function that utilizes this stored metadata to sort rows based on their filter rank, falling back to alphanumeric sorting if ranks are equal.
SOURCE: https://github.com/tanstack/table/blob/main/docs/api/features/global-filtering.md#_snippet_2

LANGUAGE: tsx
CODE:
```
import { sortingFns } from '@tanstack/[adapter]-table'

import { rankItem, compareItems } from '@tanstack/match-sorter-utils'

const fuzzyFilter = (row, columnId, value, addMeta) => {
  // Rank the item
  const itemRank = rankItem(row.getValue(columnId), value)

  // Store the ranking info
  addMeta(itemRank)

  // Return if the item should be filtered in/out
  return itemRank.passed
}

const fuzzySort = (rowA, rowB, columnId) => {
  let dir = 0

  // Only sort by rank if the column has ranking information
  if (rowA.columnFiltersMeta[columnId]) {
    dir = compareItems(
      rowA.columnFiltersMeta[columnId]!,
      rowB.columnFiltersMeta[columnId]!
    )
  }

  // Provide an alphanumeric fallback for when the item ranks are equal
  return dir === 0 ? sortingFns.alphanumeric(rowA, rowB, columnId) : dir
}
```

----------------------------------------

TITLE: Updating Table Options and Row Models
DESCRIPTION: This snippet demonstrates how to update table configuration from the old `useTable` hook and its plugin system to the new `useReactTable` hook with explicit row model imports. It shows the replacement of `usePagination` and `useSortBy` with `getPaginationRowModel` and `getSortedRowModel` respectively, emphasizing that the order of row models no longer matters.
SOURCE: https://github.com/tanstack/table/blob/main/docs/guide/migrating.md#_snippet_2

LANGUAGE: tsx
CODE:
```
- import { useTable, usePagination, useSortBy } from 'react-table'; // [!code --]
+ import { // [!code ++]
+   useReactTable, // [!code ++]
+   getCoreRowModel, // [!code ++]
+   getPaginationRowModel, // [!code ++]
+   getSortedRowModel // [!code ++]
+ } from '@tanstack/react-table'; // [!code ++]

// ...

-   const tableInstance = useTable( // [!code --]
-     { columns,  data }, // [!code --]
-     useSortBy, // [!code --]
-     usePagination, //order of hooks used to matter // [!!code --]
-     // etc. // [!code --]
-   ); // [!code --]
+   const tableInstance = useReactTable({ // [!code ++]
+     columns, // [!code ++]
+     data, // [!code ++]
+     getCoreRowModel: getCoreRowModel(), // [!code ++]
+     getPaginationRowModel: getPaginationRowModel(), // [!code ++]
+     getSortedRowModel: getSortedRowModel(), //order doesn't matter anymore! // [!code ++]
+     // etc. // [!code ++]
+   }); // [!code ++]
```