{"name": "@axa/tailwind-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./native": "./native.ts", "./web": "./web.ts"}, "license": "SEE LICENSE", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "^8.5.4", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "eslint": "^9.28.0", "mini-svg-data-uri": "^1.4.4", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "prettier": "@axa/prettier-config"}