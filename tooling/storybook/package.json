{"name": "@axa/storybook-config", "version": "0.1.0", "private": true, "description": "storybook configurations for the AXA codebase", "type": "module", "exports": {"./nextjs-vite": "./nextjs-vite.ts", "./preview": "./preview.ts", "./preview-msw": "./preview-msw.ts", "./react": "./react.ts", "./testing": "./testing.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@mdx-js/react": "^3.1.0", "@storybook/addon-a11y": "^9.0.1", "@storybook/addon-docs": "^9.0.1", "@storybook/addon-vitest": "^9.0.1", "@storybook/nextjs": "^9.0.1", "@storybook/nextjs-vite": "^9.0.1", "@storybook/react-vite": "^9.0.1", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "msw": "^2.8.7", "msw-storybook-addon": "^2.0.4", "playwright": "^1.52.0", "storybook": "^9.0.1", "vite": "^6.3.5", "vitest": "^3.1.4"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "eslint": "^9.28.0", "next": "^15.3.3", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "peerDependencies": {"next": "^15.3.3", "react": "19.1.0", "react-dom": "19.1.0"}, "prettier": "@axa/prettier-config", "msw": {"workerDirectory": ["static"]}}