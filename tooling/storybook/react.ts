import { resolve } from "path";
import type { StorybookConfig } from "@storybook/react-vite";

import { mergeConfig } from "vite";

import { getAbsolutePath } from "./utils";

export type { StorybookConfig };

const baseConfig = {
  stories: [
    "../__stories__/**/*.mdx",
    "../__stories__/**/*.stories.@(js|jsx|mjs|ts|tsx|mdx)",
  ],
  staticDirs: [resolve("public"), resolve(__dirname, "static")],
  docs: {},
  features: {
    experimentalRSC: true,
  },
  framework: "@storybook/react-vite",
  addons: [
    getAbsolutePath("@storybook/addon-docs"),
    getAbsolutePath("@storybook/addon-a11y"),
    getAbsolutePath("@storybook/addon-vitest"),
  ],
  viteFinal(config, { configType }) {
    const alias = {
      "@/": resolve(process.cwd(), "src"),
    };

    if (configType === "DEVELOPMENT") {
      // Your development configuration goes here
    }
    if (configType === "PRODUCTION") {
      // Your production configuration goes here.
    }

    return mergeConfig(config, {
      resolve: {
        alias,
      },
      define: {
        // Clerk
        "process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL": JSON.stringify(
          process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL,
        ),
        "process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL": JSON.stringify(
          process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL,
        ),
        "process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL":
          JSON.stringify(
            process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL,
          ),
        "process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL":
          JSON.stringify(
            process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL,
          ),
        "process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY": JSON.stringify(
          process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY,
        ),
        // Google Maps API Key
        "process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY": JSON.stringify(
          process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
        ),
        // Supabase
        "process.env.NEXT_PUBLIC_SUPABASE_URL": JSON.stringify(
          process.env.NEXT_PUBLIC_SUPABASE_URL,
        ),
        "process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY": JSON.stringify(
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        ),
      },
    });
  },
} satisfies StorybookConfig;

export default baseConfig;
