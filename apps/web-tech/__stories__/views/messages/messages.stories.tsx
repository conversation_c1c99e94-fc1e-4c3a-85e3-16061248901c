import type { <PERSON>a, StoryObj } from "@storybook/react";

import { faker } from "@faker-js/faker";

import { trpcMsw } from "@/api/mock";
import Messages from "@/widgets/messages";

import { sampleMessages } from "./_helpers";

const meta = {
  title: "Views/Messages/Messages",
  component: Messages,
  parameters: {
    layout: "centered",
    msw: {
      handlers: [
        trpcMsw.orders.notes.get.query(({ input }) => {
          return { notes: sampleMessages };
        }),
      ],
    },
  },
  args: {
    orderId: faker.string.uuid(),
  },
} satisfies Meta<typeof Messages>;

export default meta;
type Story = StoryObj<typeof Messages>;

export const Default: Story = {
  args: {
    orderId: faker.string.uuid(),
  },
};

export const Loading: Story = {
  args: {
    orderId: faker.string.uuid(),
    loading: true,
  },
};

export const Empty: Story = {
  args: {
    orderId: faker.string.uuid(),
  },
};

export const WithError: Story = {
  args: {
    orderId: faker.string.uuid(),
  },
};
