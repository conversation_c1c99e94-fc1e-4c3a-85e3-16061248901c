import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { faker } from "@faker-js/faker";
import { fn } from "storybook/test";

import { Thread } from "@/widgets/messages/thread";

const meta = {
  title: "Views/Messages/Thread",
  component: Thread,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Thread>;

export default meta;
type Story = StoryObj<typeof Thread>;

const sampleMessages = [
  ...Array.from({ length: 10 }).map(() => ({
    id: faker.string.uuid(),
    message: faker.lorem.sentence(),
    type: faker.helpers.arrayElement(["PUBLIC", "INTERNAL"]),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    user: {
      id: faker.string.uuid(),
      avatar: faker.image.avatar(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
    },
    canEdit: faker.datatype.boolean(),
  })),
];

export const Default: Story = {
  args: {
    messages: sampleMessages,
    onCreate: fn(),
    onEdit: fn(),
    onDelete: fn(),
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Empty: Story = {
  args: {
    messages: [],
  },
};

export const WithError: Story = {
  args: {
    error: new Error("Failed to load messages"),
  },
};
