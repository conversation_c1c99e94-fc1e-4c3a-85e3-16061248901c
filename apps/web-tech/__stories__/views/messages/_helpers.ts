import { faker } from "@faker-js/faker";

import type { MessageType } from "@/widgets/messages/types";

export const sampleMessages = [
  ...Array.from({ length: 10 }).map(() => ({
    id: faker.string.uuid(),
    message: faker.lorem.sentence(),
    type: faker.helpers.arrayElement(["PUBLIC", "INTERNAL"]),
    createdAt: faker.date.recent(),
    updatedAt: faker.date.recent(),
    user: {
      id: faker.string.uuid(),
      avatar: faker.image.avatar(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      phone: faker.phone.number(),
    },
    canEdit: faker.datatype.boolean(),
  })),
] satisfies MessageType[];
