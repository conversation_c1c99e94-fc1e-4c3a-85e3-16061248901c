import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { addDays, addHours, subDays } from "date-fns";

import type { MessageType } from "@/widgets/messages/types";

import { Message } from "@/widgets/messages/message";

const meta = {
  title: "Views/Messages/Message",
  component: Message,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Message>;

export default meta;
type Story = StoryObj<typeof meta>;

const baseUser = {
  id: "user1",
  firstName: "John",
  lastName: "Doe",
  email: "<EMAIL>",
  phone: "+****************",
  avatar: "/avatars/avatar-01.png",
};

const baseMessage: MessageType = {
  id: "msg1",
  message: "Initial assessment completed. Equipment requires calibration.",
  type: "PUBLIC",
  createdAt: new Date(),
  updatedAt: null,
  canEdit: true,
  user: baseUser,
};

export const Default: Story = {
  args: {
    message: baseMessage,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Internal: Story = {
  args: {
    message: {
      ...baseMessage,
      type: "INTERNAL",
      message:
        "Internal note: Follow up with vendor about calibration schedule.",
    },
    internalUser: true,
  },
};

export const LongMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      message:
        "This is a much longer message that spans multiple lines. It includes technical details about the equipment calibration process, maintenance schedule, and vendor coordination. We should make sure this wraps properly and maintains readability.",
    },
  },
};

export const EditedMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      message: "Updated assessment: Equipment requires urgent calibration.",
      updatedAt: addHours(new Date(), -2),
    },
  },
};

export const YesterdayMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      createdAt: subDays(new Date(), 1),
      message: "Scheduled maintenance for next week.",
    },
  },
};

export const OldMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      createdAt: subDays(new Date(), 30),
      message: "Initial setup completed.",
    },
  },
};

export const FutureMessage: Story = {
  args: {
    message: {
      ...baseMessage,
      createdAt: addDays(new Date(), 1),
      message: "Maintenance scheduled for tomorrow.",
    },
  },
};

export const NoEditPermission: Story = {
  args: {
    message: {
      ...baseMessage,
      canEdit: false,
      message: "This message cannot be edited.",
    },
  },
};

export const WithoutAvatar: Story = {
  args: {
    message: {
      ...baseMessage,
      user: {
        ...baseUser,
        avatar: "",
      },
      message: "Message from user without avatar.",
    },
  },
};
