import path from "path";
import { fileURLToPath } from "url";

// @ts-expect-error - bad types
import { PrismaPlugin } from "@prisma/nextjs-monorepo-workaround-plugin";
import { withSentryConfig } from "@sentry/nextjs";
import CopyPlugin from "copy-webpack-plugin";
import { createJiti } from "jiti";

const fileName = fileURLToPath(import.meta.url);
const dirname = path.dirname(fileName);

if (process.env.NODE_ENV === "production") {
  createJiti(fileURLToPath(import.meta.url)).import("./src/env");
}

/** @type {import("next").NextConfig} */
const coreConfig = {
  // due to blocknote/mantine, we need to disable reactStrictMode
  reactStrictMode: false,
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  transpilePackages: [
    "@axa/api-tech",
    "@axa/constants",
    "@axa/database-tech",
    "@axa/emails",
    "@axa/lib",
    "@axa/ui",
    "@prisma/client",
  ],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "tech.axapro.com",
      },
      {
        protocol: "https",
        hostname: "img.clerk.com",
      },
    ],
  },
  webpack: (config, { webpack }) => {
    const prismaRuntimeOrigin = path.resolve(
      dirname,
      "../../node_modules/@prisma/client/runtime",
    );
    const prismaRuntimeDestination = path.resolve(
      dirname,
      "../../node_modules/.prisma/client/tech/runtime",
    );

    config.plugins.push(
      new webpack.NormalModuleReplacementPlugin(
        /^\.prisma\/client/gi,
        ".prisma/client/tech",
      ),
      new CopyPlugin({
        patterns: [
          {
            from: prismaRuntimeOrigin,
            to: prismaRuntimeDestination,
          },
        ],
      }),
      new PrismaPlugin(),
      new webpack.DefinePlugin({
        __SENTRY_DEBUG__: false,
        __SENTRY_TRACING__: false,
        __RRWEB_EXCLUDE_IFRAME__: true,
        __RRWEB_EXCLUDE_SHADOW_DOM__: true,
        __SENTRY_EXCLUDE_REPLAY_WORKER__: true,
      }),
    );

    return config;
  },
};

const config = withSentryConfig(coreConfig, {
  org: "axa-professionals",
  project: "axa-tech",
  authToken: process.env.SENTRY_AUTH_TOKEN,
  silent: !process.env.CI,
  widenClientFileUpload: true,
  tunnelRoute: "/monitoring",
  disableLogger: true,
  automaticVercelMonitors: true,
  sourcemaps: {
    disable: false,
    deleteSourcemapsAfterUpload: true,
  },
  reactComponentAnnotation: {
    enabled: true,
  },
});

export default config;
