{"name": "@axa/web-tech", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .next .turbo node_modules", "dev": "pnpm with-env next dev --port 3001", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e .env --", "tunnel": "pnpx localtunnel --port 3001", "storybook": "storybook dev -p 6009 --no-open", "storybook:ci": "storybook dev -p 6009 --no-open --ci", "storybook:build": "storybook build", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@axa/api-tech": "workspace:*", "@axa/database-tech": "workspace:*", "@axa/lib": "workspace:*", "@axa/ui": "workspace:*", "@clerk/nextjs": "^6.20.2", "@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/markerclusterer": "^2.5.3", "@next/third-parties": "^15.3.3", "@sentry/nextjs": "^9.24.0", "@t3-oss/env-nextjs": "^0.13.6", "@tanstack/react-query": "^5.79.0", "@trpc/client": "^11.1.4", "@trpc/react-query": "^11.1.4", "@trpc/server": "^11.1.4", "@uploadthing/react": "^6.8.0", "@vercel/analytics": "^1.5.0", "@vis.gl/react-google-maps": "^1.5.2", "geist": "^1.4.2", "lucide-react": "^0.511.0", "next": "^15.3.3", "react": "19.1.0", "react-dom": "19.1.0", "superjson": "^2.2.2", "uploadthing": "^6.13.3", "zod": "^3.25.42"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/storybook-config": "workspace:*", "@axa/tailwind-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@prisma/nextjs-monorepo-workaround-plugin": "^6.8.2", "@storybook/nextjs-vite": "^9.0.1", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vafanassieff/msw-trpc": "2.0.0-beta.8", "copy-webpack-plugin": "^13.0.0", "dotenv-cli": "^8.0.0", "eslint": "^9.28.0", "jiti": "^2.4.2", "prettier": "^3.5.3", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "prettier": "@axa/prettier-config"}