"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import { format } from "date-fns";
import {
  CircleIcon,
  ListFilterIcon,
  MenuIcon,
  Settings2Icon,
} from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import type { UseDataTableProps } from "@axa/ui/tables";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@axa/ui/primitives/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { useSearchParamsContext } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import EmptyList from "@axa/ui/shared/EmptyList";
import { DataTable, useDataTable } from "@axa/ui/tables";
import { dataTableColumns, selectColumn } from "@axa/ui/tables/columns";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "@axa/ui/tables/helpers";

import StatementTypeBadge from "@/components/symbols/StatementType";
import {
  FilterOrganizations,
  ResetFiltersButton,
} from "@/widgets/actions/filters";

const i18n = {
  en: {
    noStatement: "There are no statements yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search statements...",
      add: "Add Statement",
    },
    noStatements: "There are no statements",
    noDescription: "No description",
    headers: {
      actor: "Created By",
      date: "Date",
      type: "Type",
      createdBy: "Created By",
      amount: "Amount",
      note: "Notes",
    },
  },
  links: {
    statements: "/app/statements/[id]",
  },
};

export type StatementsQueryResult = RouterOutputs["statements"]["getMany"];
export type StatementsType = StatementsQueryResult["statements"];
export type StatementType = StatementsType[number];
export type TableProps = UseDataTableProps<StatementType, StatementsType>;

export function StatementsTableActions({
  table,
}: {
  table: ReturnType<
    typeof useDataTable<StatementType, StatementsType>
  >["table"];
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={i18n.en.actions.tableActions}
        >
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute right-1.5 top-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.en.selection} ({selectionCount})
        </DropdownMenuLabel>
        {/* <DropdownMenuSeparator />
        <DropdownMenuItem disabled={isPending} onSelect={exportInvoices}>
          {i18n.en.actions.export}
        </DropdownMenuItem> */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const defaultStatements: StatementsType = [];

export default function ListStatements({
  loading = false,
  statements,
  pagination,
  setPagination,
  setOrganizations,
  children,
}: PropsWithChildren<{
  loading?: boolean;
  statements?: StatementsQueryResult;
  pagination: TableProps["pagination"];
  setPagination: TableProps["setPagination"];
  setOrganizations?: (organizations: string[]) => void;
}>) {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const { statementType, setStatementType } = useMemo(
    () => ({
      statementType: searchParams["statement-type"] ?? undefined,
      setStatementType: (type?: string) =>
        setSearchParams("statement-type", type),
    }),
    [searchParams, setSearchParams],
  );
  const { table } = useDataTable<StatementType, StatementsType>({
    data: statements?.statements ?? defaultStatements,
    rowCount: statements?.total,
    manualPagination: true,
    pagination,
    setPagination,
    columns: useMemo(
      () =>
        dataTableColumns<StatementType, StatementsType>([
          selectColumn as ColumnDef<StatementType, StatementsType>,
          {
            id: "type",
            accessorKey: "type",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.type}
              />
            ),
            cell: ({ row }) => (
              <StatementTypeBadge type={row.getValue("type")} />
            ),
          },
          {
            id: "amount",
            accessorKey: "balance",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.amount}
              />
            ),
            cell: ({ row }) => <Currency amount={row.getValue("amount")} />,
          },
          {
            id: "note",
            accessorKey: "notes",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.note}
              />
            ),
            cell: ({ row }) => <p children={row.getValue("note")} />,
          },
          {
            id: "date",
            accessorKey: "period",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.date}
              />
            ),
            cell: ({ row }) => {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              const period = row.getValue("date") as Date;
              const date = new Date(
                period.getTime() + period.getTimezoneOffset() * 60000,
              );

              return (
                <p className="text-nowrap">{format(date, "LLLL dd, yyyy")}</p>
              );
            },
          },
          {
            id: "actor",
            accessorKey: "actor",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.actor}
              />
            ),
            cell: ({ row }) => {
              const actor = row.original.actor ?? {
                firstName: "SYSTEM",
                lastName: "",
              };

              return <p>{`${actor.firstName} ${actor.lastName}`.trim()}</p>;
            },
          },
          {
            id: "actions",
            meta: {
              className: "w-[32px]",
            },
            header: ({ table }) => (
              <div className="flex size-full items-center justify-end">
                <StatementsTableActions table={table} />
              </div>
            ),
            // cell: ({ row }) => (
            //   <div className="flex size-full items-center justify-end">
            //     <StatementMenu template={row.original} variant="ghost" />
            //   </div>
            // ),
          },
        ]),
      [],
    ),
  });

  return (
    <Card>
      <CardHeader className="border-b p-2">
        <div className="flex items-center justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <ListFilterIcon size="20" color="currentColor" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuRadioGroup
                  value={statementType as string | undefined}
                  onValueChange={setStatementType}
                >
                  <DropdownMenuLabel>Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioItem
                    // @ts-expect-error intentionally using undefined
                    value={undefined}
                  >
                    All
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value={"PAYMENT"}>
                    Payment
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value={"FUNDING"}>
                    Funding
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value={"FEE"}>
                    Fee
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            {setOrganizations && (
              <FilterOrganizations
                table={table}
                onSelect={(values: string[]) => setOrganizations(values)}
              />
            )}
            <ResetFiltersButton table={table} />
          </div>

          <div className="border-l pl-2">
            <DataTableSettings
              sizes={[5, 10, 25, 50]}
              table={table}
              variant="ghost"
              size="icon"
              className="ml-auto"
              aria-label={i18n.en.actions.tableSettings}
            >
              <Settings2Icon size="20" color="currentColor" />
            </DataTableSettings>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <DataTable loading={loading} slots={5} table={table}>
          <EmptyList title={i18n.en.noStatement}>{children}</EmptyList>
        </DataTable>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 border-t pt-6">
        <DataTableSimplePagination table={table} />
      </CardFooter>
    </Card>
  );
}
