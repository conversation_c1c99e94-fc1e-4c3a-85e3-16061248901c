"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";

import { BillingTypeField } from "@/components/forms/fields/BillingType";

const i18n = {
  en: {
    hours: {
      label: "Hours",
      description: "The number of hours worked",
      placeholder: "Enter the number of hours",
    },
    billingRate: {
      label: "Billing Rate",
      description: "The billing rate of the time sheet",
      placeholder: "Enter the billing rate",
    },
    billingType: {
      label: "Billing Type",
      description: "The billing type of the time sheet",
      placeholder: "Enter the billing type",
    },
    paymentRate: {
      label: "Payment Rate",
      description: "The payment rate of the time sheet",
      placeholder: "Enter the payment rate",
    },
    paymentType: {
      label: "Payment Type",
      description: "The payment type of the time sheet",
      placeholder: "Enter the payment type",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      nameShort: "The timeSheet name has to be at least 2 characters.",
    },
  },
};

const timeSheetFormSchema = z.object({
  organizationId: z.string().optional(),
  providerId: z.string().optional(),
  technicianId: z.string().optional(),
  invoiceId: z.string().optional(),
  orderId: z.string().optional(),
  date: z.date().optional(),
  notes: z.string().optional(),
  hours: z.number().positive(),
  billingRate: z.string(),
  billingType: z.enum(["HOURLY", "FIXED"]),
  paymentRate: z.string().optional(),
  paymentType: z.enum(["HOURLY", "FIXED"]).optional(),
});

export type TimeSheetFormValues = z.infer<typeof timeSheetFormSchema>;
export type TimeSheetFormProps = PropsWithChildren<
  Parameters<typeof useForm<TimeSheetFormValues>>[0] & {
    onSubmit?: (values: TimeSheetFormValues) => void | Promise<void>;
    includePayment?: boolean;
  }
>;

export default function TimeSheetForm({
  children,
  onSubmit = () => void 0,
  includePayment = false,
  ...props
}: TimeSheetFormProps) {
  const form = useForm<TimeSheetFormValues>({
    ...props,
    defaultValues: {
      ...props.defaultValues,
    },
    resolver: zodResolver(timeSheetFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <BillingTypeField
          name="billingType"
          label={i18n.en.billingType.label}
          description={i18n.en.billingType.description}
          placeholder={i18n.en.billingType.placeholder}
        />

        <CurrencyField
          name="billingRate"
          label={i18n.en.billingRate.label}
          description={i18n.en.billingRate.description}
          placeholder={i18n.en.billingRate.placeholder}
        />

        {includePayment && (
          <>
            <BillingTypeField
              name="paymentType"
              label={i18n.en.paymentType.label}
              description={i18n.en.paymentType.description}
              placeholder={i18n.en.paymentType.placeholder}
            />

            <CurrencyField
              name="paymentRate"
              label={i18n.en.paymentRate.label}
              description={i18n.en.paymentRate.description}
              placeholder={i18n.en.paymentRate.placeholder}
            />
          </>
        )}

        <FormField
          control={form.control}
          name="hours"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.hours.label}</FormLabel>
              <FormDescription>{i18n.en.hours.description}</FormDescription>
              <FormControl>
                <Input
                  type="number"
                  placeholder={i18n.en.hours.placeholder}
                  {...field}
                  onChange={(event) => {
                    field.onChange(parseFloat(event.target.value));
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <TimeSheetFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function TimeSheetFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<TimeSheetFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
