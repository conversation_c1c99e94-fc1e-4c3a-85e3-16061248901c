"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { ValueStoreType } from "@axa/database-tech";
import { SelectField } from "@axa/ui/fields/Select";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    fields: {
      key: {
        label: "Name",
        description: "The value key",
        placeholder: "Enter the value key",
      },
      type: {
        label: "Type",
        description: "The type of the value",
        placeholder: "Select the type of the value",
        options: {
          EXPENSE: "Expense Type",
          CONTACT: "Contact Role",
          ORDER_TYPE: "Order Type",
          ORDER_CATEGORY: "Order Category",
          LOCATION_TYPE: "Location Type",
        } satisfies Record<ValueStoreType, string>,
      },
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      keyShort: "The value key has to be at least 2 characters.",
    },
  },
};

const valueMapFormSchema = z.object({
  organizationId: z.string().optional(),
  key: z.string().min(2, {
    message: i18n.en.messages.keyShort,
  }),
  type: z.nativeEnum(ValueStoreType),
});

export type ValueMapFormValues = z.infer<typeof valueMapFormSchema>;
export type ValueMapFormProps = PropsWithChildren<
  Parameters<typeof useForm<ValueMapFormValues>>[0] & {
    onSubmit?: (values: ValueMapFormValues) => void | Promise<void>;
  }
>;

export default function ValueMapForm({
  children,
  onSubmit = () => void 0,
  ...props
}: ValueMapFormProps) {
  const form = useForm<ValueMapFormValues>({
    ...props,
    resolver: zodResolver(valueMapFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="key"
          label={i18n.en.fields.key.label}
          description={i18n.en.fields.key.description}
          placeholder={i18n.en.fields.key.placeholder}
        />

        <SelectField
          name="type"
          label={i18n.en.fields.type.label}
          description={i18n.en.fields.type.description}
          placeholder={i18n.en.fields.type.placeholder}
          options={Object.entries(i18n.en.fields.type.options).map(
            ([value, label]) => ({
              value,
              label,
            }),
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <ValueMapFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ValueMapFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ValueMapFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
