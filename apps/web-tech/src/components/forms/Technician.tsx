"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { BillingTypeField } from "@/components/forms/fields/BillingType";
import { TechnicianLevelField } from "@/components/forms/fields/TechnicianLevel";

const i18n = {
  en: {
    level: {
      label: "Technical Level",
      description: "The technical level of the technician",
      placeholder: "Enter the level of the technician",
    },
    billingRate: {
      label: "Billing Rate",
      description: "The billing rate of the time sheet",
      placeholder: "Enter the billing rate",
    },
    billingType: {
      label: "Billing Type",
      description: "The billing type of the time sheet",
      placeholder: "Enter the billing type",
    },
    paymentType: {
      label: "Payment Type",
      description: "The billing type",
      placeholder: "Select a billing type",
    },
    paymentRate: {
      label: "Payment Rate",
      description: "The billing amount",
      placeholder: "Enter the billing amount",
    },
    actions: {
      submit: "Submit",
    },
  },
};

export const technicianFormSchema = z.object({
  level: z.string(),
  billingRate: z.string(),
  billingType: z.enum(["HOURLY", "FIXED"]),
  paymentRate: z.string().optional(),
  paymentType: z.enum(["HOURLY", "FIXED"]).optional().default("HOURLY"),
});

export type TechnicianFormValues = z.infer<typeof technicianFormSchema>;
export interface TechnicianFormProps
  extends PropsWithChildren<
    Parameters<typeof useForm<TechnicianFormValues>>[0]
  > {
  onSubmit?: (values: TechnicianFormValues) => void | Promise<void>;
  includePayment?: boolean;
}

export default function TechnicianForm({
  children,
  onSubmit = () => void 0,
  includePayment = false,
  ...props
}: TechnicianFormProps) {
  const form = useForm<TechnicianFormValues>({
    ...props,
    resolver: zodResolver(technicianFormSchema),
  });

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        {/* <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8"> */}
        <TechnicianLevelField
          label={i18n.en.level.label}
          description={i18n.en.level.description}
          name="level"
        />

        <BillingTypeField
          name="billingType"
          label={i18n.en.billingType.label}
          description={i18n.en.billingType.description}
          placeholder={i18n.en.billingType.placeholder}
        />

        <CurrencyField
          name="billingRate"
          label={i18n.en.billingRate.label}
          description={i18n.en.billingRate.description}
          placeholder={i18n.en.billingRate.placeholder}
        />

        {includePayment && (
          <>
            <BillingTypeField
              name="paymentType"
              label={i18n.en.paymentType.label}
              description={i18n.en.paymentType.description}
              placeholder={i18n.en.paymentType.placeholder}
            />

            <CurrencyField
              name="paymentRate"
              label={i18n.en.paymentRate.label}
              description={i18n.en.paymentRate.description}
              placeholder={i18n.en.paymentRate.placeholder}
            />
          </>
        )}

        {children ?? (
          <div className="flex w-full justify-center">
            <TechnicianFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function TechnicianFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<TechnicianFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
