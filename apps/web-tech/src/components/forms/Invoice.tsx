"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "The invoice name or number",
      placeholder: "Enter the invoice name or number",
    },
    dateRange: {
      label: "Date Period Range",
      description: "The invoice range of dates",
      placeholder: "Enter the dates",
    },
    dueDate: {
      label: "Due Date",
      description: "The due date of the invoice",
      placeholder: "Enter the due date",
    },
    actions: {
      submit: "Submit",
    },
    messages: {},
  },
};

const invoiceFormSchema = z.object({
  name: z.string().optional(),
  organizationId: z.string(),
  startDate: z.date(),
  endDate: z.date(),
  dueDate: z.date(),
});

export type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;
export type InvoiceFormProps = PropsWithChildren<
  Parameters<typeof useForm<InvoiceFormValues>>[0] & {
    onSubmit?: (values: InvoiceFormValues) => void | Promise<void>;
  }
>;

export default function InvoiceForm({
  children,
  onSubmit = () => void 0,
  ...props
}: InvoiceFormProps) {
  const form = useForm<InvoiceFormValues>({
    ...props,
    resolver: zodResolver(invoiceFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.name.label}</FormLabel>
              <FormDescription>{i18n.en.name.description}</FormDescription>
              <FormControl>
                <Input placeholder={i18n.en.name.placeholder} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <DateField
          type="range"
          rangeName={["startDate", "endDate"]}
          label={i18n.en.dateRange.label}
          description={i18n.en.dateRange.description}
          placeholder={i18n.en.dateRange.placeholder}
          disabled={() => false}
        />

        <DateField
          type="single"
          name="dueDate"
          label={i18n.en.dueDate.label}
          description={i18n.en.dueDate.description}
          placeholder={i18n.en.dueDate.placeholder}
          disabled={() => false}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <InvoiceFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function InvoiceFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<InvoiceFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
