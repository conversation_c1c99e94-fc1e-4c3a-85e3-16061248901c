"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import Currency from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";

const i18n = {
  en: {
    threshold: {
      label: "Threshold",
      description: "The balance threshold amount until notified",
      placeholder: "Enter the threshold",
    },
    actions: {
      submit: "Submit",
    },
    messages: {},
  },
};

const invoiceFormSchema = z.object({
  organizationId: z.string(),
  threshold: z.string(),
});

export type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;
export type InvoiceFormProps = PropsWithChildren<
  Parameters<typeof useForm<InvoiceFormValues>>[0] & {
    onSubmit?: (values: InvoiceFormValues) => void | Promise<void>;
  }
>;

export default function InvoiceForm({
  children,
  onSubmit = () => void 0,
  ...props
}: InvoiceFormProps) {
  const form = useForm<InvoiceFormValues>({
    ...props,
    resolver: zodResolver(invoiceFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="threshold"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.threshold.label}</FormLabel>
              <FormDescription>{i18n.en.threshold.description}</FormDescription>
              <FormControl>
                <Currency
                  type="string"
                  placeholder={i18n.en.threshold.placeholder}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {children ?? (
          <div className="flex w-full justify-center">
            <InvoiceFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function InvoiceFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<InvoiceFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
