"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Calendar } from "@axa/ui/primitives/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@axa/ui/primitives/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import { Textarea } from "@axa/ui/primitives/textarea";

const i18n = {
  en: {
    amount: {
      label: "Amount",
      description: "The amount of the statement",
      placeholder: "Enter the amount",
    },
    type: {
      label: "Type",
      description: "The type of statement",
      placeholder: "Select the statement transaction type",
      options: {
        FUNDING: "Funding",
        PAYMENT: "Payment",
        FEE: "Fee",
      },
    },
    date: {
      label: "Date",
      description: "The date of the statement transaction",
      placeholder: "Select the date",
    },
    notes: {
      label: "Notes",
      description: "The notes of the statement",
      placeholder: "Enter the notes",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const statementFormSchema = z.object({
  organizationId: z.string().optional(),
  amount: z.string(),
  type: z.enum(["FUNDING", "PAYMENT", "FEE"]),
  date: z.date(),
  notes: z.string().optional(),
});

export type StatementFormValues = z.infer<typeof statementFormSchema>;
export type StatementFormProps = PropsWithChildren<
  Parameters<typeof useForm<StatementFormValues>>[0] & {
    onSubmit?: (values: StatementFormValues) => void | Promise<void>;
    loading?: boolean;
    organizationId?: string;
  }
>;

export default function StatementForm({
  children,
  loading,
  onSubmit = () => void 0,
  ...props
}: StatementFormProps) {
  const form = useForm<StatementFormValues>({
    ...props,
    resolver: zodResolver(statementFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <CurrencyField
          name="amount"
          label={i18n.en.amount.label}
          description={i18n.en.amount.description}
          placeholder={i18n.en.amount.placeholder}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.type.label}</FormLabel>
              <FormDescription>{i18n.en.type.description}</FormDescription>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger disabled={loading}>
                    <SelectValue placeholder={i18n.en.type.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(i18n.en.type.options).map(([id, value]) => (
                      <SelectItem key={id} value={id}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.date.label}</FormLabel>
              <FormDescription>{i18n.en.date.description}</FormDescription>
              <FormControl>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 size-4" />
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>{i18n.en.date.placeholder}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.notes.label}</FormLabel>
              <FormDescription>{i18n.en.notes.description}</FormDescription>
              <FormControl>
                <Textarea placeholder={i18n.en.notes.placeholder} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <StatementFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function StatementFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<StatementFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
