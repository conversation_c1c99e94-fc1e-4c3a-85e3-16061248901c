"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Textarea } from "@axa/ui/primitives/textarea";

const i18n = {
  en: {
    amount: {
      label: "Amount",
      description: "The amount of the fee",
      placeholder: "Enter the amount",
    },
    notes: {
      label: "Notes",
      description: "A note to give context to the fee",
      placeholder: "Add a note to explain what the fee is for",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const feeFormSchema = z.object({
  amount: z.string(),
  notes: z.string().optional(),
});

export type FeeFormValues = z.infer<typeof feeFormSchema>;
export type FeeFormProps = PropsWithChildren<
  Parameters<typeof useForm<FeeFormValues>>[0] & {
    onSubmit?: (values: FeeFormValues) => void | Promise<void>;
  }
>;

export default function FeeForm({
  children,
  onSubmit = () => void 0,
  ...props
}: FeeFormProps) {
  const form = useForm<FeeFormValues>({
    ...props,
    resolver: zodResolver(feeFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <CurrencyField
          name="amount"
          label={i18n.en.amount.label}
          description={i18n.en.amount.description}
          placeholder={i18n.en.amount.placeholder}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.notes.label}</FormLabel>
              <FormDescription>{i18n.en.notes.description}</FormDescription>
              <FormControl>
                <Textarea placeholder={i18n.en.notes.placeholder} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <FeeFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function FeeFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<FeeFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
