"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import { Textarea } from "@axa/ui/primitives/textarea";

const i18n = {
  en: {
    amount: {
      label: "Amount",
      description: "The amount of the expense",
      placeholder: "Enter the amount",
    },
    type: {
      label: "Type",
      description: "The type of expense",
      placeholder: "Select the type",
    },
    notes: {
      label: "Notes",
      description: "The notes of the expense",
      placeholder: "Enter the notes",
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      nameShort: "The expense name has to be at least 2 characters.",
    },
  },
};

const expenseFormSchema = z.object({
  organizationId: z.string().optional(),
  providerId: z.string().optional(),
  technicianId: z.string().optional(),
  invoiceId: z.string().optional(),
  orderId: z.string().optional(),
  amount: z.string(),
  type: z.string(),
  notes: z.string().optional(),
});

export type ExpenseFormValues = z.infer<typeof expenseFormSchema>;
export type ExpenseFormProps = PropsWithChildren<
  Parameters<typeof useForm<ExpenseFormValues>>[0] & {
    onSubmit?: (values: ExpenseFormValues) => void | Promise<void>;
    loading?: boolean;
    types?: { id: string; value: string }[];
  }
>;

export default function ExpenseForm({
  children,
  loading,
  types,
  onSubmit = () => void 0,
  ...props
}: ExpenseFormProps) {
  const form = useForm<ExpenseFormValues>({
    ...props,
    resolver: zodResolver(expenseFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <CurrencyField
          name="amount"
          label={i18n.en.amount.label}
          description={i18n.en.amount.description}
          placeholder={i18n.en.amount.placeholder}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.type.label}</FormLabel>
              <FormDescription>{i18n.en.type.description}</FormDescription>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger disabled={loading}>
                    <SelectValue placeholder={i18n.en.type.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    {types?.map((type) => (
                      <SelectItem key={type.id} value={type.value}>
                        {type.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.notes.label}</FormLabel>
              <FormDescription>{i18n.en.notes.description}</FormDescription>
              <FormControl>
                <Textarea placeholder={i18n.en.notes.placeholder} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <ExpenseFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ExpenseFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ExpenseFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
