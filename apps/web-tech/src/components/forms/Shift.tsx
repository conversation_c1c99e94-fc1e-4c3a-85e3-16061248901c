"use client";

import type { PropsWithChildren } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import { TimeField } from "@axa/ui/fields/date-time/Time";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import { TooltipProvider } from "@axa/ui/primitives/tooltip";

const i18n = {
  en: {
    type: {
      label: "Service Date Type",
      description: "The type of service date",
      placeholder: "Enter the service date type",
      options: {
        SERVICE_DATE: "Service Date",
        SERVICE_WINDOW: "Service Window",
      },
    },
    actions: {
      submit: "Submit",
    },
    messages: {
      hours: "The hours have to be at least 2.",
    },
  },
};

export const shiftBaseSchema = z.object({
  type: z.enum(["SERVICE_WINDOW", "SERVICE_DATE"]),
  timeZone: z.string().optional(),
  date: z.date().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  hours: z.number().min(2, {
    message: i18n.en.messages.hours,
  }),
  startTime: z.object({
    hour: z.number(),
    minute: z.number(),
    second: z.number().optional(),
    millisecond: z.number().optional(),
  }),
  endTime: z
    .object({
      hour: z.number(),
      minute: z.number(),
      second: z.number().optional(),
      millisecond: z.number().optional(),
    })
    .optional(),
});

export const shiftFormSchema = z.discriminatedUnion("type", [
  shiftBaseSchema.extend({
    type: z.literal("SERVICE_WINDOW"),
    startDate: z.date(),
    endDate: z.date(),
  }),
  shiftBaseSchema.extend({
    type: z.literal("SERVICE_DATE"),
    date: z.date(),
  }),
]);

export type ShiftFormValues = z.infer<typeof shiftFormSchema>;
export type ShiftFormProps = PropsWithChildren<
  Parameters<typeof useForm<ShiftFormValues>>[0] & {
    timeZone?: string;
    onSubmit?: (values: ShiftFormValues) => void | Promise<void>;
  }
>;

export default function ShiftForm({
  children,
  onSubmit = () => void 0,
  timeZone = "local",
  ...props
}: ShiftFormProps) {
  const form = useForm<ShiftFormValues>({
    ...props,
    resolver: zodResolver(shiftFormSchema),
  });

  const shiftType = form.watch("type");
  const mode = shiftType === "SERVICE_WINDOW" ? "range" : "single";

  return (
    <Form {...form}>
      <form
        onSubmit={useCallback(
          async function handleStandaloneSubmit(
            event: React.FormEvent<HTMLFormElement>,
          ) {
            event.stopPropagation();
            await form.handleSubmit(onSubmit)(event);
          },
          [form, onSubmit],
        )}
        className="space-y-8"
      >
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.type.label}</FormLabel>
              <FormDescription>{i18n.en.type.description}</FormDescription>
              <FormControl>
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={i18n.en.type.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(i18n.en.type.options).map(
                      ([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <TooltipProvider>
          <DateField type={mode} timeZone={timeZone} />
          <TimeField showHours />
        </TooltipProvider>

        {children ?? (
          <div className="flex w-full justify-center">
            <ShiftFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ShiftFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ShiftFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
