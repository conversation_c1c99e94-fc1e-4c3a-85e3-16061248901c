"use client";

import type { PropsWithChildren } from "react";

import { useEffect, useMemo } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import { Textarea } from "@axa/ui/primitives/textarea";

const i18n = {
  en: {
    message: {
      label: "Message",
      description: "The message to send",
      placeholder: "Enter a message",
    },
    type: {
      label: "Message type",
      description: "The type of message",
      placeholder: "Select the message type",
      options: {
        INTERNAL: "Internal",
        PUBLIC: "General",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

const orderMessageFormSchema = z.object({
  orderId: z.string().optional(),
  messageId: z.string().optional(),
  message: z.string(),
  type: z.enum(["INTERNAL", "PUBLIC"]).default("PUBLIC"),
});

export type OrderMessageFormValues = z.infer<typeof orderMessageFormSchema>;
export type OrderMessageFormProps = PropsWithChildren<
  Parameters<typeof useForm<OrderMessageFormValues>>[0] & {
    onSubmit?: (values: OrderMessageFormValues) => void | Promise<void>;
    className?: string;
    showMessageType?: boolean;
  }
>;

export default function OrderMessageForm({
  children,
  onSubmit = () => void 0,
  className,
  showMessageType = false,
  ...props
}: OrderMessageFormProps) {
  const form = useForm<OrderMessageFormValues>({
    ...props,
    resolver: zodResolver(orderMessageFormSchema),
  });

  const defaultValues = useMemo<OrderMessageFormValues | undefined>(() => {
    return {
      orderId: form.formState.defaultValues?.orderId,
      messageId: form.formState.defaultValues?.messageId,
      message: form.formState.defaultValues?.message ?? "",
      type: form.formState.defaultValues?.type ?? "PUBLIC",
      ...props.defaultValues,
    };
  }, [form.formState.defaultValues, props.defaultValues]);

  useEffect(() => {
    if (form.formState.isSubmitSuccessful) {
      form.reset(defaultValues);
    }
  }, [form.formState.isSubmitSuccessful, defaultValues]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn("space-y-8", className)}
      >
        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="sr-only">{i18n.en.message.label}</FormLabel>
              <FormDescription className="sr-only">
                {i18n.en.message.description}
              </FormDescription>
              <FormControl>
                <Textarea
                  {...field}
                  className="h-32 w-full p-2"
                  placeholder={i18n.en.message.placeholder}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <>
            {showMessageType && <OrderMessageTypeField />}

            <div className="flex w-full justify-center">
              <OrderMessageFormSubmitButton />
            </div>
          </>
        )}
      </form>
    </Form>
  );
}

export function OrderMessageFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrderMessageFormValues>();

  return (
    <Button
      {...props}
      className={cn("w-full", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}

export function OrderMessageTypeField(props: { disabled?: boolean }) {
  const form = useFormContext<OrderMessageFormValues>();

  return (
    <FormField
      control={form.control}
      name="type"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="sr-only">{i18n.en.type.label}</FormLabel>
          <FormDescription className="sr-only">
            {i18n.en.type.description}
          </FormDescription>
          <FormControl>
            <Select
              {...props}
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value as OrderMessageFormValues["type"]);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={i18n.en.type.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(i18n.en.type.options).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
