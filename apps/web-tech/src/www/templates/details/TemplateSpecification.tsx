import { ValueStoreType } from "@axa/database-tech";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";

import { api } from "@/api/client";
import OrderSpecificationForm, {
  OrderSpecificationFormSubmitButton,
} from "@/components/forms/OrderSpecification";

const i18n = {
  en: {
    actions: {
      save: "Save",
    },
  },
};

export default function TemplateSpecification({
  loading = false,
  templateId,
  summary,
  scope,
  type,
  category,
}: {
  loading?: boolean;
  templateId?: string;
  summary?: string;
  scope?: string;
  type?: string;
  category?: string;
}) {
  const categories = api.values.getMany.useQuery({
    type: ValueStoreType.ORDER_CATEGORY,
  });
  const types = api.values.getMany.useQuery({
    type: ValueStoreType.ORDER_TYPE,
  });
  const updateTemplateMutation = api.templates.update.useMutation({
    onSuccess: () => {
      toast.success("Template updated successfully.");
    },
    onError: (error) => {
      toast.error("Failed to update template. " + error.message);
    },
  });

  const dataLoading = loading || categories.isLoading || types.isLoading;

  if (dataLoading) {
    return (
      <div className="flex size-full flex-col rounded-lg border-2 border-dashed p-0.5">
        <Skeleton className="size-full min-h-32" />
      </div>
    );
  }

  return (
    <div className="flex aspect-auto h-full flex-col justify-between rounded-md border-2 p-4">
      <OrderSpecificationForm
        loading={dataLoading}
        categories={categories.data?.values ?? []}
        types={types.data?.values ?? []}
        resetOptions={{
          keepDirty: false,
          keepIsSubmitted: false,
        }}
        defaultValues={{
          summary,
          scope,
          type,
          category,
        }}
        onSubmit={(values) => {
          if (templateId) {
            updateTemplateMutation.mutate({
              id: templateId,
              summary: values.summary,
              scope: values.scope,
              type: values.type,
              category: values.category,
            });
          }
        }}
      >
        <div className="flex w-full justify-end">
          <OrderSpecificationFormSubmitButton
            disabled={updateTemplateMutation.isPending}
          >
            {i18n.en.actions.save}
          </OrderSpecificationFormSubmitButton>
        </div>
      </OrderSpecificationForm>
    </div>
  );
}
