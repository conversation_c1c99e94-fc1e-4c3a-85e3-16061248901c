import { use<PERSON>emo } from "react";
import { Edit<PERSON>con, PlusCircleIcon, XIcon } from "lucide-react";

import type { Contact } from "@axa/database-tech";
import type { ContactFormValues } from "@axa/ui/forms/Contact";
import PreviewContact from "@axa/ui/common/PreviewContact";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type {
  AddContactProps,
  DeleteContactProps,
  UpdateContactProps,
} from "@/widgets/actions/resources/contact";

import { api } from "@/api/client";
import {
  AddContact,
  DeleteContact,
  UpdateContact,
} from "@/widgets/actions/resources/contact";
import { UpdatePerson } from "@/widgets/actions/resources/people";

const i18n = {
  en: {
    actions: {
      addContact: "Add Contact",
      updateContact: "Update Contact",
      removeContact: "Remove Contact",
      updatePerson: "Update",
    },
  },
};

export type OrderContact =
  | Pick<Contact, "id">
  | (ContactFormValues & { id: string });

export interface WorkOrderContactProps {
  local?: boolean;
  loading?: boolean;
  orderId?: string;
  templateId?: string;
  contactId?: string;
  contact?: OrderContact | null;
  onCreate?: AddContactProps["onCreate"];
  onUpdate?: UpdateContactProps["onUpdate"];
  onDelete?: DeleteContactProps["onDelete"];
}

export function WorkOrderContact({
  local = false,
  loading = false,
  orderId,
  templateId,
  contactId,
  contact: _c,
  onCreate,
  onUpdate,
  onDelete,
}: WorkOrderContactProps) {
  const contact = api.contacts.get.useQuery(
    { id: contactId ?? "" },
    {
      enabled: !!contactId && !loading && !local,
    },
  );

  if (loading || contact.isLoading) {
    return (
      <div className="flex size-full flex-col rounded-lg border-2 border-dashed p-0.5">
        <Skeleton className="size-full min-h-32" />
      </div>
    );
  }

  if (contactId) {
    const _contact = local
      ? (_c as ContactFormValues & {
          id: string;
          organization?: { id?: string };
        })
      : contact.data;
    const person = _contact?.person;

    return (
      <div className="flex size-full min-h-32 flex-col rounded-lg border-2">
        <div className="flex items-center justify-between border-b p-4">
          <div className="group flex flex-1 items-center space-x-1">
            <span className="truncate font-semibold">{_contact?.role}</span>
            <UpdateContact
              contactId={contactId}
              templateId={templateId}
              orderId={orderId}
              onUpdate={onUpdate}
              defaultValues={{
                role: _contact?.role ?? "",
                personId: person?.id ?? "",
                organizationId: _contact?.organization?.id ?? "",
                person: {
                  id: person?.id ?? "",
                  firstName: person?.firstName ?? "",
                  lastName: person?.lastName ?? "",
                  email: person?.email ?? "",
                  phone: person?.phone ?? "",
                  avatar: person?.avatar ?? "",
                  isUser: person?.isUser ?? false,
                },
              }}
            >
              <Button
                size="icon"
                variant="outline"
                className={cn(
                  "size-8 text-muted-foreground opacity-0 transition-opacity focus:opacity-100 focus-visible:opacity-100 active:opacity-100 group-hover:opacity-100 group-focus:opacity-100",
                )}
              >
                <EditIcon className="size-4" color="currentColor" />
                <span className="sr-only">{i18n.en.actions.updateContact}</span>
              </Button>
            </UpdateContact>
          </div>

          <DeleteContact
            contactId={contactId}
            templateId={templateId}
            orderId={orderId}
            onDelete={onDelete}
          >
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground"
            >
              <XIcon
                size={20}
                color="currentColor"
                aria-label={i18n.en.actions.removeContact}
              />
            </Button>
          </DeleteContact>
        </div>

        <div className="flex-1 p-4">
          <PreviewContact
            firstName={person?.firstName}
            lastName={person?.lastName}
            email={person?.email ?? ""}
            phone={person?.phone ?? ""}
            avatar={person?.avatar ?? undefined}
          />
        </div>

        <div className="p-4">
          <UpdatePerson
            disabled={person?.isUser}
            personId={person?.id ?? ""}
            defaultValues={{
              firstName: person?.firstName ?? "",
              lastName: person?.lastName ?? "",
              email: person?.email ?? "",
              phone: person?.phone ?? "",
            }}
          >
            <Button
              disabled={person?.isUser}
              variant="outline"
              className="size-full space-x-1 text-muted-foreground"
            >
              <EditIcon size={20} color="currentColor" />
              <span>{i18n.en.actions.updatePerson}</span>
            </Button>
          </UpdatePerson>
        </div>
      </div>
    );
  }

  return (
    <div className="flex size-full min-h-32 flex-col rounded-lg border-2 border-dashed p-0.5">
      <AddContact templateId={templateId} orderId={orderId} onCreate={onCreate}>
        <Button
          className="size-full space-x-1 text-muted-foreground"
          variant="ghost"
        >
          <PlusCircleIcon size={20} color="currentColor" />
          <span>{i18n.en.actions.addContact}</span>
        </Button>
      </AddContact>
    </div>
  );
}

export interface WorkOrderContactsProps {
  local?: boolean;
  loading?: boolean;
  orderId?: string;
  templateId?: string;
  contacts?: OrderContact[];
  onCreate?: AddContactProps["onCreate"];
  onUpdate?: UpdateContactProps["onUpdate"];
  onDelete?: DeleteContactProps["onDelete"];
}

export default function WorkOrderContacts({
  local = false,
  loading = false,
  orderId,
  templateId,
  contacts = [],
  onCreate,
  onUpdate,
  onDelete,
}: WorkOrderContactsProps) {
  return (
    <ul className="grid size-full grid-cols-1 grid-rows-1 gap-2 sm:grid-cols-2 md:sm:grid-cols-1 md:grid-rows-2 lg:sm:grid-cols-2">
      {useMemo(() => {
        const list = new Array(4).fill(null);

        return list.map((_, index) => {
          if (contacts[index]) {
            return contacts[index];
          }

          return null;
        });
      }, [contacts]).map((contact, key) => (
        <li
          key={contact?.id ?? key}
          className="aspect-auto size-full sm:aspect-square md:aspect-auto lg:aspect-auto"
        >
          <WorkOrderContact
            local={local}
            loading={loading}
            orderId={orderId}
            templateId={templateId}
            contactId={contact?.id ?? ""}
            contact={contact}
            onCreate={onCreate}
            onUpdate={onUpdate}
            onDelete={onDelete}
          />
        </li>
      ))}
    </ul>
  );
}
