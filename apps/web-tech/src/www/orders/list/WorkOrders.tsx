"use client";

import { Suspense, use, useState } from "react";
import Link from "next/link";
import { PlusCircleIcon } from "lucide-react";

import type { WorkOrderPriority, WorkOrderStatus } from "@axa/database-tech";
import AppView from "@axa/ui/layouts/AppView";
import { Button } from "@axa/ui/primitives/button";
import { useSearchParamsContext } from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListOrders from "@/components/ListOrders";
import OrderStatusFilter from "@/components/shared/OrderStatusFilter";
import {
  SearchParams,
  useSearchDateRangeValue,
  useSearchOrderTypeValue,
  useSearchOrganizationsValue,
  useSearchTextValue,
} from "@/widgets/actions/search";
import NotificationCenter from "@/widgets/NotificationCenter";

const i18n = {
  en: {
    title: "Work Orders",
    description: "Work orders are tasks that need to be completed.",
    noOrders: "There are no work orders",
    actions: {
      add: "New Work Order",
    },
  },
  links: {
    order: "/app/orders/[id]",
    new: "/app/orders/new",
  },
};

export function NewWorkOrder() {
  return (
    <Button size="sm" asChild className="h-7 gap-2 text-sm">
      <Link href={i18n.links.new}>
        <PlusCircleIcon className="size-4" />
        <span className="sr-only md:not-sr-only">{i18n.en.actions.add}</span>
      </Link>
    </Button>
  );
}

export type WorkOrdersData = RouterOutputs["orders"]["getMany"]["orders"];

export interface WorkOrdersViewProps {
  loading?: boolean;
  organizationId?: string;
  orders?: Promise<RouterOutputs["orders"]["getMany"]>;
}

export function WorkOrdersView(props: WorkOrdersViewProps) {
  const query = useSearchTextValue();
  const type = useSearchOrderTypeValue();
  const organizations = useSearchOrganizationsValue();
  const { startDate, endDate } = useSearchDateRangeValue();
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const status: WorkOrderStatus[] = ([] as WorkOrderStatus[]).concat(
    (searchParams.status === "ALL"
      ? []
      : ((searchParams.status as string | undefined) ??
        [])) as WorkOrderStatus[],
  );

  const orders = api.orders.getMany.useQuery(
    {
      organizationId: props.organizationId,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      organizations: organizations ? [organizations] : undefined,
      type: type ? [type] : undefined,
      category: searchParams.category
        ? [searchParams.category as string]
        : undefined,
      priority: searchParams.priority
        ? [searchParams.priority as WorkOrderPriority]
        : undefined,
      status,
      query,
      startDate,
      endDate,
      include: {
        organization: true,
        schedule: true,
        shifts: true,
        location: true,
        providers: true,
      },
    },
    {
      initialData: props.orders ? use(props.orders) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || orders.isLoading;

  return (
    <div className="space-y-6">
      <OrderStatusFilter
        loading={orders.isLoading}
        counts={orders.data?.count}
        className="w-full"
        value={searchParams.status as string | undefined}
        onValueChange={(value) => setSearchParams("status", value)}
      />

      {orders.error && <ErrorFallback error={orders.error} />}

      <ListOrders
        loading={loading}
        orders={orders.data}
        pagination={pagination}
        setPagination={setPagination}
      >
        <NewWorkOrder />
      </ListOrders>
    </div>
  );
}

export default function WorkOrdersPage(props: WorkOrdersViewProps) {
  return (
    <AppView title={i18n.en.title} size="full">
      <div className="ml-auto flex h-full items-center gap-2">
        <NewWorkOrder />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div className="mx-auto w-full max-w-screen-xl">
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <div className="mx-auto my-8 w-full max-w-screen-xl space-y-8 px-2 md:px-4 lg:px-8">
          <NotificationCenter types={["ORDER", "MESSAGE"]} />
        </div>

        <Suspense fallback={<WorkOrdersView key="orders" loading />}>
          <SearchParams>
            <WorkOrdersView {...props} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
