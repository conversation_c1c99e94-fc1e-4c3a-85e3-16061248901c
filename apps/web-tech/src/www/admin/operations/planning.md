---
title: Business Milestone KPIs & Analytics Dashboard
description: Key Performance Indicators and metrics tracking for field service operations
created: 2025-06-19
status: draft
---

# Business Milestone KPIs & Analytics Dashboard

## Overview

This document outlines key performance indicators (KPIs) and metrics that can be tracked based on our current data structure. Each KPI includes recommended visualization methods and data sources from our schema.

## Financial KPIs

### Revenue Metrics

- **Monthly Recurring Revenue (MRR)**

  - Data: `Invoice.timePeriodStart/End`, `WorkOrder.paymentRate`
  - Visualization: Line chart with trend analysis
  - Calculation: Sum of completed work orders by month

- **Average Revenue Per Work Order**

  - Data: `WorkOrder`, `TimeSheet.total`
  - Visualization: Bar chart by month/quarter
  - Segmentation: By organization, location, service type

- **Invoice Collection Rate**

  - Data: `Invoice.status`, `Statement.type`
  - Visualization: Donut chart showing PAID vs DUE vs OVERDUE
  - Target: >95% collection rate

- **Profit Margins**
  - Data: `TimeSheet.billingRate` vs `TimeSheet.paymentRate`
  - Visualization: Waterfall chart showing margin breakdown
  - Segmentation: By service category, provider level

### Cost Management

- **Cost Per Work Order**

  - Data: `Expense.amount`, `TimeSheet.paymentRate`
  - Visualization: Stacked bar chart (labor + expenses)
  - Trending: Month-over-month comparison

- **Provider Cost Analysis**
  - Data: `Provider.rates`, `Technician.paymentRate`
  - Visualization: Heat map by provider level and geography
  - Insights: Identify cost-effective providers

## Operational KPIs

### Work Order Management

- **Work Order Completion Rate**

  - Data: `WorkOrder.status` (COMPLETED vs total)
  - Visualization: Gauge chart with target threshold
  - Target: >90% completion rate

- **Average Time to Complete**

  - Data: `WorkOrder.createdAt` to `Technician.completedAt`
  - Visualization: Box plot showing distribution
  - Segmentation: By priority, category, location

- **Work Order Volume Trends**

  - Data: `WorkOrder.createdAt` count by period
  - Visualization: Multi-line chart (created, assigned, completed)
  - Forecasting: Seasonal trend analysis

- **Emergency Work Order Response Time**
  - Data: `WorkOrder.emergency = true`, timestamps
  - Visualization: Histogram of response times
  - Target: <2 hours for emergency calls

### Scheduling Efficiency

- **Schedule Utilization Rate**

  - Data: `Shift.hours` vs available capacity
  - Visualization: Capacity planning chart
  - Optimization: Resource allocation insights

- **Overtime Analysis**
  - Data: `TimeSheet.overtimeHours`
  - Visualization: Stacked area chart by month
  - Cost Impact: Overtime rate vs regular rate

## Performance KPIs

### Service Quality

- **First-Time Fix Rate**

  - Data: Work orders without follow-up orders at same location
  - Visualization: Trend line with industry benchmark
  - Target: >85% first-time resolution

- **Customer Satisfaction Score**
  - Data: Post-service feedback (would need to add to schema)
  - Visualization: NPS-style gauge chart
  - Correlation: With technician performance

### Technician Performance

- **Technician Productivity**

  - Data: `TimeSheet.hours` per work order completion
  - Visualization: Scatter plot with efficiency quadrants
  - Recognition: Top performer leaderboard

- **Provider Performance Scoring**
  - Data: Completion rate, quality scores, cost efficiency
  - Visualization: Multi-dimensional radar chart
  - Actions: Performance improvement plans

## Customer & Growth KPIs

### Customer Metrics

- **Customer Acquisition Rate**

  - Data: New `Organization` records by month
  - Visualization: Funnel chart showing conversion stages
  - Segmentation: By organization type, geography

- **Customer Retention Rate**

  - Data: Active organizations with recurring work orders
  - Visualization: Cohort analysis chart
  - Target: >95% retention for key accounts

- **Customer Lifetime Value (CLV)**
  - Data: Sum of work orders per organization over time
  - Visualization: Customer value distribution histogram
  - Strategy: High-value customer identification

### Geographic Analysis

- **Service Coverage Heat Map**

  - Data: `Location.latitude/longitude`, work order density
  - Visualization: Geographic heat map
  - Expansion: Identify underserved areas

- **Regional Performance**
  - Data: Revenue, completion rates by state/city
  - Visualization: Choropleth map
  - Optimization: Resource allocation by region

## Resource Utilization KPIs

### Asset Management

- **Provider Utilization Rate**

  - Data: Active shifts vs available capacity
  - Visualization: Resource utilization timeline
  - Optimization: Workload balancing

- **Location Service Frequency**
  - Data: `Location` work order count and patterns
  - Visualization: Service frequency bar chart
  - Maintenance: Predictive service scheduling

### Technology Adoption

- **Digital Documentation Rate**
  - Data: `Document` attachments per work order
  - Visualization: Adoption curve over time
  - Benefits: Efficiency gains tracking

## Dashboard Implementation Strategy

### High-Level Executive Dashboard

- Revenue trends and forecasts
- Key operational metrics
- Alert indicators for critical thresholds
- Geographic performance overview

### Operational Management Dashboard

- Work order pipeline and status
- Resource utilization and scheduling
- Performance metrics by team/region
- Cost analysis and budget tracking

### Financial Dashboard

- Invoice and payment tracking
- Profit margin analysis
- Expense categorization and trends
- Cash flow projections

### Field Operations Dashboard

- Real-time work order status
- Technician location and availability
- Emergency response metrics
- Quality and compliance tracking

## Recommended Chart Types by Metric

| Metric Category | Primary Chart Types      | Interactive Features           |
| :-------------- | :----------------------- | :----------------------------- |
| Revenue Trends  | Line charts, Area charts | Date range filters, drill-down |
| Cost Analysis   | Waterfall, Stacked bars  | Category filters, tooltips     |
| Performance     | Gauge, Bullet charts     | Threshold alerts, comparisons  |
| Geographic      | Heat maps, Choropleth    | Zoom, region selection         |
| Utilization     | Timeline, Gantt charts   | Resource filtering, scheduling |
| Distribution    | Histograms, Box plots    | Statistical overlays, outliers |

## Data Refresh & Alerts

### Real-Time Metrics

- Work order status changes
- Emergency service requests
- Payment receipts
- Technician check-ins

### Daily Updates

- Revenue calculations
- Performance scorecards
- Utilization reports
- Expense summaries

### Weekly/Monthly Analysis

- Trend analysis and forecasting
- Comparative performance reports
- Strategic planning metrics
- Customer satisfaction surveys

## Implementation Priorities

### Phase 1: Core Financial Metrics

1. Revenue tracking and invoicing
2. Cost analysis and profit margins
3. Basic operational metrics
4. Simple executive dashboard

### Phase 2: Operational Excellence

1. Performance management tools
2. Resource optimization
3. Quality tracking
4. Advanced scheduling metrics

### Phase 3: Strategic Analytics

1. Predictive analytics
2. Customer intelligence
3. Market expansion analysis
4. Competitive benchmarking

## Technical Considerations

### Data Sources

- Primary: PostgreSQL database (current schema)
- Secondary: External integrations (FieldNation, Clerk)
- Real-time: WebSocket connections for live updates
- Historical: Data warehouse for trend analysis

### Visualization Tools

- Charts: Recharts, D3.js, or Chart.js
- Maps: Mapbox or Google Maps integration
- Tables: TanStack Table for detailed data
- Dashboards: Custom React components

### Performance Optimization

- Materialized views for complex calculations
- Caching strategies for frequently accessed metrics
- Incremental data updates
- Efficient query optimization

---

_Next Steps: Prioritize KPIs based on business objectives and begin implementation with Phase 1 metrics._
