"use client";

import { Suspense } from "react";

import AppView from "@axa/ui/layouts/AppView";
import { SearchParams } from "@axa/ui/search";

import type { PreviewInvoicesProps } from "@/widgets/PreviewInvoices";

import { AddInvoice } from "@/widgets/actions/billing/invoice";
import PreviewInvoices from "@/widgets/PreviewInvoices";

const i18n = {
  en: {
    title: "Invoices",
    description: "Invoices are a collection of time sheets for work orders.",
  },
};

export default function InvoicesPage({
  organization,
  invoices,
}: PreviewInvoicesProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddInvoice size="sm" />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<PreviewInvoices loading />}>
          <SearchParams>
            <PreviewInvoices invoices={invoices} organization={organization} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
