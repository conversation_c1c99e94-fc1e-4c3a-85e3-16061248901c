"use client";

import { Suspense, use, useMemo, useState } from "react";
import { ChevronDownIcon } from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import AppView from "@axa/ui/layouts/AppView";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { SearchParams } from "@axa/ui/search";
import SelectOrganization from "@axa/ui/selectors/SelectOrganization";
import Currency from "@axa/ui/shared/Currency";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import { useUser } from "@/contexts/User";
import { AddStatement } from "@/widgets/actions/billing/statements";
import { UpdateBillingSettings } from "@/widgets/actions/organizations/organization";
import PreviewInvoices from "@/widgets/PreviewInvoices";
import PreviewStatements from "@/widgets/PreviewStatements";

const i18n = {
  en: {
    title: "Billing",
    balance: "Balance",
    invoices: "Invoices",
    statements: "Statements",
    currentAmount: "Current standing amount",
    currentOrganization: "Organization",
    currentOrganizationDescription: "Current organization in view",
    managedAccount: "Managed Account",
    managedAccountDescription: "This account is managed by",
    links: {
      viewStatements: "View Statements",
      billing: "Billing",
    },
    actions: {
      export: "Export",
    },
  },
  links: {
    billing: "/app/billing",
    statements: "/app/billing/[organizationId]/statements",
  },
};

export interface OrganizationBillingProps {
  loading?: boolean;
  billing?: Promise<RouterOutputs["billing"]["get"]>;
  organization?:
    | RouterOutputs["organizations"]["getMany"]["organizations"][number]
    | RouterOutputs["billing"]["getAccounts"]["accounts"][number];
  organizations?:
    | RouterOutputs["organizations"]["getMany"]["organizations"]
    | RouterOutputs["billing"]["getAccounts"]["accounts"];
  setOrganization?: (org: string) => void;
  setQuery?: (query: string) => void;
}

export function OrganizationBilling(props: OrganizationBillingProps) {
  const billing = api.billing.get.useQuery(
    {
      organizationId: props.organization?.id,
    },
    {
      enabled: !props.loading || !!props.organization?.id,
      initialData: props.billing ? use(props.billing) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || billing.isLoading;
  const error = billing.error;
  return (
    <section className="flex flex-1 flex-col gap-4">
      {error && <ErrorFallback error={error} />}

      <header className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card className="flex flex-col justify-between">
          <CardHeader>
            <CardTitle>{i18n.en.currentOrganization}</CardTitle>
            <CardDescription>
              {i18n.en.currentOrganizationDescription}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="flex flex-1 flex-row gap-2">
              <SelectOrganization<
                | RouterOutputs["organizations"]["getMany"]["organizations"][number]
                | RouterOutputs["billing"]["getAccounts"]["accounts"][number]
              >
                loading={loading}
                variant="ghost"
                align="end"
                className="p-2"
                data={props.organizations ?? []}
                value={props.organization?.id}
                onSelect={(org) => props.setOrganization?.(org.id)}
                onValueChange={(query) => props.setQuery?.(query)}
              >
                <PreviewOrganization
                  loading={loading}
                  organization={{
                    id: props.organization?.id ?? "",
                    name: props.organization?.name ?? "",
                    avatar: props.organization?.avatar,
                  }}
                />
                <div className="flex aspect-square size-8 items-center justify-center">
                  <ChevronDownIcon className="size-5" />
                </div>
              </SelectOrganization>
            </div>
          </CardContent>
        </Card>

        <Card shadow="lift-heavy" className="flex flex-col justify-between">
          <CardHeader>
            <div className="flex flex-row gap-2">
              <div className="flex-1">
                <CardTitle>{i18n.en.balance}</CardTitle>
                <CardDescription>{i18n.en.currentAmount}</CardDescription>
              </div>
              <UpdateBillingSettings
                threshold={billing.data?.threshold}
                organizationId={props.organization?.id ?? ""}
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold">
                <Currency amount={billing.data?.balance ?? 0} />
              </h2>
            </div>
          </CardContent>
        </Card>
      </header>
      {props.organization?.type === "ACCOUNT" && (
        <CardFooter className="flex flex-col items-start gap-1 rounded-lg border pt-4">
          <CardDescription>{i18n.en.managedAccountDescription}</CardDescription>
          <PreviewOrganization
            organization={{
              id: billing.data?.manager?.id ?? "",
              name: billing.data?.manager?.name ?? "",
              avatar: billing.data?.manager?.avatar ?? "",
            }}
          />
        </CardFooter>
      )}
    </section>
  );
}

export interface BillingViewProps {
  loading?: boolean;
  billing?: Promise<RouterOutputs["billing"]["get"]>;
  accounts?: Promise<RouterOutputs["billing"]["getAccounts"]>;
  invoices?: Promise<RouterOutputs["invoices"]["getMany"]>;
}

export function BillingView(props: BillingViewProps) {
  const [query, setQuery] = useState("");
  const [pagination] = useState({
    pageSize: 5,
    pageNumber: 0,
  });
  const { organization, loading: userLoading, isBilling } = useUser();

  const accounts = api.billing.getAccounts.useQuery(
    {
      query,
      pageSize: pagination.pageSize,
      pageNumber: pagination.pageNumber,
    },
    {
      initialData: props.accounts ? use(props.accounts) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || userLoading || accounts.isLoading;
  const error = accounts.error;

  const organizationId = isBilling
    ? (accounts.data?.accounts ?? [])[0]?.id
    : organization?.id;

  const [selection, setSelection] = useState(organizationId);
  const activeOrganizationId = selection ?? organizationId;

  const activeOrganization = useMemo(() => {
    return accounts.data?.accounts.find(
      (org) => org.id === activeOrganizationId,
    );
  }, [activeOrganizationId, accounts.data?.accounts]);

  return (
    <div className="w-full space-y-10">
      <Suspense fallback={<div>Loading...</div>}>
        <OrganizationBilling
          loading={loading}
          billing={props.billing}
          organization={activeOrganization}
          organizations={accounts.data?.accounts}
          setOrganization={setSelection}
          setQuery={setQuery}
        />
      </Suspense>

      {error && <ErrorFallback error={error} />}

      <section className="flex flex-1 flex-col gap-4 lg:gap-6">
        <header className="flex items-center">
          <h2 className="text-lg font-semibold md:text-xl">
            {i18n.en.statements}
          </h2>

          {isBilling && (
            <div className="flex w-full justify-end gap-4">
              <AddStatement organizationId={activeOrganization?.id}>
                <Button
                  variant="primary"
                  size="sm"
                  disabled={!activeOrganization?.id || loading}
                >
                  Add Statement
                </Button>
              </AddStatement>
            </div>
          )}
        </header>

        <Suspense fallback={<div>Loading...</div>}>
          <PreviewStatements
            loading={loading}
            organizationId={activeOrganization?.id ?? ""}
          />
        </Suspense>
      </section>

      <section className="flex flex-1 flex-col gap-4 lg:gap-6">
        <header className="flex items-center">
          <h2 className="text-lg font-semibold md:text-xl">
            {i18n.en.invoices}
          </h2>
        </header>

        <Suspense fallback={<div>Loading...</div>}>
          <PreviewInvoices
            billing
            loading={loading}
            invoices={props.invoices}
            organization={activeOrganization?.id ?? ""}
          />
        </Suspense>
      </section>
    </div>
  );
}

export default function BillingPage({ billing, invoices }: BillingViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div />
      <Suspense fallback={<BillingView loading />}>
        <SearchParams>
          <BillingView invoices={invoices} billing={billing} />
        </SearchParams>
      </Suspense>
    </AppView>
  );
}
