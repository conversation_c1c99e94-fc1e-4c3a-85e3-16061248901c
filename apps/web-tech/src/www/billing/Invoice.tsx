"use client";

import { Suspense, use, useCallback, useState } from "react";
import { format } from "date-fns";

import type { RouterOutputs } from "@axa/api-tech";
import type { InvoiceStatus } from "@axa/database-tech";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import AppView from "@axa/ui/layouts/AppView";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import { SearchParams } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListStatements from "@/components/ListStatements";
import { useUser } from "@/contexts/User";
import { InvoiceMenu } from "@/widgets/actions/billing/invoice";
import PreviewTimeSheets from "@/widgets/PreviewTimeSheets";

const i18n = {
  en: {
    title: "Invoice",
    timeSheets: "Time Sheets",
    timePeriod: "Time Period",
    afterExpenses: "after expenses",
    balance: "Total Balance",
    pending: "Pending Balance",
    due: "Due",
    fees: "Fees",
  },
  links: {
    invoices: "/app/finances/invoices",
    billing: "/app/billing",
  },
};

export function PreviewFees(props: { loading?: boolean; invoice?: string }) {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const fees = api.statements.getMany.useQuery(
    { invoiceId: props.invoice, type: "FEE" },
    {
      enabled: !!props.invoice && !props.loading,
    },
  );

  return (
    <ListStatements
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      loading={props.loading || fees.isLoading}
      statements={fees.data}
      pagination={pagination}
      setPagination={setPagination}
    />
  );
}

export function InvoiceActionBar(props: {
  loading?: boolean;
  status?: InvoiceStatus;
  invoiceId?: string;
}) {
  const { status = "DRAFT" } = props;
  const user = useUser();
  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading;

  const utils = api.useUtils();
  const markPaid = api.invoices.markPaid.useMutation({
    onSuccess: async () => {
      await utils.invoices.get.invalidate({ id: props.invoiceId });
      toast.success("Invoice finalized successfully");
    },
    onError: (error) => {
      toast.error(`Failed to finalize invoice: ${error.message}`);
    },
  });
  const finalize = useCallback(
    () =>
      markPaid.mutate({
        id: props.invoiceId ?? "",
      }),
    [props.invoiceId, markPaid],
  );

  const actions = [];
  if (status === "DRAFT") {
    actions.push(<Button variant="outline">Open</Button>);
  } else if (status === "DUE") {
    if (user.isBilling) {
      actions.push(
        <Button
          variant="outline"
          onClick={finalize}
          disabled={markPaid.isPending}
        >
          Mark Paid
        </Button>,
      );
    }
  }

  return (
    <div className="flex items-center justify-between gap-2">
      {actions.map((action, index) => (
        <div key={index}>{action}</div>
      ))}
      {loading ? <Skeleton className="h-5 w-24" /> : <Badge>{status}</Badge>}
    </div>
  );
}

export interface InvoiceViewProps {
  billing?: boolean;
  loading?: boolean;
  invoiceId: string;
  invoice?: Promise<RouterOutputs["invoices"]["get"]>;
}

export function InvoiceView(props: InvoiceViewProps) {
  const user = useUser();
  const invoice = api.invoices.get.useQuery(
    { id: props.invoiceId },
    {
      enabled: !props.loading && !user.loading,
      initialData: props.invoice ? use(props.invoice) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading || invoice.isLoading;

  const periodDate = loading
    ? null
    : new Date(
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        (invoice.data?.timePeriodStart!.getTime() ?? 0) +
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          (invoice.data?.timePeriodStart!.getTimezoneOffset() ?? 0) * 60000,
      );
  const dueDate = loading
    ? null
    : new Date(
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        (invoice.data?.dueDate!.getTime() ?? 0) +
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          (invoice.data?.dueDate!.getTimezoneOffset() ?? 0) * 60000,
      );

  return (
    <AppView
      title={
        loading ? (
          <Skeleton className="h-9 w-[200px]" />
        ) : (
          [i18n.en.title, ["#", invoice.data?.name ?? "000"].join("")]
            .join(" ")
            .trim()
        )
      }
      goBackUrl={props.billing ? i18n.links.billing : i18n.links.invoices}
    >
      <div className="flex justify-end">
        <InvoiceMenu invoice={invoice.data} rerouteOnDelete />
      </div>

      <div className="flex flex-col gap-4">
        {invoice.error && <ErrorFallback error={invoice.error} />}

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <Card className="lg:col-span-1" shadow="lift-heavy">
            <CardHeader>
              <CardDescription>{i18n.en.balance}</CardDescription>
              {loading ? (
                <Skeleton className="h-9 w-40" />
              ) : (
                <CardTitle className="text-3xl">
                  <Currency amount={invoice.data?.balance ?? 0} />
                </CardTitle>
              )}
              <CardDescription>{i18n.en.afterExpenses}</CardDescription>
            </CardHeader>
          </Card>
          <Card className="lg:col-span-1" shadow="lift-heavy">
            <CardHeader>
              <CardDescription>{i18n.en.pending}</CardDescription>
              {loading ? (
                <Skeleton className="h-9 w-40" />
              ) : (
                <CardTitle className="text-3xl">
                  <Currency amount={invoice.data?.pending ?? 0} />
                </CardTitle>
              )}
              <CardDescription>{i18n.en.afterExpenses}</CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <CardDescription>{i18n.en.timePeriod}</CardDescription>
              {loading ? (
                <Skeleton className="h-9 w-32" />
              ) : (
                <CardTitle className="text-3xl">
                  {format(periodDate ?? 0, "LLLL yyyy")}
                </CardTitle>
              )}
              {loading ? (
                <Skeleton className="h-4 w-28" />
              ) : (
                <CardDescription className="flex items-center space-x-1">
                  <span>{i18n.en.due}</span>
                  <span>{format(dueDate ?? 0, "PPP")}</span>
                </CardDescription>
              )}
            </CardHeader>
          </Card>
        </div>

        <section className="flex flex-row items-center gap-2 rounded-lg border">
          <header className="flex flex-1 items-center gap-2 p-4">
            <div>
              <PreviewOrganization
                loading={loading}
                organization={invoice.data?.organization}
              />
            </div>
          </header>

          <footer className="border-s p-4">
            <InvoiceActionBar
              loading={loading}
              invoiceId={invoice.data?.id}
              status={invoice.data?.status}
            />
          </footer>
        </section>

        <section className="mt-6 flex flex-col gap-4">
          <header className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">{i18n.en.timeSheets}</h2>
          </header>
          <PreviewTimeSheets
            billing={props.billing}
            loading={loading}
            invoice={invoice.data?.id}
          />
        </section>
        <section className="mt-6 flex flex-col gap-4">
          <header className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">{i18n.en.fees}</h2>
          </header>
          <PreviewFees loading={loading} invoice={invoice.data?.id} />
        </section>
      </div>
    </AppView>
  );
}

export default function InvoicePage(props: InvoiceViewProps) {
  return (
    <Suspense fallback={<InvoiceView loading invoiceId={props.invoiceId} />}>
      <SearchParams>
        <InvoiceView invoiceId={props.invoiceId} invoice={props.invoice} />
      </SearchParams>
    </Suspense>
  );
}
