"use client";

import { Suspense } from "react";

import AppView from "@axa/ui/layouts/AppView";
import { SearchParams } from "@axa/ui/search";

import type { PreviewTimeSheetsProps } from "@/widgets/PreviewTimeSheets";

import { useUser } from "@/contexts/User";
import { AddTimeSheet } from "@/widgets/actions/billing/time-sheet";
import PreviewTimeSheets from "@/widgets/PreviewTimeSheets";

const i18n = {
  en: {
    title: "Time Sheets",
    description:
      "TimeSheets are the records for work order technicians. They contain the billing information and hours worked per work order.",
  },
};

export default function TimeSheetsPage({ timeSheets }: PreviewTimeSheetsProps) {
  const user = useUser();
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        {user.isBilling && <AddTimeSheet size="sm" />}
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <Suspense fallback={<PreviewTimeSheets loading />}>
          <SearchParams>
            <PreviewTimeSheets timeSheets={timeSheets} />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
