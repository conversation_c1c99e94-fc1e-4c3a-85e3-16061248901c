"use client";

import { Suspense, use, useCallback } from "react";
import { useClerk } from "@clerk/nextjs";

import type { RouterOutputs } from "@axa/api-tech";
import AppView from "@axa/ui/layouts/AppView";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { Switch } from "@axa/ui/primitives/switch";
import { toast } from "@axa/ui/primitives/toast";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import { useUser } from "@/contexts/User";
import { OrganizationMenu } from "@/widgets/actions/organizations/organization";
import { EmailTemplates } from "@/www/organizations/settings/EmailTemplates";

export function OrganizationLogo(props: { organizationId: string }) {
  const clerk = useClerk();

  if (clerk.organization?.id === props.organizationId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Organization Logo</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Upload a logo for your organization.</p>
          <Button>Upload Logo</Button>
        </CardContent>
      </Card>
    );
  }

  return null;
}

function OrganizationSettings(props: {
  organizationId: string;
  organization?: Promise<RouterOutputs["organizations"]["get"]>;
}) {
  const organization = api.organizations.get.useQuery(
    { id: props.organizationId },
    {
      initialData: props.organization ? use(props.organization) : undefined,
    },
  );

  const setOrgSettingsMutation = api.organizations.updateSettings.useMutation({
    onSuccess: async () => {
      await organization.refetch();
      toast.success("Organization settings updated successfully.");
    },
    onError: (error) => {
      toast.error("Failed to update organization settings. " + error.message);
    },
  });

  const setSettings = useCallback(
    async (
      settings: {
        reports?: {
          daily?: boolean;
          weekly?: boolean;
        };
      } = {},
    ) => {
      const currentSettings = organization.data?.organization.settings ?? {};
      await setOrgSettingsMutation.mutateAsync({
        organizationId: props.organizationId,
        settings: {
          reports: {
            daily: settings.reports?.daily ?? currentSettings.reports?.daily,
            weekly: settings.reports?.weekly ?? currentSettings.reports?.weekly,
          },
        },
      });
    },
    [organization, props.organizationId, setOrgSettingsMutation],
  );

  return (
    <Card shadow="lift">
      <CardHeader className="flex w-full flex-row items-center justify-between">
        <CardTitle className="grow">Settings</CardTitle>
      </CardHeader>
      <CardContent>
        {organization.isLoading ? (
          <Skeleton className="h-32 w-full" />
        ) : (
          <div className="flex flex-col gap-6">
            <div className="flex items-start justify-between gap-2">
              <p className="text-muted-foreground">Generate daily reports</p>
              <Switch
                disabled={setOrgSettingsMutation.isPending}
                checked={
                  organization.data?.organization.settings.reports?.daily
                }
                onCheckedChange={async (checked) => {
                  await setSettings({ reports: { daily: checked } });
                }}
              />
            </div>
            <div className="flex flex-row items-start justify-between gap-2">
              <p className="text-muted-foreground">Generate weekly reports</p>
              <Switch
                disabled={setOrgSettingsMutation.isPending}
                checked={
                  organization.data?.organization.settings.reports?.weekly
                }
                onCheckedChange={async (checked) => {
                  await setSettings({ reports: { weekly: checked } });
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export interface OrganizationViewProps {
  organizationId: string;
  organization?: Promise<RouterOutputs["organizations"]["get"]>;
  members?: Promise<RouterOutputs["members"]["getAll"]>;
  invites?: Promise<RouterOutputs["invitations"]["getAll"]>;
}

export function OrganizationView(props: OrganizationViewProps) {
  const user = useUser();
  const organization = api.organizations.get.useQuery(
    { id: props.organizationId },
    {
      initialData: props.organization ? use(props.organization) : undefined,
    },
  );

  if (organization.error) {
    return <ErrorFallback error={organization.error} />;
  }

  return (
    <AppView
      title={
        organization.data?.organization.name ? (
          `${organization.data.organization.name} settings`
        ) : (
          <Skeleton className="h-8 w-32" />
        )
      }
      goBackUrl="/app/organizations"
    >
      <div className="flex items-center justify-end">
        <OrganizationMenu
          organization={organization.data?.organization}
          rerouteOnDelete
        />
      </div>
      <div className="flex w-full flex-col gap-12">
        <section>
          <OrganizationSettings
            organizationId={props.organizationId}
            organization={props.organization}
          />
        </section>

        {user.isInternal && (
          <EmailTemplates organizationId={props.organizationId} />
        )}
      </div>
    </AppView>
  );
}

export default function OrganizationPage(props: OrganizationViewProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OrganizationView
        organizationId={props.organizationId}
        organization={props.organization}
      />
    </Suspense>
  );
}
