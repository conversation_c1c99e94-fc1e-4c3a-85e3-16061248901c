"use client";

import { useCallback } from "react";
import Image from "next/image";

import type {
  TemplateContent,
  TemplateWithMetadata,
} from "@axa/ui/editors/EmailTemplate";
import { EmailTemplate } from "@axa/ui/editors/EmailTemplate";
import { But<PERSON> } from "@axa/ui/primitives/button";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import { toast } from "@axa/ui/primitives/toast";
import EmptyList from "@axa/ui/shared/EmptyList";

import { api } from "@/api/client";

export const initialContent = `
<p>Thank you for accepting this work order. Attached to this email is your time sheet if you would kindly fill it out and attach it to your work order at the end of the service.</p>

<p>Upon arrival at the client location, please contact your work order manager and let them know you have arrived onsite.</p>

<ul>
  <li>Valid drivers license or government issued ID - electronic IDs are <strong>NOT ACCEPTED</strong> onsite</li>
</ul>
`.trim();
export function EmailTemplates(props: { organizationId: string }) {
  const templates = api.templates.emails.getMany.useQuery({
    organizationId: props.organizationId,
  });

  const createEmailTemplate = api.templates.emails.create.useMutation({
    onSuccess: async () => {
      await templates.refetch();
      toast.success("Email template created successfully.");
    },
    onError: (error) => {
      toast.error("Failed to create email template. " + error.message);
    },
  });

  const updateEmailTemplate = api.templates.emails.update.useMutation({
    onSuccess: async () => {
      await templates.refetch();
      toast.success("Email template updated successfully.");
    },
    onError: (error) => {
      toast.error("Failed to update email template. " + error.message);
    },
  });

  const deleteEmailTemplate = api.templates.emails.delete.useMutation({
    onSuccess: async () => {
      await templates.refetch();
      toast.success("Email template deleted successfully.");
    },
    onError: (error) => {
      toast.error("Failed to delete email template. " + error.message);
    },
  });

  const createEmail = useCallback(
    () =>
      createEmailTemplate.mutate({
        name: "Dispatch Email Template",
        description: "Email to be sent out to technicians.",
        body: initialContent,
        organizationId: props.organizationId,
      }),
    [createEmailTemplate, props.organizationId],
  );

  // Handle template save (create new template)
  const handleTemplateSave = useCallback(
    async (template: TemplateWithMetadata) => {
      await updateEmailTemplate.mutateAsync({
        id: template.metadata.id!,
        name: template.title,
        description: template.description,
        body: template.content,
        organizationId: props.organizationId,
      });
    },
    [props.organizationId, updateEmailTemplate],
  );

  // Handle template update
  const handleTemplateUpdate = useCallback(
    async (template: TemplateWithMetadata) => {
      await updateEmailTemplate.mutateAsync({
        id: template.metadata.id!,
        name: template.title,
        description: template.description,
        body: template.content,
      });
    },
    [updateEmailTemplate],
  );

  // Handle template delete
  const handleTemplateDelete = useCallback(
    async (templateIdParam: string) => {
      await deleteEmailTemplate.mutateAsync({ id: templateIdParam });
    },
    [deleteEmailTemplate],
  );

  // Handle template data changes from EmailTemplate (for auto-save)
  const handleTemplateDataChange = useCallback(
    (content: TemplateContent) => {
      // TODO: Implement auto-save functionality
      // currently, fires on every change, which is not what we want
      // updateEmailTemplate.mutate({
      //   id: templateId,
      //   name: content.title,
      //   description: content.description,
      //   body: content.content,
      // });
    },
    [updateEmailTemplate],
  );

  const emails = templates.data?.emails ?? [];

  return (
    <section className="space-y-6">
      <header>
        <h2 className="text-xl font-bold">Email Templates</h2>
        <p className="mt-1 text-sm text-muted-foreground">
          Create and manage email templates for dispatching work orders to
          technicians. Use the update button to edit template metadata, or edit
          content directly in the editor.
        </p>
      </header>

      {templates.isLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-32 w-full" />
          <div className="container">
            <Skeleton className="h-80 w-full" />
          </div>
        </div>
      ) : emails.length > 0 ? (
        emails.map((template) => (
          <div key={template.id} className="space-y-4">
            <EmailTemplate
              className="h-[1200px] w-auto"
              editorHeight="min-h-[200px]"
              templateMetadata={{
                id: template.id,
                title: template.name,
                description: template.description ?? undefined,
                lastModified: template.updatedAt
                  ? new Date(template.updatedAt)
                  : undefined,
                createdAt: template.createdAt
                  ? new Date(template.createdAt)
                  : undefined,
                category: "Technician",
                usageCount: 89,
                tags: ["dispatch", "work order", "technician"],
                isPublic: true,
                author: "Template Team",
              }}
              templateContent={{
                title: template.name,
                description: template.description ?? undefined,
                content: template.body ?? initialContent,
              }}
              actions={{
                onSave: handleTemplateSave,
                onUpdate: handleTemplateUpdate,
                onDelete: handleTemplateDelete,
              }}
              features={{
                showUpdateButton: true,
                showDeleteButton: true,
                showDuplicateButton: false, // Can be enabled if you add duplicate functionality
                showUsageStats: false, // Enable if you have usage tracking
                showTags: true, // Enable if you add tag support
              }}
              onTemplateChange={handleTemplateDataChange}
              headerContent={
                <div className="space-y-4">
                  {/* Email Brand Header */}
                  <div className="text-center">
                    <Image
                      src="https://tech.axapro.com/images/brand/logo.png"
                      width="162"
                      height="97"
                      alt="AXA Professionals"
                      className="mx-auto"
                    />
                  </div>

                  {/* Work Order Heading */}
                  <h1 className="mb-6 text-center text-2xl font-normal text-foreground">
                    Work Order for{" "}
                    <span className="inline-flex items-center rounded-lg bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                      organization.name
                    </span>
                  </h1>

                  {/* Greeting */}
                  <p className="text-sm text-foreground">
                    Hi{" "}
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                      firstName
                    </span>{" "}
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                      lastName
                    </span>
                    ,
                  </p>
                </div>
              }
              footerContent={
                <div className="space-y-6">
                  {/* Work Order Details */}
                  <div className="space-y-4">
                    <div>
                      <h2 className="mb-2 text-base font-medium text-foreground">
                        Date
                      </h2>
                      <p className="text-sm text-foreground">
                        <span className="inline-flex items-center rounded-lg bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          startTime
                        </span>
                      </p>
                    </div>

                    <div>
                      <h2 className="mb-2 text-base font-medium text-foreground">
                        Location
                      </h2>
                      <p className="text-sm text-foreground">
                        <a
                          href="#"
                          className="text-teal-600 hover:underline dark:text-teal-400"
                        >
                          <span className="inline-flex items-center rounded-lg bg-orange-100 px-3 py-1 text-sm font-medium text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                            address
                          </span>
                        </a>
                      </p>
                    </div>

                    <div>
                      <h2 className="mb-2 text-base font-medium text-foreground">
                        Contacts
                      </h2>
                      <p className="text-sm text-foreground">
                        <span className="inline-flex items-center rounded-full bg-pink-100 px-2 py-1 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-200">
                          contact.role
                        </span>
                        :{" "}
                        <span className="inline-flex items-center rounded-full bg-pink-100 px-2 py-1 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-200">
                          contact.name
                        </span>{" "}
                        <a
                          href="#"
                          className="text-teal-600 hover:underline dark:text-teal-400"
                        >
                          <span className="inline-flex items-center rounded-full bg-pink-100 px-2 py-1 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-200">
                            contact.phone
                          </span>
                        </a>
                      </p>
                    </div>

                    <div>
                      <h2 className="mb-2 text-base font-medium text-foreground">
                        Scope of Work
                      </h2>
                      <p className="text-sm font-bold text-foreground">
                        <span className="inline-flex items-center rounded-lg bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                          summary
                        </span>
                      </p>
                      <p className="mt-1 text-sm text-foreground">
                        <span className="inline-flex items-center rounded-lg bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
                          scope
                        </span>
                      </p>
                    </div>
                  </div>

                  {/* Code of Conduct */}
                  <div className="rounded border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950">
                    <h3 className="mb-2 font-medium text-yellow-800 dark:text-yellow-200">
                      Code of Conduct
                    </h3>
                    <div className="space-y-2 text-sm text-yellow-700 dark:text-yellow-300">
                      <p>
                        By accepting this work order you agree to the terms
                        below. Failure to comply will result in immediate
                        termination of your contract.
                      </p>
                      <p>
                        <strong>NO</strong> weapons of any kind are permitted
                        onsite, even concealed.
                      </p>
                      <p>
                        <strong>DO NOT</strong> under any circumstances take any
                        pictures onsite - even if you are asked to, we are not
                        permitted to take any pictures on the premise.
                      </p>
                      <p>
                        <strong>DO NOT</strong> bring or substitute any other
                        person onsite without prior written approval or they
                        will be <strong>considered trespassing</strong>.
                      </p>
                      <p>
                        Please be <strong>professional at all times</strong>, as
                        client locations are under constant video surveillance.
                      </p>
                    </div>
                  </div>

                  {/* Closing */}
                  <p className="text-sm text-foreground">
                    Thank you,
                    <br />
                    AXA Professionals
                  </p>

                  <hr className="border-border" />

                  {/* Email Footer */}
                  <div className="text-xs text-muted-foreground">
                    <p>
                      This email was intended for{" "}
                      <strong>
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                          firstName
                        </span>{" "}
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
                          lastName
                        </span>
                      </strong>
                      . If you were not expecting this email, you can ignore it.
                      If you have any questions or concerns, please reach out to{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-teal-600 hover:underline dark:text-teal-400"
                      >
                        <EMAIL>
                      </a>
                      .
                    </p>
                  </div>
                </div>
              }
              showTemplateManagement={true}
            />
          </div>
        ))
      ) : (
        <EmptyList
          title="No email templates"
          description="Create email templates to send to technicians when dispatching work orders."
        >
          <Button
            className="w-1/2"
            size="lg"
            disabled={createEmailTemplate.isPending}
            onClick={createEmail}
          >
            Add Email
          </Button>
        </EmptyList>
      )}
    </section>
  );
}
