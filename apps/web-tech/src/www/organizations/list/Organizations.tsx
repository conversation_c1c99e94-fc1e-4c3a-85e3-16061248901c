"use client";

import { Suspense, use, useMemo, useState } from "react";
import { ListIcon, TableIcon } from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import AppView from "@axa/ui/layouts/AppView";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@axa/ui/primitives/tabs";
import { SearchParams, useSearchTextValue } from "@axa/ui/search";
import EmptyList from "@axa/ui/shared/EmptyList";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListOrganizations, {
  OrganizationTypeBadge,
} from "@/components/ListOrganizations";
import { useUser } from "@/contexts/User";
import {
  AddOrganization,
  OrganizationMenu,
} from "@/widgets/actions/organizations/organization";
import MemberAvatarList from "@/www/organizations/list/MemberAvatarList";

const i18n = {
  en: {
    title: "Organizations",
    description:
      "Organizations are the companies and accounts that are attributed to work orders, invoices, and other resources like locations. They are the central entities in the system that tie together all resources.",
    noOrganizations: "There are no organizations",
    accounts: "Accounts",
    actions: {
      add: "Add Organization",
    },
  },
  links: {
    organization: "/app/organizations/[id]",
  },
};

export function OrganizationCard({
  loading = false,
  organization,
}: {
  loading?: boolean;
  organization?: RouterOutputs["organizations"]["getMany"]["organizations"][number];
}) {
  return (
    <section
      key={organization?.id}
      className="flex flex-col items-center gap-4 rounded-md p-4 shadow-md dark:ring-2 dark:ring-muted"
    >
      <header
        key={organization?.id}
        className="flex w-full items-center gap-4 p-2"
      >
        <PreviewOrganization
          loading={loading}
          organization={organization}
          link
        />

        <div className="ml-auto flex gap-2">
          <OrganizationTypeBadge
            loading={loading}
            type={organization?.type ?? "CLIENT"}
          />
          <div className="flex items-center gap-2">
            {loading ? (
              <Skeleton className="aspect-square size-10" />
            ) : (
              <OrganizationMenu organization={organization} />
            )}
          </div>
        </div>
      </header>

      <footer className="flex w-full flex-row gap-2 p-2">
        <MemberAvatarList
          loading={loading}
          compress
          members={useMemo(() => {
            return organization?.members?.map((member) => ({
              id: member.id,
              name: [member.user.firstName ?? "", member.user.lastName ?? ""]
                .join(" ")
                .trim(),
              initials: [
                (member.user.firstName ?? "")[0],
                (member.user.lastName ?? "")[0],
              ]
                .join("")
                .trim(),
              avatar: member.user.avatar ?? "",
            }));
          }, [organization?.members])}
        />
      </footer>
    </section>
  );
}

export function OrganizationsView(props: {
  loading?: boolean;
  organizationId?: string;
  organizations?: Promise<RouterOutputs["organizations"]["getMany"]>;
}) {
  const query = useSearchTextValue();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const { error, loading: userLoading, organization, isClient } = useUser();
  const organizations = api.organizations.getMany.useQuery(
    {
      query,
      organizationId: props.organizationId,
      pageSize: pagination.pageSize,
      pageNumber: pagination.pageIndex,
      include: {
        manager: true,
        parent: true,
        members: true,
      },
    },
    {
      initialData: props.organizations ? use(props.organizations) : undefined,
      enabled: !props.loading,
    },
  );

  const loading = props.loading || userLoading || organizations.isLoading;

  const orgs = (organizations.data?.organizations ?? []).filter(
    (org) => org.id !== organization?.id,
  );

  const userOrg = organizations.data?.organizations.find(
    (org) => org.id === organization?.id,
  );

  return (
    <Tabs defaultValue="table-view">
      <TabsList className="my-4 ms-auto flex w-fit gap-2">
        <TabsTrigger value="table-view">
          <TableIcon className="size-5" aria-label="Table View" />
        </TabsTrigger>
        <TabsTrigger value="card-view">
          <ListIcon className="size-5" aria-label="Card View" />
        </TabsTrigger>
      </TabsList>
      {error || organizations.error ? (
        <ErrorFallback error={error || organizations.error} />
      ) : (
        <>
          <TabsContent value="card-view">
            {loading ? (
              <div className="grid space-y-6">
                <OrganizationCard loading />

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {new Array(4).fill(null).map((_, index) => (
                    <OrganizationCard key={index} loading />
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col gap-6">
                {isClient ? (
                  <section>
                    <OrganizationCard
                      organization={{
                        id: userOrg?.id ?? "",
                        type: userOrg?.type ?? "CLIENT",
                        name: userOrg?.name ?? "",
                        avatar: userOrg?.avatar ?? "",
                        members: userOrg?.members ?? [],
                        managerId: userOrg?.managerId ?? "",
                        parent: userOrg?.parent ?? null,
                        fieldNationId: userOrg?.fieldNationId ?? null,
                        manager: userOrg?.manager ?? null,
                      }}
                    />
                  </section>
                ) : null}

                <section>
                  <header>
                    <h2 className="sr-only text-2xl font-bold">
                      {i18n.en.accounts}
                    </h2>
                  </header>
                  {orgs.length > 0 ? (
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      {orgs.map((organization) => (
                        <OrganizationCard
                          key={organization.id}
                          organization={organization}
                        />
                      ))}
                    </div>
                  ) : (
                    <EmptyList title={i18n.en.noOrganizations}>
                      <AddOrganization
                        title={i18n.en.actions.add}
                        label={i18n.en.actions.add}
                      />
                    </EmptyList>
                  )}
                </section>
              </div>
            )}
          </TabsContent>
          <TabsContent value="table-view">
            <ListOrganizations
              loading={loading}
              organizations={organizations.data}
              pagination={pagination}
              setPagination={setPagination}
            />
          </TabsContent>
        </>
      )}
    </Tabs>
  );
}

export default function OrganizationsPage({
  organizationId,
  organizations,
}: {
  organizationId?: string;
  organizations?: Promise<RouterOutputs["organizations"]["getMany"]>;
}) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2">
        <AddOrganization
          size="sm"
          title={i18n.en.actions.add}
          label={i18n.en.actions.add}
        />
      </div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>
        <Suspense fallback={<OrganizationsView loading />}>
          <SearchParams>
            <OrganizationsView
              organizations={organizations}
              organizationId={organizationId}
            />
          </SearchParams>
        </Suspense>
      </div>
    </AppView>
  );
}
