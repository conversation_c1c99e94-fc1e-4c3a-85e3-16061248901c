"use server";

import { auth, clerkClient } from "@clerk/nextjs/server";
import { captureException } from "@sentry/nextjs";

import type { PersonRole } from "@axa/database-tech";
import { resend } from "@axa/lib/resend";

export async function completeOnboarding() {
  const clerk = await clerkClient();
  const { userId } = await auth();

  if (!userId) {
    return { message: "No Logged In User" };
  }

  try {
    const user = await clerk.users.getUser(userId);
    // users invited via a membership do not have a publicMetadata
    // so we need to check the memberships to determine if they are onboarded
    const memberships = await clerk.users.getOrganizationMembershipList({
      userId,
    });
    const email = user.primaryEmailAddress?.emailAddress ?? "";

    let onboarded = false;
    let currentRole = user.publicMetadata.role as PersonRole | null;
    let currentOrg = user.publicMetadata.organizationId as string | null;

    if (memberships.data.length > 0) {
      currentOrg =
        memberships.data.find(
          ({ organization }) =>
            !!organization.publicMetadata?.parentId &&
            !!organization.publicMetadata.managerId,
        )?.organization.id ??
        memberships.data[0]?.organization.id ??
        null;
    }

    if (
      email.endsWith("@axapro.com") ||
      ["ADMIN", "BILLING", "INTERNAL"].includes(currentRole! ?? "")
    ) {
      // if no role is set, set it to internal, otherwise keep it in case it is BILLING or ADMIN
      currentRole ??= "INTERNAL";
      currentOrg = null;
      onboarded = true;
    } else if (currentRole) {
      if (currentOrg) {
        onboarded = true;
        currentRole = "CLIENT";
      }
    } else if (currentOrg) {
      onboarded = true;
      currentRole = "CLIENT";
    } else {
      currentRole = "NONE";
    }

    await clerk.users.updateUserMetadata(userId, {
      publicMetadata: {
        acceptedTerms: onboarded ? new Date().toISOString() : null,
        onboarded,
        role: currentRole,
        organizationId: currentOrg,
      },
    });

    return { message: "Onboarding sucessful.", onboarded };
  } catch (err) {
    captureException(err, {
      tags: {
        action: "completeOnboarding",
      },
    });
    return { error: "There was an error updating the user metadata." };
  }
}

export async function prospectContact(data: {
  name: string;
  email: string;
  phone: string;
  companyName: string;
  message?: string;
}) {
  try {
    const result = await resend.emails.send({
      from: "AXA Tech <<EMAIL>>",
      to: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ],
      subject: "[AXA Tech] New prospect client has made contact",
      text: [
        "New Prospect Contact:",
        "",
        `Name: ${data.name}`,
        `Email: ${data.email}`,
        `Phone: ${data.phone}`,
        `Company: ${data.companyName}`,
        "",
        `Message: ${data.message ?? "No message provided."}`,
      ].join("\n"),
    });

    if (result.error) {
      throw new Error(result.error.message);
    }

    return { message: "Thank you for reaching out! We will be in touch." };
  } catch (err) {
    captureException(err);
    return { error: "There was a problem sending over the info." };
  }
}

export async function subscribeToNewsletter(data: Record<string, string>) {
  try {
    const result = await resend.emails.send({
      from: "AXA Tech <<EMAIL>>",
      to: "<EMAIL>",
      subject: "[AXA Tech] New subscriber",
      text: `${data.email}`,
    });

    if (result.error) {
      throw new Error(result.error.message);
    }

    return { message: "Thank you for subscribing!" };
  } catch (err) {
    captureException(err);
    return { error: "There was a problem subscribing." };
  }
}
