import { useCallback } from "react";

import type { MessageFormValues, MessageType } from "./types";

import { api } from "@/api/client";
import { useUser } from "@/contexts/User";
import { toast } from "@/ui/primitives/toast";

import { Thread } from "./thread";

const i18n = {
  en: {
    success: "Message created successfully",
    error: "Failed to create message",
  },
};

export default function Messages(props: {
  loading?: boolean;
  orderId?: string;
}) {
  const user = useUser();
  // TODO: add pagination using infinite scroll
  const notes = api.orders.notes.get.useQuery(
    { orderId: props.orderId ?? "" },
    {
      enabled: !props.loading || !!props.orderId,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading || notes.isLoading;

  const createNewMessageMutation = api.orders.notes.create.useMutation({
    onSuccess: async () => {
      await notes.refetch();
      toast.success(i18n.en.success);
    },
    onError: (error) => {
      toast.error(i18n.en.error + ": " + error.message);
    },
  });

  const updateMessageMutation = api.orders.notes.update.useMutation({
    onSuccess: async () => {
      await notes.refetch();
      toast.success(i18n.en.success);
    },
    onError: (error) => {
      toast.error(i18n.en.error + ": " + error.message);
    },
  });

  const deleteMessageMutation = api.orders.notes.delete.useMutation({
    onSuccess: async () => {
      await notes.refetch();
      toast.success(i18n.en.success);
    },
    onError: (error) => {
      toast.error(i18n.en.error + ": " + error.message);
    },
  });

  return (
    <Thread
      loading={loading}
      messages={notes.data?.notes}
      onCreate={useCallback(
        (values: MessageFormValues) => {
          createNewMessageMutation.mutate({
            ...values,
            orderId: props.orderId ?? "",
          });
        },
        [createNewMessageMutation, props.orderId],
      )}
      onEdit={useCallback(
        (values: MessageFormValues) => {
          updateMessageMutation.mutate({
            noteId: values.messageId ?? "",
            message: values.message,
          });
        },
        [updateMessageMutation],
      )}
      onDelete={useCallback(
        (message: Pick<MessageType, "id">) => {
          deleteMessageMutation.mutate({
            noteId: message.id,
          });
        },
        [deleteMessageMutation],
      )}
    />
  );
}
