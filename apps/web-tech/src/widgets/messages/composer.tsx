import { useCallback, useTransition } from "react";
import { SendIcon } from "lucide-react";

import type { MessageFormValues } from "./types";

import OrderMessageForm, {
  OrderMessageFormSubmitButton,
  OrderMessageTypeField,
} from "@/components/forms/OrderMessage";
import { useUser } from "@/contexts/User";
import { cn } from "@/ui/lib";

const i18n = {
  en: {
    actions: {
      send: "Send",
    },
  },
};

export function Composer({
  loading = false,
  onSubmit,
}: {
  onSubmit?: (values: MessageFormValues) => void | Promise<void>;
  loading: boolean;
}) {
  const [isPending, startTransition] = useTransition();
  const user = useUser();

  return (
    <OrderMessageForm
      className="h-[224px] w-full"
      resetOptions={{
        keepErrors: false,
        keepDirty: false,
        keepIsSubmitted: false,
      }}
      defaultValues={{
        type: user.isInternal ? "INTERNAL" : "PUBLIC",
      }}
      onSubmit={useCallback(
        (values: MessageFormValues) => {
          startTransition(async () => {
            await onSubmit?.(values);
          });
        },
        [onSubmit],
      )}
    >
      <div
        className={cn("grid grid-cols-1 gap-4", {
          "grid-cols-2": user.isInternal,
        })}
      >
        {user.isInternal && <OrderMessageTypeField disabled={loading} />}
        <div className="flex w-full items-end justify-end">
          <OrderMessageFormSubmitButton
            disabled={loading || isPending}
            className="space-x-1"
          >
            <SendIcon size={16} color="currentColor" />
            <span>{i18n.en.actions.send}</span>
          </OrderMessageFormSubmitButton>
        </div>
      </div>
    </OrderMessageForm>
  );
}
