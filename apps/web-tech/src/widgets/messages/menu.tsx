import { useCallback, useState } from "react";
import { More<PERSON><PERSON><PERSON>, Pencil, Trash } from "lucide-react";

import { But<PERSON> } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { MessageFormValues, MessageType } from "./types";

import OrderMessageForm from "@/components/forms/OrderMessage";

const i18n = {
  en: {
    menu: {
      label: "Message Actions",
      trigger: "Open message actions menu",
    },
    edit: {
      title: "Edit message",
      description: "Edit the message",
      action: "Edit message",
    },
    delete: {
      title: "Delete message",
      description: "Delete the message",
      action: "Delete message",
    },
  },
};

export function MessageMenu({
  message,
  onEdit,
  onDelete,
}: {
  message: MessageType;
  onEdit?: (values: MessageFormValues) => void | Promise<void>;
  onDelete?: (message: Pick<MessageType, "id">) => void | Promise<void>;
}) {
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="size-8"
            aria-label={i18n.en.menu.trigger}
          >
            <MoreVertical className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>{i18n.en.menu.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsEditOpen(true)}
            className="gap-2"
          >
            <Pencil className="size-4" />
            <span>{i18n.en.edit.action}</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setIsDeleteOpen(true)}
            className="gap-2 text-destructive focus:text-destructive"
          >
            <Trash className="size-4" />
            <span>{i18n.en.delete.action}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <DialogForm
        useTrigger={false}
        open={isEditOpen}
        onOpenChange={setIsEditOpen}
        title={i18n.en.edit.title}
        description={i18n.en.edit.description}
        Component={OrderMessageForm}
        defaultValues={{
          ...message,
          messageId: message.id,
        }}
        onSubmit={useCallback(
          async (values: MessageFormValues) => {
            await onEdit?.(values);
          },
          [onEdit],
        )}
      />
      <DialogConfirmation
        useTrigger={false}
        open={isDeleteOpen}
        onOpenChange={setIsDeleteOpen}
        title={i18n.en.delete.title}
        description={i18n.en.delete.description}
        onClick={useCallback(async () => {
          await onDelete?.(message);
        }, [onDelete, message])}
      />
    </>
  );
}
