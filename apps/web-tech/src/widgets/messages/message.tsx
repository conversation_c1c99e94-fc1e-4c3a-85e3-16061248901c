"use client";

import Image from "next/image";
import { format, isToday, isYesterday } from "date-fns";

import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import type { MessageFormValues, MessageType } from "./types";

import { MessageMenu } from "./menu";

const i18n = {
  en: {
    title: "Notes",
    edited: "edited ",
    noNotes: "No notes",
    today: "Today",
    yesterday: "Yesterday",
    actions: {
      send: "Send",
    },
    types: {
      INTERNAL: "Internal",
      PUBLIC: "General",
    },
  },
};

function formatMessageDate(date: Date) {
  const time = format(date, "h:mm a");

  if (isToday(date)) {
    return `${i18n.en.today} at ${time}`;
  }

  if (isYesterday(date)) {
    return `${i18n.en.yesterday} at ${time}`;
  }

  return `${format(date, "MMM d, yyyy")} at ${time}`;
}

export function Message({
  loading = false,
  message,
  internalUser = false,
  onEdit,
  onDelete,
}: {
  loading?: boolean;
  message?: MessageType;
  internalUser?: boolean;
  onEdit?: (message: MessageFormValues) => void | Promise<void>;
  onDelete?: (message: Pick<MessageType, "id">) => void;
}) {
  return (
    <div className="flex flex-row items-start justify-between gap-3">
      {loading ? (
        <Skeleton className="size-8 rounded-lg" />
      ) : (
        <Avatar className="size-8 rounded-lg">
          <AvatarImage
            asChild
            src={message?.user.avatar ?? ""}
            alt={`${message?.user.firstName} ${message?.user.lastName}`}
          >
            <Image
              src={message?.user.avatar ?? ""}
              alt={`${message?.user.firstName} ${message?.user.lastName}`}
              width={32}
              height={32}
            />
          </AvatarImage>
          <AvatarFallback>
            {message?.user.firstName[0]}
            {message?.user.lastName[0]}
          </AvatarFallback>
        </Avatar>
      )}

      <div className="flex flex-1 flex-col gap-1">
        <div className="flex h-8 w-full items-center gap-2">
          {loading ? (
            <>
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
            </>
          ) : (
            <>
              <p className="text-sm font-medium leading-none">
                {message?.user.firstName} {message?.user.lastName}
              </p>

              {message?.createdAt && (
                <time
                  dateTime={message.createdAt.toISOString()}
                  className="text-sm leading-none text-muted-foreground"
                >
                  {formatMessageDate(message.createdAt)}
                </time>
              )}
              {internalUser && (
                <div className="ms-auto">
                  <Badge variant="outline">
                    {i18n.en.types[message?.type!]}
                  </Badge>
                </div>
              )}
            </>
          )}
        </div>

        <div className="w-full text-sm">
          {loading ? (
            <div className="space-y-0.5">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          ) : (
            message?.message
          )}
        </div>

        {message?.updatedAt && (
          <time
            dateTime={message.updatedAt.toISOString()}
            className="text-sm leading-none text-muted-foreground"
          >
            {i18n.en.edited + formatMessageDate(message.updatedAt)}
          </time>
        )}
      </div>

      <div className="flex size-8 items-center justify-center">
        {loading ? (
          <Skeleton className="size-6" />
        ) : (
          message?.canEdit && (
            <MessageMenu
              message={message}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          )
        )}
      </div>
    </div>
  );
}
