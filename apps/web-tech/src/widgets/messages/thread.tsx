import { MessageCircle } from "lucide-react";

import type { MessageFormValues, MessageType } from "./types";

import { cn } from "@/ui/lib";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/ui/primitives/card";
import { ScrollArea } from "@/ui/primitives/scroll-area";
import { Skeleton } from "@/ui/primitives/skeleton";
import { ErrorAlert } from "@/ui/shared/ErrorAlert";

import { Composer } from "./composer";
import { Message } from "./message";

const i18n = {
  en: {
    title: "Messages",
    noMessages: "No messages yet",
    noMessagesDescription: "Start the conversation by sending a message",
  },
};

export function Thread({
  loading = false,
  error,
  messages,
  onCreate,
  onEdit,
  onDelete,
}: {
  loading?: boolean;
  error?: Error | string;
  messages?: MessageType[];
  onCreate?: (values: MessageFormValues) => void | Promise<void>;
  onEdit?: (message: MessageFormValues) => void | Promise<void>;
  onDelete?: (message: Pick<MessageType, "id">) => void | Promise<void>;
}) {
  return (
    <Card className="flex h-full flex-col">
      <CardHeader className="border-b">
        <CardTitle>{i18n.en.title}</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 p-0">
        {error ? (
          <div className="p-4">
            <ErrorAlert
              error={typeof error === "string" ? { message: error } : error}
            />
          </div>
        ) : (
          <ScrollArea className="h-80 w-full">
            <ul className="grid h-80">
              {loading ? (
                new Array(3).fill(null).map((_, i) => (
                  <li
                    key={i}
                    className="px-2 py-4 pe-4 ps-6 [&:not(:last-child)]:border-b"
                  >
                    <Message loading />
                  </li>
                ))
              ) : (messages?.length ?? 0) > 0 ? (
                messages?.map((message, index) => (
                  <li
                    key={index}
                    className={cn(
                      "px-2 py-4 pe-4 ps-6 [&:not(:last-child)]:border-b",
                      {
                        "bg-muted": message.type === "INTERNAL",
                      },
                    )}
                  >
                    <Message
                      message={message}
                      onEdit={onEdit}
                      onDelete={onDelete}
                    />
                  </li>
                ))
              ) : (
                <li className="grid size-full">
                  <div className="flex size-full flex-col items-center justify-center gap-2 rounded-lg border border-dashed p-4">
                    <MessageCircle className="size-8 text-muted-foreground" />
                    <div className="text-center">
                      <p className="font-medium text-muted-foreground">
                        {i18n.en.noMessages}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {i18n.en.noMessagesDescription}
                      </p>
                    </div>
                  </div>
                </li>
              )}
            </ul>
          </ScrollArea>
        )}
      </CardContent>
      <CardFooter className="grid border-t px-6 py-4">
        <Composer loading={loading} onSubmit={onCreate} />
      </CardFooter>
    </Card>
  );
}
