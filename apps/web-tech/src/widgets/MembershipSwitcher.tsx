"use client";

import { useState } from "react";
import { ChevronDownIcon } from "lucide-react";

import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { toast } from "@axa/ui/primitives/toast";
import SelectOrganization from "@axa/ui/selectors/SelectOrganization";

import type { RouterOutputs } from "@/api";

import { api, getQueryClient } from "@/api/client";
import { useUser } from "@/contexts/User";

type Organization = Pick<
  RouterOutputs["organizations"]["getMany"]["organizations"][number],
  "id" | "name" | "avatar"
>;

export default function MembershipSwitcher({
  size = "sm",
}: {
  size?: "sm" | "md" | "lg" | "xl";
}) {
  const { organization } = useUser();
  const [selection, setSelection] = useState<Organization | null>(
    organization
      ? {
          id: organization.id ?? "",
          name: organization.name ?? "",
          avatar: organization.avatar ?? "",
        }
      : null,
  );

  const memberships = api.user.memberships.getMany.useQuery();

  const changeOrganizationMutation =
    api.user.memberships.changeOrganization.useMutation({
      onSuccess: async () => {
        const queryClient = getQueryClient();
        await queryClient.invalidateQueries({
          refetchType: "all",
        });
        toast.success("Organization switched");
      },
    });

  const changeOrganization = (organization: Organization) => {
    setSelection(organization);
    changeOrganizationMutation.mutate({
      organizationId: organization.id,
    });
  };

  const loading = memberships.isLoading;
  return (
    <SelectOrganization<Organization>
      loading={loading}
      data={memberships.data?.map((m) => m.organization) ?? []}
      value={selection?.id}
      variant="ghost"
      align="end"
      className="-mx-2 p-2"
      size={size}
      onSelect={(org) => changeOrganization(org)}
    >
      <PreviewOrganization
        size={size}
        loading={loading}
        organization={{
          id: selection?.id ?? "",
          name: selection?.name ?? "",
          avatar: selection?.avatar,
        }}
      />
      <div className="flex aspect-square size-8 items-center justify-center">
        <ChevronDownIcon className="size-5" />
      </div>
    </SelectOrganization>
  );
}
