"use client";

import type { ColumnDef } from "@tanstack/react-table";

import { use, useMemo, useState } from "react";
import Link from "next/link";
import { format } from "date-fns";
import {
  CircleIcon,
  ListFilterIcon,
  MenuIcon,
  Settings2Icon,
} from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import type { InvoiceStatus } from "@axa/database-tech";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@axa/ui/primitives/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { useSearchParamsContext } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import EmptyList from "@axa/ui/shared/EmptyList";
import { DataTable, useDataTable } from "@axa/ui/tables";
import { dataTableColumns, selectColumn } from "@axa/ui/tables/columns";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "@axa/ui/tables/helpers";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import InvoiceStatusBadge from "@/components/symbols/InvoiceStatus";
import { useUser } from "@/contexts/User";
import { useExportInvoices } from "@/hooks/useExportInvoices";
import { AddInvoice, InvoiceMenu } from "@/widgets/actions/billing/invoice";

const i18n = {
  en: {
    title: "Invoices",
    description: "Invoices are a collection of time sheets for work orders.",
    invoiceTitle: "Invoice",
    noInvoices: "There are no invoices yet",
    selection: "Selection",
    actions: {
      add: "Add Invoice",
      export: "Export Invoices",
      tableSettings: "Table Settings",
    },
    headers: {
      name: "Name",
      pending: "Pending",
      balance: "Balance",
      period: "Period",
      due: "Due Date",
      status: "Status",
      organization: "Organization",
    },
  },
  links: {
    billing: "/app/billing/invoices/[id]",
    invoice: "/app/finances/invoices/[id]",
  },
};

export type InvoicesData = RouterOutputs["invoices"]["getMany"]["invoices"];

export function InvoiceTableActions({
  table,
}: {
  table: ReturnType<
    typeof useDataTable<InvoicesData[number], InvoicesData>
  >["table"];
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;
  const { exportInvoices, isPending } = useExportInvoices({
    invoices: selection.rows.map((row) => row.original.id),
  });
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button variant="ghost" size="icon" className="relative">
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute right-1.5 top-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.en.selection} ({selectionCount})
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem disabled={isPending} onSelect={exportInvoices}>
          {i18n.en.actions.export}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface PreviewInvoicesProps {
  loading?: boolean;
  billing?: boolean;
  organization?: string;
  organizations?: string[];
  invoices?: Promise<RouterOutputs["invoices"]["getMany"]>;
}

export const defaultInvoicesData: InvoicesData = [];

export default function PreviewInvoices(props: PreviewInvoicesProps) {
  const user = useUser();
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const { status, setStatus } = useMemo(
    () => ({
      status: searchParams.status ?? undefined,
      setStatus: (status?: string) => setSearchParams("status", status),
    }),
    [searchParams, setSearchParams],
  );

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const invoices = api.invoices.getMany.useQuery(
    {
      organizationId: props.organization,
      organizations: props.organizations,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      status: status as InvoiceStatus,
    },
    {
      enabled:
        !props.loading &&
        !user.loading &&
        (props.billing
          ? !!props.organization || (props.organizations?.length ?? 0) > 0
          : true),
      initialData: props.invoices ? use(props.invoices) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading || invoices.isLoading;

  const { table } = useDataTable<InvoicesData[number], InvoicesData>({
    data: invoices.data?.invoices ?? defaultInvoicesData,
    rowCount: invoices.data?.total,
    manualPagination: true,
    pagination,
    setPagination,
    columns: useMemo(
      () =>
        dataTableColumns<InvoicesData[number], InvoicesData>([
          selectColumn as ColumnDef<InvoicesData[number], InvoicesData>,
          {
            id: "status",
            accessorKey: "status",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.status}
              />
            ),
            cell: ({ row }) => (
              <InvoiceStatusBadge status={row.getValue("status")} />
            ),
          },
          {
            id: "id",
            accessorKey: "name",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.name}
              />
            ),
            cell: ({ row }) => {
              const link = props.billing
                ? i18n.links.billing
                : i18n.links.invoice;

              return (
                <div className="flex w-fit flex-col">
                  <Link
                    href={link.replace("[id]", row.original.id)}
                    className="w-full font-semibold hover:text-primary"
                  >
                    #{row.getValue("id") ?? "000"}
                  </Link>
                </div>
              );
            },
            enableHiding: false,
          },
          {
            id: "balance",
            accessorKey: "balance",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.balance}
              />
            ),
            cell: ({ row }) => <Currency amount={row.getValue("balance")} />,
          },
          {
            id: "pending",
            accessorKey: "pending",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.pending}
              />
            ),
            cell: ({ row }) => <Currency amount={row.getValue("pending")} />,
          },
          {
            id: "period",
            accessorKey: "timePeriodStart",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.period}
              />
            ),
            cell: ({ row }) => {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              const period = row.getValue("period") as Date;
              const date = new Date(
                period.getTime() + period.getTimezoneOffset() * 60000,
              );

              return <p className="text-nowrap">{format(date, "LLLL yyyy")}</p>;
            },
          },
          {
            id: "due",
            accessorKey: "dueDate",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.due}
              />
            ),
            cell: ({ row }) => {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              const due = row.getValue("due") as Date;
              const date = new Date(
                due.getTime() + due.getTimezoneOffset() * 60000,
              );

              return <p className="text-nowrap">{format(date, "PPP")}</p>;
            },
          },
          {
            id: "organization",
            accessorKey: "organization.name",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.organization}
              />
            ),
            cell: ({ row }) => (
              <Badge variant="outline" className="text-xs">
                {row.getValue("organization")}
              </Badge>
            ),
          },
          {
            id: "actions",
            meta: {
              className: "w-[32px]",
            },
            header: ({ table }) => (
              <div className="flex size-full items-center justify-end">
                <InvoiceTableActions table={table} />
              </div>
            ),
            cell: ({ row }) => (
              <div className="flex size-full items-center justify-end">
                <InvoiceMenu invoice={row.original} variant="ghost" />
              </div>
            ),
          },
        ]),
      [props.billing],
    ),
  });

  return (
    <div className="space-y-6">
      {invoices.error && <ErrorFallback error={invoices.error} />}

      <Card>
        <CardHeader className="p-2">
          <div className="flex items-center justify-between">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <ListFilterIcon size="20" color="currentColor" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuGroup>
                  <DropdownMenuLabel>Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={status as string | undefined}
                    onValueChange={setStatus}
                  >
                    <DropdownMenuRadioItem
                      // @ts-expect-error intentionally left blank
                      value={undefined}
                    >
                      All
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="OPEN">
                      Open
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="PAID">
                      Paid
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="DUE">
                      Due
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="border-l pl-2">
              <DataTableSettings
                table={table}
                variant="ghost"
                size="icon"
                aria-label={i18n.en.actions.tableSettings}
              >
                <Settings2Icon size="20" color="currentColor" />
              </DataTableSettings>
            </div>
          </div>
        </CardHeader>
        <CardContent className="border-t p-0">
          <DataTable loading={loading} table={table}>
            <EmptyList title={i18n.en.noInvoices}>
              {user.isBilling && (
                <AddInvoice
                  defaultValues={{
                    organizationId: props.organization,
                  }}
                />
              )}
            </EmptyList>
          </DataTable>
        </CardContent>

        <CardFooter className="flex flex-col gap-2 border-t pt-6">
          <DataTableSimplePagination table={table} />
        </CardFooter>
      </Card>
    </div>
  );
}
