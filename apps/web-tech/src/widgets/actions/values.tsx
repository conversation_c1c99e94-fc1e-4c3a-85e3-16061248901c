"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import { SelectOrganizationField } from "@axa/ui/selectors/SelectOrganization";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  ValueMapFormProps,
  ValueMapFormValues,
} from "@/components/forms/ValueMap";

import { api } from "@/api/client";
import ValueMapForm, {
  ValueMapFormSubmitButton,
} from "@/components/forms/ValueMap";

const i18n = {
  en: {
    titles: {
      add: "Add Value",
      update: "Update Value",
      delete: "Delete Value",
    },
    descriptions: {
      delete: "Are you sure you want to delete this value?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      created: "Value created successfully.",
      updated: "Value updated successfully.",
      deleted: "Value deleted successfully.",
      failedCreate: "Failed to create value: ",
      failedUpdate: "Failed to update value: ",
      failedDelete: "Failed to delete value: ",
    },
  },
  links: {
    values: "/app/values",
  },
};

export type ValueMapStruct = NonNullable<
  RouterOutputs["values"]["getMany"]["values"][number]
>;

export type OrganizationValueMapFormProps = Omit<
  DialogFormProps<ValueMapFormProps, ValueMapFormValues>,
  "Component" | "onSubmit"
>;

export function OrganizationValueMapForm(props: OrganizationValueMapFormProps) {
  const [query, setQuery] = useState("");
  const organizations = api.organizations.getMany.useQuery(
    {
      query,
      pageSize: 5,
      pageNumber: 0,
    },
    {
      enabled: props.open,
    },
  );

  return (
    <ValueMapForm {...props}>
      <SelectOrganizationField
        data={organizations.data?.organizations ?? []}
        loading={organizations.isLoading}
        onValueChange={(value) => {
          setQuery(value);
        }}
      />
      <div className="flex w-full justify-center">
        <ValueMapFormSubmitButton />
      </div>
    </ValueMapForm>
  );
}

export function AddValueMap(props: OrganizationValueMapFormProps) {
  const utils = api.useUtils();
  const createValueMapMutation = api.values.create.useMutation({
    onSuccess: async (_, variables) => {
      await utils.values.getMany.invalidate({
        type: variables.type,
      });
      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<ValueMapFormProps, ValueMapFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      Component={OrganizationValueMapForm}
      onSubmit={useCallback<NonNullable<ValueMapFormProps["onSubmit"]>>(
        async (values) => {
          await createValueMapMutation.mutateAsync({
            organizationId: values.organizationId,
            key: values.key,
            value: values.key,
            type: values.type,
          });
        },
        [createValueMapMutation],
      )}
    />
  );
}

export function UpdateValueMap(
  props: OrganizationValueMapFormProps &
    PropsWithChildren<{
      valueId: string;
      defaultValues?: ValueMapFormProps["defaultValues"];
    }>,
) {
  const utils = api.useUtils();
  const updateValueMapMutation = api.values.update.useMutation({
    onSuccess: async (_, variables) => {
      await utils.values.getMany.invalidate({
        type: variables.type,
      });
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<ValueMapFormProps, ValueMapFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationValueMapForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<ValueMapFormProps["onSubmit"]>>(
        async (values) => {
          await updateValueMapMutation.mutateAsync({
            id: props.valueId,
            value: values.key,
            type: values.type,
          });
        },
        [updateValueMapMutation, props.valueId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteValueMap({
  valueId,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{ valueId: string }> &
  Omit<DialogConfirmationProps, "onClick">) {
  const utils = api.useUtils();
  const deleteValueMapMutation = api.values.delete.useMutation({
    onSuccess: async () => {
      await utils.values.getMany.invalidate();
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      {...props}
      onOpenChange={onOpenChange}
      open={open}
      disabled={deleteValueMapMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteValueMapMutation.mutateAsync({ id: valueId });
      }, [deleteValueMapMutation, valueId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function ValueMapMenu({
  value,
  ...props
}: PropsWithChildren<
  {
    value?: ValueMapStruct;
  } & Omit<ButtonProps, "value">
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem disabled>Duplicate</DropdownMenuItem>
          <UpdateValueMap
            onOpenChange={handleDialogOpenChange}
            valueId={value?.id ?? ""}
            defaultValues={{
              organizationId: value?.organization?.id,
              type: value?.type,
              key: value?.key ?? "",
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateValueMap>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteValueMap
            onOpenChange={handleDialogOpenChange}
            valueId={value?.id ?? ""}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteValueMap>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
