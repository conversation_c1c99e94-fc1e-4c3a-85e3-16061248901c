"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { PersonRole } from "@axa/database-tech";
import type { ButtonProps } from "@axa/ui/primitives/button";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  OrganizationUserProps,
  UserFormProps,
  UserFormValues,
  UserRole,
} from "@/components/forms/OrgUser";
import type { DialogConfirmationProps } from "@/ui/shared/DialogConfirmation";

import { api } from "@/api/client";
import { OrganizationUserForm } from "@/components/forms/OrgUser";
import DialogConfirmation from "@/ui/shared/DialogConfirmation";

const i18n = {
  en: {
    titles: {
      update: "Update User",
      delete: "Delete User",
    },
    actions: {
      label: "Actions",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      updated: "User updated successfully.",
      failedUpdate: "Failed to update user: ",
      deleted: "User deleted successfully.",
      failedDelete: "Failed to delete user: ",
      deleteDescription: "Are you sure you want to delete this user?",
    },
  },
  links: {
    user: "/app/admin/users",
  },
};

export type UserStruct =
  | RouterOutputs["user"]["get"]
  | RouterOutputs["user"]["getMany"]["users"][number];

export type UpdateUserProps = OrganizationUserProps &
  PropsWithChildren<{
    onUpdate?: (user: UserFormValues & { id: string }) => void | Promise<void>;
  }>;

export function UpdateUser({ ...props }: UpdateUserProps) {
  const utils = api.useUtils();
  const updateUserMutation = api.user.changeUserRole.useMutation({
    onSuccess: async () => {
      await utils.user.getMany.invalidate();
      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<OrganizationUserProps, UserFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      showOrganization
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      Component={OrganizationUserForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<UserFormProps["onSubmit"]>>(
        async (values) => {
          await updateUserMutation.mutateAsync({
            userId: props.userId,
            role: values.role as PersonRole,
            organizationId: values.organizationId,
          });
        },
        [updateUserMutation, props.userId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteUser({
  user,
  children,
  onOpenChange,
  open,
  ...props
}: PropsWithChildren<{
  user: UserStruct;
}> &
  Omit<DialogConfirmationProps, "onClick">) {
  const utils = api.useUtils();
  const deleteUserMutation = api.user.delete.useMutation({
    onSuccess: async () => {
      await utils.user.getMany.invalidate();
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      title={i18n.en.titles.delete}
      description={i18n.en.messages.deleteDescription}
      {...props}
      onOpenChange={onOpenChange}
      open={open}
      disabled={deleteUserMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteUserMutation.mutateAsync({ id: user.id });
      }, [deleteUserMutation, user.id])}
    >
      {children ?? (
        <Button variant="destructive" className="w-full">
          {i18n.en.actions.delete}
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function UserMenu({
  loading = false,
  user,
  ...props
}: PropsWithChildren<
  {
    loading?: boolean;
    user?:
      | RouterOutputs["user"]["get"]
      | RouterOutputs["user"]["getMany"]["users"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            disabled={loading}
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateUser
            onOpenChange={handleDialogOpenChange}
            userId={user?.id ?? ""}
            defaultValues={{
              role: user?.role as UserRole,
              organizationId: user?.organization.id ?? undefined,
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateUser>
          <DeleteUser user={user!}>
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteUser>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
