"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  TechnicianFormProps,
  TechnicianFormValues,
} from "@/components/forms/Technician";

import { api } from "@/api/client";
import TechnicianForm from "@/components/forms/Technician";
import { useUser } from "@/contexts/User";

const i18n = {
  en: {
    titles: {
      add: "Add Technician",
      update: "Update Technician",
      delete: "Delete Technician",
    },
    descriptions: {
      delete: "Are you sure you want to delete this technician?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      created: "Technician created successfully.",
      updated: "Technician updated successfully.",
      deleted: "Technician deleted successfully.",
      failedCreate: "Failed to create technician: ",
      failedUpdate: "Failed to update technician: ",
      failedDelete: "Failed to delete technician: ",
    },
  },
  links: {
    technicians: "/app/technicians",
  },
};

export type AddTechnicianProps = Omit<
  DialogFormProps<TechnicianFormProps, TechnicianFormValues>,
  "Component" | "onSubmit"
> & {
  templateId?: string;
  orderId?: string;
  providerId?: string;
  onCreate?: DialogFormProps<
    TechnicianFormProps,
    TechnicianFormValues
  >["onSubmit"];
};

export function AddTechnician({
  templateId,
  orderId,
  providerId,
  onCreate,
  ...props
}: AddTechnicianProps) {
  const user = useUser();
  const router = useRouter();
  const utils = api.useUtils();
  const createTechnicianMutation = api.technicians.create.useMutation({
    onSuccess: async () => {
      if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      } else if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (providerId) {
        await utils.providers.get.invalidate({
          id: providerId,
        });
      } else {
        await utils.technicians.getMany.invalidate();
      }
      toast.success(i18n.en.messages.created);
      router.refresh();
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<TechnicianFormProps, TechnicianFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      includePayment={user.isBilling}
      Component={TechnicianForm}
      onSubmit={useCallback<NonNullable<TechnicianFormProps["onSubmit"]>>(
        async (values) => {
          if (onCreate) {
            await onCreate(values);
          } else {
            await createTechnicianMutation.mutateAsync({
              level: parseInt(values.level, 10),
              billingType: values.billingType,
              billingRate: parseFloat(values.billingRate ?? "40"),
              paymentType: values.paymentType,
              paymentRate: parseFloat(values.paymentRate ?? "20"),
              templateId,
              orderId,
              providerId,
            });
          }
        },
        [createTechnicianMutation, onCreate, templateId, orderId, providerId],
      )}
    />
  );
}

export type UpdateTechnicianProps = Omit<
  DialogFormProps<TechnicianFormProps, TechnicianFormValues>,
  "Component" | "onSubmit"
> &
  PropsWithChildren<{
    technicianId: string;
    templateId?: string;
    orderId?: string;
    providerId?: string;
    defaultValues?: TechnicianFormProps["defaultValues"];
    onUpdate?: (
      technician: TechnicianFormValues & { id: string },
    ) => void | Promise<void>;
  }>;

export function UpdateTechnician({
  technicianId,
  templateId,
  orderId,
  providerId,
  onUpdate,
  ...props
}: UpdateTechnicianProps) {
  const user = useUser();
  const utils = api.useUtils();
  const updateTechnicianMutation = api.technicians.update.useMutation({
    onSuccess: async () => {
      if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      } else if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (providerId) {
        await utils.providers.get.invalidate({
          id: providerId,
        });
      } else {
        // TODO: granular invalidation
        await utils.technicians.getMany.invalidate();
      }

      await utils.technicians.get.invalidate({
        id: technicianId,
      });

      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<TechnicianFormProps, TechnicianFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open}
      onOpenChange={props.onOpenChange}
      {...props}
      includePayment={user.isBilling}
      Component={TechnicianForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<TechnicianFormProps["onSubmit"]>>(
        async (values) => {
          if (onUpdate) {
            await onUpdate({
              id: technicianId,
              ...values,
            });
          } else if (technicianId) {
            await updateTechnicianMutation.mutateAsync({
              id: technicianId,
              level: parseInt(values.level, 10),
              billingType: values.billingType,
              billingRate: parseFloat(values.billingRate),
              paymentType: values.paymentType,
              paymentRate: parseFloat(values.paymentRate ?? "0"),
            });
          }
        },
        [updateTechnicianMutation, onUpdate, technicianId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export type DeleteTechnicianProps = PropsWithChildren<{
  technicianId: string;
  templateId?: string;
  orderId?: string;
  providerId?: string;
  reroute?: boolean;
  onDelete?: (id: string) => void | Promise<void>;
}> &
  Omit<DialogConfirmationProps, "onClick">;

export function DeleteTechnician({
  technicianId,
  templateId,
  orderId,
  providerId,
  reroute,
  children,
  onDelete,
  ...props
}: DeleteTechnicianProps) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteTechnicianMutation = api.technicians.delete.useMutation({
    onSuccess: async () => {
      if (templateId) {
        await utils.templates.get.invalidate({
          id: templateId,
        });
      } else if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      } else if (providerId) {
        await utils.providers.get.invalidate({
          id: providerId,
        });
      } else {
        await utils.technicians.getMany.invalidate();
      }
      if (reroute) {
        router.replace(i18n.links.technicians);
      } else {
        router.refresh();
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      {...props}
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      disabled={deleteTechnicianMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        if (onDelete) {
          await onDelete(technicianId);
        } else if (technicianId) {
          await deleteTechnicianMutation.mutateAsync({ id: technicianId });
        }
      }, [deleteTechnicianMutation, onDelete, technicianId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function TechnicianMenu({
  rerouteOnDelete,
  technician,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    technician?:
      | RouterOutputs["technicians"]["get"]
      | RouterOutputs["technicians"]["getMany"]["technicians"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateTechnician
            onOpenChange={handleDialogOpenChange}
            technicianId={technician?.id ?? ""}
            defaultValues={{
              level: `${technician?.level ?? 1}`,
              billingRate: technician?.billingRate.toString(),
              billingType: technician?.billingType ?? undefined,
              paymentRate: technician?.paymentRate.toString(),
              paymentType: technician?.paymentType ?? undefined,
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateTechnician>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteTechnician
            onOpenChange={handleDialogOpenChange}
            technicianId={technician?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteTechnician>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
