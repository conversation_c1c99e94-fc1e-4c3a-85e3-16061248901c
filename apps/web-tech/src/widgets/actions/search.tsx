"use client";

import type { ChangeEvent } from "react";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { endOfDay, startOfDay } from "date-fns";
import { CalendarIcon, XIcon } from "lucide-react";

import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { useDebounceValue } from "@axa/ui/hooks/useDebounceValue";
import { cn } from "@axa/ui/lib";
import { Button, buttonVariants } from "@axa/ui/primitives/button";
import { Calendar } from "@axa/ui/primitives/calendar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { Input } from "@axa/ui/primitives/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@axa/ui/primitives/popover";
import { Separator } from "@axa/ui/primitives/separator";
import { useSearchParamsContext } from "@axa/ui/search";
import { SelectValue } from "@axa/ui/selectors/SelectValue";

import { api } from "@/api/client";
import { OrderPriorityLabel } from "@/components/symbols/OrderPriority";

import { SearchOrganizations as SelectOrganizationsWithData } from "./organizations/organization";

export { SearchParams } from "@axa/ui/search";

const i18n = {
  en: {
    actions: {
      search: "Search",
    },
  },
};

export function useSearchPagination() {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const pageIndex = parseInt(
    (searchParams.pageIndex as string | undefined) ?? "0",
    10,
  );
  const pageSize = parseInt(
    (searchParams.pageSize as string | undefined) ?? "10",
    10,
  );

  const setPagination = useCallback(
    ({
      pageIndex,
      pageSize,
      ...props
    }: {
      pageIndex: number;
      pageSize: number;
    }) => {
      console.log({ pageIndex, pageSize, props });
      setSearchParams([
        ["pageIndex", pageIndex.toString()],
        ["pageSize", pageSize.toString()],
      ]);
    },
    [setSearchParams],
  );

  return useMemo(
    () => [{ pageIndex, pageSize }, setPagination],
    [pageIndex, pageSize, setPagination],
  );
}

export const SEARCH_TEXT_NAME = "search";

export function useSearchTextValue(name = SEARCH_TEXT_NAME) {
  const { searchParams } = useSearchParamsContext();
  return searchParams[name] as string | undefined;
}

export interface SearchTextProps {
  loading?: boolean;
  name?: string;
  placeholder?: string;
}

export function SearchText({
  loading,
  name = SEARCH_TEXT_NAME,
  placeholder = i18n.en.actions.search,
}: SearchTextProps) {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const [search, setSearch] = useDebounceValue<string>(
    (searchParams[name] as string | undefined) ?? "",
    500,
  );

  useEffect(() => {
    if (search !== searchParams.search) {
      setSearchParams(name, search);
    }
  }, [name, search, searchParams, setSearchParams]);

  return (
    <Input
      type="text"
      name={name}
      readOnly={loading}
      placeholder={placeholder}
      className="h-full min-h-8 w-[150px] lg:w-[250px]"
      defaultValue={searchParams[name]}
      onChange={useCallback(
        (event: ChangeEvent<HTMLInputElement>) => setSearch(event.target.value),
        [setSearch],
      )}
    />
  );
}

export const SEARCH_ORGANIZATIONS_NAME = "organizations";

export function useSearchOrganizationsValue() {
  const { searchParams } = useSearchParamsContext();
  return searchParams[SEARCH_ORGANIZATIONS_NAME] as string | undefined;
}

export interface SearchOrganizationsProps {
  loading?: boolean;
  name?: string;
  placeholder?: string;
}

export function SearchOrganizations({
  loading,
  name = SEARCH_ORGANIZATIONS_NAME,
  placeholder = "Search organizations...",
}: SearchOrganizationsProps) {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const [selection, setSelection] = useState<
    | {
        id: string;
        name: string;
        avatar?: string | null;
      }
    | undefined
  >();

  useEffect(() => {
    // TODO: Remove this when the search is implemented
    // FIXME: This is a temporary fix to clear the search when the organization is removed on load
    if (searchParams[name]) {
      setSearchParams(name, "");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      className={cn(
        buttonVariants({
          variant: "outline",
          size: "sm",
          className: "flex w-fit items-center justify-between",
        }),
        "p-0 px-0",
      )}
    >
      <SelectOrganizationsWithData
        size="sm"
        variant="ghost"
        loading={loading}
        useDialog={false}
        className="size-fit p-0"
        value={searchParams[name] as string | undefined}
        selection={selection}
        onSelect={(value) => {
          setSearchParams(name, value.id);
          setSelection({
            id: value.id,
            name: value.name,
            avatar: value.avatar,
          });
        }}
      >
        {selection ? (
          <PreviewOrganization
            size="sm"
            className="p-1"
            organization={{
              id: selection.id,
              name: selection.name,
              avatar: selection.avatar,
            }}
          />
        ) : (
          <div className="flex w-full items-center justify-between gap-1 p-2">
            <span className="truncate text-muted-foreground">
              {placeholder}
            </span>
          </div>
        )}
        <CaretSortIcon className="me-2 ms-auto text-muted-foreground" />
      </SelectOrganizationsWithData>
      {selection && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="rounded-s-none border-l"
          onClick={() => {
            setSearchParams(name, "");
            setSelection(undefined);
          }}
        >
          <XIcon className="size-4" />
          <span className="sr-only">Clear organization selection</span>
        </Button>
      )}
    </div>
  );
}

export const SEARCH_START_DATE_NAME = "startDate";
export const SEARCH_END_DATE_NAME = "endDate";

export function useSearchDateRangeValue() {
  const { searchParams } = useSearchParamsContext();
  return useMemo(() => {
    const startDateString = searchParams[SEARCH_START_DATE_NAME] as
      | string
      | undefined;
    const endDateString = searchParams[SEARCH_END_DATE_NAME] as
      | string
      | undefined;

    return {
      [SEARCH_START_DATE_NAME]: startDateString
        ? startOfDay(startDateString)
        : undefined,
      [SEARCH_END_DATE_NAME]: endDateString
        ? endOfDay(endDateString)
        : undefined,
    };
  }, [searchParams]);
}

export interface SearchDateRangeProps {
  loading?: boolean;
  name?: string;
  placeholder?: string;
}

export function SearchDateRange({
  loading,
  placeholder = "Search date range...",
}: SearchOrganizationsProps) {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const onSelect = useCallback(
    (dates?: { from: Date | undefined; to: Date | undefined }) => {
      setSearchParams([
        [SEARCH_START_DATE_NAME, dates?.from?.toDateString() ?? ""],
        [SEARCH_END_DATE_NAME, dates?.to?.toDateString() ?? ""],
      ]);
    },
    [setSearchParams],
  );
  const onClear = useCallback(() => {
    setSearchParams([
      [SEARCH_START_DATE_NAME, ""],
      [SEARCH_END_DATE_NAME, ""],
    ]);
  }, [setSearchParams]);

  const { values, hasValue, text } = useMemo(() => {
    const startDate = searchParams[SEARCH_START_DATE_NAME] as
      | string
      | undefined;
    const endDate = searchParams[SEARCH_END_DATE_NAME] as string | undefined;

    return {
      text: [startDate, endDate].filter(Boolean).join(" - ") || placeholder,
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      hasValue: !!(startDate || endDate),
      values: {
        from: startDate ? new Date(startDate) : undefined,
        to: endDate ? new Date(endDate) : undefined,
      },
    };
  }, [searchParams, placeholder]);

  return (
    <Popover>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: "sm",
            className: "flex items-center justify-between",
          }),
          "p-0 px-0",
        )}
      >
        <PopoverTrigger asChild className="flex-1">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className={cn("h-full space-x-2 focus:z-10", {
              "rounded-e-none": hasValue,
            })}
          >
            <CalendarIcon className="mr-auto size-4 opacity-50" />
            <span
              className={
                hasValue ? "truncate" : "truncate text-muted-foreground"
              }
            >
              {text}
            </span>
          </Button>
        </PopoverTrigger>
        {hasValue && (
          <>
            <Separator orientation="vertical" />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="rounded-s-none"
              onClick={onClear}
            >
              <XIcon className="size-4" />
              <span className="sr-only">Clear date range</span>
            </Button>
          </>
        )}
      </div>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          selected={values}
          onSelect={onSelect}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  );
}

export const SEARCH_PRIORITY_NAME = "priority";

export function useSearchPriorityValue(name = SEARCH_PRIORITY_NAME) {
  const { searchParams } = useSearchParamsContext();
  return searchParams[name] as string | undefined;
}

export function SearchPriority() {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const value = searchParams[SEARCH_PRIORITY_NAME] as
    | "LOW"
    | "MEDIUM"
    | "HIGH"
    | undefined;

  return (
    <DropdownMenu>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: "sm",
            className: "flex items-center justify-between",
          }),
          "w-full p-0 px-0",
        )}
      >
        <DropdownMenuTrigger asChild className="flex-1">
          <Button
            size="sm"
            variant="ghost"
            // variant="outline"
            // className="flex size-full justify-between gap-1 p-1"
            className={cn("flex size-full justify-between gap-1 p-1", {
              "rounded-e-none": !!value,
            })}
          >
            {
              <div className="flex size-full items-center justify-between gap-1 p-0.5">
                {value ? (
                  <span className="truncate">
                    <OrderPriorityLabel size="sm" priority={value} />
                  </span>
                ) : (
                  <span className="truncate text-muted-foreground">
                    {"Select priority"}
                  </span>
                )}
                <CaretSortIcon className="ms-auto text-muted-foreground" />
              </div>
            }
          </Button>
        </DropdownMenuTrigger>
        {!!value && (
          <>
            <Separator orientation="vertical" />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="rounded-s-none"
              onClick={() => setSearchParams(SEARCH_PRIORITY_NAME, undefined)}
            >
              <XIcon className="size-4" />
              <span className="sr-only">Clear priority</span>
            </Button>
          </>
        )}
      </div>

      <DropdownMenuContent>
        <DropdownMenuRadioGroup
          value={value}
          onValueChange={(value) =>
            setSearchParams(SEARCH_PRIORITY_NAME, value)
          }
        >
          <DropdownMenuRadioItem value="LOW">Low</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="MEDIUM">Normal</DropdownMenuRadioItem>
          <DropdownMenuRadioItem value="HIGH">High</DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export const SEARCH_ORDER_TYPE_NAME = "type";
export const SEARCH_ORDER_CATEGORY_NAME = "category";

export function useSearchOrderTypeValue(name = SEARCH_ORDER_TYPE_NAME) {
  const { searchParams } = useSearchParamsContext();
  return searchParams[name] as string | undefined;
}

export function SearchOrderType() {
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const [type, setType] = useState(searchParams[SEARCH_ORDER_TYPE_NAME]);
  const [category, setCategory] = useState(
    searchParams[SEARCH_ORDER_CATEGORY_NAME],
  );

  const types = api.utility.getType.useQuery({
    type: "ORDER_TYPE",
  });

  const typeSearch = searchParams[SEARCH_ORDER_TYPE_NAME] as string | undefined;
  const categorySearch = searchParams[SEARCH_ORDER_CATEGORY_NAME] as
    | string
    | undefined;

  useEffect(() => {
    if (types.data && !types.isLoading) {
      const selectedType = types.data.find((item) => item.value === typeSearch);
      if (selectedType) {
        setType({
          value: selectedType.value,
          id: selectedType.id,
          subtypes: selectedType.subtypes,
        });
        const subtype = selectedType.subtypes?.find(
          (item) => item.text === categorySearch,
        );
        if (subtype) {
          setCategory({
            value: subtype?.text,
            id: subtype?.id,
          });
        } else {
          setCategory(undefined);
        }
      } else {
        setType(undefined);
        setCategory(undefined);
      }
    }
  }, [types.data, types.isLoading, typeSearch, categorySearch]);

  return (
    <>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: "sm",
            className: "flex items-center justify-between",
          }),
          "w-full p-0 px-0",
        )}
      >
        <SelectValue
          size="sm"
          variant="ghost"
          data={types.data ?? []}
          loading={types.isLoading}
          placeholder="Select type"
          className={cn("h-full p-0 px-0", {
            "text-muted-foreground": !!type === false,
          })}
          defaultValue={
            searchParams[SEARCH_ORDER_TYPE_NAME] as string | undefined
          }
          value={searchParams[SEARCH_ORDER_TYPE_NAME] as string | undefined}
          onSelect={(value) => {
            setSearchParams(SEARCH_ORDER_TYPE_NAME, value.value);
          }}
        >
          {type ? (
            <>
              <span className="truncate p-2">{type.value}</span>
              <CaretSortIcon className="me-2 ms-auto text-muted-foreground" />
            </>
          ) : (
            <div className="flex w-full items-center justify-between gap-1 p-2">
              <span className="truncate text-muted-foreground">
                {"Select type"}
              </span>
              <CaretSortIcon className="ms-auto text-muted-foreground" />
            </div>
          )}
        </SelectValue>
        {type && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="rounded-s-none border-l"
            onClick={() => {
              setSearchParams([
                [SEARCH_ORDER_TYPE_NAME, ""],
                [SEARCH_ORDER_CATEGORY_NAME, ""],
              ]);
            }}
          >
            <XIcon className="size-4" />
            <span className="sr-only">Clear order type</span>
          </Button>
        )}
      </div>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: "sm",
            className: "flex items-center justify-between",
          }),
          "w-full p-0 px-0",
        )}
      >
        <SelectValue
          size="sm"
          variant="ghost"
          disabled={!!type === false}
          className={cn("h-full p-0 px-0", {
            "text-muted-foreground": !!category === false,
          })}
          placeholder="Select category"
          data={
            type?.subtypes?.map(({ id, text }) => ({
              value: text,
              id,
            })) ?? []
          }
          loading={types.isLoading}
          defaultValue={
            searchParams[SEARCH_ORDER_CATEGORY_NAME] as string | undefined
          }
          value={searchParams[SEARCH_ORDER_CATEGORY_NAME] as string | undefined}
          onSelect={(value) => {
            setSearchParams(SEARCH_ORDER_CATEGORY_NAME, value.value);
          }}
        >
          {category ? (
            <>
              <span className="truncate p-2">{category.value}</span>
              <CaretSortIcon className="me-2 ms-auto text-muted-foreground" />
            </>
          ) : (
            <div className="flex w-full items-center justify-between gap-1 p-2">
              <span className="truncate text-muted-foreground">
                {"Select category"}
              </span>
              <CaretSortIcon className="ms-auto text-muted-foreground" />
            </div>
          )}
        </SelectValue>
        {category && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="rounded-s-none border-l"
            onClick={() => {
              setSearchParams(SEARCH_ORDER_CATEGORY_NAME, "");
            }}
          >
            <XIcon className="size-4" />
            <span className="sr-only">Clear order category</span>
          </Button>
        )}
      </div>
    </>
  );
}
