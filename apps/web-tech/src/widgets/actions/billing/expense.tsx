"use client";

import type { PropsWithChildren } from "react";

import { useCallback, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { MoreVerticalIcon, TrashIcon } from "lucide-react";

import type { ButtonProps } from "@axa/ui/primitives/button";
import type { DialogConfirmationProps } from "@axa/ui/shared/DialogConfirmation";
import type { DialogFormProps } from "@axa/ui/shared/DialogForm";
import { ValueStoreType } from "@axa/database-tech";
import { Button } from "@axa/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { toast } from "@axa/ui/primitives/toast";
import DialogConfirmation from "@axa/ui/shared/DialogConfirmation";
import DialogForm from "@axa/ui/shared/DialogForm";

import type { RouterOutputs } from "@/api";
import type {
  ExpenseFormProps,
  ExpenseFormValues,
} from "@/components/forms/Expense";

import { api } from "@/api/client";
import ExpenseForm from "@/components/forms/Expense";

const i18n = {
  en: {
    titles: {
      add: "Add Expense",
      update: "Update Expense",
      delete: "Delete Expense",
    },
    descriptions: {
      delete: "Are you sure you want to delete this expense?",
    },
    actions: {
      label: "Actions",
      add: "Add",
      update: "Update",
      delete: "Delete",
    },
    messages: {
      created: "Expense created successfully.",
      updated: "Expense updated successfully.",
      deleted: "Expense deleted successfully.",
      failedCreate: "Failed to create expense: ",
      failedUpdate: "Failed to update expense: ",
      failedDelete: "Failed to delete expense: ",
    },
  },
  links: {
    expenses: "/app/expenses",
  },
};

export function AddExpense({
  timeSheetId,
  orderId,
  ...props
}: Omit<
  DialogFormProps<ExpenseFormProps, ExpenseFormValues>,
  "Component" | "onSubmit"
> & {
  timeSheetId?: string;
  orderId?: string;
}) {
  const [open, onOpenChange] = useState(false);
  const types = api.values.getMany.useQuery(
    {
      type: ValueStoreType.EXPENSE,
    },
    {
      enabled: open,
    },
  );

  const utils = api.useUtils();
  const createExpenseMutation = api.expenses.create.useMutation({
    onSuccess: async () => {
      if (timeSheetId) {
        await utils.timeSheets.get.invalidate({
          id: timeSheetId,
        });
      } else if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      }
      await utils.expenses.getMany.invalidate({
        timeSheetId,
        orderId,
      });

      toast.success(i18n.en.messages.created);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedCreate + error.message);
    },
  });

  return (
    <DialogForm<ExpenseFormProps, ExpenseFormValues>
      title={i18n.en.titles.add}
      label={i18n.en.titles.add}
      {...props}
      onOpenChange={onOpenChange}
      types={types.data?.values ?? []}
      loading={types.isPending}
      Component={ExpenseForm}
      onSubmit={useCallback<NonNullable<ExpenseFormProps["onSubmit"]>>(
        async (values) => {
          await createExpenseMutation.mutateAsync({
            amount: parseFloat(values.amount),
            type: values.type,
            notes: values.notes,
            timeSheetId,
            orderId,
          });
        },
        [createExpenseMutation, timeSheetId, orderId],
      )}
    />
  );
}

export function UpdateExpense({
  expenseId,
  timeSheetId,
  orderId,
  ...props
}: Omit<
  DialogFormProps<ExpenseFormProps, ExpenseFormValues>,
  "Component" | "onSubmit"
> &
  PropsWithChildren<{
    expenseId: string;
    timeSheetId?: string;
    orderId?: string;
    defaultValues?: ExpenseFormProps["defaultValues"];
  }>) {
  const [open, onOpenChange] = useState(false);
  const types = api.values.getMany.useQuery(
    {
      type: ValueStoreType.EXPENSE,
    },
    {
      enabled: props.open ?? open,
    },
  );

  const utils = api.useUtils();
  const updateExpenseMutation = api.expenses.update.useMutation({
    onSuccess: async () => {
      if (timeSheetId) {
        await utils.timeSheets.get.invalidate({
          id: timeSheetId,
        });
      } else if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      }

      await utils.expenses.getMany.invalidate({
        timeSheetId,
        orderId,
      });

      await utils.expenses.get.invalidate({
        id: expenseId,
      });

      toast.success(i18n.en.messages.updated);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedUpdate + error.message);
    },
  });

  return (
    <DialogForm<ExpenseFormProps, ExpenseFormValues>
      title={i18n.en.titles.update}
      label={i18n.en.titles.update}
      open={props.open ?? open}
      onOpenChange={props.onOpenChange ?? onOpenChange}
      types={types.data?.values ?? []}
      loading={types.isPending}
      {...props}
      Component={ExpenseForm}
      defaultValues={props.defaultValues}
      onSubmit={useCallback<NonNullable<ExpenseFormProps["onSubmit"]>>(
        async (values) => {
          await updateExpenseMutation.mutateAsync({
            id: expenseId,
            amount: parseFloat(values.amount),
            type: values.type,
            notes: values.notes,
            timeSheetId,
            orderId,
          });
        },
        [updateExpenseMutation, expenseId, timeSheetId, orderId],
      )}
    >
      {props.children ?? (
        <Button variant="outline" className="w-full">
          {i18n.en.actions.update}
        </Button>
      )}
    </DialogForm>
  );
}

export function DeleteExpense({
  expenseId,
  timeSheetId,
  orderId,
  reroute,
  children,
  ...props
}: PropsWithChildren<{
  expenseId: string;
  timeSheetId?: string;
  orderId?: string;
  reroute?: boolean;
}> &
  Omit<DialogConfirmationProps, "onClick">) {
  const router = useRouter();
  const utils = api.useUtils();
  const deleteExpenseMutation = api.expenses.delete.useMutation({
    onSuccess: async () => {
      if (timeSheetId) {
        await utils.timeSheets.get.invalidate({
          id: timeSheetId,
        });
      } else if (orderId) {
        await utils.orders.get.invalidate({
          id: orderId,
        });
      }

      await utils.expenses.getMany.invalidate({
        timeSheetId,
        orderId,
      });

      if (reroute) {
        router.replace(i18n.links.expenses);
      }
      toast.success(i18n.en.messages.deleted);
    },
    onError: (error) => {
      toast.error(i18n.en.messages.failedDelete + error.message);
    },
  });
  return (
    <DialogConfirmation
      {...props}
      title={i18n.en.titles.delete}
      description={i18n.en.descriptions.delete}
      disabled={deleteExpenseMutation.isPending}
      variant="destructive"
      onClick={useCallback(async () => {
        await deleteExpenseMutation.mutateAsync({ id: expenseId });
      }, [deleteExpenseMutation, expenseId])}
    >
      {children ?? (
        <Button size="icon" variant="destructive">
          <TrashIcon size="20" />
        </Button>
      )}
    </DialogConfirmation>
  );
}

export function ExpenseMenu({
  rerouteOnDelete,
  expense,
  ...props
}: PropsWithChildren<
  {
    rerouteOnDelete?: boolean;
    expense?:
      | RouterOutputs["expenses"]["get"]
      | RouterOutputs["expenses"]["getMany"]["expenses"][number];
  } & ButtonProps
>) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [hasOpenDialog, setHasOpenDialog] = useState(false);
  const dropdownTriggerRef = useRef<HTMLButtonElement | null>(null);
  const focusRef = useRef<HTMLButtonElement | null>(null);

  const handleItemSelect = useCallback(
    function handleDialogItemSelect(event: Event) {
      event.preventDefault();
      focusRef.current = dropdownTriggerRef.current;
    },
    [focusRef, dropdownTriggerRef],
  );

  const handleDialogOpenChange = useCallback(
    function handleDialogItemOpenChange(open: boolean) {
      setHasOpenDialog(open);
      if (open === false) {
        setDropdownOpen(false);
      }
    },
    [],
  );

  return (
    <DropdownMenu
      modal={dropdownOpen}
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <DropdownMenuTrigger asChild>
        {props.children ?? (
          <Button
            variant="outline"
            size="icon"
            {...props}
            ref={dropdownTriggerRef}
          >
            <MoreVerticalIcon size="20" />
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        hidden={hasOpenDialog}
        onCloseAutoFocus={(event) => {
          if (focusRef.current) {
            focusRef.current.focus();
            focusRef.current = null;
            event.preventDefault();
          }
        }}
      >
        <DropdownMenuGroup>
          <DropdownMenuLabel>{i18n.en.actions.label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <UpdateExpense
            open={dropdownOpen}
            onOpenChange={handleDialogOpenChange}
            expenseId={expense?.id ?? ""}
            defaultValues={{
              amount: expense?.amount.toString(),
              type: expense?.type,
            }}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.update}
            </DropdownMenuItem>
          </UpdateExpense>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DeleteExpense
            onOpenChange={handleDialogOpenChange}
            expenseId={expense?.id ?? ""}
            reroute={rerouteOnDelete}
          >
            <DropdownMenuItem onSelect={handleItemSelect}>
              {i18n.en.actions.delete}
            </DropdownMenuItem>
          </DeleteExpense>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
