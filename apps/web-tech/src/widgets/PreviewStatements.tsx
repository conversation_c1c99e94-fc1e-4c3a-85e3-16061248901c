"use client";

import { use, use<PERSON><PERSON>back, useMemo, useState } from "react";
import { format, subMonths, subYears } from "date-fns";
import { FileIcon, ListFilterIcon } from "lucide-react";

import type { StatementType } from "@axa/database-tech";
import { exportCSV } from "@axa/ui/lib/files";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@axa/ui/primitives/table";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@axa/ui/primitives/tabs";
import { useSearchParamsContext } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import EmptyList from "@axa/ui/shared/EmptyList";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import ListStatements from "@/components/ListStatements";

const i18n = {
  en: {
    title: "Statements",
    description: "Recent statements made to your account.",
    noStatements: "No statements found",
    filterBy: "Filter by",
    actions: {
      month: "Month",
      year: "Year",
      funding: "Funding",
      payments: "Payments",
      export: "Export",
      filter: "Filter",
    },
    table: {
      headers: {
        date: "Date",
        type: "Type",
        createdBy: "Created By",
        amount: "Amount",
      },
    },
  },
};

export interface PreviewBillingStatementsProps {
  organizationId?: string;
  statements?: Promise<RouterOutputs["billing"]["getStatements"]>;
}

export function OLD_REFERENCE_ONLY_PreviewBillingStatements(
  props: PreviewBillingStatementsProps,
) {
  type Period = "month" | "year";
  const [period, setPeriod] = useState<Period>("month");
  const [type, setType] = useState<"FUNDING" | "PAYMENT" | undefined>();
  const [pagination] = useState({
    pageNumber: 0,
    pageSize: 10,
  });

  const { startDate, endDate } = useMemo(() => {
    const now = new Date();

    return {
      endDate: now,
      startDate: period === "month" ? subMonths(now, 1) : subYears(now, 1),
    };
  }, [period]);

  const statements = api.billing.getStatements.useQuery(
    {
      organizationId: props.organizationId ?? "",
      pageNumber: pagination.pageNumber,
      pageSize: pagination.pageSize,
      type,
      startDate,
      endDate,
    },
    {
      enabled: !!props.organizationId,
      initialData: props.statements ? use(props.statements) : undefined,
    },
  );

  const exportData = useCallback(() => {
    exportCSV({
      fileName: "statements.csv",
      data: (statements.data?.statements ?? []).map((statement) => [
        format(statement.period, "PPP"),
        statement.type,
        statement.createdBy.name,
        statement.balance.toString(),
      ]),
      headers: [
        i18n.en.table.headers.date,
        i18n.en.table.headers.type,
        i18n.en.table.headers.createdBy,
        i18n.en.table.headers.amount,
      ],
    });
  }, [statements.data?.statements]);

  return (
    <section className="flex flex-col gap-2">
      <div className="flex items-center">
        <Tabs
          value={period}
          onValueChange={useCallback(
            (value: string) => setPeriod(value as Period),
            [],
          )}
        >
          <TabsList>
            <TabsTrigger value="month">{i18n.en.actions.month}</TabsTrigger>
            <TabsTrigger value="year">{i18n.en.actions.year}</TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="ml-auto flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-7 gap-1 text-sm">
                <ListFilterIcon className="size-3.5" />
                <span className="sr-only sm:not-sr-only">
                  {i18n.en.actions.filter}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>
                <span className="text-sm font-semibold">
                  {i18n.en.filterBy}
                </span>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={type === "FUNDING"}
                onCheckedChange={useCallback(
                  (checked: boolean) =>
                    checked ? setType("FUNDING") : setType(undefined),
                  [],
                )}
              >
                {i18n.en.actions.funding}
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={type === "PAYMENT"}
                onCheckedChange={useCallback(
                  (checked: boolean) =>
                    checked ? setType("PAYMENT") : setType(undefined),
                  [],
                )}
              >
                {i18n.en.actions.payments}
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button
            size="sm"
            variant="outline"
            className="h-7 gap-1 text-sm"
            onClick={exportData}
          >
            <FileIcon className="size-3.5" />
            <span className="sr-only sm:not-sr-only">
              {i18n.en.actions.export}
            </span>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="px-7">
          <CardTitle>{i18n.en.title}</CardTitle>
          <CardDescription>{i18n.en.description}</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{i18n.en.table.headers.date}</TableHead>
                <TableHead>{i18n.en.table.headers.type}</TableHead>
                <TableHead>{i18n.en.table.headers.createdBy}</TableHead>
                <TableHead className="text-right">
                  {i18n.en.table.headers.amount}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {statements.isLoading ? (
                new Array(pagination.pageSize).fill(null).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell colSpan={4} className="text-center">
                      <div className="h-10 w-full">
                        <Skeleton className="size-full" />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (statements.data?.statements.length ?? 0) > 0 ? (
                statements.data?.statements.map((statement) => (
                  <TableRow key={statement.id}>
                    <TableCell>
                      <p className="text-sm text-muted-foreground">
                        {format(statement.period, "PPP")}
                      </p>
                    </TableCell>
                    <TableCell>
                      <Badge className="text-xs" variant="muted">
                        {statement.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {statement.createdBy.name}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className="text-lg font-semibold">
                        <Currency amount={statement.balance} />
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="min-h-60 text-center">
                    <EmptyList title={i18n.en.noStatements} />
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </section>
  );
}

export interface PreviewStatementsProps {
  loading?: boolean;
  organizationId?: string;
  statements?: Promise<RouterOutputs["statements"]["getMany"]>;
}

export default function PreviewStatements(props: PreviewStatementsProps) {
  const { searchParams } = useSearchParamsContext();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });

  const statements = api.statements.getMany.useQuery(
    {
      organizationId: props.organizationId ?? "",
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      type: searchParams["statement-type"] as unknown as StatementType,
    },
    {
      enabled: !!props.organizationId && !props.loading,
      initialData: props.statements ? use(props.statements) : undefined,
    },
  );

  return (
    <div>
      {statements.error && <ErrorFallback error={statements.error} />}
      <ListStatements
        loading={props.loading || statements.isLoading}
        statements={statements.data}
        pagination={pagination}
        setPagination={setPagination}
      />
    </div>
  );
}
