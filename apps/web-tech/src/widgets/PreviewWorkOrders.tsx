"use client";

import { Suspense, use, useMemo, useState } from "react";
import Link from "next/link";
import {
  addDays,
  addWeeks,
  endOfDay,
  format,
  startOfDay,
  subDays,
  subWeeks,
} from "date-fns";
import {
  ArrowUpRight,
  CirclePlusIcon,
  PlusCircleIcon,
  Settings2Icon,
} from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import type { Address, WorkOrderStatus } from "@axa/database-tech";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { displayDateForTimeZone } from "@axa/ui/lib/dates";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { SearchParams } from "@axa/ui/search";
import EmptyList from "@axa/ui/shared/EmptyList";
import { DataTable, useDataTable } from "@axa/ui/tables";
import { dataTableColumns } from "@axa/ui/tables/columns";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "@axa/ui/tables/helpers";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import OrderRangeFilter from "@/components/shared/OrderRangeFilter";
import OrderStatusFilter from "@/components/shared/OrderStatusFilter";
import { OrderPriorityIcon } from "@/components/symbols/OrderPriority";
import OrderStatus from "@/components/symbols/OrderStatus";
import { useUser } from "@/contexts/User";
import { WorkOrderMenu } from "@/widgets/actions/work-order/work-order";

const i18n = {
  en: {
    noOrders: "There are no work orders",
    description: "Work orders are tasks that need to be completed.",
    pendingAssign: "Pending Assignment",
    pendingSchedule: "Pending Scheduling",
    actions: {
      tableSettings: "Table Settings",
      add: "Create Work Order",
    },
    headers: {
      summary: "Summary",
      status: "Status",
      priority: "Priority",
      organization: "Organization",
      provider: "Technician",
      schedule: "Service Date",
    },
  },
  links: {
    order: "/app/orders/[id]",
    new: "/app/orders/new",
  },
};

export type WorkOrdersData = RouterOutputs["orders"]["getMany"]["orders"];

export interface PreviewWorkOrdersProps {
  loading?: boolean;
  orders?: Promise<RouterOutputs["orders"]["getMany"]>;
}

export function WorkOrdersWidget(props: PreviewWorkOrdersProps) {
  const user = useUser();

  const [status, setStatus] = useState<WorkOrderStatus | "ALL">("ALL");
  const [dateRange, setDateRange] = useState<string>("7");

  const { startDate, endDate } = useMemo(() => {
    const today = new Date();
    const startDate = startOfDay(today);
    const endDate = endOfDay(today);

    switch (dateRange) {
      default:
      case "0": {
        return {
          startDate,
          endDate,
        };
      }
      case "1": {
        return {
          startDate: addDays(startDate, 1),
          endDate: addDays(endDate, 1),
        };
      }
      case "-1": {
        return {
          startDate: subDays(startDate, 1),
          endDate: subDays(endDate, 1),
        };
      }
      case "7": {
        return {
          startDate: startDate,
          endDate: addWeeks(endDate, 1),
        };
      }
      case "-7": {
        return {
          startDate: subWeeks(startDate, 1),
          endDate: startDate,
        };
      }
    }
  }, [dateRange]);

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  const orders = api.orders.getMany.useQuery(
    {
      status: status === "ALL" ? [] : [status],
      startDate,
      endDate,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      include: {
        organization: true,
        schedule: true,
        location: true,
        providers: true,
      },
    },
    {
      enabled: !props.loading,
      initialData: props.orders ? use(props.orders) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || orders.isLoading;

  const { table } = useDataTable<WorkOrdersData[number], WorkOrdersData>({
    data: orders.data?.orders ?? [],
    rowCount: orders.data?.total,
    pagination,
    setPagination,
    manualPagination: true,
    columns: useMemo(
      () =>
        dataTableColumns<WorkOrdersData[number], WorkOrdersData>([
          {
            id: "priority",
            accessorKey: "priority",
            meta: {
              className: "w-24 max-w-24",
            },
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.priority}
                className="me-auto flex w-24 max-w-24 items-center justify-start ps-2"
              />
            ),
            cell: ({ row }) => {
              return (
                <div className="me-auto flex w-16 max-w-16 items-center justify-center">
                  <OrderPriorityIcon priority={row.getValue("priority")} />
                </div>
              );
            },
          },
          {
            id: "status",
            accessorKey: "status",
            meta: {
              className: "w-32 max-w-32",
            },
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.status}
              />
            ),
            cell: ({ row }) => <OrderStatus status={row.getValue("status")} />,
          },
          {
            accessorKey: "summary",
            meta: {
              className: "flex-1",
            },
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.summary}
              />
            ),
            cell: ({ row }) => {
              const location = row.original.location as
                | (Location & {
                    address: Address;
                  })
                | null;
              return (
                <div className="flex flex-col">
                  <Link
                    href={i18n.links.order.replace("[id]", row.original.id)}
                    className="w-full max-w-[320px] truncate font-semibold hover:text-primary"
                  >
                    {row.getValue("summary")}
                  </Link>

                  <p className="max-w-[320px] truncate text-xs text-muted-foreground">
                    {row.original.scope}
                  </p>
                  <p className="max-w-[320px] truncate text-xs text-muted-foreground">
                    {location?.address.formatted}
                  </p>
                </div>
              );
            },
          },
          {
            id: "provider",
            accessorKey: "providers",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.provider}
              />
            ),
            cell: ({ row }) => {
              const [provider] = row.original.providers;

              return (
                <div className="flex w-fit flex-col">
                  <div className="w-full font-semibold hover:text-primary">
                    {provider ? (
                      <div>
                        {user.isInternal ? (
                          <Link
                            href={`/app/providers/${provider.id}`}
                            className="font-semibold transition-colors hover:text-primary"
                          >
                            <ContactName
                              showCopyButton={false}
                              name={[
                                provider.firstName,
                                provider.lastName,
                              ].join(" ")}
                            />
                          </Link>
                        ) : (
                          <ContactName
                            showCopyButton={false}
                            name={[provider.firstName, provider.lastName].join(
                              " ",
                            )}
                          />
                        )}

                        <ContactPhone
                          phone={provider.phone}
                          className="text-sm text-muted-foreground"
                        />
                      </div>
                    ) : (
                      <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                        {i18n.en.pendingAssign}
                      </div>
                    )}
                  </div>
                </div>
              );
            },
            filterFn: (row, id, value: string) =>
              value.includes(row.getValue(id)),
          },
          {
            id: "schedule",
            accessorKey: "schedule.shifts",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.schedule}
              />
            ),
            cell: ({ row }) => {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              const [shift] = row.getValue("schedule") as
                | {
                    date?: Date;
                    startDate?: Date;
                    endDate?: Date;
                    type: "SERVICE_WINDOW" | "SERVICE_DATE";
                    timeZone?: string;
                  }[]
                | [];

              const endDate =
                shift?.type === "SERVICE_WINDOW" ? shift.endDate : null;
              const startDate =
                shift?.type === "SERVICE_WINDOW"
                  ? shift.startDate
                  : shift?.date;

              return shift ? (
                <div className="flex w-fit flex-col items-start">
                  <span className="text-nowrap">
                    {format(
                      displayDateForTimeZone(
                        startDate ?? new Date(),
                        shift.timeZone ?? "local",
                      ),
                      "PPP",
                    )}
                  </span>
                  {endDate && (
                    <>
                      <span className="w-full text-center text-muted-foreground">
                        to
                      </span>
                      <span className="text-nowrap">
                        {format(
                          displayDateForTimeZone(
                            endDate,
                            shift.timeZone ?? "local",
                          ),
                          "PPP",
                        )}
                      </span>
                    </>
                  )}
                </div>
              ) : (
                <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                  {i18n.en.pendingSchedule}
                </div>
              );
            },
            filterFn: (row, id, value: string) =>
              value.includes(row.getValue(id)),
          },
          {
            id: "organization",
            accessorKey: "organization.name",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.organization}
              />
            ),
            cell: ({ row }) => (
              <PreviewOrganization
                size="sm"
                organization={{
                  id: row.original.organization.id,
                  name: row.original.organization.name,
                  avatar: row.original.organization.avatar,
                }}
              />
            ),
            filterFn: (row, id, value: string) =>
              value.includes(row.getValue(id)),
          },
          {
            id: "actions",
            header: ({ table }) => (
              <div className="flex items-center justify-between">
                <DataTableSettings
                  table={table}
                  variant="ghost"
                  size="icon"
                  className="ml-auto"
                  sizes={[5, 10, 25, 50]}
                >
                  <Settings2Icon
                    size="20"
                    color="currentColor"
                    aria-label={i18n.en.actions.tableSettings}
                  />
                </DataTableSettings>
              </div>
            ),
            cell: ({ row }) => (
              <div className="flex size-full items-center justify-end">
                <WorkOrderMenu id={row.original.id} variant="ghost" />
              </div>
            ),
          },
        ]),
      [user.isInternal],
    ),
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center justify-between gap-4 xl:flex-row">
          <div className="flex flex-1 items-center justify-between">
            <OrderStatusFilter
              loading={orders.isLoading}
              counts={orders.data?.count}
              className="flex-1"
              value={status}
              onValueChange={setStatus}
            />
          </div>

          <div className="flex flex-row items-center gap-2 p-2">
            <OrderRangeFilter value={dateRange} onValueChange={setDateRange} />

            <Button
              size="icon"
              variant="primary"
              className="min-w-40 gap-2 text-sm"
              asChild
            >
              <Link href="/app/orders/new">
                <CirclePlusIcon className="size-4" />
                <span>Add Work Order</span>
              </Link>
            </Button>
          </div>
        </div>

        {orders.error && <ErrorFallback error={orders.error} />}

        <Card>
          <CardHeader>
            <div className="flex flex-row items-start gap-2">
              <div className="flex-1 space-y-1">
                <CardTitle>Work Orders</CardTitle>
                <CardDescription>
                  Quick view of open work orders.
                </CardDescription>
              </div>

              <div className="flex h-full items-center justify-center">
                <Button
                  className="m-0 sm:min-w-40"
                  variant="outline"
                  size="icon"
                  asChild
                >
                  <Link href="/app/orders">
                    <span className="sr-only sm:not-sr-only">View All</span>
                    <ArrowUpRight className="ml-1 size-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <DataTable
              loading={loading}
              slots={5}
              table={table}
              rowClassName="h-[52px]"
            >
              <EmptyList
                className="min-h-[440px]"
                title={i18n.en.noOrders}
                description={i18n.en.description}
              >
                <Button size="sm" asChild className="h-7 gap-2 text-sm">
                  <Link href={i18n.links.new}>
                    <PlusCircleIcon className="size-4" />
                    <span className="sr-only md:not-sr-only">
                      {i18n.en.actions.add}
                    </span>
                  </Link>
                </Button>
              </EmptyList>
            </DataTable>
          </CardContent>

          <CardFooter className="flex flex-col gap-2 border-t pt-6">
            <DataTableSimplePagination table={table} />
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

export default function PreviewWorkOrders(props: PreviewWorkOrdersProps) {
  return (
    <Suspense fallback={<WorkOrdersWidget key="work-order-widget" loading />}>
      <SearchParams>
        <WorkOrdersWidget key="work-order-widget" {...props} />
      </SearchParams>
    </Suspense>
  );
}
