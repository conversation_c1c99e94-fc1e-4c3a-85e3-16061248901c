"use client";

import type { ColumnDef } from "@tanstack/react-table";

import { use, useMemo, useState } from "react";
import Link from "next/link";
import {
  CircleIcon,
  ListFilterIcon,
  MenuIcon,
  Settings2Icon,
} from "lucide-react";

import type { RouterOutputs } from "@axa/api-tech";
import type { TimeSheetStatus as TimeSheetStatusType } from "@axa/database-tech";
import ContactName from "@axa/ui/common/ContactName";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@axa/ui/primitives/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { useSearchParamsContext } from "@axa/ui/search";
import Currency from "@axa/ui/shared/Currency";
import EmptyList from "@axa/ui/shared/EmptyList";
import { DataTable, useDataTable } from "@axa/ui/tables";
import { dataTableColumns, selectColumn } from "@axa/ui/tables/columns";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "@axa/ui/tables/helpers";

import { api } from "@/api/client";
import { ErrorFallback } from "@/components/common/Error";
import TimeSheetStatus from "@/components/symbols/TimeSheetStatus";
import { useUser } from "@/contexts/User";
import {
  AddTimeSheet,
  TimeSheetMenu,
} from "@/widgets/actions/billing/time-sheet";

const i18n = {
  en: {
    title: "TimeSheets",
    description:
      "TimeSheets are the records for work order technicians. They contain the billing information and hours worked per shift.",
    noTimeSheets: "There are no time sheets",
    pendingAssign: "Pending Assignment",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search time sheets...",
    },
    headers: {
      name: "Name",
      provider: "Provider",
      total: "Total Amount",
      rate: "Billing Rate",
      type: "Billing Type",
      hours: "Hours",
      paymentRate: "Payment Rate",
      paymentType: "Payment Type",
      status: "Status",
    },
  },
  links: {
    billingTimeSheet: "/app/billing/time-sheets/[id]",
    timeSheet: "/app/finances/time-sheets/[id]",
  },
};

export type TimeSheetsData =
  RouterOutputs["timeSheets"]["getMany"]["timeSheets"];

export function TimeSheetsTableActions({
  table,
}: {
  table: ReturnType<
    typeof useDataTable<TimeSheetsData[number], TimeSheetsData>
  >["table"];
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={i18n.en.actions.tableActions}
        >
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute right-1.5 top-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.en.selection} ({selectionCount})
        </DropdownMenuLabel>
        {/* <DropdownMenuSeparator />
        <DropdownMenuItem disabled={isPending} onSelect={action}>
          {i18n.en.actions.action}
        </DropdownMenuItem> */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export interface PreviewTimeSheetsProps {
  billing?: boolean;
  loading?: boolean;
  invoice?: string;
  timeSheets?: Promise<RouterOutputs["timeSheets"]["getMany"]>;
}

export default function PreviewTimeSheets(props: PreviewTimeSheetsProps) {
  const user = useUser();
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const { status, setStatus } = useMemo(
    () => ({
      status: searchParams.status ?? undefined,
      setStatus: (status?: string) => setSearchParams("status", status),
    }),
    [searchParams, setSearchParams],
  );

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const timeSheets = api.timeSheets.getMany.useQuery(
    {
      invoiceId: props.invoice,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      status: status as TimeSheetStatusType,
    },
    {
      enabled:
        !props.loading &&
        !user.loading &&
        (props.billing ? !!props.invoice : true),
      initialData: props.timeSheets ? use(props.timeSheets) : undefined,
    },
  );

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || user.loading || timeSheets.isLoading;

  const { table } = useDataTable<TimeSheetsData[number], TimeSheetsData>({
    data: timeSheets.data?.timeSheets ?? [],
    rowCount: timeSheets.data?.total,
    pagination,
    setPagination,
    manualPagination: true,
    columns: useMemo(() => {
      const timeSheetLink = props.billing
        ? i18n.links.billingTimeSheet
        : i18n.links.timeSheet;
      return dataTableColumns<TimeSheetsData[number], TimeSheetsData>([
        selectColumn as ColumnDef<TimeSheetsData[number], TimeSheetsData>,
        {
          id: "status",
          accessorKey: "status",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.status}
            />
          ),
          cell: ({ row }) => (
            <TimeSheetStatus status={row.getValue("status")} />
          ),
        },
        {
          accessorKey: "id",
          enableHiding: false,
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.provider}
            />
          ),
          cell: ({ row }) => (
            <div className="flex w-fit flex-col">
              <Link
                href={timeSheetLink.replace("[id]", row.original.id)}
                className="w-full font-semibold hover:text-primary"
              >
                {row.original.provider ? (
                  <div>
                    <ContactName
                      showCopyButton={false}
                      name={[
                        row.original.provider.firstName,
                        row.original.provider.lastName,
                      ].join(" ")}
                    />
                  </div>
                ) : (
                  <div className="flex size-fit items-center justify-center text-nowrap rounded-lg border border-dashed p-1 font-medium text-muted-foreground hover:text-primary">
                    {i18n.en.pendingAssign}
                  </div>
                )}
              </Link>
            </div>
          ),
        },
        {
          id: "total",
          accessorKey: "total",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.total}
            />
          ),
          cell: ({ row }) => <Currency amount={row.getValue("total")} />,
        },
        {
          id: "type",
          accessorKey: "billingType",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.type}
            />
          ),
          cell: ({ row }) => (
            <Badge variant="outline">{row.getValue("type")}</Badge>
          ),
        },
        {
          id: "rate",
          accessorKey: "billingRate",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.rate}
            />
          ),
          cell: ({ row }) => <Currency amount={row.getValue("rate")} />,
        },
        {
          id: "hours",
          accessorKey: "hours",
          header: ({ column }) => (
            <DataTableColumnHeader
              column={column}
              title={i18n.en.headers.hours}
            />
          ),
          cell: ({ row }) => {
            return <p>{row.getValue("hours")}</p>;
          },
        },
        ...(user.isBilling
          ? ([
              {
                id: "paymentType",
                accessorKey: "paymentType",
                header: ({ column }) => (
                  <DataTableColumnHeader
                    column={column}
                    title={i18n.en.headers.paymentType}
                  />
                ),
                cell: ({ row }) => (
                  <Badge variant="outline">{row.getValue("paymentType")}</Badge>
                ),
              },
              {
                id: "paymentRate",
                accessorKey: "paymentRate",
                header: ({ column }) => (
                  <DataTableColumnHeader
                    column={column}
                    title={i18n.en.headers.paymentRate}
                  />
                ),
                cell: ({ row }) => (
                  <Currency amount={row.getValue("paymentRate")} />
                ),
              },
            ] as ColumnDef<TimeSheetsData[number], TimeSheetsData>[])
          : []),
        {
          id: "actions",
          header: ({ table }) => (
            <div className="flex size-full items-center justify-end">
              <TimeSheetsTableActions table={table} />
            </div>
          ),
          cell: ({ row }) =>
            user.isBilling && (
              <div className="flex size-full items-center justify-end">
                <TimeSheetMenu
                  billing={props.billing}
                  timeSheet={row.original}
                  variant="ghost"
                />
              </div>
            ),
        },
      ]);
    }, [user.isBilling, props.billing]),
  });

  return (
    <div className="space-y-6">
      {timeSheets.error && <ErrorFallback error={timeSheets.error} />}

      <Card>
        <CardHeader className="p-2">
          <div className="flex items-center justify-between">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <ListFilterIcon size="20" color="currentColor" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuRadioGroup
                  value={status as string | undefined}
                  onValueChange={setStatus}
                >
                  <DropdownMenuLabel>Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioItem
                    // @ts-expect-error intentionally using undefined
                    value={undefined}
                  >
                    All
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value={"PENDING"}>
                    Pending
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value={"APPROVED"}>
                    Approved
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value={"REJECTED"}>
                    Rejected
                  </DropdownMenuRadioItem>

                  <DropdownMenuRadioItem value={"ASSIGNED"}>
                    Assigned
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="border-l pl-2">
              <DataTableSettings
                table={table}
                variant="ghost"
                size="icon"
                aria-label={i18n.en.actions.tableSettings}
              >
                <Settings2Icon size="20" color="currentColor" />
              </DataTableSettings>
            </div>
          </div>
        </CardHeader>
        <CardContent className="border-t p-0">
          <DataTable loading={loading} table={table}>
            <EmptyList
              title={i18n.en.noTimeSheets}
              description={i18n.en.description}
            >
              {user.isBilling && <AddTimeSheet />}
            </EmptyList>
          </DataTable>
        </CardContent>

        <CardFooter className="flex flex-col gap-2 border-t pt-6">
          <DataTableSimplePagination table={table} />
        </CardFooter>
      </Card>
    </div>
  );
}
