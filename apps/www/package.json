{"name": "@axa/www", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .next .turbo node_modules", "dev": "pnpm with-env next dev", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --", "storybook": "storybook dev -p 6008", "storybook:ci": "storybook dev -p 6008 --no-open --ci", "storybook:build": "storybook build", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@axa/lib": "workspace:*", "@axa/ui": "workspace:*", "@next/third-parties": "^15.3.3", "@t3-oss/env-nextjs": "^0.13.6", "geist": "^1.4.2", "next": "^15.3.3", "react": "19.1.0", "react-dom": "19.1.0", "superjson": "^2.2.2", "zod": "^3.25.42"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/storybook-config": "workspace:*", "@axa/tailwind-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@storybook/nextjs-vite": "^9.0.1", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitest/browser": "^3.1.4", "dotenv-cli": "^8.0.0", "eslint": "^9.28.0", "jiti": "^2.4.2", "playwright": "^1.52.0", "prettier": "^3.5.3", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "prettier": "@axa/prettier-config"}