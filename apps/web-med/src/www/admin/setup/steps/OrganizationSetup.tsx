import {
  CheckCircledIcon,
  ExternalLinkIcon,
  InfoCircledIcon,
} from "@radix-ui/react-icons";

import { Alert, AlertDescription, AlertTitle } from "@axa/ui/primitives/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";

// Clerk data types based on API response structure
interface ClerkUser {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string;
  primaryEmailAddress?: {
    emailAddress: string;
  } | null;
  primaryPhoneNumber?: {
    phoneNumber: string;
  } | null;
  publicMetadata?: {
    role?: string;
    organizationId?: string;
    providerId?: string;
  };
}

interface ClerkOrganization {
  id: string;
  name: string;
  imageUrl?: string;
  publicMetadata?: {
    managerId?: string;
    parentId?: string;
    type?: string;
  };
}

interface OrganizationSetupProps {
  loading?: boolean;
  error?: string;
  availableUsers?: ClerkUser[];
  availableOrganizations?: ClerkOrganization[];
}

const i18n = {
  en: {
    status: {
      ready: "Ready to sync",
      missing: "Setup required",
      organizations: {
        found: "Internal organizations found",
        missing: "No internal organizations found",
        instruction:
          "Go to your Clerk dashboard and create an organization with type 'INTERNAL'",
      },
      users: {
        found: "Admin users found",
        missing: "No admin users found",
        instruction:
          "Go to your Clerk dashboard and create users with roles 'INTERNAL', 'BILLING', or 'ADMIN'",
      },
    },
    actions: {
      goToClerk: "Open Clerk Dashboard",
    },
    clerk: {
      title: "Clerk Dashboard Setup",
      description:
        "You need to set up the following in your Clerk dashboard before proceeding:",
      steps: {
        organizations:
          "Create at least one organization with publicMetadata.type = 'INTERNAL'",
        users:
          "Create at least one user with publicMetadata.role = 'INTERNAL', 'BILLING', or 'ADMIN'",
      },
    },
  },
};

export function OrganizationSetup({
  loading = false,
  error,
  availableUsers = [],
  availableOrganizations = [],
}: OrganizationSetupProps) {
  const hasOrganizations = availableOrganizations.length > 0;
  const hasUsers = availableUsers.length > 0;
  const canProceed = hasOrganizations && hasUsers;

  const getUserDisplayName = (user: ClerkUser): string => {
    const firstName = user.firstName || "";
    const lastName = user.lastName || "";
    const fullName = `${firstName} ${lastName}`.trim();
    return fullName || user.primaryEmailAddress?.emailAddress || "Unknown User";
  };

  const getUserInitials = (user: ClerkUser): string => {
    const firstName = user.firstName?.[0] || "";
    const lastName = user.lastName?.[0] || "";
    return (firstName + lastName).toUpperCase() || "U";
  };

  const openClerkDashboard = () => {
    window.open("https://dashboard.clerk.com", "_blank");
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Status Alert */}
      {!loading && (
        <Alert variant={canProceed ? "default" : "destructive"}>
          {canProceed ? (
            <CheckCircledIcon className="h-4 w-4" />
          ) : (
            <InfoCircledIcon className="h-4 w-4" />
          )}
          <AlertTitle>
            {canProceed ? i18n.en.status.ready : i18n.en.status.missing}
          </AlertTitle>
          <AlertDescription>
            {canProceed
              ? "All required data found. You can proceed with the next step."
              : "Missing required organizations or users. Please set them up in Clerk first."}
          </AlertDescription>
        </Alert>
      )}

      {/* Setup Instructions */}
      {!loading && !canProceed && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {i18n.en.clerk.title}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={openClerkDashboard}
                className="ml-auto"
              >
                <ExternalLinkIcon className="mr-2 h-4 w-4" />
                {i18n.en.actions.goToClerk}
              </Button>
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {i18n.en.clerk.description}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-0.5 h-2 w-2 rounded-full bg-primary" />
                <div>
                  <p className="text-sm font-medium">Organizations</p>
                  <p className="text-sm text-muted-foreground">
                    {i18n.en.clerk.steps.organizations}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="mt-0.5 h-2 w-2 rounded-full bg-primary" />
                <div>
                  <p className="text-sm font-medium">Users</p>
                  <p className="text-sm text-muted-foreground">
                    {i18n.en.clerk.steps.users}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-32 w-full" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Organizations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Organizations</span>
                <Badge variant="outline">
                  {availableOrganizations.length} found
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {hasOrganizations ? (
                <div className="space-y-3">
                  {availableOrganizations.map((org) => (
                    <div
                      key={org.id}
                      className="flex items-center gap-3 rounded-lg border p-3"
                    >
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={org.imageUrl} alt={org.name} />
                        <AvatarFallback>
                          {org.name.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">{org.name}</div>
                        <div className="text-sm text-muted-foreground">
                          Type: {org.publicMetadata?.type || "Unknown"}
                        </div>
                      </div>
                      <Badge variant="secondary">
                        {org.publicMetadata?.type || "Unknown"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-lg border border-dashed p-4 text-center">
                  <p className="text-sm text-muted-foreground">
                    {i18n.en.status.organizations.missing}
                  </p>
                  <p className="mt-1 text-xs text-muted-foreground">
                    {i18n.en.status.organizations.instruction}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Users */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Admin Users</span>
                <Badge variant="outline">{availableUsers.length} found</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {hasUsers ? (
                <div className="space-y-3">
                  {availableUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center gap-3 rounded-lg border p-3"
                    >
                      <Avatar className="h-10 w-10">
                        <AvatarImage
                          src={user.imageUrl}
                          alt={getUserDisplayName(user)}
                        />
                        <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">
                          {getUserDisplayName(user)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {user.primaryEmailAddress?.emailAddress}
                        </div>
                      </div>
                      <Badge variant="secondary">
                        {user.publicMetadata?.role || "Unknown"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-lg border border-dashed p-4 text-center">
                  <p className="text-sm text-muted-foreground">
                    {i18n.en.status.users.missing}
                  </p>
                  <p className="mt-1 text-xs text-muted-foreground">
                    {i18n.en.status.users.instruction}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
