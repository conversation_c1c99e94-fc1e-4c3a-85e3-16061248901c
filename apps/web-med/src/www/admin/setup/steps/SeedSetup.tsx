import { useState } from "react";

import { <PERSON><PERSON> } from "@axa/ui/primitives/button";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@axa/ui/primitives/card";
import { Label } from "@axa/ui/primitives/label";
import { Switch } from "@axa/ui/primitives/switch";

interface SeedData {
  // Import defaults
  roles: boolean;
  specialties: boolean;
  // Sample data
  provider: boolean;
  hiringOrganization: boolean;
  facility: boolean;
}

interface SeedSetupProps {
  initialData: SeedData;
  onSubmit: (data: SeedData) => Promise<boolean>;
  error?: string;
}

const i18n = {
  en: {
    defaults: {
      title: "Default Configuration",
      description:
        "Import standard medical roles and specialties into your system.",
      roles: {
        title: "Medical Roles",
        description: "Import standard medical roles (Doctor, Nurse, etc.)",
      },
      specialties: {
        title: "Medical Specialties",
        description: "Import medical specialties (Cardiology, Neurology, etc.)",
      },
    },
    sampleData: {
      title: "Sample Data",
      description:
        "Create sample entities to help you explore the system features.",
      provider: {
        title: "Sample Provider",
        description:
          "Creates a sample medical provider with basic profile information",
      },
      hiringOrganization: {
        title: "Sample Hiring Organization",
        description:
          "Creates a sample hiring organization for testing recruitment features",
      },
      facility: {
        title: "Sample Facility",
        description:
          "Creates a sample medical facility with location and services",
      },
    },
  },
};

export function SeedSetup({ initialData, onSubmit, error }: SeedSetupProps) {
  const [data, setData] = useState<SeedData>(initialData);

  // Update data when switches change and trigger validation
  const updateData = async (key: keyof SeedData, value: boolean) => {
    const newData = { ...data, [key]: value };
    setData(newData);

    // Trigger validation with updated data
    await onSubmit(newData);
  };

  return (
    <div className="space-y-6">
      {error && (
        <div className="rounded-md bg-destructive/15 p-4 text-destructive">
          {error}
        </div>
      )}

      <div className="space-y-6">
        {/* Default Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.defaults.title}</CardTitle>
            <p className="text-sm text-muted-foreground">
              {i18n.en.defaults.description}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-1">
                <Label className="text-base font-medium">
                  {i18n.en.defaults.roles.title}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {i18n.en.defaults.roles.description}
                </p>
              </div>
              <Switch
                checked={data.roles}
                onCheckedChange={(checked) => updateData("roles", checked)}
              />
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-1">
                <Label className="text-base font-medium">
                  {i18n.en.defaults.specialties.title}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {i18n.en.defaults.specialties.description}
                </p>
              </div>
              <Switch
                checked={data.specialties}
                onCheckedChange={(checked) =>
                  updateData("specialties", checked)
                }
              />
            </div>
          </CardContent>
        </Card>

        {/* Sample Data */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.sampleData.title}</CardTitle>
            <p className="text-sm text-muted-foreground">
              {i18n.en.sampleData.description}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-1">
                <Label className="text-base font-medium">
                  {i18n.en.sampleData.provider.title}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {i18n.en.sampleData.provider.description}
                </p>
              </div>
              <Switch
                checked={data.provider}
                onCheckedChange={(checked) => updateData("provider", checked)}
              />
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-1">
                <Label className="text-base font-medium">
                  {i18n.en.sampleData.hiringOrganization.title}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {i18n.en.sampleData.hiringOrganization.description}
                </p>
              </div>
              <Switch
                checked={data.hiringOrganization}
                onCheckedChange={(checked) =>
                  updateData("hiringOrganization", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-1">
                <Label className="text-base font-medium">
                  {i18n.en.sampleData.facility.title}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {i18n.en.sampleData.facility.description}
                </p>
              </div>
              <Switch
                checked={data.facility}
                onCheckedChange={(checked) => updateData("facility", checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
