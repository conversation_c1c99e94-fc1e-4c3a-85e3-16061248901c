import { useState } from "react";
import {
  CheckCircledIcon,
  ExclamationTriangleIcon,
  InfoCircledIcon,
} from "@radix-ui/react-icons";

import { Alert, AlertDescription, AlertTitle } from "@axa/ui/primitives/alert";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";

// Clerk data types
interface ClerkUser {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  imageUrl?: string;
  primaryEmailAddress?: {
    emailAddress: string;
  } | null;
  publicMetadata?: {
    role?: string;
    organizationId?: string;
    providerId?: string;
  };
}

interface ClerkOrganization {
  id: string;
  name: string;
  imageUrl?: string;
  publicMetadata?: {
    managerId?: string;
    parentId?: string;
    type?: string;
  };
}

interface ReviewSetupProps {
  seed: {
    // Import defaults
    roles: boolean;
    specialties: boolean;
    // Sample data
    provider: boolean;
    hiringOrganization: boolean;
    facility: boolean;
  };
  onSubmit: () => Promise<void>;
  error?: string;
  availableUsers?: ClerkUser[];
  availableOrganizations?: ClerkOrganization[];
  // Add sync mutations
  onSyncOrganizations?: () => Promise<void>;
  onSyncUsers?: () => Promise<void>;
  // Add sync status
  syncStatus?: {
    organizations: boolean;
    users: boolean;
  };
}

const i18n = {
  en: {
    actionRequired: {
      title: "Action Required",
      description:
        "You must sync both organizations and users before completing setup",
      instructions:
        "Click the sync buttons below to import your Clerk data into the platform database.",
    },
    ready: {
      title: "Ready to Complete",
      description:
        "All required data has been synced. You can now complete the setup process.",
    },
    clerk: {
      title: "Clerk Data",
      description: "Sync your organizations and users to the database",
      organizations: "Organizations",
      users: "Admin Users",
      none: "No data available",
      sync: {
        organizations: "Sync Organizations",
        users: "Sync Users",
        syncing: "Syncing...",
        synced: "Synced",
      },
    },
    defaults: {
      title: "Default Configuration",
      roles: "Medical Roles",
      specialties: "Medical Specialties",
      enabled: "Will be imported",
      disabled: "Will not be imported",
    },
    sampleData: {
      title: "Sample Data",
      provider: "Sample Provider",
      hiringOrganization: "Sample Hiring Organization",
      facility: "Sample Facility",
      willBeCreated: "Will be created",
      willNotBeCreated: "Will not be created",
    },
  },
};

export function ReviewSetup({
  seed,
  onSubmit,
  error,
  availableUsers = [],
  availableOrganizations = [],
  onSyncOrganizations,
  onSyncUsers,
  syncStatus = { organizations: false, users: false },
}: ReviewSetupProps) {
  const [syncing, setSyncing] = useState({
    organizations: false,
    users: false,
  });

  const allDataSynced = syncStatus.organizations && syncStatus.users;

  const handleSyncOrganizations = async () => {
    if (!onSyncOrganizations) return;

    try {
      setSyncing((prev) => ({ ...prev, organizations: true }));
      await onSyncOrganizations();
    } catch (error) {
      console.error("Failed to sync organizations:", error);
    } finally {
      setSyncing((prev) => ({ ...prev, organizations: false }));
    }
  };

  const handleSyncUsers = async () => {
    if (!onSyncUsers) return;

    try {
      setSyncing((prev) => ({ ...prev, users: true }));
      await onSyncUsers();
    } catch (error) {
      console.error("Failed to sync users:", error);
    } finally {
      setSyncing((prev) => ({ ...prev, users: false }));
    }
  };

  const getUserDisplayName = (user: ClerkUser): string => {
    const firstName = user.firstName || "";
    const lastName = user.lastName || "";
    const fullName = `${firstName} ${lastName}`.trim();
    return fullName || user.primaryEmailAddress?.emailAddress || "Unknown User";
  };

  const getUserInitials = (user: ClerkUser): string => {
    const firstName = user.firstName?.[0] || "";
    const lastName = user.lastName?.[0] || "";
    return (firstName + lastName).toUpperCase() || "U";
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Action Required or Ready Status */}
      {!allDataSynced ? (
        <Alert className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
          <ExclamationTriangleIcon className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertTitle className="text-amber-800 dark:text-amber-200">
            {i18n.en.actionRequired.title}
          </AlertTitle>
          <AlertDescription className="text-amber-700 dark:text-amber-300">
            {i18n.en.actionRequired.description}
            <br />
            <span className="text-sm">
              {i18n.en.actionRequired.instructions}
            </span>
          </AlertDescription>
        </Alert>
      ) : (
        <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CheckCircledIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle className="text-green-800 dark:text-green-200">
            {i18n.en.ready.title}
          </AlertTitle>
          <AlertDescription className="text-green-700 dark:text-green-300">
            {i18n.en.ready.description}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        {/* Clerk Data Summary */}
        <Card
          className={
            !allDataSynced ? "ring-2 ring-amber-200 dark:ring-amber-800" : ""
          }
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {i18n.en.clerk.title}
              {allDataSynced && (
                <CheckCircledIcon className="h-5 w-5 text-green-500" />
              )}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {i18n.en.clerk.description}
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Organizations */}
            <div>
              <div className="mb-3 flex items-center justify-between">
                <h4 className="flex items-center gap-2 font-medium">
                  {i18n.en.clerk.organizations}
                  <Badge variant="outline">
                    {availableOrganizations.length} found
                  </Badge>
                  {syncStatus.organizations && (
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                    >
                      <CheckCircledIcon className="mr-1 h-3 w-3" />
                      Synced
                    </Badge>
                  )}
                </h4>
                <Button
                  onClick={handleSyncOrganizations}
                  disabled={syncing.organizations || syncStatus.organizations}
                  size="sm"
                  variant={syncStatus.organizations ? "secondary" : "outline"}
                  className={
                    !syncStatus.organizations
                      ? "border-amber-600 bg-amber-600 text-white hover:bg-amber-700"
                      : ""
                  }
                >
                  {syncing.organizations ? (
                    <span className="flex items-center gap-2">
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      {i18n.en.clerk.sync.syncing}
                    </span>
                  ) : syncStatus.organizations ? (
                    <>
                      <CheckCircledIcon className="mr-2 h-4 w-4" />
                      {i18n.en.clerk.sync.synced}
                    </>
                  ) : (
                    i18n.en.clerk.sync.organizations
                  )}
                </Button>
              </div>
              {availableOrganizations.length > 0 ? (
                <div className="space-y-2">
                  {availableOrganizations.map((org) => (
                    <div
                      key={org.id}
                      className="flex items-center gap-3 rounded-lg border p-3"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={org.imageUrl} alt={org.name} />
                        <AvatarFallback>
                          {org.name.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{org.name}</div>
                        <div className="text-xs text-muted-foreground">
                          Type: {org.publicMetadata?.type || "Unknown"}
                        </div>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {org.publicMetadata?.type || "Unknown"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  {i18n.en.clerk.none}
                </p>
              )}
            </div>

            {/* Users */}
            <div>
              <div className="mb-3 flex items-center justify-between">
                <h4 className="flex items-center gap-2 font-medium">
                  {i18n.en.clerk.users}
                  <Badge variant="outline">{availableUsers.length} found</Badge>
                  {syncStatus.users && (
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                    >
                      <CheckCircledIcon className="mr-1 h-3 w-3" />
                      Synced
                    </Badge>
                  )}
                </h4>
                <Button
                  onClick={handleSyncUsers}
                  disabled={syncing.users || syncStatus.users}
                  size="sm"
                  variant={syncStatus.users ? "secondary" : "outline"}
                  className={
                    !syncStatus.users
                      ? "border-amber-600 bg-amber-600 text-white hover:bg-amber-700"
                      : ""
                  }
                >
                  {syncing.users ? (
                    <span className="flex items-center gap-2">
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                      {i18n.en.clerk.sync.syncing}
                    </span>
                  ) : syncStatus.users ? (
                    <>
                      <CheckCircledIcon className="mr-2 h-4 w-4" />
                      {i18n.en.clerk.sync.synced}
                    </>
                  ) : (
                    i18n.en.clerk.sync.users
                  )}
                </Button>
              </div>
              {availableUsers.length > 0 ? (
                <div className="space-y-2">
                  {availableUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center gap-3 rounded-lg border p-3"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={user.imageUrl}
                          alt={getUserDisplayName(user)}
                        />
                        <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="text-sm font-medium">
                          {getUserDisplayName(user)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {user.primaryEmailAddress?.emailAddress}
                        </div>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {user.publicMetadata?.role || "Unknown"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  {i18n.en.clerk.none}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Default Types Summary */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.defaults.title}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">{i18n.en.defaults.roles}</span>
                {seed.roles ? (
                  <div className="flex items-center gap-2 text-green-500">
                    <CheckCircledIcon className="h-5 w-5" />
                    <span className="text-sm">{i18n.en.defaults.enabled}</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-yellow-500">
                    <InfoCircledIcon className="h-5 w-5" />
                    <span className="text-sm">{i18n.en.defaults.disabled}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {i18n.en.defaults.specialties}
                </span>
                {seed.specialties ? (
                  <div className="flex items-center gap-2 text-green-500">
                    <CheckCircledIcon className="h-5 w-5" />
                    <span className="text-sm">{i18n.en.defaults.enabled}</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-yellow-500">
                    <InfoCircledIcon className="h-5 w-5" />
                    <span className="text-sm">{i18n.en.defaults.disabled}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sample Data Summary */}
        <Card>
          <CardHeader>
            <CardTitle>{i18n.en.sampleData.title}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {i18n.en.sampleData.provider}
                </span>
                {seed.provider ? (
                  <div className="flex items-center gap-2 text-green-500">
                    <CheckCircledIcon className="h-5 w-5" />
                    <span className="text-sm">
                      {i18n.en.sampleData.willBeCreated}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-yellow-500">
                    <InfoCircledIcon className="h-5 w-5" />
                    <span className="text-sm">
                      {i18n.en.sampleData.willNotBeCreated}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {i18n.en.sampleData.hiringOrganization}
                </span>
                {seed.hiringOrganization ? (
                  <div className="flex items-center gap-2 text-green-500">
                    <CheckCircledIcon className="h-5 w-5" />
                    <span className="text-sm">
                      {i18n.en.sampleData.willBeCreated}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-yellow-500">
                    <InfoCircledIcon className="h-5 w-5" />
                    <span className="text-sm">
                      {i18n.en.sampleData.willNotBeCreated}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <span className="font-medium">
                  {i18n.en.sampleData.facility}
                </span>
                {seed.facility ? (
                  <div className="flex items-center gap-2 text-green-500">
                    <CheckCircledIcon className="h-5 w-5" />
                    <span className="text-sm">
                      {i18n.en.sampleData.willBeCreated}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-yellow-500">
                    <InfoCircledIcon className="h-5 w-5" />
                    <span className="text-sm">
                      {i18n.en.sampleData.willNotBeCreated}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
