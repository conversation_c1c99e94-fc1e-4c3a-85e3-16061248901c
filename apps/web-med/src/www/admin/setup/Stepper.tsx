import type { ReactNode } from "react";

import { useState } from "react";

import { But<PERSON> } from "@axa/ui/primitives/button";
import { Card } from "@axa/ui/primitives/card";
import { Progress } from "@axa/ui/primitives/progress";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@axa/ui/primitives/tabs";

export interface Step {
  id: string;
  title: string;
  description?: string;
  content: ReactNode;
  isOptional?: boolean;
  validate?: () => boolean | Promise<boolean>;
}

export interface StepperProps {
  /**
   * Title displayed at the top of the stepper
   */
  title: string;
  /**
   * Subtitle displayed below the title
   */
  subtitle?: string;
  /**
   * Array of step configurations
   */
  steps: Step[];
  /**
   * Current active step index
   */
  currentStep: number;
  /**
   * Callback when step changes
   */
  onStepChange: (step: number) => void;
  /**
   * Callback when stepper is completed
   */
  onComplete?: () => void | Promise<void>;
  /**
   * Custom labels for buttons and actions
   */
  labels?: {
    next?: string;
    back?: string;
    skip?: string;
    finish?: string;
  };
  /**
   * Whether to show the progress bar
   */
  showProgress?: boolean;
  /**
   * Custom class name for the container
   */
  className?: string;
  /**
   * Whether to allow navigation to previous steps
   */
  allowBackNavigation?: boolean;
  /**
   * Whether to allow skipping optional steps
   */
  allowSkip?: boolean;
}

// Simple step interface for progress display only
export interface SimpleStep {
  id: string;
  title: string;
  description?: string;
}

export interface StepIndicatorProps {
  steps: SimpleStep[];
  currentStep: number;
  className?: string;
}

const defaultLabels = {
  next: "Next",
  back: "Back",
  skip: "Skip",
  finish: "Finish",
};

export function Stepper({
  title,
  subtitle,
  steps,
  currentStep,
  onStepChange,
  onComplete,
  labels = defaultLabels,
  showProgress = true,
  className = "",
  allowBackNavigation = true,
  allowSkip = true,
}: StepperProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const progress = ((currentStep + 1) / steps.length) * 100;
  const isLastStep = currentStep === steps.length - 1;

  // Ensure we have a valid step
  if (currentStep < 0 || currentStep >= steps.length) {
    throw new Error(`Invalid step index: ${currentStep}`);
  }

  const currentStepData = steps[currentStep];

  const handleNext = async () => {
    try {
      setIsProcessing(true);

      // Run validation if it exists
      const canProceed = (await currentStepData?.validate?.()) ?? true;
      if (!canProceed) {
        return;
      }

      if (isLastStep) {
        await onComplete?.();
      } else {
        onStepChange(currentStep + 1);
      }
    } catch (error) {
      console.error("Error in stepper navigation:", error);
      // Allow the parent component to handle the error display
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      onStepChange(currentStep - 1);
    }
  };

  const handleSkip = () => {
    if (currentStepData?.isOptional && !isLastStep) {
      onStepChange(currentStep + 1);
    }
  };

  return (
    <div className={className}>
      <Card className="p-6">
        <div className="mb-8 text-center">
          <h1 className="mb-2 text-3xl font-bold">{title}</h1>
          {subtitle && <p className="text-muted-foreground">{subtitle}</p>}
        </div>

        <Tabs value={currentStepData?.id} className="w-full">
          <div className="flex flex-col gap-4">
            <TabsList
              className="grid w-full"
              style={{
                gridTemplateColumns: `repeat(${steps.length}, minmax(0, 1fr))`,
              }}
            >
              {steps.map((step, index) => (
                <TabsTrigger
                  key={step.id}
                  value={step.id}
                  disabled={index !== currentStep}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {step.title}
                </TabsTrigger>
              ))}
            </TabsList>
            {showProgress && <Progress value={progress} className="mb-8" />}
          </div>

          {steps.map((step) => (
            <TabsContent key={step.id} value={step.id} className="mt-6">
              <div className="space-y-4">
                {step.description && (
                  <div>
                    <h2 className="text-2xl font-bold">{step.title}</h2>
                    <p className="text-muted-foreground">{step.description}</p>
                  </div>
                )}
                {step.content}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        <div className="mt-8 flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={!allowBackNavigation || currentStep === 0 || isProcessing}
          >
            {labels.back || defaultLabels.back}
          </Button>
          <div className="space-x-2">
            {allowSkip && currentStepData?.isOptional && !isLastStep && (
              <Button
                variant="ghost"
                onClick={handleSkip}
                disabled={isProcessing}
              >
                {labels.skip || defaultLabels.skip}
              </Button>
            )}
            <Button
              onClick={handleNext}
              disabled={isProcessing}
              className={isProcessing ? "cursor-not-allowed" : ""}
            >
              {isProcessing ? (
                <span className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {isLastStep ? "Processing..." : "Validating..."}
                </span>
              ) : isLastStep ? (
                labels.finish || defaultLabels.finish
              ) : (
                labels.next || defaultLabels.next
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

// Simple step indicator component for progress display only
export function StepIndicator({
  steps,
  currentStep,
  className = "",
}: StepIndicatorProps) {
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className={className}>
      <div className="space-y-4">
        <div className="grid w-full grid-cols-3 gap-2">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`rounded-lg p-3 text-center transition-colors ${
                index === currentStep
                  ? "bg-primary text-primary-foreground"
                  : index < currentStep
                    ? "bg-muted text-muted-foreground"
                    : "bg-muted/50 text-muted-foreground/50"
              }`}
            >
              <div className="text-sm font-medium">{step.title}</div>
              {step.description && (
                <div className="text-xs opacity-80">{step.description}</div>
              )}
            </div>
          ))}
        </div>
        <Progress value={progress} />
      </div>
    </div>
  );
}
