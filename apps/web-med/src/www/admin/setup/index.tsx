"use client";

import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { CheckCircledIcon } from "@radix-ui/react-icons";

import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@axa/ui/primitives/card";

import type { Step } from "./Stepper";

import { api } from "@/api/client";
import { useUser } from "@/components/contexts/User";

import { Stepper } from "./Stepper";
import { OrganizationSetup } from "./steps/OrganizationSetup";
import { ReviewSetup } from "./steps/ReviewSetup";
import { SeedSetup } from "./steps/SeedSetup";

interface SetupState {
  seed: {
    // Import defaults
    roles: boolean;
    specialties: boolean;
    // Sample data
    provider: boolean;
    hiringOrganization: boolean;
    facility: boolean;
  };
  sync: {
    organizations: boolean;
    users: boolean;
  };
}

const initialState: SetupState = {
  seed: {
    roles: true,
    specialties: true,
    provider: false,
    hiringOrganization: false,
    facility: false,
  },
  sync: {
    organizations: false,
    users: false,
  },
};

export default function SetupPage() {
  return (
    <Suspense fallback={<div>Loading setup...</div>}>
      <SetupView />
    </Suspense>
  );
}

export function SetupView() {
  const router = useRouter();
  const { isAdmin, loading: userLoading } = useUser();
  const [currentStep, setCurrentStep] = useState(0);
  const [state, setState] = useState<SetupState>(initialState);
  const [error, setError] = useState<string>();
  const [isCompleted, setIsCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string>();

  // Mutations
  const clerkData = api.admin.clerk.loadClerk.useMutation();
  const initializeSystem = api.admin.system.initialize.useMutation();
  const syncOrganizations =
    api.admin.clerk.syncClerkOrganizations.useMutation();
  const syncUsers = api.admin.clerk.syncClerkUsers.useMutation();

  // Load Clerk data once on component mount (only for admin users)
  useEffect(() => {
    if (userLoading || !isAdmin) return;

    // Only load if we haven't already loaded and aren't currently loading
    if (!clerkData.data && !clerkData.isPending && !clerkData.isError) {
      clerkData.mutate(undefined, {
        onError: (err) => {
          setError("Failed to load organization data");
          console.error("Clerk data loading error:", err);
        },
        onSuccess: () => {
          setError(undefined);
        },
      });
    }
  }, [userLoading, isAdmin, clerkData]);

  // Memoized filtered data
  const filteredUsers = useMemo(() => {
    if (!clerkData.data?.users) return [];

    return clerkData.data.users.filter((user) => {
      const role = user.publicMetadata?.role as string;
      return role === "INTERNAL" || role === "BILLING" || role === "ADMIN";
    });
  }, [clerkData.data?.users]);

  const filteredOrganizations = useMemo(() => {
    if (!clerkData.data?.organizations) return [];

    return clerkData.data.organizations.filter((org) => {
      const type = org.publicMetadata?.type as string;
      return type === "INTERNAL";
    });
  }, [clerkData.data?.organizations]);

  // Memoized loading state
  const isLoading = useMemo(() => {
    return (
      clerkData.isPending ||
      initializeSystem.isPending ||
      syncOrganizations.isPending ||
      syncUsers.isPending ||
      isSubmitting
    );
  }, [
    clerkData.isPending,
    initializeSystem.isPending,
    syncOrganizations.isPending,
    syncUsers.isPending,
    isSubmitting,
  ]);

  // Memoized callbacks
  const handleSeedSubmit = useCallback(async (data: SetupState["seed"]) => {
    try {
      setError(undefined);
      setState((prev) => ({ ...prev, seed: data }));
      return true; // Validation success
    } catch (err) {
      setError("Failed to save configuration");
      return false; // Validation failed
    }
  }, []);

  const handleFinalSubmit = useCallback(async () => {
    try {
      setIsSubmitting(true);
      setError(undefined);

      // 1. Initialize system defaults if selected
      if (state.seed.roles || state.seed.specialties) {
        await initializeSystem.mutateAsync();
      }

      // 2. Create sample data if requested
      if (
        state.seed.provider ||
        state.seed.hiringOrganization ||
        state.seed.facility
      ) {
        // TODO: Implement sample data creation
        console.log("Sample data creation:", {
          provider: state.seed.provider,
          hiringOrganization: state.seed.hiringOrganization,
          facility: state.seed.facility,
        });
      }

      // Mark as completed
      setIsCompleted(true);

      // Redirect to main dashboard after a brief delay to show success
      setTimeout(() => {
        router.push("/app");
      }, 2000);
    } catch (err) {
      setError("Failed to complete setup");
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, [state.seed, initializeSystem, router]);

  const handleSyncOrganizations = useCallback(async () => {
    await syncOrganizations.mutateAsync();
    setState((prev) => ({
      ...prev,
      sync: { ...prev.sync, organizations: true },
    }));
  }, [syncOrganizations]);

  const handleSyncUsers = useCallback(async () => {
    await syncUsers.mutateAsync();
    setState((prev) => ({
      ...prev,
      sync: { ...prev.sync, users: true },
    }));
  }, [syncUsers]);

  // Validation functions for each step
  const validateOrganizationStep = useCallback(async () => {
    // Check if we have both organizations and users available
    const isValid =
      filteredOrganizations.length > 0 && filteredUsers.length > 0;
    if (!isValid) {
      setValidationError(
        "Please ensure you have organizations and users configured in Clerk.",
      );
    } else {
      setValidationError(undefined);
    }
    return isValid;
  }, [filteredOrganizations.length, filteredUsers.length]);

  const validateReviewStep = useCallback(async () => {
    // Check if all required data is synced
    const isValid = state.sync.organizations && state.sync.users;
    if (!isValid) {
      setValidationError(
        "Please sync both organizations and users before completing setup.",
      );
    } else {
      setValidationError(undefined);
    }
    return isValid;
  }, [state.sync.organizations, state.sync.users]);

  // Memoized steps configuration
  const steps: Step[] = useMemo(
    () => [
      {
        id: "organization",
        title: "Organization Setup",
        description: "Configure your organization and admin user",
        validate: validateOrganizationStep,
        content: (
          <OrganizationSetup
            loading={isLoading || !clerkData.data}
            error={error}
            availableUsers={filteredUsers}
            availableOrganizations={filteredOrganizations as any}
          />
        ),
      },
      {
        id: "seed",
        title: "Configuration & Sample Data",
        description: "Choose defaults and sample data to include",
        content: (
          <SeedSetup
            initialData={state.seed}
            onSubmit={handleSeedSubmit}
            error={error}
          />
        ),
      },
      {
        id: "review",
        title: "Review",
        description: "Review and complete setup",
        validate: validateReviewStep,
        content: (
          <ReviewSetup
            seed={state.seed}
            onSubmit={handleFinalSubmit}
            error={error}
            availableUsers={filteredUsers}
            availableOrganizations={filteredOrganizations as any}
            onSyncOrganizations={handleSyncOrganizations}
            onSyncUsers={handleSyncUsers}
            syncStatus={state.sync}
          />
        ),
      },
    ],
    [
      validateOrganizationStep,
      validateReviewStep,
      isLoading,
      clerkData.data,
      error,
      filteredUsers,
      filteredOrganizations,
      state.seed,
      state.sync,
      handleSeedSubmit,
      handleFinalSubmit,
      handleSyncOrganizations,
      handleSyncUsers,
    ],
  );

  // Early returns for loading and access control
  if (userLoading) {
    return (
      <div className="mx-auto max-w-4xl space-y-8 p-6">
        <div className="text-center">
          <p className="text-muted-foreground">Checking permissions...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="mx-auto max-w-4xl space-y-8 p-6">
        <div className="space-y-4 text-center">
          <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-8">
            <h1 className="mb-2 text-3xl font-bold text-destructive">
              Access Denied
            </h1>
            <p className="text-muted-foreground">
              You must be an admin user to access the initial setup wizard.
            </p>
            <p className="mt-2 text-sm text-muted-foreground">
              Please contact your system administrator if you believe this is an
              error.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show success screen after completion
  if (isCompleted) {
    return (
      <div className="mx-auto max-w-4xl space-y-8 p-6">
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
              <CheckCircledIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-2xl text-green-800 dark:text-green-200">
              Setup Completed Successfully!
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-6 text-green-700 dark:text-green-300">
              Your AXA Medical platform has been initialized with your selected
              configuration. You will be redirected to the dashboard shortly.
            </p>
            <div className="space-y-2">
              <Button
                onClick={() => router.push("/app")}
                className="w-full bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
              >
                Go to Dashboard
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push("/app/admin/system")}
                className="w-full border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900"
              >
                View System Settings
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl space-y-8 p-6">
      {validationError && (
        <div className="rounded-lg border border-amber-200 bg-amber-50 p-4 dark:border-amber-800 dark:bg-amber-950">
          <div className="flex items-center">
            <div className="ml-3">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                {validationError}
              </p>
            </div>
          </div>
        </div>
      )}

      <Stepper
        title="Initial Setup"
        subtitle="Configure your organization and initial settings"
        steps={steps}
        currentStep={currentStep}
        onStepChange={setCurrentStep}
        onComplete={handleFinalSubmit}
        allowBackNavigation={true}
        showProgress={true}
        labels={{
          finish: isSubmitting ? "Completing Setup..." : "Complete Setup",
        }}
      />
    </div>
  );
}
