"use client";

import { FolderSyncIcon } from "lucide-react";

import AppView from "@axa/ui/layouts/AppView";
import { Button } from "@axa/ui/primitives/button";

import type { RouterError, RouterOutputs } from "@/api";
import type { api } from "@/api/client";

import { ErrorFallback } from "@/components/shared/Error";
import ListUsers from "@/components/tables/ListUsers";

import InvitationList from "./Invitations";

const i18n = {
  en: {
    title: "Users",
    description: "Users are the individuals that you work with.",
  },
};

export interface UsersViewProps {
  loading?: boolean;
  users: {
    data?: RouterOutputs["user"]["getMany"];
    error?: RouterError;
    isLoading: boolean;
  };
  invitations: {
    data?: RouterOutputs["user"]["invitations"]["getMany"];
    error?: RouterError;
    isLoading: boolean;
  };
  syncPeopleMutation: ReturnType<
    typeof api.admin.clerk.syncClerkUsers.useMutation
  >;
  syncOrganizationsMutation: ReturnType<
    typeof api.admin.clerk.syncClerkOrganizations.useMutation
  >;
  revokeUserInvitation: ReturnType<
    typeof api.user.invitations.revoke.useMutation
  >;
  sendUserInvitation: ReturnType<typeof api.user.invitations.send.useMutation>;
}

export default function Users({
  users,
  invitations,
  syncPeopleMutation,
  syncOrganizationsMutation,
  revokeUserInvitation,
  sendUserInvitation,
}: UsersViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex w-full items-center justify-end gap-2"></div>

      <div className="flex h-full flex-col gap-4">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <Button
            className="space-x-2"
            onClick={() => {
              syncPeopleMutation.mutate();
            }}
          >
            <FolderSyncIcon className="size-4" />
            <span>Sync Users</span>
          </Button>

          <Button
            className="space-x-2"
            onClick={async () => {
              await syncOrganizationsMutation.mutateAsync();
            }}
          >
            <FolderSyncIcon className="size-4" />
            <span>Sync Organizations</span>
          </Button>
        </div>

        <div className="space-y-6">
          {users.error && <ErrorFallback error={users.error} />}

          <ListUsers users={users.data} loading={users.isLoading} />
        </div>

        <InvitationList
          invitations={invitations}
          revokeUserInvitation={revokeUserInvitation}
          sendUserInvitation={sendUserInvitation}
        />
      </div>
    </AppView>
  );
}
