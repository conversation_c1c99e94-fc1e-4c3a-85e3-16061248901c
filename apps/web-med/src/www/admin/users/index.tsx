"use client";

import { Suspense, use } from "react";

import type { PersonRole } from "@axa/database-medical";
import { toast } from "@axa/ui/primitives/toast";
import {
  SearchParams,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

import type { RouterOutputs } from "@/api";

import { api } from "@/api/client";

import Users from "./Users";

export type UsersQueryResult = RouterOutputs["user"]["getMany"];
export type UsersData = UsersQueryResult["items"];

export interface UsersViewProps {
  loading?: boolean;
  users?: Promise<UsersQueryResult>;
  invitations?: Promise<RouterOutputs["user"]["invitations"]["getMany"]>;
}

export function UsersView(props: UsersViewProps) {
  const pagination = useSearchPaginationValue("user");
  const role = useSearchFilterValue<PersonRole>("role", "user");
  const query = useSearchTextValue("user");

  const users = api.user.getMany.useQuery(
    {
      query,
      // organizations: organizations ? [organizations] : undefined,
      pageNumber: pagination.pageIndex,
      pageSize: pagination.pageSize,
      roles: role ? [role] : undefined,
    },
    {
      enabled: !props.loading,
      initialData: props.users ? use(props.users) : undefined,
    },
  );

  const invitations = api.user.invitations.getMany.useQuery(
    {},
    {
      enabled: !props.loading,
      initialData: props.invitations ? use(props.invitations) : undefined,
    },
  );

  const sendUserInvitation = api.user.invitations.send.useMutation({
    onSuccess: async () => {
      await users.refetch();
      await invitations.refetch();
      toast.success("Invitation sent successfully!");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const revokeUserInvitation = api.user.invitations.revoke.useMutation({
    onSuccess: async () => {
      await users.refetch();
      await invitations.refetch();
      toast.success("Invitation revoked successfully!");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const syncPeopleMutation = api.admin.clerk.syncClerkUsers.useMutation({
    onSuccess: async () => {
      await users.refetch();
      await invitations.refetch();
      toast.success("Users synced");
    },
  });

  const syncOrganizationsMutation =
    api.admin.clerk.syncClerkOrganizations.useMutation({
      onSuccess: async () => {
        await users.refetch();
        await invitations.refetch();
        toast.success("Organizations synced");
      },
    });

  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
  const loading = props.loading || users.isLoading;

  return (
    <Users
      loading={loading}
      users={users}
      invitations={invitations}
      sendUserInvitation={sendUserInvitation}
      revokeUserInvitation={revokeUserInvitation}
      syncPeopleMutation={syncPeopleMutation}
      syncOrganizationsMutation={syncOrganizationsMutation}
    />
  );
}

export default function UsersPage({ users, invitations }: UsersViewProps) {
  return (
    <Suspense fallback={<UsersView loading />}>
      <SearchParams>
        <UsersView users={users} invitations={invitations} />
      </SearchParams>
    </Suspense>
  );
}
