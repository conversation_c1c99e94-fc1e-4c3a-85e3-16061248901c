"use client";

import { Suspense, use } from "react";

import { toast } from "@axa/ui/primitives/toast";

import type { RouterOutputs } from "@/api";

import { JobPostStatus } from "@/api";
import { api } from "@/api/client";
import {
  SearchParams,
  useSearchTextValue,
  useSearchValueResult,
} from "@/ui/search";
import Job from "@/www/organizations/job/Job";

const PAGE_SIZE = 5;

export function JobView(props: {
  loading?: boolean;
  id: string;
  job?: Promise<RouterOutputs["jobs"]["get"]>;
}) {
  const prospectSearchText = useSearchTextValue("prospect", "prospecting");
  const prospectSpecialty = useSearchValueResult("specialty", "prospecting");

  const job = api.jobs.get.useQuery(
    {
      id: props.id,
      include: {
        organization: true,
        location: true,
        department: true,
        specialties: true,
        schedule: true,
        thread: true,
        offers: true,
        applications: true,
        actions: true,
        contracts: true,
        provider: true,
        contacts: true,
        position: true,
      },
    },
    {
      enabled: !props.loading,
      initialData: props.job ? use(props.job) : undefined,
    },
  );

  const providers = api.providers.prospecting.search.useQuery(
    {
      query: prospectSearchText,
      specialties: prospectSpecialty ? [prospectSpecialty] : undefined,
      coordinates: job.data?.location
        ? {
            radius: 100, // 100 mile radius
            latitude: job.data.location.address.latitude,
            longitude: job.data.location.address.longitude,
          }
        : undefined,
      jobId: job.data?.id,
      roles: job.data?.role ? [job.data.role] : undefined,
      include: {
        person: true,
        specialties: true,
        qualifications: true,
        experiences: true,
        verification: true,
        settings: true,
        address: true,
      },
      config: {
        sorting: "score",
      },
      pageSize: PAGE_SIZE,
      pageNumber: 0,
    },
    {
      enabled:
        !job.isLoading && job.data && job.data.status !== JobPostStatus.DRAFT,
      staleTime: 30000,
      retry: 2,
    },
  );

  const applications = api.providers.prospecting.applications.useQuery(
    {
      jobId: job.data?.id ?? "",
      pageSize: PAGE_SIZE,
      pageNumber: 0,
      role: job.data?.role ?? undefined,
      include: {
        provider: true,
      },
    },
    {
      enabled:
        !job.isLoading && job.data && job.data.status !== JobPostStatus.DRAFT,
      staleTime: 30000,
      retry: 2,
    },
  );

  const offers = api.providers.prospecting.offers.useQuery(
    {
      jobId: job.data?.id ?? "",
      pageSize: PAGE_SIZE,
      pageNumber: 0,
      role: job.data?.role ?? undefined,
      include: {
        provider: true,
      },
    },
    {
      enabled:
        !job.isLoading && job.data && job.data.status !== JobPostStatus.DRAFT,
      staleTime: 30000,
      retry: 2,
    },
  );

  const update = api.jobs.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const publish = api.jobs.organization.publish.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post published successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const unpublish = api.jobs.organization.unpublish.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post unpublished successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const cancel = api.jobs.organization.cancel.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post cancelled successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const linkDepartment = api.departments.link.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post linked to department successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const unlinkDepartment = api.departments.unlink.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post unlinked from department successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const linkLocation = api.locations.link.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post linked to facility successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const unlinkLocation = api.locations.unlink.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Job post unlinked from facility successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const linkContact = api.contacts.link.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact linked to job successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const unlinkContact = api.contacts.unlink.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact unlinked from job successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const createContact = api.contacts.create.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const updateContact = api.contacts.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const deleteContact = api.contacts.delete.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Contact deleted successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const createSchedule = api.schedule.create.useMutation({
    onSuccess: () => {
      void job.refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const updateSchedule = api.schedule.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Schedule updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const createBlock = api.schedule.blocks.create.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Time block created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const updateBlock = api.schedule.blocks.update.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Time block updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const deleteBlock = api.schedule.blocks.delete.useMutation({
    onSuccess: () => {
      void job.refetch();
      toast.success("Time block deleted successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const sendOffer = api.offers.organization.send.useMutation({
    onSuccess: async () => {
      await job.refetch();
      toast.success("Offer sent successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const acceptApplication = api.applications.organization.approve.useMutation({
    onSuccess: async () => {
      await job.refetch();
      toast.success("Application accepted");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const rejectApplication = api.applications.organization.reject.useMutation({
    onSuccess: async () => {
      await job.refetch();
      toast.success("Application rejected");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const withdrawOffer = api.offers.organization.withdraw.useMutation({
    onSuccess: async () => {
      await job.refetch();
      toast.success("Offer withdrawn");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  return (
    <Job
      loading={props.loading}
      job={{
        loading: job.isLoading,
        data: job.data,
        error: job.error,
        refetch: async () => {
          await job.refetch();
        },
      }}
      providers={{
        loading: providers.isLoading,
        data: providers.data,
        error: providers.error,
        refetch: async () => {
          await providers.refetch();
        },
      }}
      applications={{
        loading: applications.isLoading,
        data: applications.data,
        error: applications.error,
        refetch: async () => {
          await applications.refetch();
        },
      }}
      offers={{
        loading: offers.isLoading,
        data: offers.data,
        error: offers.error,
        refetch: async () => {
          await offers.refetch();
        },
      }}
      update={update}
      publish={publish}
      unpublish={unpublish}
      cancel={cancel}
      linkDepartment={linkDepartment}
      unlinkDepartment={unlinkDepartment}
      linkLocation={linkLocation}
      unlinkLocation={unlinkLocation}
      linkContact={linkContact}
      unlinkContact={unlinkContact}
      createContact={createContact}
      updateContact={updateContact}
      deleteContact={deleteContact}
      createSchedule={createSchedule}
      updateSchedule={updateSchedule}
      createBlock={createBlock}
      updateBlock={updateBlock}
      deleteBlock={deleteBlock}
      sendOffer={sendOffer}
      acceptApplication={acceptApplication}
      rejectApplication={rejectApplication}
      withdrawOffer={withdrawOffer}
    />
  );
}

export default function JobPage(props: {
  id: string;
  job?: Promise<RouterOutputs["jobs"]["get"]>;
}) {
  return (
    <Suspense fallback={<JobView loading id={props.id} />}>
      <SearchParams>
        <JobView id={props.id} job={props.job} />
      </SearchParams>
    </Suspense>
  );
}
