/**
 * PROVIDER SEARCH BAR COMPONENT
 * =============================
 *
 * This component handles the header section and all search functionality
 * for the providers panel. It's a dedicated component for the search interface.
 *
 * FEATURES:
 * - Header with title and subtitle
 * - Text search input
 * - Filter dropdowns (role, join date, criteria)
 * - Results summary display
 * - Responsive layout: single row on large screens, stacked on small screens
 * - Clean separation of search UI concerns
 */

import { useState } from "react";
import { ChevronDownIcon } from "lucide-react";

import { Button } from "@axa/ui/primitives/button";
import { SearchFilter, SearchText } from "@axa/ui/search";

interface FilterOption {
  readonly label: string;
  readonly value: string;
}

export interface ProviderSearchBarProps {
  // Results data
  resultsCount: number;
  totalResults: number;
  loading: boolean;

  // Configuration
  searchNamespace: string;
}

/**
 * FILTER OPTION CONFIGURATIONS
 * ============================
 *
 * Static filter options - no need to pass as props
 */
export const ROLE_FILTER_NAME = "role";
const ROLE_FILTER_OPTIONS = [
  { label: "All Positions", value: "" },
  { label: "Physician", value: "Physician" },
  { label: "Nurse", value: "Nurse" },
  { label: "Technician", value: "Technician" },
  { label: "Therapist", value: "Therapist" },
  { label: "Assistant", value: "Assistant" },
  { label: "Specialist", value: "Specialist" },
] as const;

export const JOIN_DATE_FILTER_NAME = "joinDate";
const JOIN_DATE_OPTIONS = [
  { label: "All Time", value: "" },
  { label: "Last 30 Days", value: "30" },
  { label: "Last 90 Days", value: "90" },
  { label: "Last 6 Months", value: "180" },
] as const;

export const CRITERIA_FILTER_NAME = "criteria";
const CRITERIA_FILTER_OPTIONS = [
  { label: "All Providers", value: "" },
  { label: "Verified Only", value: "verified" },
  { label: "Available for Work", value: "available" },
  { label: "High Rated", value: "high-rated" },
] as const;

// Internationalization object
const i18n = {
  en: {
    title: "Medical Providers Network",
    subtitle: "Discover and scout medical professionals",
    searchPlaceholder: "Search providers by name or title...",
    filters: {
      role: "Position",
      joinDate: "Join Date",
      criteria: "Criteria",
    },
    results: {
      showing: "Showing {count} of {total} providers",
    },
  },
};

export default function ProviderSearchBar({
  resultsCount,
  totalResults,
  loading,
  searchNamespace = "providers",
}: ProviderSearchBarProps) {
  // Local state to track selected filter values for immediate UI feedback
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [selectedJoinDate, setSelectedJoinDate] = useState<string>("");
  const [selectedCriteria, setSelectedCriteria] = useState<string>("");

  // Transform filter options into SearchFilter group format
  const roleFilterGroup = {
    id: "role",
    label: i18n.en.filters.role,
    options: ROLE_FILTER_OPTIONS.map((option) => ({
      value: option.value || null,
      label: option.label,
    })),
  };

  const joinDateFilterGroup = {
    id: "joinDate",
    label: i18n.en.filters.joinDate,
    options: JOIN_DATE_OPTIONS.map((option) => ({
      value: option.value || null,
      label: option.label,
    })),
  };

  const criteriaFilterGroup = {
    id: "criteria",
    label: i18n.en.filters.criteria,
    options: CRITERIA_FILTER_OPTIONS.map((option) => ({
      value: option.value || null,
      label: option.label,
    })),
  };

  // Get display label for selected value
  const getSelectedLabel = (
    options: readonly FilterOption[],
    value: string,
  ) => {
    if (!value) return options[0]?.label || "All";
    return options.find((option) => option.value === value)?.label || "All";
  };

  // Handle filter changes
  const handleRoleChange = (value: string) => {
    setSelectedRole(value);
  };

  const handleJoinDateChange = (value: string) => {
    setSelectedJoinDate(value);
  };

  const handleCriteriaChange = (value: string) => {
    setSelectedCriteria(value);
  };

  return (
    <div className="space-y-6">
      {/* Search and Filter Section - Responsive Grid Layout */}
      <div className="grid grid-cols-2 gap-4 lg:grid-cols-6">
        {/* Text Search - Full width on small (span 2), 1/2 width on large (span 3) */}
        <div className="col-span-2 lg:col-span-3">
          <SearchText
            placeholder={i18n.en.searchPlaceholder}
            group={searchNamespace}
            name="query"
            className="w-full"
          />
        </div>

        {/* Role Filter - Full width on small (span 2), 1/6 width on large (span 1) */}
        <div className="col-span-2 lg:col-span-1">
          <SearchFilter
            name={ROLE_FILTER_NAME}
            groups={[{ ...roleFilterGroup, id: searchNamespace }]}
            loading={loading}
            onChange={handleRoleChange}
            className="w-full"
          >
            <Button
              variant="outline"
              className="h-10 w-full justify-between font-normal"
              aria-label={`Filter by ${i18n.en.filters.role}`}
            >
              <span className="truncate">
                {getSelectedLabel(ROLE_FILTER_OPTIONS, selectedRole)}
              </span>
              <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </SearchFilter>
        </div>

        {/* Join Date Filter - 1/2 width on small (span 1), 1/6 width on large (span 1) */}
        <div className="col-span-1 lg:col-span-1">
          <SearchFilter
            name={JOIN_DATE_FILTER_NAME}
            groups={[{ ...joinDateFilterGroup, id: searchNamespace }]}
            loading={loading}
            onChange={handleJoinDateChange}
            className="w-full"
          >
            <Button
              variant="outline"
              className="h-10 w-full justify-between font-normal"
              aria-label={`Filter by ${i18n.en.filters.joinDate}`}
            >
              <span className="truncate">
                {getSelectedLabel(JOIN_DATE_OPTIONS, selectedJoinDate)}
              </span>
              <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </SearchFilter>
        </div>

        {/* Criteria Filter - 1/2 width on small (span 1), 1/6 width on large (span 1) */}
        <div className="col-span-1 lg:col-span-1">
          <SearchFilter
            name={CRITERIA_FILTER_NAME}
            groups={[{ ...criteriaFilterGroup, id: searchNamespace }]}
            loading={loading}
            onChange={handleCriteriaChange}
            className="w-full"
          >
            <Button
              variant="outline"
              className="h-10 w-full justify-between font-normal"
              aria-label={`Filter by ${i18n.en.filters.criteria}`}
            >
              <span className="truncate">
                {getSelectedLabel(CRITERIA_FILTER_OPTIONS, selectedCriteria)}
              </span>
              <ChevronDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </SearchFilter>
        </div>
      </div>

      {/* Results Summary */}
      {!loading && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {i18n.en.results.showing
              .replace("{count}", resultsCount.toString())
              .replace("{total}", totalResults.toString())}
          </p>
        </div>
      )}
    </div>
  );
}
