/**
 * PROVIDERS PANEL PRESENTATION COMPONENT
 * ======================================
 *
 * This is the presentation layer component for the providers panel.
 * It receives all data and handlers as props from the data layer component.
 *
 * FEATURES:
 * - Provider search bar (delegated to ProviderSearchBar component)
 * - Provider cards grid layout using ProviderCard component
 * - Pagination controls
 * - Empty states and error handling
 * - Loading states and skeletons
 */

import { Search, Users } from "lucide-react";

import { SearchPagination } from "@axa/ui/search";

import type { RouterError, RouterOutputs } from "@/api";

import { cn } from "@/ui/lib";

import ProviderCard from "./ProviderCard";
import ProviderSearchBar from "./ProviderSearchBar";

// Define the provider type from the API
type ProviderType = RouterOutputs["providers"]["search"]["items"][number];

export interface ProvidersPanelProps {
  // Data props
  providers: ProviderType[];
  totalProviders: number;
  loading: boolean;
  error: RouterError;

  // Pagination
  currentPage: number;
  pageSize: number;

  // Layout configuration
  gridColumns?: 2 | 3 | 4;
  className?: string; // Allow data layer to control container styling

  // Event handlers
  onRefresh: () => void;

  // Configuration
  searchNamespace: string;
}

// Internationalization object
const i18n = {
  en: {
    title: "Medical Providers Network",
    subtitle: "Discover and scout medical professionals",
    searchPlaceholder: "Search providers by name or title...",
    filters: {
      role: "Position",
      joinDate: "Join Date",
      criteria: "Criteria",
    },
    results: {
      noResults: "No providers found",
      noResultsDescription:
        "Try adjusting your search criteria or filters to find medical professionals.",
      suggestions: {
        title: "Suggestions:",
        items: [
          "Clear your search filters",
          "Try searching by specialty or location",
          "Browse all available providers",
        ],
      },
    },
    errors: {
      loadFailed: "Failed to load providers",
      retry: "Retry",
    },
  },
};

export default function ProvidersPanel({
  providers,
  totalProviders,
  loading,
  error,
  currentPage,
  pageSize,
  gridColumns = 3, // Default to 3 columns
  className = "", // Default to empty string
  onRefresh,
  searchNamespace = "providers",
}: ProvidersPanelProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {/* Header Section */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">{i18n.en.title}</h2>
        <p className="text-muted-foreground">{i18n.en.subtitle}</p>
      </div>

      {/* Search Bar Section */}
      <ProviderSearchBar
        resultsCount={providers.length}
        totalResults={totalProviders}
        loading={loading}
        searchNamespace={searchNamespace}
      />

      {error && !loading && (
        <div className="mx-auto space-y-6">
          <div className="rounded-lg border-2 border-destructive/20 bg-destructive/5 p-8 text-center">
            <h2 className="mb-2 text-lg font-semibold text-destructive">
              {i18n.en.errors.loadFailed}
            </h2>
            <p className="mb-4 text-sm text-muted-foreground">
              There was an error loading the providers. Please try again.
            </p>
            <button
              onClick={onRefresh}
              className="rounded-md bg-destructive px-4 py-2 text-destructive-foreground transition-colors hover:bg-destructive/90"
            >
              {i18n.en.errors.retry}
            </button>
          </div>
        </div>
      )}

      {/* Dynamic Provider Cards Grid */}
      <div
        className={cn("grid grid-cols-1 gap-8", {
          /**
           * DYNAMIC GRID UTILITY
           * ====================
           *
           * Generates responsive grid classes based on the number of columns.
           * Provides progressive enhancement with logical breakpoint transitions:
           *
           * This ensures optimal viewing experience across all screen sizes.
           */
          "lg:grid-cols-2": gridColumns === 2,
          "lg:grid-cols-2 2xl:grid-cols-3": gridColumns === 3,
          "lg:grid-cols-2 2xl:grid-cols-4": gridColumns === 4,
        })}
      >
        {loading ? (
          // Loading state - show skeleton cards
          Array.from({ length: pageSize }).map((_, index) => (
            <ProviderCard key={index} loading={true} />
          ))
        ) : providers.length > 0 ? (
          // Loaded state - show provider cards
          providers.map((provider) => (
            <ProviderCard key={provider.id} provider={provider} />
          ))
        ) : (
          // Empty state - Beautiful centered design
          <div className="col-span-full rounded-lg border border-muted/50 bg-gradient-to-br from-background to-muted/20 p-12 text-center shadow-sm">
            <div className="mx-auto max-w-md">
              {/* Icon with gradient background */}
              <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/5 ring-1 ring-primary/10">
                <Search className="h-8 w-8 text-primary/60" />
              </div>

              {/* Main message */}
              <h3 className="mb-3 text-lg font-semibold text-foreground">
                {i18n.en.results.noResults}
              </h3>
              <p className="mb-6 text-sm text-muted-foreground">
                {i18n.en.results.noResultsDescription}
              </p>

              {/* Suggestions */}
              <div className="rounded-md bg-muted/30 p-4 text-left">
                <h4 className="mb-2 text-sm font-medium text-foreground">
                  {i18n.en.results.suggestions.title}
                </h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {i18n.en.results.suggestions.items.map(
                    (suggestion, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-primary/40" />
                        {suggestion}
                      </li>
                    ),
                  )}
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Pagination - Simplified for now */}
      {!loading && totalProviders > pageSize && (
        <SearchPagination
          showPageNumbers={false}
          totalItems={totalProviders}
          defaultPageIndex={currentPage}
          defaultPageSize={pageSize}
        />
        // <div className="flex justify-center">
        //   <div className="flex items-center gap-2">
        //     <button
        //       className="rounded-md border border-input bg-background px-3 py-2 text-sm transition-colors hover:bg-accent disabled:opacity-50"
        //       disabled={currentPage <= 1}
        //     >
        //       Previous
        //     </button>
        //     <span className="px-3 py-2 text-sm">
        //       Page {currentPage} of {Math.ceil(totalProviders / pageSize)}
        //     </span>
        //     <button
        //       className="rounded-md border border-input bg-background px-3 py-2 text-sm transition-colors hover:bg-accent disabled:opacity-50"
        //       disabled={currentPage >= Math.ceil(totalProviders / pageSize)}
        //     >
        //       Next
        //     </button>
        //   </div>
        // </div>
      )}
    </div>
  );
}
