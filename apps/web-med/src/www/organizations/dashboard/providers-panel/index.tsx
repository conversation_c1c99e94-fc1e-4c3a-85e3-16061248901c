/**
 * PROVIDERS PANEL - DATA LAYER
 * ============================
 *
 * This is the data entry point for the Providers Panel component.
 * It handles API integration, URL-driven state management, and business logic.
 *
 * For complete documentation, see: ./README.md
 */

"use client";

import { Suspense, useMemo } from "react";

import {
  SearchParams,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
} from "@axa/ui/search";

import { api } from "@/api/client";

import {
  CRITERIA_FILTER_NAME,
  JOIN_DATE_FILTER_NAME,
  ROLE_FILTER_NAME,
} from "./ProviderSearchBar";
import ProvidersPanel from "./ProvidersPanel";
import ProvidersPanelSkeleton from "./ProvidersPanelSkeleton";

/**
 * SEARCH PARAMETER CONFIGURATION
 * ==============================
 *
 * Namespace: "providers" - prevents conflicts with other dashboard components
 */
const SEARCH_NAMESPACE = "providers";

/**
 * MAIN PROVIDERS PANEL DATA LAYER
 * ===============================
 *
 * Handles all data fetching, URL state management, and business logic
 * for the providers panel component.
 */
function ProvidersDataLayer() {
  // URL-driven state management
  const searchQuery = useSearchTextValue(SEARCH_NAMESPACE, "query");
  const roleFilter = useSearchFilterValue(SEARCH_NAMESPACE, ROLE_FILTER_NAME);
  const joinDateFilter = useSearchFilterValue(
    SEARCH_NAMESPACE,
    JOIN_DATE_FILTER_NAME,
  );
  const criteriaFilter = useSearchFilterValue(
    SEARCH_NAMESPACE,
    CRITERIA_FILTER_NAME,
  );
  const { pageIndex, pageSize } = useSearchPaginationValue(SEARCH_NAMESPACE);

  // Convert pageIndex (0-based) to pageNumber (1-based) for API
  const pageNumber = pageIndex + 1;

  // Override page size for optimal 2-column display (6 rows × 2 columns = 12 items)
  const optimizedPageSize = 2;

  // Grid configuration
  const gridColumns = 2; // Current configuration: 2-column layout (responsive: 1 → 2 columns)

  // Prepare API input
  const searchInput = useMemo(
    () => ({
      query: searchQuery,
      role: roleFilter,
      joinDate: joinDateFilter as "30" | "90" | "180",
      criteria: criteriaFilter as "verified" | "available" | "high-rated",
      pageNumber,
      pageSize: optimizedPageSize, // Use fixed page size for 2-column layout
      sortBy: "createdAt" as const,
      sortOrder: "desc" as const,
    }),
    [
      searchQuery,
      roleFilter,
      joinDateFilter,
      criteriaFilter,
      pageNumber,
      // Note: optimizedPageSize is constant, so no need to include in dependencies
    ],
  );

  // Fetch provider data using the new search procedure
  const { data, error, isLoading, refetch } = api.providers.search.useQuery(
    searchInput,
    {
      // Refetch when the component becomes visible again
      refetchOnWindowFocus: false,
      // Cache data for 5 minutes
      staleTime: 1000 * 60 * 5,
    },
  );

  // Pass all data and handlers to the presentation component
  return (
    <ProvidersPanel
      // Data
      providers={data?.items ?? []}
      totalProviders={data?.pagination.total ?? 0}
      // Loading and error states
      loading={isLoading}
      error={error}
      // Pagination
      currentPage={pageNumber}
      pageSize={optimizedPageSize}
      // Layout configuration
      gridColumns={gridColumns}
      // Event handlers
      onRefresh={() => refetch()}
      // Configuration
      searchNamespace={SEARCH_NAMESPACE}
    />
  );
}

export default function ProvidersSearch() {
  return (
    <Suspense fallback={<ProvidersPanelSkeleton />}>
      <SearchParams>
        <ProvidersDataLayer />
      </SearchParams>
    </Suspense>
  );
}
