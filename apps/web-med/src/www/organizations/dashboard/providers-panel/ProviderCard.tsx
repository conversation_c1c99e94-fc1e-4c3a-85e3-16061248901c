/**
 * PROVIDER CARD COMPONENT
 * ======================
 *
 * A polished provider card component that displays medical provider information
 * in the providers panel. Handles loading, error, and loaded states internally
 * for a cohesive user experience.
 *
 * FEATURES:
 * - Displays provider avatar, name, title, specialties, and metadata
 * - Shows verification status and availability indicators
 * - Respects privacy rules (no contact information)
 * - Handles loading and error states
 * - Optimized for the specified 150-160px height requirement
 * - Click handler for profile navigation
 * - Responsive design with hover effects
 *
 * PRIVACY COMPLIANCE:
 * - NO email or phone number display (marketplace rules)
 * - Only shows public profile information
 * - Location shows city/state only (no full address)
 */

import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import {
  Briefcase,
  CalendarDays,
  CheckCircle,
  Clock,
  MapPin,
  Star,
} from "lucide-react";

import type { BadgeProps } from "@axa/ui/primitives/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import { Card, CardContent } from "@axa/ui/primitives/card";
import { Skeleton } from "@axa/ui/primitives/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@axa/ui/primitives/tooltip";

import type { RouterOutputs } from "@/api";

import { cn } from "@/ui/lib";

// Define the provider type from the NEW search API
type ProviderType = RouterOutputs["providers"]["search"]["items"][number];

// Internationalization object
const i18n = {
  en: {
    viewProfile: "View Profile",
    joinedOn: "Joined",
    specialties: "Specialties",
    moreSpecialties: "+{count} more",
    verified: "Verified",
    pending: "Pending",
    availableForWork: "Available",
    yearsExperience: "{count}+ years experience",
    score: "Score: {score}/100",
    noTitle: "Medical Professional",
    noSpecialties: "No specialties listed",
    loading: "Loading provider information...",
    error: "Unable to load provider information",
    retry: "Retry",
    location: "Location",
    noLocation: "Location not specified",
  },
};

/**
 * PROVIDER CARD PROPS
 * ===================
 */
interface ProviderCardProps {
  provider?: ProviderType;
  loading?: boolean;
  error?: string | Error | null;
  className?: string;
}

/**
 * HELPER FUNCTIONS
 * ===============
 */

// Extract city and state from individual fields for privacy and flexibility
function getLocationDisplay(address?: ProviderType["address"]): string {
  if (!address) return i18n.en.noLocation;

  const { city, state } = address;

  // Build location string from individual components
  if (city && state) {
    return `${city}, ${state}`;
  } else if (city) {
    return city;
  } else if (state) {
    return state;
  }

  return i18n.en.noLocation;
}

// Get verification status display
function getVerificationStatus(verification?: ProviderType["verification"]) {
  if (!verification)
    return { label: i18n.en.pending, variant: "outline" as const };

  const isVerified = verification.status === "APPROVED";

  return {
    label: isVerified ? i18n.en.verified : i18n.en.pending,
    variant: isVerified ? "default" : ("outline" as const),
  };
}

// Get years of experience from job experiences
function getYearsOfExperience(
  experiences?: ProviderType["experiences"],
): number {
  if (!experiences || experiences.length === 0) return 0;

  const totalMonths = experiences.reduce((total, exp) => {
    const start = new Date(exp.startDate);
    const end = exp.endDate ? new Date(exp.endDate) : new Date();
    const months =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());
    return total + Math.max(0, months);
  }, 0);

  return Math.floor(totalMonths / 12);
}

/**
 * LOADING STATE COMPONENT
 * =======================
 */
function ProviderCardSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn("h-56 transition-shadow hover:shadow-md", className)}>
      <CardContent className="h-full p-6">
        <div className="flex h-full animate-pulse flex-col space-y-3 sm:space-y-4">
          {/* Header with avatar and name */}
          <div className="flex items-start gap-3 sm:gap-4">
            <Skeleton className="h-14 w-14 flex-shrink-0 rounded-xl sm:h-16 sm:w-16" />
            <div className="flex-1 space-y-2 sm:space-y-3">
              <Skeleton className="h-4 w-32 sm:h-5 sm:w-40" />
              <Skeleton className="h-3 w-24 sm:h-4 sm:w-32" />
            </div>
            <Skeleton className="h-5 w-16 rounded-full sm:h-6 sm:w-20" />
          </div>

          {/* Specialties */}
          <div className="flex gap-1.5 sm:gap-2">
            <Skeleton className="h-5 w-16 rounded-full sm:h-6 sm:w-20" />
            <Skeleton className="h-5 w-20 rounded-full sm:h-6 sm:w-24" />
            <Skeleton className="h-5 w-12 rounded-full sm:h-6 sm:w-16" />
          </div>

          {/* Metadata row */}
          <div className="flex gap-2.5 sm:gap-4">
            <Skeleton className="h-3 w-20 sm:h-4 sm:w-24" />
            <Skeleton className="h-3 w-16 sm:h-4 sm:w-20" />
            <Skeleton className="h-3 w-20 sm:h-4 sm:w-28" />
          </div>

          {/* Bottom section */}
          <div className="flex flex-1 flex-col justify-end pt-2 sm:pt-0">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-20 rounded-full sm:h-6 sm:w-24" />
              <Skeleton className="h-8 w-24 rounded-md sm:h-9 sm:w-28" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * ERROR STATE COMPONENT
 * =====================
 */
function ProviderCardError({
  error,
  onRetry,
  className,
}: {
  error: string | Error;
  onRetry?: () => void;
  className?: string;
}) {
  const errorMessage = error instanceof Error ? error.message : error;

  return (
    <Card
      className={cn("h-56 border-destructive/20 bg-destructive/5", className)}
    >
      <CardContent className="flex h-full flex-col items-center justify-center space-y-3 p-6 text-center">
        <div className="rounded-full bg-destructive/10 p-3">
          <svg
            className="h-6 w-6 text-destructive"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.232 15.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <p className="text-sm font-medium text-destructive">{i18n.en.error}</p>
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="border-destructive/20 text-destructive hover:bg-destructive/10"
          >
            {i18n.en.retry}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * MAIN PROVIDER CARD COMPONENT
 * ============================
 */
export default function ProviderCard({
  provider,
  loading = false,
  error,
  className,
}: ProviderCardProps) {
  // Handle loading state
  if (loading) {
    return <ProviderCardSkeleton className={className} />;
  }

  // Handle error state
  if (error) {
    return <ProviderCardError error={error} className={className} />;
  }

  // Handle missing provider data
  if (!provider) {
    return (
      <ProviderCardError
        error="Provider data not available"
        className={className}
      />
    );
  }

  // Extract data for display
  const fullName = provider.person
    ? `${provider.person.firstName} ${provider.person.lastName}`
    : "Unknown Provider";
  const title = provider.title ?? i18n.en.noTitle;
  const location = getLocationDisplay(provider.address);
  const verification = getVerificationStatus(provider.verification);
  const yearsExp = Math.floor(provider.yearsOfExperience || 0);
  const isAvailable = provider.settings?.openToWork;
  const joinDate = formatDistanceToNow(new Date(provider.createdAt), {
    addSuffix: true,
  });

  // Specialties display (show first 3, with overflow indicator)
  const specialties = provider.specialties ?? [];
  const displaySpecialties = specialties.slice(0, 3);
  const additionalSpecialties = specialties.length - 3;

  return (
    <TooltipProvider>
      <Card
        className={cn(
          "group h-56 border-muted/50 bg-gradient-to-br from-background to-muted/20 transition-all duration-300 hover:shadow-xl hover:shadow-primary/5",
          "hover:border-primary/30 hover:bg-gradient-to-br hover:from-background hover:to-primary/5",
          "hover:-translate-y-1",
          className,
        )}
      >
        <CardContent className="flex h-full flex-col p-6">
          {/* Header Section: Fixed height container - ~72px */}
          <div className="sm:h-18 flex h-16 items-start gap-3 sm:gap-4">
            <div className="relative flex-shrink-0">
              <Avatar className="h-14 w-14 rounded-xl border-2 border-primary/10 ring-2 ring-transparent transition-all group-hover:border-primary/20 group-hover:ring-primary/10 sm:h-16 sm:w-16">
                <AvatarImage
                  src={provider.person?.avatar ?? ""}
                  alt={fullName}
                  className="rounded-xl"
                />
                <AvatarFallback className="rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 text-base font-bold text-primary sm:text-lg">
                  {provider.person?.firstName.charAt(0) ?? "P"}
                  {provider.person?.lastName.charAt(0) ?? "P"}
                </AvatarFallback>
              </Avatar>
              {/* Online/Availability indicator */}
              {isAvailable && (
                <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-background bg-green-500 shadow-sm sm:h-5 sm:w-5" />
              )}
            </div>

            <div className="min-w-0 flex-1 overflow-hidden">
              <h3 className="truncate text-base font-bold leading-tight text-foreground transition-colors group-hover:text-primary sm:text-lg">
                {fullName}
              </h3>
              <p className="mt-0.5 truncate text-sm font-medium text-muted-foreground sm:mt-1">
                {title}
              </p>
              <div className="mt-1.5 flex items-center gap-1.5 text-xs text-muted-foreground sm:mt-2 sm:gap-2">
                <MapPin className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{location}</span>
              </div>
            </div>

            <Badge
              variant={verification.variant as BadgeProps["variant"]}
              className="flex-shrink-0 text-xs font-medium shadow-sm"
            >
              {verification.label === i18n.en.verified ? (
                <CheckCircle className="mr-1 h-3 w-3 sm:mr-1.5" />
              ) : (
                <Clock className="mr-1 h-3 w-3 sm:mr-1.5" />
              )}
              {verification.label}
            </Badge>
          </div>

          {/* Specialties Section: Fixed height container - ~32px */}
          <div className="mt-3 h-6 sm:mt-4 sm:h-7">
            <div className="flex h-full flex-wrap gap-1.5 overflow-hidden sm:gap-2">
              {displaySpecialties.map((specialty) => (
                <Badge
                  key={specialty.id}
                  variant="outline"
                  className="h-5 flex-shrink-0 bg-secondary/40 px-2 py-0.5 text-xs font-medium transition-colors hover:bg-secondary/60 sm:h-6 sm:px-3 sm:py-1"
                >
                  <span className="truncate">{specialty.name}</span>
                </Badge>
              ))}
              {additionalSpecialties > 0 && (
                <Tooltip>
                  <TooltipTrigger>
                    <Badge
                      variant="outline"
                      className="h-5 flex-shrink-0 bg-muted/60 px-2 py-0.5 text-xs font-medium transition-colors hover:bg-muted sm:h-6 sm:px-3 sm:py-1"
                    >
                      {i18n.en.moreSpecialties.replace(
                        "{count}",
                        additionalSpecialties.toString(),
                      )}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-48">
                    <div className="space-y-1">
                      {specialties.slice(3).map((specialty) => (
                        <div key={specialty.id} className="text-xs font-medium">
                          {specialty.name}
                        </div>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
              {specialties.length === 0 && (
                <span className="text-xs italic text-muted-foreground">
                  {i18n.en.noSpecialties}
                </span>
              )}
            </div>
          </div>

          {/* Metadata Row: Fixed height container - ~20px */}
          <div className="mt-3 flex h-4 items-center gap-2.5 text-xs text-muted-foreground sm:mt-4 sm:h-5 sm:gap-4">
            {yearsExp > 0 && (
              <Tooltip>
                <TooltipTrigger className="flex items-center gap-1 transition-colors hover:text-foreground sm:gap-1.5">
                  <Briefcase className="h-3.5 w-3.5 flex-shrink-0 sm:h-4 sm:w-4" />
                  <span className="whitespace-nowrap font-medium">
                    {yearsExp}+ years
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {i18n.en.yearsExperience.replace(
                      "{count}",
                      yearsExp.toString(),
                    )}
                  </p>
                </TooltipContent>
              </Tooltip>
            )}

            <Tooltip>
              <TooltipTrigger className="flex items-center gap-1 transition-colors hover:text-foreground sm:gap-1.5">
                <CalendarDays className="h-3.5 w-3.5 flex-shrink-0 sm:h-4 sm:w-4" />
                <span className="truncate font-medium">{joinDate}</span>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {i18n.en.joinedOn} {joinDate}
                </p>
              </TooltipContent>
            </Tooltip>

            {provider.score > 0 && (
              <Tooltip>
                <TooltipTrigger className="flex items-center gap-1 transition-colors hover:text-foreground sm:gap-1.5">
                  <Star className="h-3.5 w-3.5 flex-shrink-0 fill-yellow-400 text-yellow-400 sm:h-4 sm:w-4" />
                  <span className="font-medium">{provider.score}</span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {i18n.en.score.replace(
                      "{score}",
                      provider.score.toString(),
                    )}
                  </p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>

          {/* Bottom Section: Use remaining space with minimum height - ~40px */}
          <div className="mt-auto flex min-h-10 items-end justify-between pt-3 sm:min-h-11 sm:pt-4">
            <div className="flex items-center">
              {isAvailable && (
                <Badge className="border-green-200 bg-green-100 text-xs font-medium text-green-800 shadow-sm hover:bg-green-200">
                  {i18n.en.availableForWork}
                </Badge>
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              className="h-8 flex-shrink-0 px-3 text-sm font-medium transition-all duration-200 group-hover:border-primary group-hover:bg-primary group-hover:text-primary-foreground group-hover:shadow-md sm:h-9 sm:px-4"
              asChild
            >
              <Link
                href={`/app/providers/${provider.id}`}
                className="flex items-center gap-1.5 sm:gap-2"
              >
                <span className="whitespace-nowrap">{i18n.en.viewProfile}</span>
                <svg
                  className="h-3.5 w-3.5 transition-transform group-hover:translate-x-0.5 sm:h-4 sm:w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
