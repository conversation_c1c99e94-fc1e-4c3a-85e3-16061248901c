import AppView from "@axa/ui/layouts/AppView";

import type { RouterError, RouterOutputs } from "@/api";

import DashboardAnalytics from "./DashboardAnalytics";
import DashboardApplications from "./DashboardApplications";
// import DashboardCharts from "./DashboardCharts";
import DashboardGreeting from "./DashboardGreeting";
import DashboardPositions from "./DashboardPositions";
import ProvidersPanel from "./providers-panel";

// import DashboardSchedule from "./DashboardSchedule";

export interface DashboardProps {
  loading?: boolean;
  analytics: {
    data?: RouterOutputs["organizations"]["analytics"]["dashboard"];
    loading?: boolean;
    error?: RouterError;
  };
  shifts: {
    data?: RouterOutputs["shifts"]["getMany"];
    loading?: boolean;
    error?: RouterError;
  };
  jobs: {
    data?: RouterOutputs["jobs"]["getMany"];
    loading?: boolean;
    error?: RouterError;
  };
  applications: {
    data?: RouterOutputs["applications"]["getMany"];
    loading?: boolean;
    error?: RouterError;
  };
}

export function DashboardSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-20 rounded-lg bg-muted" />
      <div className="mt-8 space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="h-32 rounded-lg bg-muted" />
          ))}
        </div>
        <div className="h-[400px] rounded-lg bg-muted" />
        <div className="grid gap-6 md:grid-cols-2">
          <div className="h-[300px] rounded-lg bg-muted" />
          <div className="h-[300px] rounded-lg bg-muted" />
        </div>
      </div>
    </div>
  );
}

export default function Dashboard({
  loading = false,
  analytics,
  shifts,
  jobs,
  applications,
}: DashboardProps) {
  if (loading) {
    return (
      <AppView>
        <div />
        <DashboardSkeleton />
      </AppView>
    );
  }

  return (
    <AppView>
      <div />
      <div className="flex flex-col gap-8">
        <div className="mx-auto w-full max-w-screen-lg">
          <DashboardGreeting />
        </div>

        <DashboardAnalytics analytics={analytics} />

        {/* <DashboardSchedule shifts={shifts} /> */}

        <div className="grid gap-6 md:grid-cols-2">
          <DashboardApplications applications={applications} />
          <DashboardPositions jobs={jobs} />
        </div>

        <ProvidersPanel />
      </div>
    </AppView>
  );
}
