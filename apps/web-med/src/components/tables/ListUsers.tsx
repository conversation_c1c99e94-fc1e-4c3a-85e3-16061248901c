"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import Image from "next/image";
import { CircleIcon, MenuIcon, Settings2Icon } from "lucide-react";

import type { UseDataTableProps } from "@axa/ui/tables";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { Button } from "@axa/ui/primitives/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@axa/ui/primitives/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@axa/ui/primitives/dropdown-menu";
import { SearchFilter, SearchText, useSearchPagination } from "@axa/ui/search";
import EmptyList from "@axa/ui/shared/EmptyList";
import { DataTable, useDataTable } from "@axa/ui/tables";
import { dataTableColumns, selectColumn } from "@axa/ui/tables/columns";
import {
  DataTableColumnHeader,
  DataTableSettings,
  DataTableSimplePagination,
} from "@axa/ui/tables/helpers";

import type { RouterOutputs } from "@/api";

import { PersonRole } from "@/api";
import { UserMenu } from "@/components/actions/user";

const i18n = {
  en: {
    noPerson: "There are no users yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search users...",
    },
    headers: {
      role: "Role",
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
    filters: {
      role: "Role",
      options: {
        ALL: "All",
        ADMIN: "Admin",
        BILLING: "Billing",
        INTERNAL: "Internal",
        PROVIDER: "Provider",
        CLIENT: "Client",
        USER: "User",
        NONE: "None",
      },
    },
  },
  links: {
    users: "/app/admin/users/[id]",
  },
};

const groupName = "user";
const filterGroups = [
  {
    id: "role",
    label: i18n.en.filters.role,
    options: [
      {
        value: null,
        label: i18n.en.filters.options.ALL,
      },
      {
        value: PersonRole.ADMIN,
        label: i18n.en.filters.options.ADMIN,
      },
      {
        value: PersonRole.BILLING,
        label: i18n.en.filters.options.BILLING,
      },
      {
        value: PersonRole.INTERNAL,
        label: i18n.en.filters.options.INTERNAL,
      },
      {
        value: PersonRole.PROVIDER,
        label: i18n.en.filters.options.PROVIDER,
      },
      {
        value: PersonRole.CLIENT,
        label: i18n.en.filters.options.CLIENT,
      },
      {
        value: PersonRole.USER,
        label: i18n.en.filters.options.USER,
      },
      {
        value: PersonRole.NONE,
        label: i18n.en.filters.options.NONE,
      },
    ] satisfies { value: PersonRole | null; label: string }[],
  },
];

export type UserQueryResult = RouterOutputs["user"]["getMany"];
export type UserType = UserQueryResult["items"];
export type PersonType = UserType[number];
export type TableProps = UseDataTableProps<PersonType, UserType>;

export function UserTableActions({
  table,
}: {
  table: ReturnType<typeof useDataTable<PersonType, UserType>>["table"];
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={i18n.en.actions.tableActions}
        >
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute right-1.5 top-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.en.selection} ({selectionCount})
        </DropdownMenuLabel>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default function ListUser({
  loading = false,
  users,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
}: PropsWithChildren<{
  loading?: boolean;
  users?: UserQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
}>) {
  const { pagination, setPagination } = useSearchPagination({
    group: groupName,
    defaultPageSize,
    defaultPageIndex,
  });

  const { table } = useDataTable<PersonType, UserType>({
    data: users?.items ?? [],
    rowCount: users?.total,
    manualPagination: true,
    pagination,
    setPagination,
    columns: useMemo(
      () =>
        dataTableColumns<PersonType, UserType>([
          selectColumn as ColumnDef<PersonType, UserType>,
          {
            id: "avatar",
            accessorKey: "avatar",
            header: () => null,
            cell: ({ row }) => (
              <Avatar className="size-10">
                <AvatarImage
                  asChild
                  src={row.original.avatar ?? undefined}
                  alt={`${row.original.firstName} ${row.original.lastName}`}
                >
                  <Image
                    src={row.original.avatar ?? ""}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                    width={40}
                    height={40}
                    layout="fixed"
                  />
                </AvatarImage>
                <AvatarFallback>
                  {row.original.firstName.charAt(0)}
                  {row.original.lastName.charAt(0)}
                </AvatarFallback>
              </Avatar>
            ),
          },
          {
            id: "name",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.name}
              />
            ),
            cell: ({ row }) => (
              <div className="flex w-full items-center gap-2">
                <ContactName
                  className="font-semibold"
                  link={i18n.links.users.replace("[id]", row.original.id)}
                  name={`${row.original.firstName} ${row.original.lastName}`}
                />
              </div>
            ),
            enableHiding: false,
          },
          {
            id: "email",
            accessorKey: "email",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.email}
              />
            ),
            cell: ({ row }) => <ContactEmail email={row.getValue("email")} />,
          },
          {
            id: "phone",
            accessorKey: "phone",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.phone}
              />
            ),
            cell: ({ row }) => <ContactPhone phone={row.getValue("phone")} />,
          },
          {
            id: "role",
            accessorKey: "role",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.role}
              />
            ),
            cell: ({ row }) => (
              <Badge variant="outline" className="w-fit text-nowrap text-xs">
                {row.original.role}
              </Badge>
            ),
            filterFn: (row, id, value: string) => {
              return value.includes(row.original.role);
            },
          },
          {
            id: "organization",
            accessorKey: "organization.id",
            header: ({ column }) => (
              <DataTableColumnHeader
                column={column}
                title={i18n.en.headers.organization}
              />
            ),
            cell: ({ row }) =>
              row.original.organization ? (
                <PreviewOrganization organization={row.original.organization} />
              ) : null,
            filterFn: (row, id, value: string) => {
              return value.includes(row.original.organization?.id ?? "");
            },
          },
          {
            id: "actions",
            meta: {
              className: "w-[32px]",
            },
            header: ({ table }) => (
              <div className="flex size-full items-center justify-end">
                <UserTableActions table={table} />
              </div>
            ),
            cell: ({ row }) => (
              <div className="flex size-full items-center justify-end">
                <UserMenu variant="ghost" user={row.original} />
              </div>
            ),
          },
        ]),
      [],
    ),
  });

  return (
    <Card>
      <CardHeader className="border-b p-2">
        <div className="flex items-center justify-between">
          <div className="border-r pr-2">
            <SearchFilter name={groupName} groups={filterGroups} />
          </div>

          <div className="flex h-fit flex-1 gap-2 overflow-scroll p-1 px-2">
            <SearchText
              group={groupName}
              loading={loading}
              placeholder={i18n.en.actions.search}
            />
            {filters}
          </div>

          <div className="border-l pl-2">
            <DataTableSettings
              table={table}
              variant="ghost"
              size="icon"
              className="ml-auto"
              aria-label={i18n.en.actions.tableSettings}
            >
              <Settings2Icon size="20" color="currentColor" />
            </DataTableSettings>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <DataTable loading={loading} table={table}>
          <EmptyList title={i18n.en.noPerson}>{children}</EmptyList>
        </DataTable>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 border-t pt-6">
        <DataTableSimplePagination table={table} />
      </CardFooter>
    </Card>
  );
}
