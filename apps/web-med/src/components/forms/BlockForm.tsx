import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  Calendar,
  CalendarDays,
  CalendarIcon,
  HelpCircle,
  Info,
  Repeat,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import TimePickerField from "@axa/ui/fields/date-time/TimePicker";
import { cn } from "@axa/ui/lib";
import { Alert, AlertDescription } from "@axa/ui/primitives/alert";
import { Button } from "@axa/ui/primitives/button";
import { Calendar as CalendarComponent } from "@axa/ui/primitives/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@axa/ui/primitives/popover";
import { RadioGroup, RadioGroupItem } from "@axa/ui/primitives/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@axa/ui/primitives/tabs";

import { TimeBlockRecurrence, TimeBlockType } from "@/api";

const DAYS_OF_WEEK = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
] as const;

// Define the form modes
type FormMode = "exact" | "recurring" | "span";

// Define the form schema with proper types
const blockFormSchema = z.discriminatedUnion("mode", [
  z.object({
    mode: z.literal("exact"),
    type: z.nativeEnum(TimeBlockType),
    startTime: z.number(),
    endTime: z.number(),
    exactDate: z.date(),
    dayOfWeek: z.undefined(),
    recurrence: z.undefined(),
    startDate: z.undefined(),
    endDate: z.undefined(),
  }),
  z.object({
    mode: z.literal("recurring"),
    type: z.nativeEnum(TimeBlockType),
    startTime: z.number(),
    endTime: z.number(),
    dayOfWeek: z.number().min(0).max(6),
    recurrence: z.nativeEnum(TimeBlockRecurrence),
    exactDate: z.undefined(),
    startDate: z.undefined(),
    endDate: z.undefined(),
  }),
  z.object({
    mode: z.literal("span"),
    type: z.nativeEnum(TimeBlockType),
    startTime: z.number().optional(),
    endTime: z.number().optional(),
    startDate: z.date(),
    endDate: z.date(),
    exactDate: z.undefined(),
    dayOfWeek: z.undefined(),
    recurrence: z.undefined(),
  }),
]);

type BlockFormValues = z.infer<typeof blockFormSchema>;

// Initialize form with proper defaults based on mode
const getDefaultValues = (
  mode: FormMode,
  defaultValues: Partial<BlockFormValues> | undefined,
  defaultType: TimeBlockType,
): BlockFormValues => {
  const baseValues = {
    type: defaultType,
    startTime: defaultValues?.startTime ?? 9 * 60 + 0,
    endTime: defaultValues?.endTime ?? 17 * 60 + 0,
  };

  switch (mode) {
    case "recurring":
      return {
        ...baseValues,
        mode: "recurring" as const,
        dayOfWeek: 0,
        recurrence: defaultValues?.recurrence ?? TimeBlockRecurrence.WEEKLY,
        exactDate: undefined,
        startDate: undefined,
        endDate: undefined,
      };
    case "span":
      return {
        ...baseValues,
        mode: "span" as const,
        startDate: defaultValues?.startDate ?? new Date(),
        endDate: defaultValues?.endDate ?? new Date(),
        exactDate: undefined,
        dayOfWeek: undefined,
        recurrence: undefined,
      };
    case "exact":
      return {
        ...baseValues,
        mode: "exact" as const,
        exactDate: defaultValues?.exactDate ?? new Date(),
        dayOfWeek: undefined,
        recurrence: undefined,
        startDate: undefined,
        endDate: undefined,
      };
  }
};

interface BlockFormProps {
  defaultValues?: Partial<BlockFormValues>;
  onSubmit: (values: BlockFormValues) => void;
  onCancel: () => void;
  allowedTypes?: TimeBlockType[];
  allowedRecurrences?: TimeBlockRecurrence[];
  initialMode?: FormMode;
}

// Helper component for info alerts with dark mode support
const InfoAlert = ({
  children,
  variant = "info",
}: {
  children: React.ReactNode;
  variant?: "info" | "warning" | "success";
}) => {
  const variants = {
    info: {
      light:
        "border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950/30 dark:text-blue-200",
      icon: "text-blue-500 dark:text-blue-400",
    },
    warning: {
      light:
        "border-amber-200 bg-amber-50 text-amber-800 dark:border-amber-800 dark:bg-amber-950/30 dark:text-amber-200",
      icon: "text-amber-500 dark:text-amber-400",
    },
    success: {
      light:
        "border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950/30 dark:text-green-200",
      icon: "text-green-500 dark:text-green-400",
    },
  };

  return (
    <Alert variant="default" className={cn(variants[variant].light)}>
      <Info className={cn("h-4 w-4", variants[variant].icon)} />
      <AlertDescription className="text-xs">{children}</AlertDescription>
    </Alert>
  );
};

export default function BlockForm({
  defaultValues,
  onSubmit,
  onCancel,
  allowedTypes = [
    TimeBlockType.SHIFT,
    TimeBlockType.AVAILABILITY,
    TimeBlockType.TIME_OFF,
  ],
  allowedRecurrences = [
    TimeBlockRecurrence.WEEKLY,
    TimeBlockRecurrence.BIWEEKLY,
    TimeBlockRecurrence.MONTHLY,
    TimeBlockRecurrence.QUARTERLY,
    TimeBlockRecurrence.YEARLY,
  ],
  initialMode = "recurring",
}: BlockFormProps) {
  const [mode, setMode] = useState<FormMode>(
    defaultValues?.mode ?? initialMode,
  );

  // Ensure we have a valid default type
  const defaultType = defaultValues?.type ?? allowedTypes[0];
  if (!defaultType) {
    throw new Error("No valid TimeBlockType available");
  }

  const form = useForm<BlockFormValues>({
    resolver: zodResolver(blockFormSchema),
    defaultValues: getDefaultValues(mode, defaultValues, defaultType),
  });

  // Update form values when mode changes
  const handleModeChange = (newMode: FormMode) => {
    setMode(newMode);
    form.reset(getDefaultValues(newMode, defaultValues, defaultType));
  };

  // Handle form submission
  const handleSubmit = (values: BlockFormValues) => {
    onSubmit(values);
  };

  // Watch for changes to startDate to update endDate if needed
  const startDate = form.watch("startDate");
  const endDate = form.watch("endDate");

  useEffect(() => {
    if (mode === "span" && startDate && (!endDate || endDate < startDate)) {
      form.setValue("endDate", new Date(startDate));
    }
  }, [startDate, endDate, form, mode]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Mode Selection - Now with dark mode support */}
        <FormField
          control={form.control}
          name="mode"
          render={({ field }) => (
            <FormItem>
              <div className="rounded-md border border-input bg-background p-1 dark:border-input/20">
                <Tabs
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    handleModeChange(value as FormMode);
                  }}
                  className="w-full"
                >
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger
                      value="exact"
                      className="flex items-center gap-1.5"
                    >
                      <Calendar className="h-4 w-4" />
                      <span>Exact Date</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="recurring"
                      className="flex items-center gap-1.5"
                    >
                      <Repeat className="h-4 w-4" />
                      <span>Recurring</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="span"
                      className="flex items-center gap-1.5"
                    >
                      <CalendarDays className="h-4 w-4" />
                      <span>Date Range</span>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="exact" className="pt-4">
                    <FormDescription className="mb-4">
                      Schedule for a specific date only
                    </FormDescription>
                  </TabsContent>

                  <TabsContent value="recurring" className="pt-4">
                    <FormDescription className="mb-4">
                      Schedule a recurring time block on specific days
                    </FormDescription>
                  </TabsContent>

                  <TabsContent value="span" className="pt-4">
                    <FormDescription className="mb-4">
                      Schedule a block spanning multiple dates
                    </FormDescription>
                  </TabsContent>
                </Tabs>
              </div>
            </FormItem>
          )}
        />

        {/* Explanatory information based on mode */}
        {mode === "exact" && (
          <InfoAlert>
            Use this mode for one-time events that occur on a specific date,
            like a special shift or appointment.
          </InfoAlert>
        )}

        {mode === "recurring" && (
          <InfoAlert>
            Perfect for regular schedules that repeat on the same day each week,
            bi-weekly, or monthly.
          </InfoAlert>
        )}

        {mode === "span" && (
          <InfoAlert>
            Use for blocks that span multiple consecutive days, like vacation
            time or multi-day events.
          </InfoAlert>
        )}

        {/* Block Type - Only shown if there are multiple allowed types */}
        {allowedTypes.length > 1 && (
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>What type of time block is this?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {allowedTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type === TimeBlockType.SHIFT
                          ? "Work Shift"
                          : type === TimeBlockType.AVAILABILITY
                            ? "Available Time"
                            : type === TimeBlockType.TIME_OFF
                              ? "Time Off"
                              : type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Time Selection - Using TimePickerField with proper types */}
        <div className="grid grid-cols-2 gap-4">
          <TimePickerField
            name="startTime"
            label="Start Time"
            description="The time the block will start"
          />
          <TimePickerField
            name="endTime"
            label="End Time"
            description="The time the block will end"
          />
        </div>

        {/* Mode-specific fields */}
        {mode === "exact" && (
          <FormField
            control={form.control}
            name="exactDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value ? (
                          format(field.value, "EEEE, MMMM d, yyyy")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormDescription>
                  The exact date this time block will occur
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {mode === "recurring" && (
          <>
            <FormField
              control={form.control}
              name="dayOfWeek"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Which day of the week?</FormLabel>
                  <RadioGroup
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    defaultValue={field.value?.toString()}
                    className="grid grid-cols-7 gap-2"
                  >
                    {DAYS_OF_WEEK.map((day, index) => (
                      <FormItem
                        key={day}
                        className="flex flex-col items-center space-y-1.5"
                      >
                        <FormControl>
                          <RadioGroupItem
                            value={index.toString()}
                            id={`day-${index}`}
                            className="peer sr-only"
                          />
                        </FormControl>
                        <FormLabel
                          htmlFor={`day-${index}`}
                          className="flex h-12 w-full cursor-pointer items-center justify-center rounded-md border border-input bg-background p-2 text-center text-sm hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 peer-data-[state=checked]:text-primary dark:border-input/20 dark:hover:bg-accent/20"
                        >
                          {day.substring(0, 3)}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                  <FormMessage />
                </FormItem>
              )}
            />

            <InfoAlert variant="warning">
              <div>
                <p className="font-medium">Tip: Recurring blocks</p>
                <p>
                  These blocks will appear on your schedule according to the
                  recurrence pattern you select below.
                </p>
              </div>
            </InfoAlert>

            <FormField
              control={form.control}
              name="recurrence"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How often does this repeat?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <Repeat className="mr-2 h-4 w-4" />
                        <SelectValue placeholder="Select recurrence pattern" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {allowedRecurrences.map((recurrence) => (
                        <SelectItem key={recurrence} value={recurrence}>
                          {recurrence === TimeBlockRecurrence.WEEKLY
                            ? "Every week"
                            : recurrence === TimeBlockRecurrence.BIWEEKLY
                              ? "Every two weeks"
                              : recurrence === TimeBlockRecurrence.MONTHLY
                                ? "Every month"
                                : recurrence === TimeBlockRecurrence.QUARTERLY
                                  ? "Every three months"
                                  : "Every year"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    How frequently this time block will repeat
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

        {mode === "span" && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                          >
                            {field.value ? (
                              format(field.value, "MMM d, yyyy")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            field.onChange(date);
                            // If end date is before the new start date, update it
                            const currentEndDate = form.getValues("endDate");
                            if (
                              date &&
                              (!currentEndDate || currentEndDate < date)
                            ) {
                              form.setValue("endDate", new Date(date));
                            }
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground",
                            )}
                          >
                            {field.value ? (
                              format(field.value, "MMM d, yyyy")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => {
                            const startDate = form.getValues("startDate");
                            return startDate ? date < startDate : false;
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="rounded-md border border-green-200 bg-green-50 p-3 text-xs text-green-800 dark:border-green-800 dark:bg-green-950/30 dark:text-green-200">
              <div className="flex items-start gap-2">
                <Info className="mt-0.5 h-4 w-4 text-green-500" />
                <div>
                  <p>
                    The end date will automatically update if you select a start
                    date that comes after it.
                  </p>
                </div>
              </div>
            </div>
          </>
        )}

        <div className="flex justify-end gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="border-input hover:bg-accent hover:text-accent-foreground dark:border-input/20"
          >
            Cancel
          </Button>
          <Button type="submit">Save Time Block</Button>
        </div>
      </form>
    </Form>
  );
}
