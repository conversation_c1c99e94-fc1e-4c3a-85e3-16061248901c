"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext, useWatch } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import { IDField } from "@axa/ui/fields/text/ID";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";

import { QualificationStatus, QualificationType } from "@/api";
import { useUser } from "@/components/contexts/User";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "Name of the qualification",
      placeholder: "Enter qualification name",
    },
    type: {
      label: "Type",
      description: "Type of qualification",
      placeholder: "Select qualification type",
      options: {
        [QualificationType.DEGREE]: "Degree",
        [QualificationType.LICENSE]: "License",
        [QualificationType.CERTIFICATE]: "Certificate",
        [QualificationType.OTHER]: "Other",
      },
    },
    institution: {
      label: "Institution",
      description: "Institution that issued the qualification",
      placeholder: "Enter institution name",
    },
    identifier: {
      label: "Identifier",
      description: "Unique identifier or number",
      placeholder: "Enter identifier",
    },
    state: {
      label: "State",
      description: "State where issued",
      placeholder: "Enter state",
    },
    country: {
      label: "Country",
      description: "Country where issued",
      placeholder: "Enter country",
    },
    startDate: {
      label: "Start Date",
      description: "When the qualification started",
    },
    endDate: {
      label: "End Date",
      description: "When the qualification ends/expired",
    },
    issueDate: {
      label: "Issue Date",
      description: "When the qualification was issued",
    },
    expirationDate: {
      label: "Expiration Date",
      description: "When the qualification expires",
    },
    status: {
      label: "Status",
      description: "Current status of the qualification",
      placeholder: "Select status",
      options: {
        [QualificationStatus.PENDING]: "Pending",
        [QualificationStatus.APPROVED]: "Approved",
        [QualificationStatus.REJECTED]: "Rejected",
        [QualificationStatus.EXPIRED]: "Expired",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

const qualificationFormSchema = z.object({
  providerId: z.string().optional(),
  name: z
    .string()
    .min(2, { message: "Name must be at least 2 characters long" }),
  type: z.nativeEnum(QualificationType),
  institution: z.string().optional(),
  identifier: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  status: z
    .nativeEnum(QualificationStatus)
    .default(QualificationStatus.PENDING),
});

export type QualificationFormValues = z.infer<typeof qualificationFormSchema>;
export type QualificationFormProps = PropsWithChildren<
  Parameters<typeof useForm<QualificationFormValues>>[0] & {
    onSubmit?: (values: QualificationFormValues) => void | Promise<void>;
  }
>;

export default function QualificationForm({
  children,
  onSubmit = () => void 0,
  ...props
}: QualificationFormProps) {
  const { isInternal = false } = useUser();
  const form = useForm<QualificationFormValues>({
    ...props,
    resolver: zodResolver(qualificationFormSchema),
  });

  // Watch the 'type' field value
  const qualificationType = useWatch({
    control: form.control,
    name: "type",
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.type.label}</FormLabel>
              <FormDescription>{i18n.en.type.description}</FormDescription>
              <FormControl>
                {/* Use field.onChange to update the form state */}
                <Select {...field} onValueChange={field.onChange}>
                  <SelectTrigger id={field.name}>
                    <SelectValue placeholder={i18n.en.type.placeholder} />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(i18n.en.type.options).map(
                      ([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      ),
                    )}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <TextField
          name="institution"
          label={i18n.en.institution.label}
          description={i18n.en.institution.description}
          placeholder={i18n.en.institution.placeholder}
        />

        {/* Conditional Identifier field */}
        {(qualificationType === QualificationType.LICENSE ||
          qualificationType === QualificationType.CERTIFICATE) && (
          <IDField
            name="identifier"
            label={i18n.en.identifier.label}
            description={i18n.en.identifier.description}
            placeholder={i18n.en.identifier.placeholder}
          />
        )}

        {/* Conditional State and Country fields */}
        {qualificationType === QualificationType.LICENSE && (
          <div className="grid grid-cols-2 gap-4">
            <TextField
              name="state"
              label={i18n.en.state.label}
              description={i18n.en.state.description}
              placeholder={i18n.en.state.placeholder}
            />
            <TextField
              name="country"
              label={i18n.en.country.label}
              description={i18n.en.country.description}
              placeholder={i18n.en.country.placeholder}
            />
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <DateField
            name="startDate"
            disabled={() => false}
            label={
              qualificationType === QualificationType.DEGREE
                ? i18n.en.startDate.label
                : i18n.en.issueDate.label
            }
            description={
              qualificationType === QualificationType.DEGREE
                ? i18n.en.startDate.description
                : i18n.en.issueDate.description
            }
          />
          <DateField
            name="endDate"
            disabled={() => false}
            label={
              qualificationType === QualificationType.DEGREE
                ? i18n.en.endDate.label
                : i18n.en.expirationDate.label
            }
            description={
              qualificationType === QualificationType.DEGREE
                ? i18n.en.endDate.description
                : i18n.en.expirationDate.description
            }
          />
        </div>

        {isInternal && (
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.status.label}</FormLabel>
                <FormDescription>{i18n.en.status.description}</FormDescription>
                <FormControl>
                  <Select {...field} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder={i18n.en.status.placeholder} />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(i18n.en.status.options).map(
                        ([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ),
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {children ?? (
          <div className="flex w-full justify-center">
            <QualificationFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function QualificationFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<QualificationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
