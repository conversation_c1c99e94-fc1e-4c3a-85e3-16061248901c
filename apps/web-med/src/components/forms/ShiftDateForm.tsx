"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    date: {
      label: "New Shift Date",
      description: "Select a new date for the shift",
      placeholder: "Select date",
    },
    actions: {
      submit: "Reschedule",
    },
  },
};

const shiftDateFormSchema = z.object({
  date: z.date({
    required_error: "Please select a date",
  }),
});

export type ShiftDateFormValues = z.infer<typeof shiftDateFormSchema>;
export type ShiftDateFormProps = PropsWithChildren<{
  defaultValues?: Partial<ShiftDateFormValues>;
  onSubmit?: (values: ShiftDateFormValues) => void | Promise<void>;
}>;

export default function ShiftDateForm({
  children,
  onSubmit = () => void 0,
  defaultValues,
}: ShiftDateFormProps) {
  const form = useForm<ShiftDateFormValues>({
    resolver: zodResolver(shiftDateFormSchema),
    defaultValues: defaultValues ?? {
      date: undefined,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <DateField
          name="date"
          label={i18n.en.date.label}
          description={i18n.en.date.description}
          placeholder={i18n.en.date.placeholder}
          disabled={(date) => date < new Date()} // Disable past dates
        />

        {children ?? (
          <div className="flex w-full items-center justify-center">
            <ShiftDateFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ShiftDateFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ShiftDateFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled ??
        (form.formState.isSubmitting || !form.formState.isDirty)
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
