"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import Day<PERSON>ickerField from "@axa/ui/fields/date-time/DayPicker";
import TimePickerField from "@axa/ui/fields/date-time/TimePicker";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "Name of the timeBlock",
      placeholder: "Enter timeBlock name",
    },
    description: {
      label: "Description",
      description: "Description of the timeBlock",
      placeholder: "Enter timeBlock description",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const timeBlockFormSchema = z.object({
  id: z.string().optional(),
  type: z.string().optional(),
  startTime: z.number().optional(),
  endTime: z.number().optional(),
  dayOfWeek: z.string().optional(),
  recurrence: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

export type TimeBlockFormValues = z.infer<typeof timeBlockFormSchema>;
export type TimeBlockFormProps = PropsWithChildren<
  Parameters<typeof useForm<TimeBlockFormValues>>[0] & {
    onSubmit?: (values: TimeBlockFormValues) => void | Promise<void>;
  }
>;

export default function TimeBlockForm({
  children,
  onSubmit = () => void 0,
  ...props
}: TimeBlockFormProps) {
  const form = useForm<TimeBlockFormValues>({
    ...props,
    resolver: zodResolver(timeBlockFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <DayPickerField name="dayOfWeek" label="Day of Week" />

        <div className="flex w-full flex-col gap-4 sm:flex-row">
          <TimePickerField name="startTime" label="Start Time" />
          <TimePickerField name="endTime" label="End Time" />
        </div>

        {children ?? (
          <div className="flex w-full items-center justify-center">
            <TimeBlockFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function TimeBlockFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<TimeBlockFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
