"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      placeholder: "Enter name",
    },
    actions: {
      submit: "Save",
    },
  },
};

const providerFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

export type ProviderFormValues = z.infer<typeof providerFormSchema>;
export type ProviderFormProps = PropsWithChildren<{
  onSubmit?: (values: ProviderFormValues) => void | Promise<void>;
}>;

export default function ProviderForm({
  children,
  onSubmit = () => void 0,
}: ProviderFormProps) {
  const form = useForm<ProviderFormValues>({
    resolver: zodResolver(providerFormSchema),
    defaultValues: {
      name: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <TextField
          name="name"
          label={i18n.en.name.label}
          placeholder={i18n.en.name.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end">
            <ProviderFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ProviderFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ProviderFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={props.disabled ?? form.formState.isSubmitting}
      type="submit"
    >
      {children}
    </Button>
  );
}
