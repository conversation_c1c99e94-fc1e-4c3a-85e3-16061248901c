"use client";

import { Trash } from "lucide-react";
import { useFormContext } from "react-hook-form";

import { DateField } from "@axa/ui/fields/date-time/Date";
import { TimeField } from "@axa/ui/fields/date-time/Time";
import { <PERSON><PERSON> } from "@axa/ui/primitives/button";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import { TooltipProvider } from "@axa/ui/primitives/tooltip";

const i18n = {
  en: {
    fields: {
      type: {
        label: "Block Type",
        placeholder: "Select block type...",
        options: {
          AVAILABILITY: "Availability",
          TIME_OFF: "Time Off",
          SHIFT: "Shift",
        },
      },
      recurrence: {
        label: "Recurrence",
        placeholder: "Select recurrence...",
        options: {
          WEEKLY: "Weekly",
          BIWEEKLY: "Bi-weekly",
          MONTHLY: "Monthly",
          QUARTERLY: "Quarterly",
          YEARLY: "Yearly",
        },
      },
      hours: {
        label: "Hours",
        placeholder: "Enter hours...",
      },
      startDate: {
        label: "Start Date",
        placeholder: "Select start date...",
      },
      endDate: {
        label: "End Date",
        placeholder: "Select end date...",
      },
      startsAt: {
        label: "Starts At",
        placeholder: "Select start time...",
      },
      endsAt: {
        label: "Ends At",
        placeholder: "Select end time...",
      },
      timeZone: {
        label: "Time Zone",
        placeholder: "Select time zone...",
      },
    },
    actions: {
      remove: "Remove Time Block",
    },
  },
};

export interface TimeBlockFieldProps {
  index: number;
  onRemove: () => void;
}

export function TimeBlockField({ index, onRemove }: TimeBlockFieldProps) {
  const { control } = useFormContext();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <FormField
          control={control}
          name={`timeBlocks.${index}.type`}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormLabel>{i18n.en.fields.type.label}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue
                      placeholder={i18n.en.fields.type.placeholder}
                    />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(i18n.en.fields.type.options).map(
                    ([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ),
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="button" variant="ghost" size="icon" onClick={onRemove}>
          <Trash className="size-4" />
          <span className="sr-only">{i18n.en.actions.remove}</span>
        </Button>
      </div>

      <FormField
        control={control}
        name={`timeBlocks.${index}.recurrence`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{i18n.en.fields.recurrence.label}</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue
                    placeholder={i18n.en.fields.recurrence.placeholder}
                  />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {Object.entries(i18n.en.fields.recurrence.options).map(
                  ([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ),
                )}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name={`timeBlocks.${index}.hours`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{i18n.en.fields.hours.label}</FormLabel>
            <FormControl>
              <Input
                type="number"
                min="0"
                step="0.5"
                onChange={(e) => field.onChange(parseFloat(e.target.value))}
                placeholder={i18n.en.fields.hours.placeholder}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <TooltipProvider>
        <div className="grid grid-cols-2 gap-4">
          <DateField
            name="startDate"
            label={i18n.en.fields.startDate.label}
            description={i18n.en.fields.startDate.placeholder}
            placeholder={i18n.en.fields.startDate.placeholder}
          />

          <DateField
            name="endDate"
            label={i18n.en.fields.endDate.label}
            description={i18n.en.fields.endDate.placeholder}
            placeholder={i18n.en.fields.endDate.placeholder}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <TimeField
            label={i18n.en.fields.startsAt.label}
            description={i18n.en.fields.startsAt.placeholder}
          />

          <TimeField
            label={i18n.en.fields.endsAt.label}
            description={i18n.en.fields.endsAt.placeholder}
          />
        </div>
      </TooltipProvider>

      <FormField
        control={control}
        name={`timeBlocks.${index}.timeZone`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{i18n.en.fields.timeZone.label}</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue
                    placeholder={i18n.en.fields.timeZone.placeholder}
                  />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {Intl.supportedValuesOf("timeZone").map((timeZone) => (
                  <SelectItem key={timeZone} value={timeZone}>
                    {timeZone}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
