"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import {
  AddressAutocompleteField,
  zAddress,
} from "@axa/ui/fields/AddressAutocomplete";
import { PhoneNumberField } from "@axa/ui/fields/numeric/PhoneNumber";
import { EmailField } from "@axa/ui/fields/text/Email";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";

import { OrganizationClass, OrganizationType } from "@/api";

import { useUser } from "../contexts/User";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "Name of the Organization",
      placeholder: "Enter the name of the organization",
      length: "The organization name has to be at least 2 characters.",
    },
    email: {
      label: "Email",
      description: "Email address of the organization",
      placeholder: "Enter the email address",
      invalid: "Please enter a valid email address.",
    },
    phone: {
      label: "Phone",
      description: "Phone number to the organization",
      placeholder: "Enter the phone number",
      length: "Please enter a valid phone number.",
    },
    type: {
      label: "Type",
      description: "The type of organization",
      placeholder: "Select the organization type",
    },
    class: {
      label: "Class",
      description: "The type of business of the organization",
      placeholder: "Select the organization class",
    },
    address: {
      label: "Address",
      description: "Business address of the organization",
      placeholder: "Enter the address",
    },
    actions: {
      submit: "Save Changes",
    },
  },
};

const organizationInformationSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(2, {
    message: i18n.en.name.length,
  }),
  email: z.string().email({
    message: i18n.en.email.invalid,
  }),
  phone: z.string().min(10, {
    message: i18n.en.phone.length,
  }),
  type: z.nativeEnum(OrganizationType).default(OrganizationType.CLIENT),
  class: z.nativeEnum(OrganizationClass),
  address: zAddress,
});

const typeOptions = [
  { label: "Internal", value: OrganizationType.INTERNAL },
  { label: "Client", value: OrganizationType.CLIENT },
  { label: "Account", value: OrganizationType.ACCOUNT },
] as const;

const classOptions = [
  { label: "Private Company", value: OrganizationClass.PRIVATE },
  { label: "Non-Profit Organization", value: OrganizationClass.NONPROFIT },
  { label: "Government Organization", value: OrganizationClass.GOVERNMENT },
] as const;

export type OrganizationInformationFormValues = z.infer<
  typeof organizationInformationSchema
>;

export interface OrganizationInformationFormProps {
  defaultValues?: Partial<OrganizationInformationFormValues>;
  onSubmit?: (
    values: OrganizationInformationFormValues,
  ) => void | Promise<void>;
}

export default function OrganizationInformation({
  defaultValues,
  onSubmit = () => void 0,
  children,
}: PropsWithChildren<OrganizationInformationFormProps>) {
  const { isInternal } = useUser();

  const form = useForm<OrganizationInformationFormValues>({
    resolver: zodResolver(organizationInformationSchema),
    defaultValues: {
      ...defaultValues,
      type: OrganizationType.CLIENT,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <AddressAutocompleteField
          label={i18n.en.address.label}
          description={i18n.en.address.description}
          placeholder={i18n.en.address.placeholder}
        />

        <EmailField
          name="email"
          label={i18n.en.email.label}
          description={i18n.en.email.description}
          placeholder={i18n.en.email.placeholder}
        />

        <PhoneNumberField
          name="phone"
          label={i18n.en.phone.label}
          description={i18n.en.phone.description}
          placeholder={i18n.en.phone.placeholder}
        />

        <div className="grid grid-cols-2 gap-4">
          {isInternal ? (
            <>
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem className="flex h-full flex-col justify-between gap-2 space-y-0">
                    <FormLabel>{i18n.en.type.label}</FormLabel>
                    <FormDescription>
                      {i18n.en.type.description}
                    </FormDescription>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={i18n.en.type.placeholder} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {typeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="class"
                render={({ field }) => (
                  <FormItem className="flex h-full flex-col justify-between gap-2 space-y-0">
                    <FormLabel>{i18n.en.class.label}</FormLabel>
                    <FormDescription>
                      {i18n.en.class.description}
                    </FormDescription>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={i18n.en.class.placeholder}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {classOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          ) : (
            <FormField
              control={form.control}
              name="class"
              render={({ field }) => (
                <FormItem className="col-span-2 flex h-full flex-col justify-between gap-2 space-y-0">
                  <FormLabel>{i18n.en.class.label}</FormLabel>
                  <FormDescription>{i18n.en.class.description}</FormDescription>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={i18n.en.class.placeholder} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {classOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        {children ?? (
          <div className="flex justify-center">
            <OrganizationInformationSubmit />
          </div>
        )}
      </form>
    </Form>
  );
}

export function OrganizationInformationSubmit({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<OrganizationInformationFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
