"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";

import { PaymentType } from "@/api";

const i18n = {
  en: {
    type: {
      label: "Job Type",
      description: "The type of job",
      placeholder: "Select the job type",
      options: {
        PERMANENT: "Permanent",
        TEMPORARY: "Temporary",
        PER_DIEM: "Per Diem",
      },
    },
    paymentType: {
      label: "Payment Type",
      description: "The type of payment",
      placeholder: "Select the payment type",
      options: {
        HOURLY: "Hourly",
        FIXED: "Fixed",
      },
    },
    paymentAmount: {
      label: "Pay Amount",
      description: "The pay amount for the job",
      placeholder: "Enter the pay amount",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const paymentFormSchema = z.object({
  paymentType: z.enum([PaymentType.HOURLY, PaymentType.FIXED]),
  paymentAmount: z.string().min(0),
  paymentRate: z.number().min(1),
  nightRate: z.number().min(1).optional(),
  overtimeRate: z.number().min(1).optional(),
  holidayRate: z.number().min(1).optional(),
  bonusRate: z.number().min(1).optional(),
});

export type JobPostPaymentFormValues = z.infer<typeof paymentFormSchema>;

export interface JobPostPaymentFormProps {
  defaultValues?: Partial<JobPostPaymentFormValues>;
  onSubmit: (values: JobPostPaymentFormValues) => void;
}

export default function JobPostPaymentForm({
  defaultValues,
  onSubmit,
  children,
}: PropsWithChildren<JobPostPaymentFormProps>) {
  const form = useForm<JobPostPaymentFormValues>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      paymentType: defaultValues?.paymentType ?? PaymentType.HOURLY,
      paymentAmount: defaultValues?.paymentAmount ?? "0",
      paymentRate: defaultValues?.paymentRate ?? 1,
      nightRate: defaultValues?.nightRate ?? 1.25,
      overtimeRate: defaultValues?.overtimeRate ?? 1.5,
      holidayRate: defaultValues?.holidayRate ?? 2,
      bonusRate: defaultValues?.bonusRate ?? 1,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="paymentType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Type</FormLabel>
                <FormDescription>
                  The type of payment for the job
                </FormDescription>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={PaymentType.HOURLY}>Hourly</SelectItem>
                    <SelectItem value={PaymentType.FIXED}>Fixed</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <CurrencyField
            name="paymentAmount"
            label="Base Rate"
            placeholder="Enter base rate"
          />
        </div>

        <div className="space-y-4">
          <h3 className="text-sm font-medium">Rate Multipliers</h3>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="nightRate"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between space-x-2 rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Night Rate</FormLabel>
                    <FormDescription>
                      Rate multiplier for night shifts
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      step={0.25}
                      className="w-20"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value))
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="overtimeRate"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between space-x-2 rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Overtime Rate</FormLabel>
                    <FormDescription>
                      Rate multiplier for overtime hours
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      step={0.25}
                      className="w-20"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value))
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="holidayRate"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between space-x-2 rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Holiday Rate</FormLabel>
                    <FormDescription>
                      Rate multiplier for holiday shifts
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      step={0.25}
                      className="w-20"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value))
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bonusRate"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between space-x-2 rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Bonus Rate</FormLabel>
                    <FormDescription>
                      Rate multiplier for bonus pay
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      step={0.25}
                      className="w-20"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value))
                      }
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        {children ?? (
          <div className="flex justify-center gap-2">
            <JobPostPaymentFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function JobPostPaymentFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<JobPostPaymentFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
