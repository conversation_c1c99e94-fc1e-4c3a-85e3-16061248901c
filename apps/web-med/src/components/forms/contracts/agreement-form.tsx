"use client";

import type { ButtonHTMLAttributes } from "react";

import { useCallback } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { addMonths } from "date-fns";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import { DateTimeField } from "@axa/ui/fields/date-time/DateTime";
import { cn } from "@axa/ui/lib";
import { But<PERSON> } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { ContractType } from "@/api";

import { AgreementSection, zAgreement } from "./agreement";
import { SignersSection, zSignatures } from "./signatures";

// Import the i18n object for localization
const i18n = {
  en: {
    labels: {
      provider: "Provider",
      organization: "Organization",
      internal: "Internal User",
      document: "Upload Document",
      documensoId: "Documenso Document ID",
      expiresAt: "Expiration Date",
    },
    descriptions: {
      provider: "Select the provider who will sign this agreement",
      organization: "Select the organization that will sign this agreement",
      internal: "Select the internal user who will sign this agreement",
      document: "Upload a document for this agreement",
      documensoId: "Enter a Documenso document ID to link to this agreement",
      expiresAt: "Set an expiration date for this agreement",
    },
    placeholders: {
      documensoId: "Enter Documenso document ID",
      provider: "Select a provider",
      organization: "Select an organization",
      internal: "Select an internal user",
    },
    actions: {
      submit: "Create Agreement",
      addSigner: "Add Signer",
      removeSigner: "Remove",
    },
  },
};

// Define the schema for the form
export const agreementFormSchema = z.object({
  contractId: z.string(),
  type: z.enum(["job-position", "service-rate", "custom"]).optional(),

  // Resolved signers array
  signers: zSignatures.optional(),
  agreement: zAgreement,

  // Additional fields
  expiresAt: z.date().optional(),
});

// Define the form values type
export type AgreementFormValues = z.infer<typeof agreementFormSchema>;

// Define the props for the form
export interface AgreementFormProps {
  contractId: string;
  contractType?: ContractType;
  defaultValues?: Partial<AgreementFormValues>;
  onSubmit: (values: AgreementFormValues) => Promise<void>;
  children?: React.ReactNode;
  organizationId?: string;
}

// Define button props type
type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> & {
  className?: string;
};

// Submit button component
export function AgreementFormSubmitButton({
  className,
  ...props
}: ButtonProps) {
  const form = useFormContext<AgreementFormValues>();

  return (
    <Button
      type="submit"
      disabled={!form.formState.isValid || form.formState.isSubmitting}
      className={cn("min-w-[100px]", className)}
      {...props}
    >
      {form.formState.isSubmitting ? "Saving..." : i18n.en.actions.submit}
    </Button>
  );
}

// Main form component
export default function AgreementForm({
  contractId,
  contractType,
  defaultValues = {},
  onSubmit,
  children,
  organizationId,
}: AgreementFormProps) {
  const form = useForm<AgreementFormValues>({
    resolver: zodResolver(agreementFormSchema),
    defaultValues: {
      contractId,
      ...defaultValues,
      signers: defaultValues.signers ?? [],
      agreement: defaultValues.agreement ?? {
        generate:
          contractType === ContractType.EMPLOYMENT ||
          contractType === ContractType.SERVICE_RATE,
      },
      // Set default expiration date to one month from now
      expiresAt: defaultValues.expiresAt ?? addMonths(new Date(), 1),
      type:
        defaultValues.type ??
        (contractType === ContractType.EMPLOYMENT
          ? "job-position"
          : contractType === ContractType.SERVICE_RATE
            ? "service-rate"
            : "custom"),
    },
  });

  // Handle form submission
  const handleSubmit = useCallback(
    async (values: AgreementFormValues) => {
      await onSubmit(values);
    },
    [onSubmit],
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <SignersSection
          name="signers"
          contractType={contractType}
          isCreate={true}
          organizationId={organizationId}
        />

        <AgreementSection
          name="agreement"
          contractType={contractType}
          isCreate={true}
        />

        {/* Expiration date field */}
        <DateTimeField
          name="expiresAt"
          label={i18n.en.labels.expiresAt}
          description={i18n.en.descriptions.expiresAt}
          minDateTime={new Date()}
          disabled={form.formState.isSubmitting}
          className="mb-4"
        />

        {/* Submit button or children */}
        <div className="flex justify-end">
          {children ?? <AgreementFormSubmitButton />}
        </div>
      </form>
    </Form>
  );
}
