"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { TextField } from "@/ui/fields/text/Text";

const i18n = {
  en: {
    fields: {
      title: {
        label: "Contract Title",
        placeholder: "Enter contract title...",
        description: "A descriptive name for this contract",
      },
    },
    actions: {
      submit: "Save Changes",
    },
  },
};

// Simple schema that only includes title
const simpleContractUpdateSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(2, "The title has to be at least 2 characters."),
});

export type SimpleContractUpdateFormValues = z.infer<
  typeof simpleContractUpdateSchema
>;

export interface SimpleContractUpdateFormProps {
  defaultValues?: Partial<SimpleContractUpdateFormValues>;
  onSubmit?: (values: SimpleContractUpdateFormValues) => void | Promise<void>;
}

export function SimpleContractUpdateFormSubmitButton({
  className,
  ...props
}: ButtonProps) {
  const form = useFormContext<SimpleContractUpdateFormValues>();

  return (
    <Button
      type="submit"
      disabled={!form.formState.isValid || form.formState.isSubmitting}
      className={cn("min-w-[100px]", className)}
      {...props}
    >
      {form.formState.isSubmitting ? "Saving..." : i18n.en.actions.submit}
    </Button>
  );
}

export default function SimpleContractUpdateForm({
  defaultValues,
  onSubmit = () => void 0,
  children,
}: PropsWithChildren<SimpleContractUpdateFormProps>) {
  const form = useForm<SimpleContractUpdateFormValues>({
    resolver: zodResolver(simpleContractUpdateSchema),
    defaultValues: {
      title: "",
      ...defaultValues,
    },
    mode: "onChange",
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8"
        noValidate
      >
        <TextField
          name="title"
          label={i18n.en.fields.title.label}
          placeholder={i18n.en.fields.title.placeholder}
          description={i18n.en.fields.title.description}
        />

        {children ?? (
          <div className="flex justify-end">
            <SimpleContractUpdateFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}
