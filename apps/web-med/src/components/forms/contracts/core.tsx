"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { ContractType } from "@/api";
import { SelectOrganizationField } from "@/components/selectors/SelectOrganization";
import { TextField } from "@/ui/fields/text/Text";

import { ContractTypeField } from "../fields/ContractType";
import { AgreementSection, zAgreement } from "./agreement";
import { SignersSection, zSignatures } from "./signatures";

const i18n = {
  en: {
    fields: {
      title: {
        label: "Contract Title",
        placeholder: "Enter contract title...",
        description: "A descriptive name for this contract",
      },
      type: {
        label: "Contract Type",
        placeholder: "Select contract type...",
        description: "The category of agreement this contract represents",
      },
      organization: {
        label: "Organization",
        placeholder: "Select organization...",
        description: "The organization this contract is associated with",
      },
    },
    actions: {
      submit: "Save",
    },
  },
};

const coreContractFormSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(2, "The title has to be at least 2 characters."),
  type: z.nativeEnum(ContractType),
  organization: z.string(),
  signers: zSignatures,
  agreement: zAgreement, // Optional agreement
});

export type CoreContractFormValues = z.infer<typeof coreContractFormSchema>;

export interface CoreContractFormProps {
  defaultValues?: Partial<CoreContractFormValues>;
  onSubmit?: (values: CoreContractFormValues) => void | Promise<void>;
  /** Whether this is for creating a new contract (true) or updating an existing one (false) */
  isCreate?: boolean;
}

export function CoreContractFormSubmitButton({
  className,
  ...props
}: ButtonProps) {
  const form = useFormContext<CoreContractFormValues>();

  return (
    <Button
      type="submit"
      disabled={!form.formState.isValid || form.formState.isSubmitting}
      className={cn("min-w-[100px]", className)}
      {...props}
    >
      {form.formState.isSubmitting ? "Saving..." : i18n.en.actions.submit}
    </Button>
  );
}

export default function CoreContractForm({
  defaultValues,
  onSubmit = () => void 0,
  isCreate = true,
  children,
}: PropsWithChildren<CoreContractFormProps>) {
  const form = useForm<CoreContractFormValues>({
    resolver: zodResolver(coreContractFormSchema),
    defaultValues: defaultValues ?? {
      id: undefined,
      title: "",
      type: undefined,
      organization: "",
      signers: [],
      agreement: {
        generate: false,
        document: undefined,
        documensoId: undefined,
      },
    },
    mode: "onChange",
  });

  // Watch contract type and organization to pass to child components
  const contractType = form.watch("type");
  const organizationId = form.watch("organization");

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <TextField
          name="title"
          label={i18n.en.fields.title.label}
          description={i18n.en.fields.title.description}
          placeholder={i18n.en.fields.title.placeholder}
        />
        <ContractTypeField
          name="type"
          label={i18n.en.fields.type.label}
          description={i18n.en.fields.type.description}
          placeholder={i18n.en.fields.type.placeholder}
        />

        <SelectOrganizationField
          name="organization"
          label={i18n.en.fields.organization.label}
          description={i18n.en.fields.organization.description}
          placeholder={i18n.en.fields.organization.placeholder}
        />

        <SignersSection
          isCreate={isCreate}
          contractType={contractType}
          organizationId={organizationId}
        />

        {/* Agreement section */}
        <AgreementSection isCreate={isCreate} contractType={contractType} />

        {children ?? (
          <>
            <div className="flex w-full justify-center">
              <CoreContractFormSubmitButton />
            </div>
          </>
        )}
      </form>
    </Form>
  );
}
