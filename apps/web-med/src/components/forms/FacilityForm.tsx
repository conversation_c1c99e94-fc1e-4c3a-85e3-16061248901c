"use client";

import type { PropsWithChildren } from "react";

import React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import {
  AddressAutocompleteField,
  zAddress,
} from "@axa/ui/fields/AddressAutocomplete";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import {
  FacilityTypeField,
  zFacilityType,
} from "@/components/forms/fields/FacilityType";

const i18n = {
  en: {
    why: {
      title: "Why do we need facility information?",
      description:
        "Detailed facility information helps healthcare professionals understand their potential workplace and ensures we can match them with opportunities that align with their skills and preferences.",
    },
    fields: {
      name: {
        label: "Facility Name",
        placeholder: "Enter the name of your facility",
        description:
          "The name that healthcare professionals will see when browsing opportunities. This should be the commonly known name of your facility.",
        errors: {
          required: "Please enter your facility name",
        },
      },
      address: {
        label: "Address",
        placeholder: "Enter the address of your facility",
        description:
          "The address of your facility helps healthcare professionals find it when browsing opportunities.",
      },
      facilityType: {
        label: "Facility Type",
        placeholder: "Select the type of your facility",
        description:
          "The type of your facility helps healthcare professionals understand what they can expect when working at your facility.",
      },
      description: {
        label: "Description",
        placeholder: "Describe your facility...",
        description:
          "Provide details about your facility's specialties, equipment, and what makes it unique. This helps attract the right healthcare professionals.",
      },
    },
    actions: {
      submit: "Submit",
    },
  },
};

const facilityFormSchema = z.object({
  type: zFacilityType,
  address: zAddress,
  name: z.string().min(1, i18n.en.fields.name.errors.required),
  description: z.string().optional(),
  organizationId: z.string().optional(),
});

export type FacilityFormValues = z.infer<typeof facilityFormSchema>;
export type FacilityFormProps = PropsWithChildren<
  Parameters<typeof useForm<FacilityFormValues>>[0] & {
    onSubmit?: (values: FacilityFormValues) => void | Promise<void>;
    isCompact?: boolean;
  }
>;

export function FacilityFormWhy() {
  return (
    <div className="flex flex-col gap-2 rounded-lg border border-border bg-blue-50 p-4 dark:bg-blue-950 dark:text-foreground">
      <p className="text-sm font-semibold">{i18n.en.why.title}</p>
      <p className="text-sm text-muted-foreground">{i18n.en.why.description}</p>
    </div>
  );
}

export default function FacilityForm({
  children,
  onSubmit = () => void 0,
  isCompact,
  ...props
}: FacilityFormProps) {
  const form = useForm<FacilityFormValues>({
    resolver: zodResolver(facilityFormSchema),
    defaultValues: props.defaultValues ?? {
      type: undefined,
      name: "",
      description: "",
      address: {
        formatted: "",
        country: "",
        state: "",
        city: "",
        street: "",
        postal: "",
        latitude: 0,
        longitude: 0,
      },
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <AddressAutocompleteField
          label={i18n.en.fields.address.label}
          description={i18n.en.fields.address.description}
          placeholder={i18n.en.fields.address.placeholder}
        />

        <TextField
          name="name"
          label={i18n.en.fields.name.label}
          description={i18n.en.fields.name.description}
          placeholder={i18n.en.fields.name.placeholder}
        />

        <FacilityTypeField
          isCompact={isCompact}
          label={i18n.en.fields.facilityType.label}
          description={i18n.en.fields.facilityType.description}
          placeholder={i18n.en.fields.facilityType.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.fields.description.label}
          description={i18n.en.fields.description.description}
          placeholder={i18n.en.fields.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <FacilityFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function FacilityFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<FacilityFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled ??
        (form.formState.isSubmitting || !form.formState.isDirty)
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
