"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    name: {
      label: "Name",
      description: "Name of the specialty",
      placeholder: "Enter specialty name",
    },
    description: {
      label: "Description",
      description: "Description of the specialty",
      placeholder: "Enter specialty description",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const specialtyFormSchema = z.object({
  id: z.string().optional(),
  providerId: z.string().optional(),
  jobId: z.string().optional(),
  shiftId: z.string().optional(),
  experienceId: z.string().optional(),
  name: z
    .string()
    .min(2, { message: "Name must be at least 2 characters long" }),
  description: z.string().optional(),
});

export type SpecialtyFormValues = z.infer<typeof specialtyFormSchema>;
export type SpecialtyFormProps = PropsWithChildren<
  Parameters<typeof useForm<SpecialtyFormValues>>[0] & {
    onSubmit?: (values: SpecialtyFormValues) => void | Promise<void>;
  }
>;

export default function SpecialtyForm({
  children,
  onSubmit = () => void 0,
  ...props
}: SpecialtyFormProps) {
  const form = useForm<SpecialtyFormValues>({
    ...props,
    resolver: zodResolver(specialtyFormSchema),
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <SpecialtyFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function SpecialtyFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<SpecialtyFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled ??
        (form.formState.isSubmitting || !form.formState.isDirty)
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
