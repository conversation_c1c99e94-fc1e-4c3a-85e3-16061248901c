"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    role: {
      label: "Role",
      description: "What was the role of the job?",
      placeholder: "Enter your job title",
    },
    description: {
      label: "Description",
      description: "Can you describe the job?",
      placeholder: "Enter job description",
    },
    company: {
      label: "Company",
      description: "What was the company name?",
      placeholder: "Enter company or hospital name",
    },
    startDate: {
      label: "Start Date",
      description: "When did you start working at the company?",
      placeholder: "Select start date for this role",
    },
    endDate: {
      label: "End Date",
      description: "When did you leave the company?",
      placeholder: "Select end date for this role",
    },
    actions: {
      submit: "Save",
    },
  },
};

const jobExperienceSchema = z.object({
  id: z.string().optional(),
  providerId: z.string().optional(),
  role: z.string().min(1, "Role is required"),
  company: z.string().min(1, "Company is required"),
  description: z.string().optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  specialties: z.array(z.string()).optional(),
});

export type JobExperienceFormValues = z.infer<typeof jobExperienceSchema>;
export type JobExperienceFormProps = PropsWithChildren<{
  defaultValues?: JobExperienceFormValues;
  onSubmit?: (values: JobExperienceFormValues) => void | Promise<void>;
}>;

export default function JobExperienceForm({
  children,
  defaultValues,
  onSubmit = () => void 0,
}: JobExperienceFormProps) {
  const form = useForm<JobExperienceFormValues>({
    resolver: zodResolver(jobExperienceSchema),
    defaultValues: {
      role: defaultValues?.role ?? "",
      company: defaultValues?.company ?? "",
      startDate: defaultValues?.startDate ?? new Date(),
      endDate: defaultValues?.endDate ?? undefined,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <TextField
          name="role"
          label={i18n.en.role.label}
          placeholder={i18n.en.role.placeholder}
          description={i18n.en.role.description}
        />

        <TextField
          name="company"
          label={i18n.en.company.label}
          placeholder={i18n.en.company.placeholder}
          description={i18n.en.company.description}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          placeholder={i18n.en.description.placeholder}
          description={i18n.en.description.description}
        />

        <DateField
          disabled={() => false}
          name="startDate"
          label={i18n.en.startDate.label}
          description={i18n.en.startDate.description}
          placeholder={i18n.en.startDate.placeholder}
        />

        <DateField
          disabled={() => false}
          name="endDate"
          label={i18n.en.endDate.label}
          description={i18n.en.endDate.description}
          placeholder={i18n.en.endDate.placeholder}
        />

        {/* <FormField
          control={form.control}
          name="specialties"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{i18n.en.fields.specialties.label}</FormLabel>
              <FormDescription>
                {i18n.en.fields.specialties.placeholder}
              </FormDescription>
              <FormControl>
                <SearchSpecialty
                  onSelect={(
                    specialty: RouterOutputs["specialties"]["getMany"]["items"][number],
                  ) => field.onChange([...(field.value ?? []), specialty.id])}
                  defaultValue={field.value?.join(", ")}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}

        {children ?? (
          <div className="flex w-full justify-end">
            <JobExperienceFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function JobExperienceFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<JobExperienceFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      disabled={props.disabled || form.formState.isSubmitting}
      type="submit"
    >
      {children}
    </Button>
  );
}
