"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    language: {
      label: "Language",
      placeholder: "Enter language name",
      presets: "Common languages",
    },
    actions: {
      submit: "Save",
    },
  },
};

// Common languages preset
const commonLanguages = [
  "English",
  "Spanish",
  "French",
  "Arabic",
  "Mandarin",
  "Hindi",
  "Portuguese",
  "Russian",
  "Japanese",
  "German",
  "Korean",
  "Italian",
];

const languageFormSchema = z.object({
  language: z.string().min(1, "Language is required"),
});

export type ProviderLanguageFormValues = z.infer<typeof languageFormSchema>;
export type ProviderLanguageFormProps = PropsWithChildren<{
  onSubmit?: (values: ProviderLanguageFormValues) => void | Promise<void>;
}>;

export default function ProviderLanguageForm({
  children,
  onSubmit = () => void 0,
}: ProviderLanguageFormProps) {
  const form = useForm<ProviderLanguageFormValues>({
    resolver: zodResolver(languageFormSchema),
    defaultValues: {
      language: "",
    },
  });

  const setLanguage = (language: string) => {
    form.setValue("language", language);
    void form.trigger("language");
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <TextField
          name="language"
          label={i18n.en.language.label}
          placeholder={i18n.en.language.placeholder}
        />

        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            {i18n.en.language.presets}
          </p>
          <div className="flex flex-wrap gap-2">
            {commonLanguages.map((language) => (
              <Button
                key={language}
                type="button"
                size="sm"
                variant="outline"
                onClick={() => setLanguage(language)}
              >
                {language}
              </Button>
            ))}
          </div>
        </div>

        {children ?? (
          <div className="flex w-full justify-end">
            <ProviderLanguageFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function ProviderLanguageFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ProviderLanguageFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={props.disabled ?? form.formState.isSubmitting}
      type="submit"
    >
      {children}
    </Button>
  );
}
