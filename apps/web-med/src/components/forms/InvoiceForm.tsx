"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { IDField } from "@axa/ui/fields/text/ID";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

const i18n = {
  en: {
    id: {
      label: "ID",
      placeholder: "Enter ID",
    },
    actions: {
      submit: "Save",
    },
  },
};

const invoiceFormSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

export type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;
export type InvoiceFormProps = PropsWithChildren<{
  onSubmit?: (values: InvoiceFormValues) => void | Promise<void>;
}>;

export default function ProviderLanguageForm({
  children,
  onSubmit = () => void 0,
}: InvoiceFormProps) {
  const form = useForm<InvoiceFormValues>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      id: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <IDField
          name="id"
          label={i18n.en.id.label}
          placeholder={i18n.en.id.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-end">
            <InvoiceFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function InvoiceFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<InvoiceFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={props.disabled ?? form.formState.isSubmitting}
      type="submit"
    >
      {children}
    </Button>
  );
}
