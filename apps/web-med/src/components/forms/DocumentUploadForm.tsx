"use client";

import type { PropsWithChildren } from "react";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { toast } from "@axa/ui/primitives/toast";

import type { DocumentUploadResult } from "@/components/forms/fields/Document";

import { DocumentField, zDocument } from "@/components/forms/fields/Document";

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_FILE_TYPES = [
  "application/pdf",
  "image/jpeg",
  "image/png",
  "image/jpg",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

const i18n = {
  en: {
    title: {
      label: "Document Title",
      description: "A descriptive title for the document",
      placeholder: "Enter document title",
    },
    description: {
      label: "Description",
      description: "Brief description of the document",
      placeholder: "Enter document description",
    },
    document: {
      label: "Upload Document",
      description: "Select a document to upload (PDF, JPEG, PNG, DOC, DOCX)",
      placeholder: "Select a file",
    },
    actions: {
      submit: "Upload Document",
    },
    messages: {
      title: {
        required: "Document title is required",
        min: "Title must be at least 3 characters long",
      },
      description: {
        min: "Description must be at least 10 characters long",
      },
      document: {
        required: "Document is required",
        size: `File size must be less than 5MB`,
        type: "File must be a PDF, JPEG, PNG, DOC, or DOCX",
      },
      success: "Document uploaded successfully!",
      error: "Failed to upload document. Please try again.",
      invalidFile: "Please select a valid file before submitting.",
    },
  },
};

const documentUploadFormSchema = z.object({
  title: z
    .string({
      required_error: i18n.en.messages.title.required,
    })
    .min(3, { message: i18n.en.messages.title.min }),
  description: z
    .string()
    .min(10, { message: i18n.en.messages.description.min })
    .optional(),
  document: zDocument,
});

export type DocumentUploadFormValues = z.infer<typeof documentUploadFormSchema>;

export interface DocumentUploadSubmitData {
  title: string;
  description?: string;
  uploadResult: {
    url: string;
    path: string;
    fileName: string;
    fileType: string;
    fileSize: number;
  } | null;
}

export type DocumentUploadFormProps = PropsWithChildren<
  Parameters<typeof useForm<DocumentUploadFormValues>>[0] & {
    uploadDocument?: (file: File) => Promise<DocumentUploadResult>;
    isUploading?: boolean;
    uploadProgress?: number;
    uploadError?: string | null;
    onSubmit?: (data: DocumentUploadSubmitData) => void | Promise<void>;
    onError?: (error: string) => void | Promise<void>;
  }
>;

export default function DocumentUploadForm({
  children,
  uploadDocument,
  isUploading = false,
  uploadProgress = 0,
  uploadError = null,
  onSubmit,
  onError,
  ...props
}: DocumentUploadFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [progress, setProgress] = useState(uploadProgress);

  const form = useForm<DocumentUploadFormValues>({
    ...props,
    resolver: zodResolver(documentUploadFormSchema),
  });

  useEffect(() => {
    setProgress(uploadProgress);
  }, [uploadProgress]);

  const handleSubmit = async (values: DocumentUploadFormValues) => {
    try {
      setIsSubmitting(true);

      const file = values.document?.[0];

      if (
        !file ||
        !ACCEPTED_FILE_TYPES.includes(file.type) ||
        file.size > MAX_FILE_SIZE
      ) {
        toast.error(i18n.en.messages.invalidFile);
        if (onError) await onError(i18n.en.messages.invalidFile);
        return;
      }

      if (!uploadDocument) {
        toast.error("Upload functionality not provided");
        if (onError) await onError("Upload functionality not provided");
        return;
      }

      const uploadResult = await uploadDocument(file);

      if (!uploadError) {
        toast.success(i18n.en.messages.success);

        // Call the onSubmit callback with the form values and upload result
        // We know uploadResult is not null here because of the condition above
        if (onSubmit) {
          await onSubmit({
            title: values.title,
            description: values.description,
            uploadResult: {
              url: uploadResult.url,
              path: uploadResult.id,
              fileName: uploadResult.filename,
              fileType: uploadResult.contentType,
              fileSize: uploadResult.size,
            },
          });
        }

        form.reset();
      } else {
        const errorMessage = uploadError;
        toast.error(errorMessage);
        if (onError) await onError(errorMessage);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : i18n.en.messages.error;
      toast.error(errorMessage);
      if (onError) await onError(errorMessage);
    } finally {
      setIsSubmitting(false);
      setProgress(0);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        <TextField
          name="title"
          label={i18n.en.title.label}
          description={i18n.en.title.description}
          placeholder={i18n.en.title.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        <FormField
          control={form.control}
          name="document"
          render={({ field: { onChange, value } }) => (
            <FormItem>
              <FormLabel>{i18n.en.document.label}</FormLabel>
              <FormDescription>{i18n.en.document.description}</FormDescription>
              <FormControl>
                <DocumentField
                  name="document"
                  value={value}
                  onChange={onChange}
                  placeholder={i18n.en.document.placeholder}
                  allowedFileTypes={ACCEPTED_FILE_TYPES}
                  maxSizeInMB={MAX_FILE_SIZE / (1024 * 1024)}
                  uploadDocument={isSubmitting ? uploadDocument : undefined}
                  isUploading={isSubmitting || isUploading}
                  progress={progress}
                  onUploadComplete={(_result) => {
                    // This is handled by the form submit handler
                  }}
                  onUploadError={(_error) => {
                    // This is handled by the form submit handler
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <DocumentUploadFormSubmitButton
              isUploading={isSubmitting || isUploading}
            />
          </div>
        )}
      </form>
    </Form>
  );
}

export function DocumentUploadFormSubmitButton({
  children = i18n.en.actions.submit,
  isUploading = false,
  ...props
}: ButtonProps & { isUploading?: boolean }) {
  const form = useFormContext();

  return (
    <Button
      type="submit"
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        props.disabled ??
        (isUploading || form.formState.isSubmitting || !form.formState.isValid)
      }
    >
      {isUploading ? "Uploading..." : children}
    </Button>
  );
}
