"use client";

import type { PropsWithChildren } from "react";

import React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DescriptionField } from "@axa/ui/fields/text/Description";
import { TextField } from "@axa/ui/fields/text/Text";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import { Form } from "@axa/ui/primitives/form";

import { DepartmentType } from "@/api";

import { DepartmentTypeField, zDepartmentType } from "./fields/DepartmentType";

const i18n = {
  en: {
    name: {
      label: "Department Name",
      description:
        "The name of the department, such as 'Cardiology' or 'Neurology'.",
      placeholder: "Enter department name",
    },
    description: {
      label: "Description",
      description:
        "Describe the department and its purpose or function within the facility.",
      placeholder: "Enter department description",
    },
    type: {
      label: "Department Type",
      description:
        "Select the type of department, such as 'Department', 'Center', 'Room', or 'Other'.",
      placeholder: "Select department type",
    },
    messages: {
      required: "Department name is required",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const departmentFormSchema = z.object({
  facilityId: z.string(),
  type: zDepartmentType,
  name: z.string().min(1, i18n.en.messages.required),
  description: z.string().optional(),
});

export type DepartmentFormValues = z.infer<typeof departmentFormSchema>;
export type DepartmentFormProps = PropsWithChildren<
  Parameters<typeof useForm<DepartmentFormValues>>[0] & {
    onSubmit?: (values: DepartmentFormValues) => void | Promise<void>;
  }
>;

export default function DepartmentForm({
  children,
  onSubmit = () => void 0,
  ...props
}: DepartmentFormProps) {
  const form = useForm<DepartmentFormValues>({
    resolver: zodResolver(departmentFormSchema),
    defaultValues: props.defaultValues ?? {
      name: "",
      description: "",
      type: DepartmentType.DEPARTMENT,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <TextField
          name="name"
          label={i18n.en.name.label}
          description={i18n.en.name.description}
          placeholder={i18n.en.name.placeholder}
        />

        <DepartmentTypeField
          name="type"
          label={i18n.en.type.label}
          description={i18n.en.type.description}
          placeholder={i18n.en.type.placeholder}
        />

        <DescriptionField
          name="description"
          label={i18n.en.description.label}
          description={i18n.en.description.description}
          placeholder={i18n.en.description.placeholder}
        />

        {children ?? (
          <div className="flex w-full justify-center">
            <DepartmentFormSubmitButton />
          </div>
        )}
      </form>
    </Form>
  );
}

export function DepartmentFormSubmitButton({
  children = "Submit",
  ...props
}: ButtonProps) {
  const form = useFormContext<DepartmentFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
