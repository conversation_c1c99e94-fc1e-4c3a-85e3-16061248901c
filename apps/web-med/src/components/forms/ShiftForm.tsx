"use client";

import type { PropsWithChildren } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFormContext } from "react-hook-form";
import { z } from "zod";

import type { ButtonProps } from "@axa/ui/primitives/button";
import { DateField } from "@axa/ui/fields/date-time/Date";
import TimePickerField from "@axa/ui/fields/date-time/TimePicker";
import { CurrencyField } from "@axa/ui/fields/numeric/Currency";
import { cn } from "@axa/ui/lib";
import { Button } from "@axa/ui/primitives/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@axa/ui/primitives/form";
import { Input } from "@axa/ui/primitives/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@axa/ui/primitives/select";
import { Textarea } from "@axa/ui/primitives/textarea";
import { TooltipProvider } from "@axa/ui/primitives/tooltip";

import { PaymentType } from "@/api";

const i18n = {
  en: {
    summary: {
      label: "Summary",
      description: "A brief summary of the shift",
      placeholder: "Enter a brief summary of the shift",
    },
    scope: {
      label: "Description",
      description: "Detailed description of the shift",
      placeholder: "Enter the shift description",
    },
    type: {
      label: "Shift Type",
      description: "The type of shift",
      placeholder: "Select the shift type",
      options: {
        DAY: "Day",
        NIGHT: "Night",
        EVENING: "Evening",
      },
    },
    paymentType: {
      label: "Payment Type",
      description: "The type of payment",
      placeholder: "Select the payment type",
      options: {
        HOURLY: "Hourly",
        FLAT_RATE: "Flat Rate",
        PER_DIEM: "Per Diem",
      },
    },
    paymentRate: {
      label: "Payment Rate",
      description: "The payment rate for the shift",
      placeholder: "Enter the payment rate",
    },
    startDate: {
      label: "Start Date",
      description: "The start date of the shift",
      placeholder: "Select the start date",
    },
    endDate: {
      label: "End Date",
      description: "The end date of the shift",
      placeholder: "Select the end date",
    },
    startTime: {
      label: "Start Time",
      description: "The start time of the shift",
      placeholder: "Select the start time",
    },
    endTime: {
      label: "End Time",
      description: "The end time of the shift",
      placeholder: "Select the end time",
    },
    actions: {
      submit: "Submit",
    },
  },
};

const shiftFormSchema = z.object({
  id: z.string().optional(),
  organizationId: z.string().optional(),
  positionId: z.string().optional(),
  jobId: z.string().optional(),
  providerId: z.string().optional(),
  summary: z
    .string()
    .min(5, { message: "Summary must be at least 5 characters long" }),
  scope: z
    .string()
    .min(10, { message: "Description must be at least 10 characters long" }),
  paymentType: z.nativeEnum(PaymentType),
  paymentRate: z
    .number()
    .positive({ message: "Payment rate must be a positive number" }),
  startDate: z.date(),
  endDate: z.date(),
  startTime: z.object({
    hour: z.number(),
    minute: z.number(),
  }),
  endTime: z.object({
    hour: z.number(),
    minute: z.number(),
  }),
  timeZone: z.string().optional(),
  location: z.string().optional(),
  schedule: z.string().optional(),
  role: z.string().optional(),
  department: z.string().optional(),
  specialty: z.string().optional(),
});

export type ShiftFormValues = z.infer<typeof shiftFormSchema>;
export type ShiftFormProps = PropsWithChildren<
  Parameters<typeof useForm<ShiftFormValues>>[0] & {
    onSubmit?: (values: ShiftFormValues) => void | Promise<void>;
  }
>;

export default function ShiftForm({
  children,
  onSubmit = () => void 0,
  ...props
}: ShiftFormProps) {
  const form = useForm<ShiftFormValues>({
    ...props,
    resolver: zodResolver(shiftFormSchema),
  });

  return (
    <TooltipProvider>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="summary"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.summary.label}</FormLabel>
                <FormDescription>{i18n.en.summary.description}</FormDescription>
                <FormControl>
                  <Input placeholder={i18n.en.summary.placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="scope"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.scope.label}</FormLabel>
                <FormDescription>{i18n.en.scope.description}</FormDescription>
                <FormControl>
                  <Textarea
                    placeholder={i18n.en.scope.placeholder}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="paymentType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{i18n.en.paymentType.label}</FormLabel>
                <FormDescription>
                  {i18n.en.paymentType.description}
                </FormDescription>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue
                        placeholder={i18n.en.paymentType.placeholder}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(i18n.en.paymentType.options).map(
                        ([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ),
                      )}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <CurrencyField
            name="paymentRate"
            label={i18n.en.paymentRate.label}
            description={i18n.en.paymentRate.description}
            placeholder={i18n.en.paymentRate.placeholder}
          />

          <DateField
            name="startDate"
            label={i18n.en.startDate.label}
            description={i18n.en.startDate.description}
            placeholder={i18n.en.startDate.placeholder}
          />

          <DateField
            name="endDate"
            label={i18n.en.endDate.label}
            description={i18n.en.endDate.description}
            placeholder={i18n.en.endDate.placeholder}
          />

          <TimePickerField
            name="startTime"
            label={i18n.en.startTime.label}
            description={i18n.en.startTime.description}
          />

          <TimePickerField
            name="endTime"
            label={i18n.en.endTime.label}
            description={i18n.en.endTime.description}
          />

          {children ?? (
            <div className="flex w-full justify-center">
              <ShiftFormSubmitButton />
            </div>
          )}
        </form>
      </Form>
    </TooltipProvider>
  );
}

export function ShiftFormSubmitButton({
  children = i18n.en.actions.submit,
  ...props
}: ButtonProps) {
  const form = useFormContext<ShiftFormValues>();

  return (
    <Button
      {...props}
      className={cn("min-w-40", props.className)}
      disabled={
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        props.disabled || form.formState.isSubmitting || !form.formState.isDirty
      }
      type="submit"
    >
      {children}
    </Button>
  );
}
