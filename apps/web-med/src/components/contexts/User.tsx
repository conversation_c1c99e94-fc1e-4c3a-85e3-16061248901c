"use client";

import type { QueryObserver } from "@tanstack/react-query";
import type { PropsWithChildren } from "react";

import { createContext, use, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useClerk } from "@clerk/nextjs";

import type {
  OrganizationStatus,
  OrganizationType,
  ProviderStatus,
  VerificationStatus,
} from "@axa/database-medical";
import { toast } from "@axa/ui/primitives/toast";

import type { RouterError, RouterInputs, RouterOutputs } from "@/api";

import { api } from "@/api/client";

export type UserType = RouterOutputs["user"]["me"];

export enum AppMode {
  PROVIDER = "PROVIDER",
  ORGANIZATION = "ORGANIZATION",
}

export interface UserContextType {
  signOut: () => Promise<void>;
  switchMode: (mode: AppMode) => Promise<RouterOutputs["user"]["switchMode"]>;
  updateNotificationSettings: (
    values: RouterInputs["user"]["updateNotificationSettings"],
  ) => Promise<RouterOutputs["user"]["updateNotificationSettings"]>;
  refetch: QueryObserver<UserType>["refetch"] | (() => Promise<void>);
  error?: RouterError | null;
  initialized: boolean;
  loading: boolean;
  isInternal?: boolean;
  isBilling?: boolean;
  isAdmin?: boolean;
  isClient?: boolean;
  isProvider?: boolean;
  id?: string;
  role: UserType["role"] | null;
  firstName?: string;
  lastName?: string;
  nickName?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  mode: AppMode | null;
  onboarded?: boolean | null;
  acceptedTerms?: Date | null;
  providerId?: string | null;
  organizationId?: string | null;
  settings: {
    notifications: boolean;
    appNotifications: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
    smsNotifications: boolean;
    contracts: boolean;
    reports: boolean;
    messages: boolean;
    shifts: boolean;
    jobs: boolean;
    invoices: boolean;
    payments: boolean;
  } | null;
  organization: {
    id?: string;
    name?: string;
    avatar?: string | null;
    type?: OrganizationType;
    status?: OrganizationStatus;
    customerId?: string | null;
  } | null;
  provider: {
    id: string;
    status: ProviderStatus;
    accountId: string | null;
    verificationStatus: VerificationStatus | null;
    backgroundCheckStatus: VerificationStatus | null;
    i9VerificationStatus: VerificationStatus | null;
    identityVerificationStatus: VerificationStatus | null;
  } | null;
}

export const UserContext = createContext<UserContextType>({
  signOut: async () => {
    throw new Error("User context not used within provider");
  },
  refetch: async () => {
    throw new Error("User context not used within provider");
  },
  switchMode: async () => {
    throw new Error("User context not used within provider");
  },
  updateNotificationSettings: async () => {
    throw new Error("User context not used within provider");
  },
  initialized: false,
  loading: true,
  error: null,
  isInternal: false,
  isBilling: false,
  isAdmin: false,
  isClient: false,
  isProvider: false,
  role: null,
  id: "",
  mode: null,
  onboarded: null,
  acceptedTerms: null,
  providerId: null,
  organizationId: null,
  firstName: "",
  lastName: "",
  nickName: "",
  avatar: "",
  email: "",
  phone: "",
  organization: null,
  provider: null,
  settings: null,
});

export function useUser() {
  const ctx = use(UserContext);

  if (!ctx.initialized) {
    throw new Error("User context not used within provider");
  }

  return ctx;
}

export function UserProvider(
  props: PropsWithChildren<{
    user?: Promise<UserType>;
  }>,
) {
  const router = useRouter();
  const { signOut } = useClerk();
  const user = api.user.me.useQuery(undefined, {
    initialData: props.user ? use(props.user) : undefined,
  });

  const switchModeMutation = api.user.switchMode.useMutation({
    onSuccess: async (data) => {
      await user.refetch();

      if (data.mode === "PROVIDER") router.push("/providers/app");
      if (data.mode === "ORGANIZATION") router.push("/app");
    },
  });

  const updateSettingsMutation =
    api.user.updateNotificationSettings.useMutation({
      onSuccess: async () => {
        await user.refetch();
        toast.success("Notification preferences updated successfully");
      },
      onError: (error) => {
        console.error("Failed to update settings:", error);
        toast.error("Failed to update preferences. Please try again.");
      },
    });

  const role = user.data?.role ?? null;

  return (
    <UserContext.Provider
      value={useMemo(
        () => ({
          initialized: true,
          signOut: async () => {
            await signOut();
            router.push("/sign-in");
            await user.refetch();
          },
          switchMode: (mode: "PROVIDER" | "ORGANIZATION") =>
            switchModeMutation.mutateAsync({ mode }),
          updateNotificationSettings: (values: Record<string, boolean>) =>
            updateSettingsMutation.mutateAsync(values),
          refetch: user.refetch as QueryObserver<UserType>["refetch"],
          error: user.error as RouterError | null | undefined,
          loading: user.isLoading,
          role,
          isInternal: ["INTERNAL", "BILLING", "ADMIN"].includes(role ?? ""),
          isBilling: ["BILLING", "ADMIN"].includes(role ?? ""),
          isAdmin: role === "ADMIN",
          isClient: role === "CLIENT",
          isProvider: role === "PROVIDER",
          id: user.data?.id,
          firstName: user.data?.firstName ?? "",
          lastName: user.data?.lastName ?? "",
          avatar: user.data?.avatar ?? "",
          email: user.data?.email?.emailAddress ?? "",
          phone: user.data?.phone?.phoneNumber ?? "",
          mode: user.data?.mode as AppMode,
          onboarded: user.data?.onboarded ?? null,
          acceptedTerms: user.data?.acceptedTerms ?? null,
          providerId: user.data?.providerId,
          organizationId: user.data?.organizationId,
          settings: user.data?.settings ?? null,
          organization: user.data?.organization
            ? {
                id: user.data.organization.id,
                name: user.data.organization.name,
                avatar: user.data.organization.avatar,
                status: user.data.organization.status,
                type: user.data.organization.type,
                customerId: user.data.organization.customerId,
              }
            : null,
          provider: user.data?.provider
            ? {
                id: user.data.provider.id,
                status: user.data.provider.status,
                accountId: user.data.provider.accountId,
                verificationStatus:
                  user.data.provider.verification?.status ?? null,
                backgroundCheckStatus:
                  user.data.provider.verification?.backgroundCheckStatus ??
                  null,
                i9VerificationStatus:
                  user.data.provider.verification?.i9VerificationStatus ?? null,
                identityVerificationStatus:
                  user.data.provider.verification?.identityVerificationStatus ??
                  null,
              }
            : null,
        }),
        [
          user,
          role,
          signOut,
          router,
          switchModeMutation.mutateAsync,
          updateSettingsMutation.mutateAsync,
        ],
      )}
    >
      {props.children}
    </UserContext.Provider>
  );
}
