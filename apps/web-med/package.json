{"name": "@axa/web-medical", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .next .turbo node_modules", "dev": "NODE_OPTIONS='--inspect' pnpm with-env next dev --port 3002", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e .env --", "storybook": "storybook dev -p 6010 --no-open", "storybook:ci": "storybook dev -p 6010 --no-open --ci", "storybook:build": "storybook build", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@axa/api-medical": "workspace:*", "@axa/constants": "workspace:*", "@axa/database-medical": "workspace:*", "@axa/lib": "workspace:*", "@axa/ui": "workspace:*", "@clerk/nextjs": "^6.20.2", "@documenso/embed-react": "0.2.1-rc.0", "@sentry/nextjs": "^9.24.0", "@stripe/connect-js": "^3.3.24", "@stripe/react-connect-js": "^3.3.24", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^6.1.0", "@supabase/supabase-js": "^2.49.8", "@t3-oss/env-nextjs": "^0.13.6", "@tanstack/react-query": "^5.79.0", "@trpc/client": "^11.1.4", "@trpc/react-query": "^11.1.4", "@trpc/server": "^11.1.4", "@turf/turf": "^7.2.0", "@vercel/functions": "^2.1.0", "geist": "^1.4.2", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "next": "^15.3.3", "react": "19.1.0", "react-dom": "19.1.0", "superjson": "^2.2.2", "zod": "^3.25.42", "zustand": "^5.0.5"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/storybook-config": "workspace:*", "@axa/tailwind-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@prisma/nextjs-monorepo-workaround-plugin": "^6.8.2", "@rollup/plugin-alias": "^5.1.1", "@storybook/nextjs-vite": "^9.0.1", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vafanassieff/msw-trpc": "2.0.0-beta.8", "copy-webpack-plugin": "^13.0.0", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9.28.0", "jiti": "^2.4.2", "prettier": "^3.5.3", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "prettier": "@axa/prettier-config"}