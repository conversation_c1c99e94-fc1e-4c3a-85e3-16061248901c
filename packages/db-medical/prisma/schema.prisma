datasource db {
  schemas   = ["public", "stripe"]
  provider  = "postgresql"
  url       = env("DB_URL")
  directUrl = env("DB_URL_NON_POOLING")
}

generator client {
  provider        = "prisma-client-js"
  output          = "../../../node_modules/.prisma/client/medical"
  previewFeatures = ["multiSchema"]
}

generator msw {
  provider = "pnpm tsx ./prisma/msw.ts"
}

model Address {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  formatted    String
  street       String?
  city         String?
  borough      String?
  neighborhood String?
  county       String?
  postal       String?
  state        String?
  country      String

  latitude  Float
  longitude Float

  timeZone String

  person       Person?
  provider     Provider?
  location     Location?
  organization Organization?

  actions Action[]

  @@index([formatted, postal, state, country, latitude, longitude], name: "address_idx")
  @@schema("public")
}

model Document {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  name        String
  description String?
  url         String
  size        Int
  type        String

  jobs      JobPost[]
  positions JobPosition[]
  shifts    Shift[]
  locations Location[]
  people    Person[]
  actions   Action[]

  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?
  provider       Provider?     @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId     String?

  agreement     Agreement?
  qualification Qualification?

  @@index([name, description, type, organizationId], name: "document_idx")
  @@schema("public")
}

enum PersonRole {
  ADMIN
  BILLING
  INTERNAL
  CLIENT
  PROVIDER
  USER
  NONE

  @@schema("public")
}

model Person {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  role  PersonRole @default(NONE)
  title String?

  avatar    String?
  gender    String?
  prefix    String?
  firstName String
  lastName  String
  email     String? @unique
  phone     String? @unique

  activity   Action[]    @relation("actor")
  actions    Action[]    @relation("person")
  contacts   Contact[]
  contracts  Contract[]
  reviews    Review[]
  messages   Message[]
  signatures Signature[]
  documents  Document[]
  threads    Thread[]

  provider Provider?
  settings PersonSetting?

  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  addressId      String?       @unique
  address        Address?      @relation(fields: [addressId], references: [id], onDelete: Cascade)

  @@index([firstName, lastName, email, phone, role], name: "person_idx")
  @@schema("public")
}

model PersonSetting {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  notifications      Boolean @default(true)
  appNotifications   Boolean @default(true)
  emailNotifications Boolean @default(true)
  pushNotifications  Boolean @default(true)
  smsNotifications   Boolean @default(false)

  contracts Boolean @default(true)
  reports   Boolean @default(true)
  messages  Boolean @default(true)
  shifts    Boolean @default(true)
  jobs      Boolean @default(true)
  invoices  Boolean @default(true)
  payments  Boolean @default(true)

  personId String @unique
  person   Person @relation(fields: [personId], references: [id], onDelete: Cascade)

  @@index([personId], name: "person_setting_idx")
  @@schema("public")
}

enum AccountStatus {
  PENDING
  SUBMITTED
  COMPLETED
  CANCELLED

  @@schema("public")
}

enum VerificationStatus {
  PENDING
  VERIFYING
  APPROVED
  REJECTED
  CANCELLED

  @@schema("public")
}

enum ProviderStatus {
  PENDING
  ACTIVE
  INACTIVE
  SUSPENDED
  REJECTED

  @@schema("public")
}

model Provider {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  // stripe integration
  accountId     String?        @unique
  accountStatus AccountStatus? @default(PENDING)

  verificationStatus VerificationStatus? @default(PENDING)
  status             ProviderStatus      @default(PENDING)
  title              String?
  gender             String?
  spokenLanguages    String[]

  score Float @default(100)

  calendarId String?    @unique
  calendar   Schedule?  @relation("ProviderSchedule", fields: [calendarId], references: [id])
  schedules  Schedule[] @relation("ProviderShifts")

  referrals      Referral[]
  documents      Document[]
  specialties    Specialty[]
  qualifications Qualification[]
  experiences    JobExperience[]
  reviews        Review[]
  contracts      Contract[]
  shifts         Shift[]
  positions      JobPosition[]
  applications   Application[]
  offers         Offer[]
  payouts        Payout[]
  actions        Action[]
  incidents      Incident[]

  settings     ProviderSetting?
  verification ProviderVerification?
  personId     String                @unique
  person       Person                @relation(fields: [personId], references: [id], onDelete: Cascade)
  addressId    String                @unique
  address      Address               @relation(fields: [addressId], references: [id], onDelete: Cascade)

  @@index([personId, addressId, accountId, calendarId, status, accountStatus, verificationStatus, title, gender, spokenLanguages], name: "provider_idx")
  @@schema("public")
}

model ProviderVerification {
  id                   String    @id @default(cuid())
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  deletedAt            DateTime?
  verifiedAt           DateTime?
  i9VerifiedAt         DateTime?
  identityVerifiedAt   DateTime?
  backgroundVerifiedAt DateTime?

  status                     VerificationStatus  @default(PENDING)
  backgroundCheckStatus      VerificationStatus? @default(PENDING)
  i9VerificationStatus       VerificationStatus? @default(PENDING)
  identityVerificationStatus VerificationStatus? @default(PENDING)

  notes String?

  actions Action[]

  providerId String   @unique
  provider   Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@index([providerId, status, backgroundCheckStatus, i9VerificationStatus, identityVerificationStatus], name: "provider_verification_idx")
  @@schema("public")
}

model ProviderSetting {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  openToWork     Boolean? @default(true)
  openToOnCall   Boolean? @default(false)
  openToRelocate Boolean? @default(false)
  openToTravel   Boolean? @default(false)

  minRelocateDistance Int?
  maxRelocateDistance Int?
  minTravelDistance   Int?
  maxTravelDistance   Int?

  providerId String   @unique
  provider   Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@index([providerId], name: "provider_setting_idx")
  @@schema("public")
}

enum OrganizationType {
  INTERNAL
  CLIENT
  ACCOUNT

  @@schema("public")
}

enum OrganizationClass {
  PRIVATE
  NONPROFIT
  GOVERNMENT

  @@schema("public")
}

enum OrganizationBillingType {
  INVOICE // accumulated billing based on interval
  CHARGE // billed per shift
  NONE

  @@schema("public")
}

enum OrganizationBillingFrequency {
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY

  @@schema("public")
}

enum OrganizationStatus {
  PENDING
  ACTIVE
  INACTIVE
  SUSPENDED
  REJECTED

  @@schema("public")
}

enum OrganizationMode {
  INDEPENDENT
  ASSISTED

  @@schema("public")
}

model Organization {
  id       String @id @default(cuid())
  path     String @unique
  depth    Int
  numchild Int    @default(0)

  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?
  approvedAt DateTime?
  rejectedAt DateTime?

  // stripe integration
  customerId    String?        @unique
  accountId     String?        @unique
  accountStatus AccountStatus? @default(PENDING)
  taxId         String?        @unique
  vatId         String?        @unique
  structure     String?

  balance                   Float                        @default(0)
  threshold                 Float                        @default(0)
  assistPercentage          Float                        @default(1.18)
  basePercentage            Float                        @default(1.15)
  cancellationFeePercentage Float                        @default(1.25)
  billingFrequency          OrganizationBillingFrequency @default(MONTHLY)
  autoPay                   Boolean                      @default(false)

  score Float @default(100)

  name    String                  @unique
  type    OrganizationType        @default(CLIENT)
  status  OrganizationStatus      @default(PENDING)
  class   OrganizationClass       @default(PRIVATE)
  billing OrganizationBillingType @default(INVOICE)
  mode    OrganizationMode        @default(INDEPENDENT)
  avatar  String?
  phone   String?
  email   String?

  managerId String?
  manager   Organization?  @relation("Manager", fields: [managerId], references: [id], onDelete: Restrict)
  accounts  Organization[] @relation("Manager")

  scheduleId String?    @unique
  schedule   Schedule?  @relation("OrganizationSchedule", fields: [scheduleId], references: [id])
  schedules  Schedule[] @relation("OrganizationShifts")

  contracts    Contract[]
  contacts     Contact[]
  people       Person[]
  documents    Document[]
  locations    Location[]
  buildings    Building[]
  departments  Department[]
  invoices     Invoice[]
  jobs         JobPost[]
  positions    JobPosition[]
  shifts       Shift[]
  reviews      Review[]
  applications Application[]
  offers       Offer[]
  experiences  JobExperience[]
  incidents    Incident[]

  values  Value[]
  actions Action[]

  settings OrganizationSetting?

  addressId String?  @unique
  address   Address? @relation(fields: [addressId], references: [id], onDelete: Cascade)

  @@index([path, name, type, status, class, billing, managerId, customerId, scheduleId], name: "organization_idx")
  @@schema("public")
}

model OrganizationSetting {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  defaultMode            OrganizationMode @default(INDEPENDENT)
  defaultJobPostPrivacy  Boolean          @default(false)
  offerExpirationDays    Int              @default(0)
  contractExpirationDays Int              @default(7)
  defaultShiftDuration   Int              @default(12)

  actions Action[]

  organizationId String       @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId], name: "organization_setting_idx")
  @@schema("public")
}

enum InvoiceStatus {
  DRAFT
  OPEN
  DUE
  PAID
  VOID

  @@schema("public")
}

model Invoice {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
  paidAt    DateTime?
  dueAt     DateTime?

  // TODO: consolidate dates used

  status InvoiceStatus @default(DRAFT)
  name   String?
  number String
  date   DateTime
  due    DateTime
  total  Float

  // stripe integration
  stripeCustomerId      String  @unique
  stripeChargeId        String? @unique
  stripePaymentIntentId String? @unique
  stripeAmount          Float?

  shifts  Shift[]
  actions Action[]

  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String

  @@index([number, status, date, due, organizationId, stripePaymentIntentId, stripeChargeId, stripeCustomerId], name: "invoice_idx")
  @@schema("public")
}

enum PayoutType {
  PLATFORM
  EXTERNAL

  @@schema("public")
}

enum PayoutStatus {
  PENDING
  PROCESSING
  CANCELLED
  COMPLETED
  FAILED

  @@schema("public")
}

model Payout {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  processedAt DateTime?
  paidAt      DateTime?
  failedAt    DateTime?
  cancelledAt DateTime?

  startDate DateTime
  endDate   DateTime

  stripeAccountId    String?
  stripePayoutId     String? @unique
  stripePayoutAmount Float?

  type           PayoutType   @default(PLATFORM)
  status         PayoutStatus @default(PENDING)
  amount         Float        @default(0.0)
  overtimeAmount Float        @default(0.0)
  holidayAmount  Float        @default(0.0)
  nightAmount    Float        @default(0.0)
  bonusAmount    Float        @default(0.0)

  shifts Shift[]

  providerId String
  provider   Provider @relation(fields: [providerId], references: [id])

  @@index([processedAt, paidAt, failedAt, cancelledAt, status, providerId, stripeAccountId, stripePayoutId, startDate, endDate], name: "payout_idx")
  @@schema("public")
}

enum LocationType {
  CAMPUS
  HOSPITAL
  CLINIC
  OFFICE
  PHARMACY
  LAB
  IMAGING
  REHABILITATION
  OTHER

  @@schema("public")
}

model Location {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  type        LocationType
  name        String
  description String?

  hasParking         Boolean @default(false)
  hasPublicTransport Boolean @default(false)

  buildings   Building[]
  departments Department[]
  shifts      Shift[]
  documents   Document[]
  jobs        JobPost[]
  positions   JobPosition[]
  experiences JobExperience[]
  contacts    Contact[]
  actions     Action[]

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  addressId      String       @unique
  address        Address      @relation(fields: [addressId], references: [id], onDelete: Cascade)

  @@index([name, type, organizationId, addressId], name: "location_idx")
  @@schema("public")
}

model Building {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  name        String
  description String?

  departments Department[]
  contacts    Contact[]
  actions     Action[]

  locationId     String
  location       Location     @relation(fields: [locationId], references: [id])
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id])

  @@index([name, locationId, organizationId], name: "building_idx")
  @@schema("public")
}

enum DepartmentType {
  CENTER
  INSTITUTE
  DEPARTMENT
  WARD
  UNIT
  ROOM
  OTHER

  @@schema("public")
}

model Department {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  type        DepartmentType @default(DEPARTMENT)
  name        String
  description String?

  building       Building?    @relation(fields: [buildingId], references: [id])
  buildingId     String?
  location       Location     @relation(fields: [locationId], references: [id])
  locationId     String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String

  contacts    Contact[]
  shifts      Shift[]
  jobs        JobPost[]
  positions   JobPosition[]
  experiences JobExperience[]
  actions     Action[]

  @@index([name, type, locationId, organizationId, buildingId], name: "department_idx")
  @@schema("public")
}

model Contact {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  role String @default("CONTACT")

  personId       String
  person         Person        @relation(fields: [personId], references: [id], onDelete: Cascade)
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  locations   Location[]
  buildings   Building[]
  departments Department[]
  jobs        JobPost[]
  positions   JobPosition[]
  shifts      Shift[]
  actions     Action[]

  @@index([role, personId, organizationId], name: "contact_idx")
  @@schema("public")
}

enum PayType {
  HOURLY
  FIXED

  @@schema("public")
}

enum ShiftStatus {
  PENDING
  CONFIRMED
  ACTIVE
  COMPLETED
  APPROVED
  CANCELLED
  REJECTED

  @@schema("public")
}

model Shift {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  confirmedAt DateTime?
  startedAt   DateTime?
  completedAt DateTime?
  cancelledAt DateTime?
  approvedAt  DateTime?
  rejectedAt  DateTime?
  billedAt    DateTime?
  invoicedAt  DateTime?
  paidAt      DateTime?

  status  ShiftStatus @default(PENDING)
  summary String
  scope   String
  role    String

  timeZone         String
  startDate        DateTime
  endDate          DateTime
  hours            Float    @default(2.0)
  overtimeHours    Float    @default(0.0)
  nightTimeHours   Float    @default(0.0)
  holidayTimeHours Float    @default(0.0)
  bonusHours       Float    @default(0.0)

  paymentType    PayType @default(HOURLY)
  paymentAmount  Float   @default(0.0)
  paymentRate    Float   @default(1.0)
  nightRate      Float   @default(1.25)
  nightAmount    Float   @default(0.0)
  overtimeRate   Float   @default(1.5)
  overtimeAmount Float   @default(0.0)
  holidayRate    Float   @default(2.0)
  holidayAmount  Float   @default(0.0)
  bonusRate      Float   @default(1.0)
  bonusAmount    Float   @default(0.0)
  paymentTotal   Float   @default(0.0)

  billingType   PayType @default(HOURLY)
  billingRate   Float   @default(1.15)
  billingAmount Float   @default(0.0)
  isBillable    Boolean @default(true)

  // stripe integration
  stripeTransferId     String? @unique
  stripeTransferAmount Float?
  // for per shift payments
  stripeChargeId       String? @unique
  stripeChargeAmount   Float?

  specialties Specialty[]
  documents   Document[]
  contacts    Contact[]
  actions     Action[]
  incidents   Incident[]

  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String
  invoice        Invoice      @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId      String
  location       Location     @relation(fields: [locationId], references: [id], onDelete: Cascade)
  locationId     String
  job            JobPost      @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId          String
  position       JobPosition  @relation(fields: [positionId], references: [id])
  positionId     String
  department     Department?  @relation(fields: [departmentId], references: [id])
  departmentId   String?

  provider   Provider? @relation(fields: [providerId], references: [id])
  providerId String?
  payout     Payout?   @relation(fields: [payoutId], references: [id])
  payoutId   String?

  thread Thread?
  review Review?

  @@index([summary, scope, status], name: "shift_idx")
  @@index([startDate, endDate], name: "shift_date_idx")
  @@index([organizationId, locationId, jobId, departmentId, providerId, payoutId, invoiceId, stripeTransferId, stripeChargeId], name: "shift_relation_idx")
  @@schema("public")
}

enum JobPostStatus {
  DRAFT
  PUBLISHED
  FILLED
  COMPLETED
  CANCELLED
  EXPIRED

  @@schema("public")
}

enum JobPostType {
  PERMANENT
  TEMPORARY
  PER_DIEM

  @@schema("public")
}

enum JobPostMode {
  INDEPENDENT
  ASSISTED

  @@schema("public")
}

enum JobPostPriority {
  LOW
  MEDIUM
  HIGH

  @@schema("public")
}

model JobPost {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  expiresAt   DateTime?
  startsAt    DateTime?
  endsAt      DateTime?
  publishedAt DateTime?
  filledAt    DateTime?
  cancelledAt DateTime?
  completedAt DateTime?
  expiredAt   DateTime?
  archivedAt  DateTime?

  status   JobPostStatus   @default(DRAFT)
  mode     JobPostMode     @default(INDEPENDENT)
  type     JobPostType     @default(PER_DIEM)
  priority JobPostPriority @default(MEDIUM)

  summary String
  scope   String
  role    String

  paymentType   PayType @default(HOURLY)
  paymentAmount Float   @default(0.0)
  paymentRate   Float   @default(1.0)
  nightRate     Float   @default(1.25)
  overtimeRate  Float   @default(1.5)
  holidayRate   Float   @default(2.0)
  bonusRate     Float   @default(1.0)

  billingType PayType @default(HOURLY)
  billingRate Float   @default(1.15)
  isBillable  Boolean @default(true)

  requiresInterview Boolean @default(false)
  hasBenefits       Boolean @default(false)
  hasSignOnBonus    Boolean @default(false)
  hasRelocation     Boolean @default(false)
  hasTravel         Boolean @default(false)
  hasHousing        Boolean @default(false)
  hasStudentLoan    Boolean @default(false)
  has401k           Boolean @default(false)
  has403b           Boolean @default(false)

  contacts    Contact[]
  shifts      Shift[]
  documents   Document[]
  specialties Specialty[]
  actions     Action[]

  applications Application[]
  offers       Offer[]

  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  schedule       Schedule?    @relation(fields: [scheduleId], references: [id])
  scheduleId     String?      @unique
  location       Location?    @relation(fields: [locationId], references: [id])
  locationId     String?
  department     Department?  @relation(fields: [departmentId], references: [id])
  departmentId   String?

  position   JobPosition? @relation(fields: [positionId], references: [id], onDelete: Cascade)
  positionId String?      @unique
  thread     Thread?

  @@index([summary, scope, status], name: "job_idx")
  @@index([startsAt, endsAt, expiresAt, publishedAt, filledAt, cancelledAt, completedAt, expiredAt], name: "job_date_idx")
  @@index([organizationId, locationId, scheduleId, departmentId, positionId], name: "job_relation_idx")
  @@schema("public")
}

enum JobPositionStatus {
  PENDING
  ACTIVE
  INACTIVE
  COMPLETED
  TERMINATED
  ABANDONED
  CANCELLED

  @@schema("public")
}

model JobPosition {
  id           String    @id @default(cuid())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?
  activeAt     DateTime?
  inactiveAt   DateTime?
  terminatedAt DateTime?
  abandonedAt  DateTime?
  completedAt  DateTime?
  cancelledAt  DateTime?
  archivedAt   DateTime?

  status  JobPositionStatus @default(PENDING)
  type    JobPostType       @default(PER_DIEM)
  role    String
  summary String
  scope   String

  paymentType   PayType @default(HOURLY)
  paymentAmount Float   @default(0.0)
  paymentRate   Float   @default(1.0)
  nightRate     Float   @default(1.25)
  overtimeRate  Float   @default(1.5)
  holidayRate   Float   @default(2.0)
  bonusRate     Float   @default(1.0)

  billingType PayType @default(HOURLY)
  billingRate Float   @default(1.15)
  isBillable  Boolean @default(true)

  schedule   Schedule @relation(fields: [scheduleId], references: [id])
  scheduleId String   @unique
  provider   Provider @relation(fields: [providerId], references: [id])
  providerId String

  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  location       Location     @relation(fields: [locationId], references: [id])
  locationId     String
  department     Department?  @relation(fields: [departmentId], references: [id])
  departmentId   String?

  job         JobPost?
  thread      Thread?
  application Application?
  offer       Offer?

  contracts   Contract[]
  contacts    Contact[]
  shifts      Shift[]
  documents   Document[]
  specialties Specialty[]
  actions     Action[]

  @@index([scheduleId, providerId, locationId, departmentId], name: "job_position_relation_idx")
  @@schema("public")
}

enum ScheduleType {
  ORGANIZATION
  PROVIDER
  SHIFT

  @@schema("public")
}

model Schedule {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
  startsAt  DateTime?
  endsAt    DateTime?

  type ScheduleType @default(SHIFT)
  name String?

  blocks  TimeBlock[]
  actions Action[]

  business       Organization? @relation("OrganizationSchedule")
  organization   Organization? @relation("OrganizationShifts", fields: [organizationId], references: [id])
  organizationId String?
  personal       Provider?     @relation("ProviderSchedule")
  provider       Provider?     @relation("ProviderShifts", fields: [providerId], references: [id])
  providerId     String?

  job      JobPost?
  position JobPosition?

  @@index([providerId, organizationId, type], name: "schedule_relation_idx")
  @@schema("public")
}

enum TimeBlockType {
  AVAILABILITY
  TIME_OFF
  SHIFT
  BLOCK

  @@schema("public")
}

enum TimeBlockRecurrence {
  WEEKLY
  BIWEEKLY
  MONTHLY
  QUARTERLY
  YEARLY

  @@schema("public")
}

// The TimeBlock model represents specific instances of availability, shifts, or time off
model TimeBlock {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
  startsAt  DateTime?
  endsAt    DateTime?

  timeZone   String?
  type       TimeBlockType?       @default(SHIFT)
  recurrence TimeBlockRecurrence?
  hours      Float?               @default(0.0)
  startDate  DateTime?
  endDate    DateTime?
  startTime  Int?                 @default(0)
  endTime    Int?                 @default(0)
  dayOfWeek  Int?

  scheduleId String
  schedule   Schedule @relation(fields: [scheduleId], references: [id])

  @@index([type, startDate, endDate, startsAt, endsAt, timeZone, recurrence, hours, scheduleId], name: "time_block_idx")
  @@schema("public")
}

model JobExperience {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  role        String
  description String?
  company     String?
  startDate   DateTime
  endDate     DateTime?

  specialties Specialty[]
  actions     Action[]

  provider       Provider      @relation(fields: [providerId], references: [id])
  providerId     String
  facilityId     String?
  facility       Location?     @relation(fields: [facilityId], references: [id])
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  departmentId   String?
  department     Department?   @relation(fields: [departmentId], references: [id])

  @@index([role, company, startDate, endDate], name: "experience_idx")
  @@index([providerId], name: "experience_provider_idx")
  @@schema("public")
}

enum QualificationType {
  DEGREE
  LICENSE
  CERTIFICATE
  OTHER

  @@schema("public")
}

enum QualificationStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED

  @@schema("public")
}

model Qualification {
  id         String    @id @default(cuid())
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?
  approvedAt DateTime?
  rejectedAt DateTime?
  expiresAt  DateTime?

  status QualificationStatus @default(PENDING)

  type        QualificationType
  name        String
  institution String?
  identifier  String?
  state       String?
  country     String?

  startDate DateTime?
  endDate   DateTime?

  provider   Provider  @relation(fields: [providerId], references: [id])
  providerId String
  document   Document? @relation(fields: [documentId], references: [id])
  documentId String?   @unique

  actions Action[]

  @@index([status, type, name, institution, identifier, state, country], name: "qualification_idx")
  @@index([providerId, documentId], name: "qualification_connection_idx")
  @@schema("public")
}

model Specialty {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  name        String  @unique
  description String?

  providers   Provider[]
  jobs        JobPost[]
  positions   JobPosition[]
  shifts      Shift[]
  experiences JobExperience[]

  @@index([name, description], name: "specialty_idx")
  @@schema("public")
}

enum ApplicationStatus {
  PENDING
  ACCEPTED
  REJECTED
  WITHDRAWN
  CLOSED

  @@schema("public")
}

model Application {
  id          String            @id @default(cuid())
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  deletedAt   DateTime?
  acceptedAt  DateTime?
  rejectedAt  DateTime?
  withdrawnAt DateTime?
  closedAt    DateTime?
  status      ApplicationStatus @default(PENDING)
  notes       String?

  job            JobPost      @relation(fields: [jobId], references: [id])
  jobId          String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  provider       Provider     @relation(fields: [providerId], references: [id])
  providerId     String
  position       JobPosition? @relation(fields: [positionId], references: [id])
  positionId     String?      @unique
  contract       Contract?    @relation(fields: [contractId], references: [id])
  contractId     String?      @unique

  thread Thread?

  actions Action[]

  @@index([status, organizationId, providerId, jobId, positionId, contractId], name: "application_idx")
  @@schema("public")
}

enum OfferStatus {
  PENDING
  ACCEPTED
  REJECTED
  WITHDRAWN
  CLOSED

  @@schema("public")
}

model Offer {
  id          String    @id @default(cuid())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  expiresAt   DateTime?
  acceptedAt  DateTime?
  rejectedAt  DateTime?
  withdrawnAt DateTime?
  closedAt    DateTime?

  status OfferStatus @default(PENDING)
  notes  String?

  job            JobPost      @relation(fields: [jobId], references: [id])
  jobId          String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  provider       Provider     @relation(fields: [providerId], references: [id])
  providerId     String
  position       JobPosition? @relation(fields: [positionId], references: [id])
  positionId     String?      @unique
  contract       Contract?    @relation(fields: [contractId], references: [id])
  contractId     String?      @unique

  thread Thread?

  actions Action[]

  @@index([status, organizationId, providerId, jobId, positionId, contractId], name: "offer_idx")
  @@schema("public")
}

enum ContractType {
  EMPLOYMENT
  NON_COMPETE
  NON_DISCLOSURE
  SERVICE_RATE
  SERVICE_AGREEMENT
  OTHER

  @@schema("public")
}

enum ContractStatus {
  DRAFT
  PENDING
  SIGNED
  REJECTED
  EXPIRED

  @@schema("public")
}

model Contract {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
  expiresAt DateTime?

  status ContractStatus @default(DRAFT)
  type   ContractType
  title  String

  provider       Provider?     @relation(fields: [providerId], references: [id])
  providerId     String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?
  position       JobPosition?  @relation(fields: [positionId], references: [id])
  positionId     String?

  thread      Thread?
  offer       Offer?
  application Application?

  signatures Signature[]
  agreements Agreement[]
  people     Person[]
  actions    Action[]

  @@index([title, type, status], name: "contract_idx")
  @@index([organizationId, providerId, positionId], name: "contract_relation_idx")
  @@schema("public")
}

enum AgreementStatus {
  PENDING
  SIGNED
  REJECTED
  EXPIRED

  @@schema("public")
}

model Agreement {
  id         String    @id @default(cuid())
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?
  expiresAt  DateTime?
  signedAt   DateTime?
  rejectedAt DateTime?

  status AgreementStatus @default(PENDING)

  // Documenso integration
  documensoId  String? @unique
  documensoUrl String? @unique

  contractId String
  contract   Contract  @relation(fields: [contractId], references: [id])
  documentId String?   @unique
  document   Document? @relation(fields: [documentId], references: [id])

  signatures Signature[]

  @@index([contractId, documentId], name: "agreement_relation_idx")
  @@schema("public")
}

enum SignatureStatus {
  PENDING
  SIGNED
  REJECTED

  @@schema("public")
}

model Signature {
  id         String    @id @default(cuid())
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?
  signedAt   DateTime?
  rejectedAt DateTime?

  status SignatureStatus @default(PENDING)
  role   String

  // Documenso integration
  documensoToken String? @unique

  contractId  String
  contract    Contract  @relation(fields: [contractId], references: [id])
  agreementId String
  agreement   Agreement @relation(fields: [agreementId], references: [id])
  personId    String
  person      Person    @relation(fields: [personId], references: [id])

  @@index([status, contractId, personId, role], name: "signature_idx")
  @@schema("public")
}

enum IncidentSeverity {
  MINOR
  MAJOR
  CRITICAL

  @@schema("public")
}

enum IncidentStatus {
  OPEN
  IN_PROGRESS
  RESOLVED

  @@schema("public")
}

enum IncidentType {
  SAFETY
  HEALTH
  ENVIRONMENT
  OTHER

  @@schema("public")
}

model Incident {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  title       String
  description String?
  severity    IncidentSeverity @default(MINOR)
  status      IncidentStatus   @default(OPEN)
  type        IncidentType     @default(OTHER)

  providerId String?
  provider   Provider? @relation(fields: [providerId], references: [id])

  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  shiftId String?
  shift   Shift?  @relation(fields: [shiftId], references: [id])

  thread  Thread?
  actions Action[]

  @@index([providerId, organizationId, shiftId], name: "incident_idx")
  @@schema("public")
}

model Review {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  rating  Int
  comment String?

  //  categories
  punctuality     Int?
  professionalism Int?
  communication   Int?
  bedsideManner   Int?
  knowledge       Int?

  provider       Provider      @relation(fields: [providerId], references: [id])
  providerId     String
  reviewer       Person?       @relation(fields: [reviewerId], references: [id])
  reviewerId     String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  organizationId String?
  shift          Shift?        @relation(fields: [shiftId], references: [id])
  shiftId        String?       @unique

  actions Action[]

  @@index([rating, providerId])
  @@schema("public")
}

model Thread {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  messages Message[]
  users    Person[]

  shift         Shift?       @relation(fields: [shiftId], references: [id])
  shiftId       String?      @unique
  application   Application? @relation(fields: [applicationId], references: [id])
  applicationId String?      @unique
  offer         Offer?       @relation(fields: [offerId], references: [id])
  offerId       String?      @unique
  job           JobPost?     @relation(fields: [jobId], references: [id])
  jobId         String?      @unique
  position      JobPosition? @relation(fields: [positionId], references: [id])
  positionId    String?      @unique
  contract      Contract?    @relation(fields: [contractId], references: [id])
  contractId    String?      @unique
  incident      Incident?    @relation(fields: [incidentId], references: [id])
  incidentId    String?      @unique

  @@index([shiftId, applicationId, offerId, jobId], name: "thread_idx")
  @@schema("public")
}

model Message {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  content String

  author   Person @relation(fields: [authorId], references: [id])
  authorId String
  thread   Thread @relation(fields: [threadId], references: [id])
  threadId String

  @@index([threadId, authorId])
  @@schema("public")
}

enum ValueType {
  TAG
  ACTION
  CONTACT
  MEDICAL_ROLE
  SPECIALTY

  @@schema("public")
}

model Value {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  type  ValueType
  value String

  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([type, value], name: "value_idx")
  @@schema("public")
}

model Action {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  type     String
  metadata Json?

  actor                  Person                @relation(name: "actor", fields: [actorId], references: [id], onDelete: Cascade)
  actorId                String
  person                 Person?               @relation(name: "person", fields: [personId], references: [id], onDelete: Cascade)
  personId               String?
  organization           Organization?         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId         String?
  job                    JobPost?              @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId                  String?
  position               JobPosition?          @relation(fields: [positionId], references: [id], onDelete: Cascade)
  positionId             String?
  shift                  Shift?                @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  shiftId                String?
  location               Location?             @relation(fields: [locationId], references: [id], onDelete: Cascade)
  locationId             String?
  provider               Provider?             @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId             String?
  offer                  Offer?                @relation(fields: [offerId], references: [id], onDelete: Cascade)
  offerId                String?
  application            Application?          @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  applicationId          String?
  contract               Contract?             @relation(fields: [contractId], references: [id], onDelete: Cascade)
  contractId             String?
  qualification          Qualification?        @relation(fields: [qualificationId], references: [id], onDelete: Cascade)
  qualificationId        String?
  document               Document?             @relation(fields: [documentId], references: [id], onDelete: Cascade)
  documentId             String?
  building               Building?             @relation(fields: [buildingId], references: [id], onDelete: Cascade)
  buildingId             String?
  department             Department?           @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  departmentId           String?
  address                Address?              @relation(fields: [addressId], references: [id], onDelete: Cascade)
  addressId              String?
  review                 Review?               @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  reviewId               String?
  invoice                Invoice?              @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId              String?
  jobExperience          JobExperience?        @relation(fields: [jobExperienceId], references: [id], onDelete: Cascade)
  jobExperienceId        String?
  schedule               Schedule?             @relation(fields: [scheduleId], references: [id], onDelete: Cascade)
  scheduleId             String?
  contact                Contact?              @relation(fields: [contactId], references: [id], onDelete: Cascade)
  contactId              String?
  incident               Incident?             @relation(fields: [incidentId], references: [id], onDelete: Cascade)
  incidentId             String?
  providerVerification   ProviderVerification? @relation(fields: [providerVerificationId], references: [id])
  providerVerificationId String?
  organizationSetting    OrganizationSetting?  @relation(fields: [organizationSettingId], references: [id])
  organizationSettingId  String?

  @@index([actorId, organizationId, jobId, shiftId, locationId, providerId, offerId, applicationId, contractId, qualificationId, documentId, buildingId, departmentId, reviewId, invoiceId, jobExperienceId, scheduleId, contactId, incidentId, providerVerificationId, organizationSettingId], name: "action_idx")
  @@schema("public")
}

enum ReferralStatus {
  INVITED
  SIGNED_UP
  APPROVED
  PAID

  @@schema("public")
}

model Referral {
  id            String   @id @default(cuid())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
                     
  email         String
  status ReferralStatus   // INVITED, SIGNED_UP, APPROVED, PAID
  
  payoutId      String?  // Stripe payout ID
  invitationId  String   @unique     // Clerk invitation ID

  // Many to one relationship between a provider and referrals 
  provider      Provider  @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId    String 

  @@index([providerId,invitationId,payoutId,email,status])
  @@schema("public")
}