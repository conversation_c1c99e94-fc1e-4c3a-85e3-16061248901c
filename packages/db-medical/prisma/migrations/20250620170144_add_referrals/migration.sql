-- CreateEnum
CREATE TYPE "public"."ReferralStatus" AS ENUM ('INVITED', 'SIGNED_UP', 'APPROVED', 'PAID');

-- CreateTable
CREATE TABLE "public"."Referral" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "email" TEXT NOT NULL,
    "status" "public"."ReferralStatus" NOT NULL,
    "payoutId" TEXT,
    "invitationId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,

    CONSTRAINT "Referral_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Referral_invitationId_key" ON "public"."Referral"("invitationId");

-- CreateIndex
CREATE INDEX "Referral_providerId_invitationId_payoutId_email_status_idx" ON "public"."Referral"("providerId", "invitationId", "payoutId", "email", "status");

-- AddForeignKey
ALTER TABLE "public"."Referral" ADD CONSTRAINT "Referral_providerId_fkey" FOREIGN KEY ("providerId") REFERENCES "public"."Provider"("id") ON DELETE CASCADE ON UPDATE CASCADE;
