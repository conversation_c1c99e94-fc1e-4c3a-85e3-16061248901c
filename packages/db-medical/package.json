{"name": "@axa/database-medical", "private": true, "version": "0.1.0", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./prisma/index.ts"}, "./edge": {"types": "./dist/edge.d.ts", "default": "./prisma/edge.ts"}, "./client": {"types": "./dist/prisma/client/index.d.ts", "default": "./prisma/client/index.ts"}, "./generated": {"types": "./dist/generated/mswjsdb.d.ts", "default": "./prisma/generated/mswjsdb.ts"}, "./seed": {"types": "./dist/seed.d.ts", "default": "./prisma/seed.ts"}, "./env": {"types": "./dist/env.d.ts", "default": "./prisma/env.ts"}}, "files": ["dist", "prisma"], "license": "SEE LICENSE", "scripts": {"build": "pnpm db:generate && pnpm db:deploy && tsc && pnpm build:check", "build:check": "tsx scripts/integrity-check.ts", "clean": "rm -rf .turbo node_modules prisma/generated dist", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "test": "vitest run --coverage", "test:watch": "vitest --watch", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "db:studio": "prisma studio --port 5557", "db:generate": "prisma generate", "db:push": "prisma db push --preview-feature --skip-generate", "db:seed": "tsx src/seed.ts", "db:reset": "prisma migrate reset --preview-feature", "db:migrate": "prisma migrate dev --preview-feature", "db:deploy": "prisma migrate deploy --preview-feature"}, "dependencies": {"@prisma/client": "^6.8.2", "@t3-oss/env-nextjs": "^0.13.6", "prisma-extension-bark": "^0.2.2", "zod": "^3.25.42"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@faker-js/faker": "^9.8.0", "@mswjs/data": "^0.16.2", "@prisma/generator-helper": "^6.8.2", "eslint": "^9.28.0", "prettier": "^3.5.3", "prisma": "^6.8.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "prettier": "@axa/prettier-config"}