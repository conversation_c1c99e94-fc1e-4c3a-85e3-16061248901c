/**
 * ⚠️ WARNING: This test suite connects to a real database.
 * It creates and deletes only records prefixed with "TEST_".
 * DO NOT run this on production or shared DBs.
 */

import { afterAll, beforeAll, describe, expect, it } from "vitest";
import pkg from "../../../node_modules/.prisma/client/medical";
import { config } from "dotenv";

const { PrismaClient } = pkg;

// Load your dev DB connection
config({ path: ".env" });

const prisma = new PrismaClient();

beforeAll(async () => {
  await prisma.$connect();
});

afterAll(async () => {
  // Clean up only test data
  await prisma.referral.deleteMany({
    where: {
      invitationId: { startsWith: "TEST_" },
    },
  });
  await prisma.provider.deleteMany({
    where: {
      personId: { startsWith: "test_person_" },
    },
  });

  await prisma.person.deleteMany({
    where: {
      id: { startsWith: "test_person_" },
    },
  });

  await prisma.address.deleteMany({
    where: {
      id: { startsWith: "test_address_" },
    },
  });

  await prisma.$disconnect();
});

describe("Referral table (safe test)", () => {
  let testProviderId: string;

  beforeAll(async () => {
    const testPerson = await prisma.person.create({
      data: {
        id: "test_person_001",
        firstName: "Testy",
        lastName: "McTestface",
        email: "<EMAIL>",
      },
    });

    const testAddress = await prisma.address.create({
      data: {
        id: "test_address_001",
        street: "123 Test St",
        city: "Testville",
        state: "TS",
        postal: "00000",
        formatted: "123 Test St, Testville, TS 00000",
        country: "US",
        latitude: 0,
        longitude: 0,
        timeZone: "UTC",
      },
    });

    const provider = await prisma.provider.create({
      data: {
        personId: testPerson.id,
        addressId: testAddress.id,
      },
    });

    testProviderId = provider.id;
  });

  it("creates a referral and reads it back", async () => {
    const referral = await prisma.referral.create({
      data: {
        invitationId: "TEST_001",
        email: "<EMAIL>",
        status: "INVITED",
        providerId: testProviderId,
      },
    });

    expect(referral.invitationId).toBe("TEST_001");

    const found = await prisma.referral.findUnique({
      where: { id: referral.id },
    });

    expect(found?.email).toBe("<EMAIL>");
    expect(found?.providerId).toBe(testProviderId);
  });

  it("updates a referral status", async () => {
    const referral = await prisma.referral.create({
      data: {
        invitationId: "TEST_002",
        email: "<EMAIL>",
        status: "INVITED",
        providerId: testProviderId,
      },
    });

    const updated = await prisma.referral.update({
      where: { id: referral.id },
      data: { status: "SIGNED_UP" },
    });

    expect(updated.status).toBe("SIGNED_UP");
  });

  it("deletes a referral safely", async () => {
    const referral = await prisma.referral.create({
      data: {
        invitationId: "TEST_003",
        email: "<EMAIL>",
        status: "INVITED",
        providerId: testProviderId,
      },
    });

    await prisma.referral.delete({ where: { id: referral.id } });

    const deleted = await prisma.referral.findUnique({
      where: { id: referral.id },
    });

    expect(deleted).toBeNull();
  });
});
