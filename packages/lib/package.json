{"name": "@axa/lib", "version": "0.1.0", "private": true, "types": "./dist/src/**/*.ts", "type": "module", "exports": {"./*/*.ts": {"types": "./dist/*/*.d.ts", "default": "./src/*/*.ts"}, "./*": {"types": "./dist/*.d.ts", "default": "./src/*.ts"}, "./utils": {"types": "./dist/utils/*.d.ts", "default": "./src/utils/index.ts"}, "./utils/*.ts": {"types": "./dist/utils/*.d.ts", "default": "./src/utils/*.ts"}, "./env": {"types": "./dist/env.d.ts", "default": "./src/env.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf .turbo node_modules dist", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.1", "@t3-oss/env-nextjs": "^0.13.6", "@upstash/qstash": "^2.8.1", "@upstash/query": "^0.0.2", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "resend": "^4.5.1", "twilio": "^5.7.0"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@sentry/nextjs": "^9.24.0", "eslint": "^9.28.0", "next": "^15.3.3", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "peerDependencies": {"@sentry/nextjs": "^8.24.0", "next": "^15.2.4"}, "prettier": "@axa/prettier-config"}