.bn-shadcn,
.bn-shadcn.dark {
  --bn-colors-editor-text: hsl(var(--foreground));
  --bn-colors-editor-background: hsl(var(--background));
  --bn-colors-menu-text: hsl(var(--foreground));
  --bn-colors-menu-background: hsl(var(--primary));
  --bn-colors-tooltip-text: hsl(var(--foreground));
  --bn-colors-tooltip-background: hsl(var(--primary));
  --bn-colors-hovered-text: hsl(var(--foreground));
  --bn-colors-hovered-background: hsl(var(--primary));
  --bn-colors-selected-text: hsl(var(--foreground));
  --bn-colors-selected-background: hsl(var(--primary));
  --bn-colors-disabled-text: hsl(var(--foreground));
  --bn-colors-disabled-background: hsl(var(--primary));
  --bn-colors-shadow: hsl(var(--primary));
  --bn-colors-border: hsl(var(--primary));
  --bn-colors-side-menu: hsl(var(--primary));
  --bn-color-highlight-colors: hsl(var(--foreground));
  --bn-border-radius: var(--radius);
  --bn-font-family: var(--font-sans);
}
