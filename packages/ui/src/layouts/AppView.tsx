import type { PropsWithChildren, ReactNode } from "react";

import { Children } from "react";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";
import { ErrorBoundary } from "react-error-boundary";

import { Button } from "@axa/ui/primitives/button";

import { cn } from "../lib";
import { ErrorAlert } from "../shared/ErrorAlert";

export default function AppView({
  loading = false,
  title,
  description,
  goBackUrl,
  size = "content",
  children,
}: PropsWithChildren<{
  loading?: boolean;
  title?: ReactNode;
  description?: ReactNode;
  goBackUrl?: string;
  size?: "full" | "content";
}>) {
  const [header, ...content] = Children.toArray(children);
  return (
    <main className="flex size-full flex-col">
      <ErrorBoundary
        fallbackRender={({ error, resetErrorBoundary }) => (
          <div className="flex size-full items-center justify-center">
            <ErrorAlert error={error} retry={resetErrorBoundary} />
          </div>
        )}
      >
        <div className="my-4 flex size-full flex-col justify-between gap-8 sm:my-6 md:my-8">
          <header className="mx-auto flex w-full max-w-screen-xl flex-col items-start gap-2">
            <div className="flex w-full max-w-screen-xl items-center gap-2">
              {goBackUrl && (
                <div>
                  <Button
                    disabled={loading}
                    variant="outline"
                    size="icon"
                    asChild
                  >
                    <Link href={goBackUrl}>
                      <ChevronLeft className="cursor-pointer" />
                    </Link>
                  </Button>
                </div>
              )}
              <div className="flex flex-1 flex-col gap-2 truncate">
                {title ? (
                  <h1 className="truncate text-xl font-semibold md:text-3xl">
                    {title}
                  </h1>
                ) : null}
              </div>
              <div className="ms-auto">{header}</div>
            </div>
            {description ? (
              <div
                className={cn(
                  "flex flex-row items-center justify-center gap-2",
                  {
                    "ps-10": !!goBackUrl,
                  },
                )}
              >
                <p className="text-muted-foreground">{description}</p>
              </div>
            ) : null}
          </header>

          <div
            className={cn("mx-auto w-full flex-1", {
              "max-w-screen-xl": size === "content",
              "max-w-screen-2xl": size === "full",
            })}
          >
            {content}
          </div>
        </div>
      </ErrorBoundary>
    </main>
  );
}
