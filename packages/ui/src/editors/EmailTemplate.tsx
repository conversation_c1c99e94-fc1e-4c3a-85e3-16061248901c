import React from "react";
import {
  Calendar,
  Copy,
  Edit,
  FileText,
  Save,
  Trash2,
  Users,
} from "lucide-react";

import type { EntityFormValues } from "@/ui/forms/Entity";

import type { EmailEditorProps } from "./EmailEditor";

import EntityForm from "@/ui/forms/Entity";
import { cn } from "@/ui/lib";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/ui/primitives/alert-dialog";
import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Input } from "@/ui/primitives/input";
import { Label } from "@/ui/primitives/label";
import { Textarea } from "@/ui/primitives/textarea";
import DialogForm from "@/ui/shared/DialogForm";

import { EmailEditor } from "./EmailEditor";

/** Template metadata for tracking and management */
export interface TemplateMetadata {
  /** Template ID (for existing templates) */
  id?: string;
  /** Template name/title */
  title: string;
  /** Template description */
  description?: string;
  /** Template category */
  category?: string;
  /** Creation date */
  createdAt?: Date;
  /** Last modified date */
  lastModified?: Date;
  /** Number of times template has been used */
  usageCount?: number;
  /** Template tags for organization */
  tags?: string[];
  /** Whether template is public/shared */
  isPublic?: boolean;
  /** Template author/creator */
  author?: string;
}

/** Template content structure */
export interface TemplateContent {
  /** Template title/subject */
  title: string;
  /** Template description */
  description?: string;
  /** Main email content (HTML) */
  content: string;
  /** Additional metadata fields */
  additionalMetadata?: Record<string, string>;
}

/** Complete template with metadata */
export interface TemplateWithMetadata extends TemplateContent {
  /** Template metadata */
  metadata: TemplateMetadata;
}

/** Template action handlers */
export interface TemplateActions {
  /** Save template */
  onSave?: (template: TemplateWithMetadata) => Promise<void>;
  /** Update template (only available for existing templates) */
  onUpdate?: (template: TemplateWithMetadata) => Promise<void>;
  /** Delete template (only available for existing templates) */
  onDelete?: (templateId: string) => Promise<void>;
  /** Duplicate template */
  onDuplicate?: (template: TemplateWithMetadata) => Promise<void>;
  /** Cancel editing */
  onCancel?: () => void;
}

/** Feature flags for template editor */
export interface TemplateFeatures {
  /** Show delete button */
  showDeleteButton?: boolean;
  /** Show update button */
  showUpdateButton?: boolean;
  /** Show duplicate button */
  showDuplicateButton?: boolean;
  /** Show cancel button */
  showCancelButton?: boolean;
  /** Show metadata fields */
  showMetadataFields?: boolean;
  /** Show usage statistics */
  showUsageStats?: boolean;
  /** Show template tags */
  showTags?: boolean;
  /** Allow category selection */
  showCategory?: boolean;
  /** Show template footer */
  showTemplateFooter?: boolean;
}

export interface EmailTemplateProps
  extends Omit<EmailEditorProps, "value" | "onValueChange" | "onChange"> {
  /** Template metadata */
  templateMetadata?: TemplateMetadata;
  /** Template content */
  templateContent?: TemplateContent;
  /** Template action handlers */
  actions?: TemplateActions;
  /** Feature configuration */
  features?: TemplateFeatures;
  /** Override default template header with custom content */
  templateHeader?: React.ReactNode;
  /** Override default template footer with custom content */
  templateFooter?: React.ReactNode;
  /** Additional CSS classes for the template container */
  templateClassName?: string;
  /** Callback when template content changes */
  onTemplateChange?: (content: TemplateContent) => void;
  /** Show default template management UI */
  showTemplateManagement?: boolean;
}

/** Format relative time for display */
function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60),
  );

  if (diffInMinutes < 1) return "Just now";
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) return `${diffInDays}d ago`;

  return date.toLocaleDateString();
}

/**
 * EmailTemplate component for creating and managing email templates
 * with structured content including title, main content, footer text, and metadata.
 */
export function EmailTemplate({
  templateMetadata,
  templateContent,
  actions,
  features = {},
  templateHeader,
  templateFooter,
  templateClassName,
  className,
  onTemplateChange,
  showTemplateManagement = true,
  header,
  headerContent,
  footerContent,
  footer,
  ...emailEditorProps
}: EmailTemplateProps) {
  const {
    showDeleteButton = true,
    showUpdateButton = true,
    showDuplicateButton = true,
    showCancelButton = false,
    showMetadataFields = false,
    showUsageStats = true,
    showTags = true,
    showCategory = false,
    showTemplateFooter = true,
  } = features;

  // Local state for template content
  const [localContent, setLocalContent] = React.useState<TemplateContent>({
    title: templateContent?.title ?? "",
    description: templateContent?.description,
    content: templateContent?.content ?? "",
    additionalMetadata: templateContent?.additionalMetadata ?? {},
  });

  // Loading states for async operations
  const [isLoading, setIsLoading] = React.useState({
    save: false,
    delete: false,
    duplicate: false,
  });

  // Update local content when props change (avoid unnecessary updates)
  React.useEffect(() => {
    if (templateContent) {
      setLocalContent(templateContent);
    }
  }, [
    templateContent?.title,
    templateContent?.description,
    templateContent?.content,
  ]);

  // Handle content changes
  const handleContentChange = React.useCallback(
    (field: keyof TemplateContent, value: string) => {
      setLocalContent((prev) => {
        const newContent = { ...prev, [field]: value };
        onTemplateChange?.(newContent);
        return newContent;
      });
    },
    [onTemplateChange],
  );

  // Handle main content change from editor
  const handleEditorChange = React.useCallback(
    (html: string) => {
      handleContentChange("content", html);
    },
    [handleContentChange],
  );

  // Handle save action
  const handleSave = React.useCallback(async () => {
    if (!actions?.onSave) return;

    setIsLoading((prev) => ({ ...prev, save: true }));
    try {
      await actions.onSave({
        ...localContent,
        metadata: templateMetadata ?? { title: localContent.title },
      });
    } finally {
      setIsLoading((prev) => ({ ...prev, save: false }));
    }
  }, [actions, localContent, templateMetadata]);

  // Handle update action via dialog form
  const handleUpdateSubmit = React.useCallback(
    async (values: EntityFormValues) => {
      if (!actions?.onUpdate) return;

      await actions.onUpdate({
        ...localContent,
        title: values.title,
        description: values.description,
        metadata: templateMetadata
          ? {
              ...templateMetadata,
              title: values.title,
              description: values.description,
            }
          : { title: values.title, description: values.description },
      });

      // Update local content with new values
      setLocalContent((prev) => ({
        ...prev,
        title: values.title,
        description: values.description || "",
      }));
    },
    [actions, localContent, templateMetadata],
  );

  // Handle delete action
  const handleDelete = React.useCallback(async () => {
    if (!templateMetadata?.id || !actions?.onDelete) return;

    setIsLoading((prev) => ({ ...prev, delete: true }));
    try {
      await actions.onDelete(templateMetadata.id);
    } finally {
      setIsLoading((prev) => ({ ...prev, delete: false }));
    }
  }, [actions, templateMetadata]);

  // Handle duplicate action
  const handleDuplicate = React.useCallback(async () => {
    if (!actions?.onDuplicate) return;

    setIsLoading((prev) => ({ ...prev, duplicate: true }));
    try {
      await actions.onDuplicate({
        ...localContent,
        metadata: {
          ...templateMetadata,
          title: `${localContent.title} (Copy)`,
          id: undefined,
        },
      });
    } finally {
      setIsLoading((prev) => ({ ...prev, duplicate: false }));
    }
  }, [actions, localContent, templateMetadata]);

  // Render default template management header
  const renderDefaultTemplateHeader = () => (
    <div className="bg-background p-6">
      <div className="space-y-4">
        {/* Title and Description */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium text-foreground">
                {localContent.title || "Untitled"}
              </h2>

              <p className="text-sm text-muted-foreground">
                {localContent.description || "No description"}
              </p>
            </div>

            {/* Action buttons - positioned on the right */}
            <div className="flex flex-col gap-2 md:flex-row">
              {showUpdateButton && templateMetadata?.id && (
                <DialogForm
                  Component={EntityForm}
                  onSubmit={handleUpdateSubmit}
                  type="update"
                  title="Update Template"
                  description="Update the template title and description"
                  label="Update"
                  size="sm"
                  variant="outline"
                  defaultValues={{
                    id: templateMetadata.id,
                    title: localContent.title,
                    description: localContent.description || "",
                  }}
                >
                  <Button variant="outline" size="sm">
                    <Edit className="size-4 sm:mr-2" />
                    <span className="hidden sm:inline">Edit</span>
                  </Button>
                </DialogForm>
              )}
              {showDeleteButton && templateMetadata?.id && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      disabled={isLoading.delete}
                    >
                      <Trash2 className="size-4 sm:mr-2" />
                      <span className="hidden sm:inline">
                        {isLoading.delete ? "Deleting..." : "Delete"}
                      </span>
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Template</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete "
                        {localContent.title || "this template"}"? This action
                        cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDelete}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete Template
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>

          {/* Template metadata */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4">
              {templateMetadata?.category && (
                <Badge variant="secondary">{templateMetadata.category}</Badge>
              )}
              {templateMetadata?.isPublic && (
                <Badge variant="outline">Public</Badge>
              )}
              {showUsageStats && templateMetadata?.usageCount !== undefined && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Users className="size-3" />
                  <span>Used {templateMetadata.usageCount} times</span>
                </div>
              )}
              {templateMetadata?.lastModified && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Calendar className="size-3" />
                  <span>
                    Modified {formatRelativeTime(templateMetadata.lastModified)}
                  </span>
                </div>
              )}
            </div>

            {/* Action buttons - positioned on the right */}
            <div className="flex flex-row gap-2">
              <Button
                size="sm"
                onClick={handleSave}
                disabled={isLoading.save}
                className="flex w-full flex-1 gap-2 md:w-auto md:flex-none"
              >
                <Save className="size-4" />
                <span>{isLoading.save ? "Saving..." : "Save Template"}</span>
              </Button>
              {showDuplicateButton && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDuplicate}
                  disabled={isLoading.duplicate}
                >
                  <Copy className="size-4 sm:mr-2" />
                  <span className="hidden sm:inline">
                    {isLoading.duplicate ? "Duplicating..." : "Duplicate"}
                  </span>
                </Button>
              )}
              {showCancelButton && (
                <Button variant="ghost" size="sm" onClick={actions?.onCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Action buttons */}
      </div>
    </div>
  );

  // Render default template footer
  const renderDefaultTemplateFooter = () =>
    showTemplateFooter ? (
      <div className="space-y-4 bg-background p-6">
        {/* Template tags */}
        {showTags &&
          templateMetadata?.tags &&
          templateMetadata.tags.length > 0 && (
            <div className="flex items-center gap-2">
              <FileText className="size-4 text-muted-foreground" />
              <div className="flex flex-wrap gap-1">
                {templateMetadata.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

        {/* Metadata fields */}
        {showMetadataFields && (
          <div className="space-y-2">
            <Label>Additional Metadata</Label>
            <div className="text-sm text-muted-foreground">
              <p>Category: {templateMetadata?.category ?? "Uncategorized"}</p>
              {templateMetadata?.author && (
                <p>Author: {templateMetadata.author}</p>
              )}
              {templateMetadata?.createdAt && (
                <p>
                  Created: {templateMetadata.createdAt.toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    ) : null;

  // Determine what to use for header and footer
  const finalHeader = showTemplateManagement
    ? (templateHeader ?? renderDefaultTemplateHeader())
    : header;

  const finalFooter = showTemplateManagement
    ? (templateFooter ?? renderDefaultTemplateFooter())
    : footer;

  return (
    <div className={cn("w-full", templateClassName)}>
      <EmailEditor
        {...emailEditorProps}
        className={cn("w-full", className)}
        value={localContent.content}
        onValueChange={handleEditorChange}
        header={finalHeader}
        headerContent={headerContent}
        footerContent={footerContent}
        footer={finalFooter}
      />
    </div>
  );
}
