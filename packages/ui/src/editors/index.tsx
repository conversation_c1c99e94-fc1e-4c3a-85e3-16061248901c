"use client";

import dynamic from "next/dynamic";

import { Skeleton } from "@/ui/primitives/skeleton";

export const Editor = dynamic(() => import("./Editor"), {
  ssr: false,
  loading: () => (
    <div>
      <Skeleton />
    </div>
  ),
});

export const EmailEditor = dynamic(
  () => import("./EmailEditor").then((mod) => mod.EmailEditor),
  {
    ssr: false,
    loading: () => (
      <div>
        <Skeleton />
      </div>
    ),
  },
);

export const EmailComposer = dynamic(
  () =>
    import("./EmailComposer").then((mod) => ({ default: mod.EmailComposer })),
  {
    ssr: false,
    loading: () => (
      <div>
        <Skeleton />
      </div>
    ),
  },
);

export const EmailTemplate = dynamic(
  () =>
    import("./EmailTemplate").then((mod) => ({ default: mod.EmailTemplate })),
  {
    ssr: false,
    loading: () => (
      <div>
        <Skeleton />
      </div>
    ),
  },
);

export const JobPostEditor = dynamic(
  () => import("./JobPostEditor").then((mod) => mod.JobPostEditor),
  {
    ssr: false,
    loading: () => (
      <div>
        <Skeleton />
      </div>
    ),
  },
);

export const JobPostComposer = dynamic(
  () =>
    import("./JobPostComposer").then((mod) => ({
      default: mod.JobPostComposer,
    })),
  {
    ssr: false,
    loading: () => (
      <div>
        <Skeleton />
      </div>
    ),
  },
);

// Direct exports for better tree shaking and type support
export type { EditorProps } from "./Editor";

export type {
  EmailEditorProps,
  EmailContent,
  SanitizeConfig,
} from "./EmailEditor";

export type {
  EmailComposerProps,
  EmailComposerActions,
  EmailComposerFeatures,
  EmailMetadata,
  EmailStatus,
  EmailPriority,
} from "./EmailComposer";

export type {
  EmailTemplateProps,
  TemplateMetadata,
  TemplateContent,
  TemplateWithMetadata,
  TemplateActions,
  TemplateFeatures,
} from "./EmailTemplate";

export type { JobPostEditorProps, JobPostContent } from "./JobPostEditor";

export type {
  JobPostComposerProps,
  JobPostComposerActions,
  JobPostComposerFeatures,
  JobPostMetadata,
  JobPostStatus,
  JobPostPriority,
  JobPostType,
} from "./JobPostComposer";

export { useEditor } from "./use-editor";
export type { UseEditorOptions, EditorContent } from "./use-editor";
