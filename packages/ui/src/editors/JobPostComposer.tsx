import React, { useState } from "react";
import {
  Briefcase,
  Building,
  Clock,
  DollarSign,
  Eye,
  MapPin,
  Save,
  Send,
  Settings,
  Users,
} from "lucide-react";

import type { JobPostEditorProps } from "./JobPostEditor";

import { cn } from "@/ui/lib";
import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";

import { JobPostEditor } from "./JobPostEditor";

/** Job post status types */
type JobPostStatus = "draft" | "published" | "paused" | "closed" | "archived";

/** Job post priority types */
type JobPostPriority = "standard" | "featured" | "urgent";

/** Job post type categories */
type JobPostType =
  | "full-time"
  | "part-time"
  | "contract"
  | "internship"
  | "remote";

/** Job post action handlers */
interface JobPostComposerActions {
  /** Preview job post */
  onPreview?: () => void;
  /** Save draft */
  onSave?: () => void;
  /** Publish job post */
  onPublish?: () => void;
  /** Schedule publication */
  onSchedule?: () => void;
  /** Open job post settings */
  onSettings?: () => void;
  /** View applications */
  onViewApplications?: () => void;
}

/** Job post metadata for header display */
interface JobPostMetadata {
  /** Job title */
  title?: string;
  /** Current status */
  status?: JobPostStatus;
  /** Priority level */
  priority?: JobPostPriority;
  /** Job type */
  jobType?: JobPostType;
  /** Company name */
  company?: string;
  /** Location */
  location?: string;
  /** Salary range */
  salaryRange?: string;
  /** Number of applications received */
  applicationCount?: number;
  /** Last saved timestamp */
  lastSaved?: Date | string;
  /** Publication date */
  publishedAt?: Date | string;
  /** Application deadline */
  applicationDeadline?: Date | string;
  /** Whether job is remote-friendly */
  isRemote?: boolean;
  /** Whether job is featured */
  isFeatured?: boolean;
}

/** Feature flags to control JobPostComposer UI elements */
interface JobPostComposerFeatures {
  /** Show job title */
  showTitle?: boolean;
  /** Show job status badge */
  showStatus?: boolean;
  /** Show priority badge */
  showPriority?: boolean;
  /** Show job type badge */
  showJobType?: boolean;
  /** Show company name */
  showCompany?: boolean;
  /** Show location */
  showLocation?: boolean;
  /** Show salary range */
  showSalaryRange?: boolean;
  /** Show application count */
  showApplicationCount?: boolean;
  /** Show last saved time */
  showLastSaved?: boolean;
  /** Show remote badge */
  showRemoteBadge?: boolean;
  /** Show featured badge */
  showFeaturedBadge?: boolean;
  /** Show preview button */
  showPreviewButton?: boolean;
  /** Show save button */
  showSaveButton?: boolean;
  /** Show publish button */
  showPublishButton?: boolean;
  /** Show schedule button */
  showScheduleButton?: boolean;
  /** Show settings button */
  showSettingsButton?: boolean;
  /** Show applications button */
  showApplicationsButton?: boolean;
}

interface JobPostComposerProps
  extends Omit<JobPostEditorProps, "header" | "footer"> {
  /** Job post metadata for header display */
  metadata?: JobPostMetadata;
  /** Action handlers for buttons */
  actions?: JobPostComposerActions;
  /** Feature flags to control UI elements */
  features?: JobPostComposerFeatures;
  /** Custom header content (overrides default header) */
  customHeader?: React.ReactNode;
  /** Custom footer content (overrides default footer) */
  customFooter?: React.ReactNode;
  /** Additional CSS classes for the composer wrapper */
  composerClassName?: string;
}

// Default feature configuration
const defaultFeatures: Required<JobPostComposerFeatures> = {
  showTitle: true,
  showStatus: true,
  showPriority: true,
  showJobType: true,
  showCompany: true,
  showLocation: true,
  showSalaryRange: true,
  showApplicationCount: true,
  showLastSaved: true,
  showRemoteBadge: true,
  showFeaturedBadge: true,
  showPreviewButton: true,
  showSaveButton: true,
  showPublishButton: true,
  showScheduleButton: true,
  showSettingsButton: true,
  showApplicationsButton: true,
};

// Status badge variants
const statusVariants: Record<
  JobPostStatus,
  "default" | "secondary" | "destructive" | "outline"
> = {
  draft: "outline",
  published: "default",
  paused: "secondary",
  closed: "destructive",
  archived: "outline",
};

// Priority badge variants
const priorityVariants: Record<
  JobPostPriority,
  "default" | "secondary" | "destructive" | "outline"
> = {
  standard: "outline",
  featured: "default",
  urgent: "destructive",
};

// Job type colors
const jobTypeVariants: Record<
  JobPostType,
  "default" | "secondary" | "destructive" | "outline"
> = {
  "full-time": "default",
  "part-time": "secondary",
  contract: "outline",
  internship: "secondary",
  remote: "outline",
};

// Priority labels
const priorityLabels: Record<JobPostPriority, string> = {
  standard: "Standard",
  featured: "Featured",
  urgent: "Urgent",
};

// Format last saved time
function formatLastSaved(lastSaved?: Date | string): string {
  if (!lastSaved) return "";

  const date = typeof lastSaved === "string" ? new Date(lastSaved) : lastSaved;
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));

  if (diffMins < 1) return "Just saved";
  if (diffMins < 60)
    return `Saved ${diffMins} minute${diffMins === 1 ? "" : "s"} ago`;

  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24)
    return `Saved ${diffHours} hour${diffHours === 1 ? "" : "s"} ago`;

  return `Saved on ${date.toLocaleDateString()}`;
}

export function JobPostComposer({
  metadata = {},
  actions = {},
  features = {},
  customHeader,
  customFooter,
  composerClassName,
  className,
  showSkeleton = false, // Default to false for composer
  ...jobPostEditorProps
}: JobPostComposerProps) {
  const [isLoading, setIsLoading] = useState(false);

  const mergedFeatures = { ...defaultFeatures, ...features };
  const {
    title = "New Job Post",
    status = "draft",
    priority = "standard",
    jobType = "full-time",
    company,
    location,
    salaryRange,
    applicationCount,
    lastSaved,
    isRemote = false,
    isFeatured = false,
  } = metadata;

  const handleAction = (actionFn?: () => void) => async () => {
    if (!actionFn) return;

    setIsLoading(true);
    try {
      await actionFn();
    } finally {
      setIsLoading(false);
    }
  };

  // Default header content - controls area with padding added here
  const defaultHeader = (
    <div className="p-4">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          {mergedFeatures.showTitle && (
            <h3 className="text-lg font-semibold">{title}</h3>
          )}
          <div className="flex flex-wrap items-center gap-2">
            {mergedFeatures.showStatus && (
              <Badge variant={statusVariants[status]} className="capitalize">
                {status}
              </Badge>
            )}
            {mergedFeatures.showPriority && priority !== "standard" && (
              <Badge variant={priorityVariants[priority]}>
                {priorityLabels[priority]}
              </Badge>
            )}
            {mergedFeatures.showJobType && (
              <Badge variant={jobTypeVariants[jobType]} className="capitalize">
                <Briefcase className="mr-1 size-3" />
                {jobType.replace("-", " ")}
              </Badge>
            )}
            {mergedFeatures.showCompany && company && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Building className="size-3" />
                <span>{company}</span>
              </div>
            )}
            {mergedFeatures.showLocation && location && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <MapPin className="size-3" />
                <span>{location}</span>
              </div>
            )}
            {mergedFeatures.showSalaryRange && salaryRange && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <DollarSign className="size-3" />
                <span>{salaryRange}</span>
              </div>
            )}
            {mergedFeatures.showRemoteBadge && isRemote && (
              <Badge variant="outline">
                <MapPin className="mr-1 size-3" />
                Remote
              </Badge>
            )}
            {mergedFeatures.showFeaturedBadge && isFeatured && (
              <Badge variant="outline">Featured</Badge>
            )}
            {mergedFeatures.showLastSaved && (
              <span className="text-sm text-muted-foreground">
                {formatLastSaved(lastSaved)}
              </span>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          {mergedFeatures.showPreviewButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAction(actions.onPreview)}
              disabled={isLoading}
            >
              <Eye className="mr-2 size-4" />
              Preview
            </Button>
          )}
          {mergedFeatures.showSaveButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction(actions.onSave)}
              disabled={isLoading}
            >
              <Save className="mr-2 size-4" />
              Save Draft
            </Button>
          )}
          {mergedFeatures.showScheduleButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction(actions.onSchedule)}
              disabled={isLoading}
            >
              <Clock className="mr-2 size-4" />
              Schedule
            </Button>
          )}
          {mergedFeatures.showPublishButton && (
            <Button
              size="sm"
              onClick={handleAction(actions.onPublish)}
              disabled={isLoading}
            >
              <Send className="mr-2 size-4" />
              Publish
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  // Default footer content - controls area with padding added here
  const defaultFooter = (
    <div className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {mergedFeatures.showApplicationCount &&
            applicationCount !== undefined && (
              <span className="flex items-center gap-1 text-sm text-muted-foreground">
                <Users className="size-4" />
                Applications: {applicationCount.toLocaleString()}
              </span>
            )}
          {mergedFeatures.showFeaturedBadge && isFeatured && (
            <Badge variant="outline" className="text-xs">
              Featured Listing
            </Badge>
          )}
        </div>
        <div className="flex gap-2">
          {mergedFeatures.showApplicationsButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAction(actions.onViewApplications)}
              disabled={isLoading}
            >
              <Users className="mr-2 size-4" />
              View Applications
            </Button>
          )}
          {mergedFeatures.showSettingsButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAction(actions.onSettings)}
              disabled={isLoading}
            >
              <Settings className="mr-2 size-4" />
              Job Settings
            </Button>
          )}
          {mergedFeatures.showScheduleButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction(actions.onSchedule)}
              disabled={isLoading}
            >
              <Clock className="mr-2 size-4" />
              Schedule
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className={cn("w-full", composerClassName)}>
      <JobPostEditor
        {...jobPostEditorProps}
        className={cn("w-full", className)}
        header={customHeader || defaultHeader}
        footer={customFooter || defaultFooter}
        showSkeleton={showSkeleton}
      />
    </div>
  );
}

// Export types for external use
export type {
  JobPostComposerProps,
  JobPostComposerActions,
  JobPostComposerFeatures,
  JobPostMetadata,
  JobPostStatus,
  JobPostPriority,
  JobPostType,
};
