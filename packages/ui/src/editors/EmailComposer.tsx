"use client";

import type { ComponentProps } from "react";

import { useState } from "react";
import { Clock, Eye, Save, Send, Settings, Target, Users } from "lucide-react";

import type { EmailEditorProps } from "./EmailEditor";

import { cn } from "@/ui/lib";
import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";

import { EmailEditor } from "./EmailEditor";

/** Email status types */
type EmailStatus = "draft" | "scheduled" | "sent" | "archived";

/** Email priority levels */
type EmailPriority = "low" | "normal" | "high" | "urgent";

/** Email composer action handlers */
interface EmailComposerActions {
  onPreview?: () => void;
  onSave?: () => void;
  onSend?: () => void;
  onSchedule?: () => void;
  onSettings?: () => void;
}

/** Email metadata for display */
interface EmailMetadata {
  title?: string;
  status?: EmailStatus;
  priority?: EmailPriority;
  recipientCount?: number;
  lastSaved?: Date | string;
  isAbTestReady?: boolean;
  campaignType?: string;
}

/** Feature flags to control what elements are shown */
interface EmailComposerFeatures {
  showTitle?: boolean;
  showStatus?: boolean;
  showPriority?: boolean;
  showLastSaved?: boolean;
  showRecipientCount?: boolean;
  showAbTestBadge?: boolean;
  showPreviewButton?: boolean;
  showSaveButton?: boolean;
  showSendButton?: boolean;
  showScheduleButton?: boolean;
  showSettingsButton?: boolean;
}

interface EmailComposerProps
  extends Omit<EmailEditorProps, "header" | "footer"> {
  /** Email metadata for header display */
  metadata?: EmailMetadata;
  /** Action handlers for buttons */
  actions?: EmailComposerActions;
  /** Feature flags to control UI elements */
  features?: EmailComposerFeatures;
  /** Custom header content (overrides default header) */
  customHeader?: React.ReactNode;
  /** Custom footer content (overrides default footer) */
  customFooter?: React.ReactNode;
  /** Additional CSS classes for the composer wrapper */
  composerClassName?: string;
}

const defaultFeatures: EmailComposerFeatures = {
  showTitle: true,
  showStatus: true,
  showPriority: true,
  showLastSaved: true,
  showRecipientCount: true,
  showAbTestBadge: true,
  showPreviewButton: true,
  showSaveButton: true,
  showSendButton: true,
  showScheduleButton: false,
  showSettingsButton: true,
};

const statusVariants: Record<
  EmailStatus,
  ComponentProps<typeof Badge>["variant"]
> = {
  draft: "secondary",
  scheduled: "default",
  sent: "default",
  archived: "outline",
};

const priorityVariants: Record<
  EmailPriority,
  ComponentProps<typeof Badge>["variant"]
> = {
  low: "outline",
  normal: "secondary",
  high: "destructive",
  urgent: "destructive",
};

const priorityLabels: Record<EmailPriority, string> = {
  low: "Low Priority",
  normal: "Normal",
  high: "High Priority",
  urgent: "Urgent",
};

function formatLastSaved(lastSaved?: Date | string): string {
  if (!lastSaved) return "Never saved";

  const date = typeof lastSaved === "string" ? new Date(lastSaved) : lastSaved;
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));

  if (diffMins < 1) return "Just saved";
  if (diffMins < 60)
    return `Saved ${diffMins} minute${diffMins === 1 ? "" : "s"} ago`;

  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24)
    return `Saved ${diffHours} hour${diffHours === 1 ? "" : "s"} ago`;

  return `Saved on ${date.toLocaleDateString()}`;
}

export function EmailComposer({
  metadata = {},
  actions = {},
  features = {},
  customHeader,
  customFooter,
  composerClassName,
  className,
  showSkeleton = false, // Default to false for composer
  ...emailEditorProps
}: EmailComposerProps) {
  const [isLoading, setIsLoading] = useState(false);

  const mergedFeatures = { ...defaultFeatures, ...features };
  const {
    title = "Email Composition",
    status = "draft",
    priority = "normal",
    recipientCount,
    lastSaved,
    isAbTestReady = false,
    campaignType,
  } = metadata;

  const handleAction = (actionFn?: () => void) => async () => {
    if (!actionFn) return;

    setIsLoading(true);
    try {
      await actionFn();
    } finally {
      setIsLoading(false);
    }
  };

  // Default header content - controls area with padding added here since it's not in the editor
  const defaultHeader = (
    <div className="p-4">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          {mergedFeatures.showTitle && (
            <h3 className="text-lg font-semibold">{title}</h3>
          )}
          <div className="flex items-center gap-2">
            {mergedFeatures.showStatus && (
              <Badge variant={statusVariants[status]} className="capitalize">
                {status}
              </Badge>
            )}
            {mergedFeatures.showPriority && priority !== "normal" && (
              <Badge variant={priorityVariants[priority]}>
                {priorityLabels[priority]}
              </Badge>
            )}
            {mergedFeatures.showAbTestBadge && isAbTestReady && (
              <Badge variant="outline">
                <Target className="mr-1 size-3" />
                A/B Test Ready
              </Badge>
            )}
            {campaignType && (
              <Badge variant="outline" className="text-xs">
                {campaignType}
              </Badge>
            )}
            {mergedFeatures.showLastSaved && (
              <span className="text-sm text-muted-foreground">
                {formatLastSaved(lastSaved)}
              </span>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          {mergedFeatures.showPreviewButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAction(actions.onPreview)}
              disabled={isLoading}
            >
              <Eye className="mr-2 size-4" />
              Preview
            </Button>
          )}
          {mergedFeatures.showSaveButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction(actions.onSave)}
              disabled={isLoading}
            >
              <Save className="mr-2 size-4" />
              Save Draft
            </Button>
          )}
          {mergedFeatures.showScheduleButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction(actions.onSchedule)}
              disabled={isLoading}
            >
              <Clock className="mr-2 size-4" />
              Schedule
            </Button>
          )}
          {mergedFeatures.showSendButton && (
            <Button
              size="sm"
              onClick={handleAction(actions.onSend)}
              disabled={isLoading}
            >
              <Send className="mr-2 size-4" />
              Send Now
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  // Default footer content - controls area with padding added here since it's not in the editor
  const defaultFooter = (
    <div className="p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {mergedFeatures.showRecipientCount &&
            recipientCount !== undefined && (
              <span className="flex items-center gap-1 text-sm text-muted-foreground">
                <Users className="size-4" />
                Recipients: {recipientCount.toLocaleString()} contacts
              </span>
            )}
          {mergedFeatures.showAbTestBadge && isAbTestReady && (
            <Badge variant="outline" className="text-xs">
              <Target className="mr-1 size-3" />
              A/B Test Ready
            </Badge>
          )}
        </div>
        <div className="flex gap-2">
          {mergedFeatures.showSettingsButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAction(actions.onSettings)}
              disabled={isLoading}
            >
              <Settings className="mr-2 size-4" />
              Advanced Settings
            </Button>
          )}
          {mergedFeatures.showScheduleButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleAction(actions.onSchedule)}
              disabled={isLoading}
            >
              <Clock className="mr-2 size-4" />
              Schedule
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className={cn("w-full", composerClassName)}>
      <EmailEditor
        {...emailEditorProps}
        className={cn("w-full", className)}
        header={customHeader || defaultHeader}
        footer={customFooter || defaultFooter}
        showSkeleton={showSkeleton}
      />
    </div>
  );
}

// Export types for external use
export type {
  EmailComposerProps,
  EmailComposerActions,
  EmailComposerFeatures,
  EmailMetadata,
  EmailStatus,
  EmailPriority,
};
