"use client";

import type { Block } from "@blocknote/core";

import { useCallback, useEffect, useState } from "react";
import DOMPurify from "dompurify";

import type { EditorContent, EditorProps } from "./Editor";

import { cn } from "@/ui/lib";

import Editor from "./Editor";

/** Configuration options for DOMPurify HTML sanitization */
interface SanitizeConfig {
  ALLOWED_TAGS?: string[];
  ALLOWED_ATTR?: string[];
  FORBID_TAGS?: string[];
  FORBID_ATTR?: string[];
  ALLOW_DATA_ATTR?: boolean;
  [key: string]: unknown;
}

/** Email editor content in all supported formats */
interface EmailContent {
  blocks: Block[];
  markdown: string;
  html: string;
}

interface EmailEditorProps
  extends Omit<EditorProps, "value" | "onChange" | "initialContent"> {
  /** HTML content value - primary format for email persistence */
  value?: string;
  /** Default HTML content */
  defaultValue?: string;
  /** Callback when HTML content changes - primary callback for persistence */
  onValueChange?: (htmlContent: string) => void;
  /** Legacy callback for HTML content changes */
  onChange?: (htmlContent: string) => void;
  /** Additional callback for accessing all content formats */
  onContentChange?: (content: EmailContent) => void;
  /** Additional CSS classes for the editor container */
  className?: string;
  /** Show skeleton loading placeholders */
  showSkeleton?: boolean;
  /** Height class for the editor (e.g., "min-h-[400px]") */
  editorHeight?: string;
  /** Additional CSS classes for the root container */
  containerClassName?: string;
  /** React nodes to render in the header control area (no padding) */
  header?: React.ReactNode;
  /** React nodes to render in the header content area (with padding) */
  headerContent?: React.ReactNode;
  /** React nodes to render in the footer content area (with padding) */
  footerContent?: React.ReactNode;
  /** React nodes to render in the footer control area (no padding) */
  footer?: React.ReactNode;
  /** Enable/disable HTML sanitization (default: true) */
  sanitizeHtml?: boolean;
  /** Custom DOMPurify config for HTML sanitization */
  sanitizeConfig?: SanitizeConfig;
}

export function EmailEditor({
  value = "",
  defaultValue = "",
  onValueChange,
  onChange,
  onContentChange,
  className,
  editorHeight = "min-h-[400px]",
  sanitizeHtml = true,
  showSkeleton = true,
  header,
  headerContent,
  footerContent,
  footer,
  sanitizeConfig,
  ...props
}: EmailEditorProps) {
  // Initialize with HTML content - primary format for email persistence
  const [htmlContent, setHtmlContent] = useState<string>(() => {
    const initialContent = defaultValue || value;
    return sanitizeHtml
      ? String(DOMPurify.sanitize(initialContent, sanitizeConfig))
      : initialContent;
  });

  // Update HTML content when value prop changes
  useEffect(() => {
    if (value !== undefined && value !== htmlContent) {
      const sanitized = sanitizeHtml
        ? String(DOMPurify.sanitize(value, sanitizeConfig))
        : value;
      setHtmlContent(sanitized);
    }
  }, [value, htmlContent, sanitizeHtml, sanitizeConfig]);

  // Sanitize and emit HTML content changes
  const handleContentUpdate = useCallback(
    (newHtmlContent: string): void => {
      const finalContent = sanitizeHtml
        ? String(DOMPurify.sanitize(newHtmlContent, sanitizeConfig))
        : newHtmlContent;

      setHtmlContent(finalContent);

      // Emit HTML content changes - primary persistence format
      onValueChange?.(finalContent);
      onChange?.(finalContent);
    },
    [onValueChange, onChange, sanitizeHtml, sanitizeConfig],
  );

  // Handle editor content changes with all formats
  const handleEditorChange = useCallback(
    (content: EditorContent): void => {
      // Update HTML content - primary format
      handleContentUpdate(content.html);

      // Provide access to all formats if needed
      onContentChange?.({
        blocks: content.blocks,
        markdown: content.markdown,
        html: content.html,
      });
    },
    [handleContentUpdate, onContentChange],
  );

  // Direct HTML change handler for optimal performance
  const handleHtmlChange = useCallback(
    (newHtmlContent: string): void => {
      handleContentUpdate(newHtmlContent);
    },
    [handleContentUpdate],
  );

  return (
    <section
      className={cn(
        "w-full overflow-auto rounded-lg border bg-background",
        "flex flex-col gap-1",
        "has-[.editor-focus:focus-within]:ring-2 has-[.editor-focus:focus-within]:ring-ring has-[.editor-focus:focus-within]:ring-offset-2",
        className,
      )}
    >
      {/* Header Control Area - No padding, for controls/metadata */}
      {(header || (showSkeleton && !headerContent)) && (
        <header className="border-b">
          {header ??
            (showSkeleton && (
              <div className="p-4 ps-14">
                <div className="space-y-3">
                  <div className="h-4 w-2/3 animate-pulse rounded-lg bg-muted" />
                  <div className="h-3 w-1/2 animate-pulse rounded-lg bg-muted/60" />
                </div>
              </div>
            ))}
        </header>
      )}

      <article className="flex-1 overflow-auto">
        {/* Header Content Area - With padding, for email content text */}
        {headerContent && (
          <header className={cn("p-4 ps-14", header ? "" : "border-b")}>
            {headerContent}
          </header>
        )}

        {/* Main Editor Area - Only this area triggers focus ring on container */}
        <div
          className={cn(
            "editor-focus w-full min-w-60",
            editorHeight,
            "overflow-hidden py-4",
          )}
        >
          <Editor
            {...props}
            initialContent={{
              type: "html",
              content: htmlContent,
            }}
            onChange={handleEditorChange}
            onHTMLChange={handleHtmlChange}
          />
        </div>

        {/* Footer Content Area - With padding, for email content text */}
        {footerContent && (
          <footer className={cn("p-4 ps-14", footer ? "" : "border-t")}>
            {footerContent}
          </footer>
        )}
      </article>

      {/* Footer Control Area - No padding, for controls/actions */}
      {(footer || (showSkeleton && !footerContent)) && (
        <footer className="border-t">
          {footer ??
            (showSkeleton && (
              <div className="p-4 ps-14">
                <div className="space-y-3">
                  <div className="h-4 w-2/3 animate-pulse rounded-lg bg-muted" />
                  <div className="h-3 w-1/2 animate-pulse rounded-lg bg-muted/60" />
                </div>
              </div>
            ))}
        </footer>
      )}
    </section>
  );
}

// Export the props interface for external use
export type { EmailEditorProps, EmailContent, SanitizeConfig };
