import type { Block } from "@blocknote/core";

import { useCallback, useEffect, useMemo, useState } from "react";
import { useCreateBlockNote } from "@blocknote/react";

export interface UseEditorOptions {
  initialContent?: {
    type: "blocks" | "markdown" | "html";
    content: string | Block[];
  };
  onChange?: (content: {
    blocks: Block[];
    markdown: string;
    html: string;
  }) => void;
  onMarkdownChange?: (markdown: string) => void;
  onHTMLChange?: (html: string) => void;
  onBlocksChange?: (blocks: Block[]) => void;
}

export interface EditorContent {
  blocks: Block[];
  markdown: string;
  html: string;
}

function customToMarkdown(text: string) {
  return text;
}

export function useEditor(options: UseEditorOptions = {}) {
  const {
    initialContent,
    onChange,
    onMarkdownChange,
    onHTMLChange,
    onBlocksChange,
  } = options;

  const [content, setContent] = useState<EditorContent>({
    blocks: [],
    markdown: "",
    html: "",
  });
  const [isInitialized, setIsInitialized] = useState(false);

  const editor = useCreateBlockNote({
    pasteHandler: ({ event, editor, defaultPasteHandler }) => {
      if (event.clipboardData?.types.includes("text/my-custom-format")) {
        // You can do any custom logic here, for example you could transform the clipboard data before pasting it
        const md = customToMarkdown(
          event.clipboardData.getData("text/my-custom-format"),
        );

        // The editor is able paste markdown (`pasteMarkdown`), HTML (`pasteHTML`), or plain text (`pasteText`)
        void editor.pasteMarkdown(md);
        // We handled the paste event, so return true, returning false will cancel the paste event
        return true;
      }

      // If we didn't handle the paste event, call the default paste handler to do the default behavior
      return defaultPasteHandler();
    },
  });

  // Convert current editor content to all formats
  const updateContent = useCallback(async () => {
    const blocks = editor.document;
    const markdown = await editor.blocksToMarkdownLossy(blocks);
    const html = await editor.blocksToHTMLLossy(blocks);

    const newContent = { blocks, markdown, html };
    setContent(newContent);

    // Call individual format callbacks
    onMarkdownChange?.(markdown);
    onHTMLChange?.(html);
    onBlocksChange?.(blocks);
    onChange?.(newContent);
  }, [editor, onChange, onMarkdownChange, onHTMLChange, onBlocksChange]);

  // Handle editor changes
  const handleEditorChange = useCallback(() => {
    void updateContent();
  }, [updateContent]);

  // Initialize editor with content
  useEffect(() => {
    if (!initialContent || isInitialized) return;

    async function loadInitialContent() {
      if (!initialContent) return;

      let blocks: Block[] = [];

      try {
        switch (initialContent.type) {
          case "markdown":
            if (typeof initialContent.content === "string") {
              blocks = await editor.tryParseMarkdownToBlocks(
                initialContent.content,
              );
            }
            break;
          case "html":
            if (typeof initialContent.content === "string") {
              blocks = await editor.tryParseHTMLToBlocks(
                initialContent.content,
              );
            }
            break;
          case "blocks":
            if (Array.isArray(initialContent.content)) {
              blocks = initialContent.content;
            }
            break;
          default:
            // Default to empty blocks if type is unknown
            blocks = [];
        }

        if (blocks.length > 0) {
          editor.replaceBlocks(editor.document, blocks);
        }

        // Update content state after initialization
        await updateContent();
        setIsInitialized(true);
      } catch (error) {
        console.error("Error loading initial content:", error);
        setIsInitialized(true);
      }
    }

    void loadInitialContent();
  }, [editor, initialContent, updateContent, isInitialized]);

  // Update content on first mount if no initial content
  useEffect(() => {
    if (!initialContent && !isInitialized) {
      void updateContent();
      setIsInitialized(true);
    }
  }, [initialContent, updateContent, isInitialized]);

  // Content manipulation methods
  const setMarkdownContent = useCallback(
    async (markdown: string) => {
      try {
        const blocks = await editor.tryParseMarkdownToBlocks(markdown);
        editor.replaceBlocks(editor.document, blocks);
      } catch (error) {
        console.error("Error setting markdown content:", error);
      }
    },
    [editor],
  );

  const setHTMLContent = useCallback(
    async (html: string) => {
      try {
        const blocks = await editor.tryParseHTMLToBlocks(html);
        editor.replaceBlocks(editor.document, blocks);
      } catch (error) {
        console.error("Error setting HTML content:", error);
      }
    },
    [editor],
  );

  const setBlocksContent = useCallback(
    (blocks: Block[]) => {
      try {
        editor.replaceBlocks(editor.document, blocks);
      } catch (error) {
        console.error("Error setting blocks content:", error);
      }
    },
    [editor],
  );

  // Export methods for full HTML (with structure)
  const getFullHTML = useCallback(
    async (inputBlocks?: Block[]): Promise<string> => {
      return await editor.blocksToFullHTML(inputBlocks ?? editor.document);
    },
    [editor],
  );

  return useMemo(() => {
    return {
      // Core editor instance
      editor,

      // Content in different formats
      content,
      blocks: content.blocks,
      markdown: content.markdown,
      html: content.html,

      // Content setters
      setMarkdownContent,
      setHTMLContent,
      setBlocksContent,

      // Export methods
      getFullHTML,

      // Event handler for editor changes
      onChange: handleEditorChange,

      // Initialization state
      isInitialized,
    };
  }, [
    editor,
    content,
    setMarkdownContent,
    setHTMLContent,
    setBlocksContent,
    getFullHTML,
    handleEditorChange,
    isInitialized,
  ]);
}
