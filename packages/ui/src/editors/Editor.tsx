"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DragHandleButton,
  SideMenu,
  SideMenuController,
} from "@blocknote/react";
import { BlockNoteView } from "@blocknote/shadcn";

import { cn } from "@/ui/lib";
import * as Badge from "@/ui/primitives/badge";
import * as Button from "@/ui/primitives/button";
import * as Select from "@/ui/primitives/select";
import { useTheme } from "@/ui/primitives/theme";

import type { EditorContent, UseEditorOptions } from "./use-editor";

import { useEditor } from "./use-editor";

import "@blocknote/core/fonts/inter.css";
import "@blocknote/shadcn/style.css";
import "../styles/editor.css";

export type { EditorContent };

export interface EditorProps extends UseEditorOptions {
  className?: string;
  onChange?: (content: EditorContent) => void;
}

export default function Editor(props: EditorProps) {
  const { forcedTheme, theme = forcedTheme } = useThem<PERSON>();
  const { editor, onChange: handleEditorChange } = useEditor(props);
  return (
    <div className={cn("size-full min-w-60", props.className)}>
      <BlockNoteView
        theme={theme === "dark" ? "dark" : "light"}
        data-color-scheme={theme === "dark" ? "dark" : "light"}
        className={cn("size-full min-w-60", {
          dark: theme === "dark",
        })}
        editor={editor}
        onChange={handleEditorChange}
        shadCNComponents={{
          Button,
          Badge,
          Select,
        }}
      >
        <SideMenuController
          sideMenu={(props) => (
            <SideMenu {...props}>
              <AddBlockButton {...props} />
              <DragHandleButton {...props} />
            </SideMenu>
          )}
        />
      </BlockNoteView>
    </div>
  );
}
