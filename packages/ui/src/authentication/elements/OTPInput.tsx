import * as Clerk from "@clerk/elements/common";

import { cn } from "@axa/ui/lib";

export default function OTPInput() {
  return (
    <Clerk.Input
      type="otp"
      className="flex justify-center has-[:disabled]:opacity-50"
      autoSubmit
      render={({ value, status }) => {
        return (
          <div
            data-status={status}
            className={cn(
              "relative flex size-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",
              {
                "z-10 ring-2 ring-ring ring-offset-background":
                  status === "cursor" || status === "selected",
              },
            )}
          >
            {value}
            {status === "cursor" && (
              <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
                <div className="animate-caret-blink h-4 w-px bg-foreground duration-1000" />
              </div>
            )}
          </div>
        );
      }}
    />
  );
}
