"use client";

import type { DateRange } from "react-day-picker";

import { useCallback, useMemo } from "react";
import { endOfDay, format, startOfDay } from "date-fns";

import { useSearchParamsContext } from "./SearchParams";

export const SEARCH_DATE_GROUP = "range";
export const SEARCH_DATE_NAME = "date";
export const SEARCH_START_DATE_NAME = "startDate";
export const SEARCH_END_DATE_NAME = "endDate";

export function useSearchDateRangeValue(
  group = SEARCH_DATE_GROUP,
  name = SEARCH_DATE_NAME,
) {
  const { searchParams } = useSearchParamsContext();
  return useMemo(() => {
    const key = [group, name].filter(Boolean).join("-");
    const startDateString = searchParams[
      [key, SEARCH_START_DATE_NAME].join("-")
    ] as string | undefined;
    const endDateString = searchParams[
      [key, SEARCH_END_DATE_NAME].join("-")
    ] as string | undefined;

    return {
      [SEARCH_START_DATE_NAME]: startDateString
        ? startOfDay(startDateString)
        : undefined,
      [SEARCH_END_DATE_NAME]: endDateString
        ? endOfDay(endDateString)
        : undefined,
    };
  }, [searchParams, group, name]);
}

export interface UseSearchDateRangeOptions {
  name?: string;
  group?: string;
}

export function useSearchDateRange({
  name = SEARCH_DATE_NAME,
  group = SEARCH_DATE_GROUP,
}: UseSearchDateRangeOptions = {}) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const onSelect = useCallback(
    (dates?: DateRange) => {
      setSearchParams([
        [
          [key, SEARCH_START_DATE_NAME].join("-"),
          dates?.from?.toISOString() ?? "",
        ],
        [[key, SEARCH_END_DATE_NAME].join("-"), dates?.to?.toISOString() ?? ""],
      ]);
    },
    [setSearchParams, key],
  );

  const onClear = useCallback(() => {
    setSearchParams([
      [[key, SEARCH_START_DATE_NAME].join("-"), ""],
      [[key, SEARCH_END_DATE_NAME].join("-"), ""],
    ]);
  }, [setSearchParams, key]);

  const { values, hasValue, text } = useMemo(() => {
    const startDate = searchParams[[key, SEARCH_START_DATE_NAME].join("-")] as
      | string
      | undefined;
    const endDate = searchParams[[key, SEARCH_END_DATE_NAME].join("-")] as
      | string
      | undefined;

    return {
      text: [startDate, endDate]
        .filter((date): date is string => Boolean(date))
        .map((date: string) => {
          return format(new Date(date), "MM/dd/yyyy");
        })
        .join(" - "),
      hasValue: !!(startDate ?? endDate),
      values: {
        from: startDate ? new Date(startDate) : undefined,
        to: endDate ? new Date(endDate) : undefined,
      },
    };
  }, [searchParams, key]);

  return {
    onSelect,
    onClear,
    values,
    hasValue,
    text,
  };
}
