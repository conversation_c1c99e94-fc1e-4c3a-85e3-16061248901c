import { useCallback, useState } from "react";

import type { GenericNode } from "@/ui/selectors/Selector";

import { useDebounceValue } from "@/ui/hooks/useDebounceValue";
import { useSearchParamsContext } from "@/ui/search/SearchParams";

export const SEARCH_VALUE_NAME = "value";
export const SEARCH_VALUE_GROUP = "value";

export function useSearchValueResult<T extends string = string>(
  group = SEARCH_VALUE_GROUP,
  name = SEARCH_VALUE_NAME,
) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams } = useSearchParamsContext();
  return searchParams[key] as T | undefined;
}

export interface SearchValueOption<T extends string = string> {
  value: T;
  label: string;
  id: string;
}

export interface UseSearchValueOptions<
  T extends string = string,
  V extends GenericNode = SearchValueOption<T>,
> {
  name: string;
  group?: string;
  defaultValue?: T;
  onChange?: (value?: string) => void;
  onSelect?: (value?: V) => void;
  debounce?: number;
}

export function useSearchValue<
  T extends string = string,
  V extends SearchValueOption<T> = SearchValueOption<T>,
>({
  name = SEARCH_VALUE_NAME,
  group,
  defaultValue,
  onChange,
  onSelect,
  debounce = 500,
}: UseSearchValueOptions<T, V>) {
  const key = [group, name].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const [value, setValue] = useDebounceValue(
    searchParams[key] ?? defaultValue,
    debounce,
  );
  const [selection, setSelection] = useState<V | null>(null);

  const onSelectionChange = useCallback(
    (value?: V) => {
      setSearchParams([[key, value?.value ?? ""]]);
      setSelection(value ?? null);
      onSelect?.(value);
    },
    [onSelect, setSearchParams, key],
  );

  const onValueChange = useCallback(
    (value?: string) => {
      setValue(value);
      onChange?.(value);
    },
    [onChange, setValue],
  );

  const onClear = useCallback(() => {
    onSelectionChange();
    onValueChange();
  }, [onSelectionChange, onValueChange]);

  return {
    value,
    selection,
    onSelectionChange,
    onValueChange,
    onClear,
  };
}
