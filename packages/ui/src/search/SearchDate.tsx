"use client";

import { CalendarIcon, XIcon } from "lucide-react";

import { cn } from "../lib";
import { Button, buttonVariants } from "../primitives/button";
import { Calendar } from "../primitives/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../primitives/popover";
import { Separator } from "../primitives/separator";
import {
  SEARCH_DATE_GROUP,
  SEARCH_DATE_NAME,
  useSearchDateRange,
} from "./date";

const i18n = {
  en: {
    placeholder: "Search date range...",
    clear: "Clear date range",
  },
};

export interface SearchDateRangeProps {
  loading?: boolean;
  name?: string;
  group?: string;
  placeholder?: string;
  className?: string;
}

export function SearchDateRange({
  loading,
  name = SEARCH_DATE_NAME,
  group = SEARCH_DATE_GROUP,
  placeholder = i18n.en.placeholder,
  className,
}: SearchDateRangeProps) {
  const { onSelect, onClear, values, hasValue, text } = useSearchDateRange({
    name,
    group,
  });

  return (
    <Popover>
      <div
        className={cn(
          buttonVariants({
            variant: "outline",
            size: "sm",
            className: "flex items-center justify-between",
          }),
          "p-0 px-0",
          className,
        )}
      >
        <PopoverTrigger asChild className="flex-1">
          <Button
            disabled={loading}
            type="button"
            variant="ghost"
            size="sm"
            className={cn("h-full space-x-2 focus:z-10", {
              "rounded-e-none": hasValue,
            })}
          >
            <CalendarIcon className="mr-auto size-4 opacity-50" />
            <span
              className={
                hasValue ? "truncate" : "truncate text-muted-foreground"
              }
            >
              {text || placeholder}
            </span>
          </Button>
        </PopoverTrigger>
        {hasValue && (
          <>
            <Separator orientation="vertical" />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="rounded-s-none"
              onClick={onClear}
            >
              <XIcon className="size-4" />
              <span className="sr-only">{i18n.en.clear}</span>
            </Button>
          </>
        )}
      </div>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="range"
          selected={values}
          onSelect={onSelect}
          numberOfMonths={2}
        />
      </PopoverContent>
    </Popover>
  );
}
