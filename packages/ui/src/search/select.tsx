"use client";

import { useCallback, useEffect } from "react";

import { useDebounceValue } from "../hooks/useDebounceValue";
import { useSearchParamsContext } from "./SearchParams";

export const SEARCH_SELECT_NAME = "select";

export function useSearchSelectValue<T extends string | number | null = string>(
  group?: string,
  name = SEARCH_SELECT_NAME,
) {
  const { searchParams } = useSearchParamsContext();
  const key = [name, group].filter(Boolean).join("-");
  return searchParams[key] as T | undefined;
}

export function useSearchSelect<T extends string | number | null = string>(
  group?: string,
  name = SEARCH_SELECT_NAME,
  defaultValue?: T,
  onChange?: (value: T) => void | Promise<void>,
) {
  const key = [name, group].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();
  const [value, setValue] = useDebounceValue<T>(
    (searchParams[key] as T | undefined) ?? (defaultValue as T),
    500,
  );

  const handleValueChange = useCallback(
    async (value: T) => {
      setValue(value);
      await onChange?.(value);
    },
    [setValue, onChange],
  );

  useEffect(() => {
    if (value !== searchParams[key]) {
      setSearchParams(key, value?.toString() ?? "");
    }
  }, [key, value, searchParams, setSearchParams]);

  return [value, handleValueChange] as const;
}
