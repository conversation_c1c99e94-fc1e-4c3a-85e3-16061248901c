import type { OnChangeFn, PaginationState } from "@tanstack/react-table";

import { useCallback, useMemo } from "react";

import { useSearchParamsContext } from "./SearchParams";

export const SEARCH_PAGINATION_NAME = "pagination";

function getSearchPaginationValue({
  group,
  name = SEARCH_PAGINATION_NAME,
  defaultPageIndex = 0,
  defaultPageSize = 10,
  searchParams,
}: {
  group?: string;
  name?: string;
  defaultPageIndex?: number;
  defaultPageSize?: number;
  searchParams: Record<string, string | string[] | undefined>;
}) {
  const key = [name, group].filter(Boolean).join("-");
  const pageIndex = searchParams[[key, "index"].join("-")] as
    | string
    | undefined;
  const pageSize = searchParams[[key, "size"].join("-")] as string | undefined;

  // Helper function to parse with fallback
  const parseWithFallback = (value: string | undefined, fallback: number) => {
    if (!value || value.trim() === "") return fallback;
    const parsed = parseInt(value, 10);
    return Number.isNaN(parsed) ? fallback : parsed;
  };

  return {
    pageIndex: parseWithFallback(pageIndex, defaultPageIndex),
    pageSize: parseWithFallback(pageSize, defaultPageSize),
  };
}

export function useSearchPaginationValue(
  group?: string,
  name = SEARCH_PAGINATION_NAME,
) {
  const { searchParams } = useSearchParamsContext();
  return getSearchPaginationValue({
    group,
    name,
    defaultPageIndex: 0,
    defaultPageSize: 10,
    searchParams,
  });
}

export function useSearchPagination({
  group,
  name = SEARCH_PAGINATION_NAME,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  onChange,
}: {
  group?: string;
  name?: string;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  onChange?: OnChangeFn<PaginationState>;
} = {}) {
  const key = [name, group].filter(Boolean).join("-");
  const { searchParams, setSearchParams } = useSearchParamsContext();

  const { pageIndex, pageSize } = useMemo(() => {
    return getSearchPaginationValue({
      group,
      name,
      defaultPageIndex,
      defaultPageSize,
      searchParams,
    });
  }, [searchParams, name, group, defaultPageIndex, defaultPageSize]);

  const setPagination = useCallback<OnChangeFn<PaginationState>>(
    (value: PaginationState | ((old: PaginationState) => PaginationState)) => {
      const newValue =
        typeof value === "function"
          ? value({
              pageIndex,
              pageSize,
            })
          : value;
      setSearchParams([
        [[key, "index"].join("-"), newValue.pageIndex.toString()],
        [[key, "size"].join("-"), newValue.pageSize.toString()],
      ]);
      onChange?.(newValue);
    },
    [setSearchParams, onChange, key, pageIndex, pageSize],
  );

  return useMemo(
    () => ({ pagination: { pageIndex, pageSize }, setPagination }),
    [pageIndex, pageSize, setPagination],
  );
}
