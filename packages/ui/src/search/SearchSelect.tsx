"use client";

import type { ComponentPropsWithoutRef } from "react";

import { Loader2Icon } from "lucide-react";

import { cn } from "../lib";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../primitives/select";
import { SEARCH_SELECT_NAME, useSearchSelect } from "./select";

const i18n = {
  en: {
    actions: {
      select: "Select",
    },
  },
};

type SelectContentProps = ComponentPropsWithoutRef<typeof SelectContent>;
export interface SearchSelectProps<T extends { value: string; label: string }> {
  loading?: boolean;
  name?: string;
  group?: string;
  placeholder?: string;
  className?: string;
  options: T[];
  defaultValue?: string;
  onChange?: (value: string) => void;
  position?: SelectContentProps["position"];
  align?: SelectContentProps["align"];
  side?: SelectContentProps["side"];
  sideOffset?: SelectContentProps["sideOffset"];
}

export function SearchSelect<T extends { value: string; label: string }>({
  loading,
  name = SEARCH_SELECT_NAME,
  group,
  placeholder = i18n.en.actions.select,
  className,
  options,
  defaultValue,
  onChange,
  position = "item-aligned",
  align = "start",
  side = "bottom",
  sideOffset = 0,
}: SearchSelectProps<T>) {
  const [value, handleValueChange] = useSearchSelect(
    group,
    name,
    defaultValue,
    onChange,
  );

  return (
    <Select name={name} defaultValue={value} onValueChange={handleValueChange}>
      <SelectTrigger className={cn("h-full min-h-8 w-[150px]", className)}>
        {loading ? (
          <Loader2Icon className="size-4 animate-spin" />
        ) : (
          <SelectValue placeholder={placeholder} />
        )}
      </SelectTrigger>
      <SelectContent
        position={position}
        align={align}
        side={side}
        sideOffset={sideOffset}
      >
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
