import { useCallback } from "react";
import { XIcon } from "lucide-react";

import type { GenericNode, SelectorProps } from "@/ui/selectors/Selector";

import type { SearchValueOption } from "./value";

import { cn } from "@/ui/lib";
import { Button, buttonVariants } from "@/ui/primitives/button";
import { Selector } from "@/ui/selectors/Selector";

import { SEARCH_VALUE_NAME, useSearchValue } from "./value";

export interface SearchValueProps<
  T extends string = string,
  V extends GenericNode = SearchValueOption<T>,
> extends SelectorProps<V> {
  loading?: boolean;
  name: string;
  group?: string;
  placeholder?: string;
  defaultValue?: T;
  onChange?: (value?: string) => void;
  onSelect?: (value?: V) => void;
  debounce?: number;
  className?: string;
}

export function SearchValue<
  T extends string = string,
  V extends SearchValueOption<T> = SearchValueOption<T>,
>({
  loading,
  size = "sm",
  variant = "outline",
  align = "center",
  useDialog = false,
  disabled = false,
  name = SEARCH_VALUE_NAME,
  group,
  placeholder,
  defaultValue,
  className,
  onChange,
  onSelect,
  debounce = 500,
  ...props
}: SearchValueProps<T, V>) {
  const { value, selection, onSelectionChange, onValueChange, onClear } =
    useSearchValue({
      name,
      group,
      defaultValue,
      onChange,
      onSelect,
      debounce,
    });

  return (
    <div
      className={cn(
        buttonVariants({
          variant,
          size,
          className: "flex items-center justify-between",
        }),
        "w-full p-0 px-0",
        className,
      )}
    >
      <Selector<V>
        {...props}
        loading={loading}
        disabled={disabled}
        size={size}
        variant="ghost"
        align={align}
        className={cn("h-8 p-2", className)}
        useDialog={useDialog}
        label={placeholder}
        // data={options}
        value={selection?.value}
        onValueChange={onValueChange}
        onSelect={onSelectionChange}
        renderId={useCallback((value: V) => value.value, [])}
        renderValue={useCallback((value: V) => value.value, [])}
        renderItem={useCallback((value: V) => value.label, [])}
      />
      {value && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="rounded-s-none border-l"
          onClick={onClear}
        >
          <XIcon className="size-4" />
          <span className="sr-only">Clear value</span>
        </Button>
      )}
    </div>
  );
}
