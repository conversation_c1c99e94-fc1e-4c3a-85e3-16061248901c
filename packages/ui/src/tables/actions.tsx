"use client";

import { DownloadIcon } from "lucide-react";

import type { ActionContext, TableAction } from "./table";

import { exportCSV } from "@/ui/lib/files";

type ExportCSVAction<TData> = TableAction<TData> & {
  onClick: (context: ActionContext<TData>) => void;
};

/**
 * Converts an array of objects to CSV data format for export
 */
export function convertObjectsToCSVData<TData extends Record<string, unknown>>(
  data: TData[],
  columns?: string[],
): {
  headers: string[];
  data: (string | number)[][];
} {
  if (data.length === 0) return { headers: [], data: [] };

  // Use provided columns or extract from first object
  const headers = columns || Object.keys(data[0] ?? {});

  // Convert object data to array format expected by exportCSV
  const csvData = data.map((row) =>
    headers.map((header) => {
      const value = row[header];
      if (value == null) return "";
      return String(value);
    }),
  );

  return { headers, data: csvData };
}

/**
 * Creates a properly typed CSV export action with custom headers and resolvers
 *
 * @param headers - Array of keys to export. If not provided, all primitive keys will be used
 * @param options - Configuration options including resolvers for complex fields
 */
export function createTypedExportCSVAction<TData extends Record<string, any>>(
  headers?: (keyof TData)[],
  options?: {
    resolvers?: Partial<
      Record<
        keyof TData,
        (value: TData[keyof TData], row: TData) => string | number
      >
    >;
    filename?: string;
    label?: string;
  },
): TableAction<TData> {
  const {
    resolvers,
    filename = "export.csv",
    label = "Export Selected as CSV",
  } = options || {};

  return {
    type: "selection",
    label,
    icon: DownloadIcon,
    variant: "default",
    disabled: (context: ActionContext<TData>) => {
      if (context.type !== "selection") return true;
      return context.selectedRows.length === 0;
    },
    onClick: (context: ActionContext<TData>) => {
      if (context.type !== "selection") return;

      const { selectedRows } = context;
      if (selectedRows.length === 0) return;

      // Determine headers to use
      let finalHeaders: string[];
      if (headers && headers.length > 0) {
        finalHeaders = headers.map(String);
      } else if (selectedRows.length > 0) {
        // Fall back to all primitive-valued keys
        const firstRow = selectedRows[0];
        if (firstRow) {
          finalHeaders = Object.keys(firstRow).filter((key) => {
            const value = firstRow[key];
            return (
              value == null ||
              typeof value === "string" ||
              typeof value === "number" ||
              typeof value === "boolean"
            );
          });
        } else {
          finalHeaders = [];
        }
      } else {
        finalHeaders = [];
      }

      // Convert data using resolvers where provided
      const csvData = selectedRows.map((row) =>
        finalHeaders.map((header) => {
          const key = header as keyof TData;
          const value = row[key];

          // Use resolver if provided
          const resolver = resolvers?.[key];
          if (resolver && typeof resolver === "function") {
            return String(resolver(value, row));
          }

          // Default serialization
          if (value == null) return "";
          return String(value);
        }),
      );

      // Generate timestamp for unique filename
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:.]/g, "-");
      const finalFilename =
        filename.replace(/\.csv$/, "") + `_${timestamp}.csv`;

      // Export using the existing utility
      exportCSV({
        fileName: finalFilename,
        headers: finalHeaders,
        data: csvData,
      });
    },
  };
}

/**
 * Creates an export CSV action for selected table rows
 */
export function createExportCSVAction<
  TData extends Record<string, any>,
>(options: {
  filename?: string;
  columns?: string[];
  label?: string;
}): ExportCSVAction<TData> {
  const {
    filename = "export.csv",
    columns,
    label = "Export Selected as CSV",
  } = options;

  return {
    type: "selection",
    label,
    icon: DownloadIcon,
    variant: "default",
    disabled: (context: ActionContext<TData>) => {
      if (context.type !== "selection") return true;
      return context.selectedRows.length === 0;
    },
    onClick: (context: ActionContext<TData>) => {
      if (context.type !== "selection") return;

      const { selectedRows } = context;
      if (selectedRows.length === 0) return;

      // Generate timestamp for unique filename
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:.]/g, "-");
      const finalFilename =
        filename.replace(/\.csv$/, "") + `_${timestamp}.csv`;

      // Convert to CSV data format and export using utility
      const { headers, data } = convertObjectsToCSVData(selectedRows, columns);
      exportCSV({
        fileName: finalFilename,
        headers,
        data,
      });
    },
  };
}

/**
 * Stock action: Export selected rows as CSV with default settings
 */
export const exportSelectedAsCSV = createExportCSVAction({
  label: "Export Selected as CSV",
  filename: "selected_items.csv",
});

/**
 * Creates a bulk delete action for selected table rows
 */
export function createBulkDeleteAction<TData>(options: {
  onDelete: (selectedRows: TData[]) => void | Promise<void>;
  label?: string;
  confirmMessage?: string;
}): TableAction<TData> {
  const {
    onDelete,
    label = "Delete Selected",
    confirmMessage = "Are you sure you want to delete the selected items? This action cannot be undone.",
  } = options;

  return {
    type: "selection",
    label,
    variant: "destructive",
    disabled: (context: ActionContext<TData>) => {
      if (context.type !== "selection") return true;
      return context.selectedRows.length === 0;
    },
    onClick: async (context: ActionContext<TData>) => {
      if (context.type !== "selection") return;

      const { selectedRows } = context;
      if (selectedRows.length === 0) return;

      // Show confirmation dialog
      const confirmed = window.confirm(
        `${confirmMessage}\n\nSelected items: ${selectedRows.length}`,
      );

      if (confirmed) {
        await onDelete(selectedRows);
      }
    },
  };
}

/**
 * Creates a bulk status update action for selected table rows
 */
export function createBulkStatusAction<TData>(options: {
  onStatusChange: (
    selectedRows: TData[],
    status: string,
  ) => void | Promise<void>;
  status: string;
  label?: string;
}): TableAction<TData> {
  const {
    onStatusChange,
    status,
    label = `Mark Selected as ${status}`,
  } = options;

  return {
    type: "selection",
    label,
    variant: "default",
    disabled: (context: ActionContext<TData>) => {
      if (context.type !== "selection") return true;
      return context.selectedRows.length === 0;
    },
    onClick: async (context: ActionContext<TData>) => {
      if (context.type !== "selection") return;

      const { selectedRows } = context;
      if (selectedRows.length === 0) return;

      await onStatusChange(selectedRows, status);
    },
  };
}
