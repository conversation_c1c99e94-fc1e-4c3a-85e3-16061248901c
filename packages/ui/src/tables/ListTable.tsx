"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";

import { useMemo } from "react";
import { CircleIcon, MenuIcon, Settings2Icon } from "lucide-react";

import { DataTable, useDataTable } from ".";
import { Button } from "../primitives/button";
import { Card, CardContent, CardFooter, CardHeader } from "../primitives/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "../primitives/dropdown-menu";
import { useSearchPagination } from "../search/pagination";
import { SearchFilter } from "../search/SearchFilter";
import { SearchText } from "../search/SearchText";
import EmptyList from "../shared/EmptyList";
import { dataTableColumns } from "./columns";
import { DataTableSettings, DataTableSimplePagination } from "./helpers";

/**
 * Generic table actions component
 */
export function TableActions<TData, TValue>({
  table,
  i18n,
}: {
  table: ReturnType<typeof useDataTable<TData, TValue>>["table"];
  i18n: {
    selection: string;
    actions: {
      tableActions: string;
    };
  };
}) {
  const selection = table.getSelectedRowModel();
  const selectionCount = selection.rows.length;
  const hasSelection = selectionCount > 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={!hasSelection}>
        <Button
          variant="ghost"
          size="icon"
          className="relative"
          aria-label={i18n.actions.tableActions}
        >
          <MenuIcon size="20" color="currentColor" />
          <CircleIcon
            size="10"
            data-visible={hasSelection}
            className="absolute right-1.5 top-1.5 fill-red-400 text-red-400 opacity-0 transition-opacity data-[visible='true']:opacity-100"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>
          {i18n.selection} ({selectionCount})
        </DropdownMenuLabel>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface FilterOption {
  value: string | null;
  label: string;
}

interface FilterGroup {
  id: string;
  label: string;
  options: FilterOption[];
}

interface PaginatedResponse<TItem> {
  items: TItem[];
  total: number;
}

interface ListTableProps<TItem, TData extends TItem[]> {
  loading?: boolean;
  data?: PaginatedResponse<TItem>;
  defaultPageSize?: number;
  defaultPageIndex?: number;
  filters?: React.ReactNode | React.ReactNode[];
  columns: (tableHelpers: {
    i18n: {
      emptyText: string | null;
      selection: string;
      actions: {
        tableSettings: string;
        tableActions: string;
        search: string;
      };
      headers?: Record<string, string>;
      filters?: Record<string, Record<string, string>>;
    };
    TableActions: typeof TableActions;
  }) => ColumnDef<TItem, TData>[];
  i18n: {
    emptyText: string | null;
    selection: string;
    actions: {
      tableSettings: string;
      tableActions: string;
      search: string;
    };
    headers?: Record<string, string>;
  };
  groupName?: string;
  filterGroups?: FilterGroup[];
}

/**
 * Generic ListTable component that can be used for any type of resource
 */
export default function ListTable<TItem, TData extends TItem[]>({
  loading = false,
  data,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  filters,
  children,
  columns,
  i18n,
  groupName,
  filterGroups,
}: PropsWithChildren<ListTableProps<TItem, TData>>) {
  const { pagination, setPagination } = useSearchPagination({
    group: groupName,
    defaultPageSize,
    defaultPageIndex,
  });

  const { table } = useDataTable<TItem, TData>({
    data: (data?.items ?? []) as unknown as TData,
    rowCount: data?.total,
    manualPagination: true,
    pagination,
    setPagination,
    columns: useMemo(
      () => dataTableColumns<TItem, TData>(columns({ i18n, TableActions })),
      [columns, i18n],
    ),
  });

  return (
    <Card>
      <CardHeader className="border-b p-2">
        <div className="flex items-center justify-between">
          <div className="border-r pr-2">
            <SearchFilter name={groupName} groups={filterGroups ?? []} />
          </div>

          <div className="flex h-fit flex-1 gap-2 overflow-scroll p-1 px-2">
            <SearchText
              group={groupName}
              loading={loading}
              placeholder={i18n.actions.search}
            />
            {filters}
          </div>

          <div className="border-l pl-2">
            <DataTableSettings
              table={table}
              variant="ghost"
              size="icon"
              className="ml-auto"
              aria-label={i18n.actions.tableSettings}
            >
              <Settings2Icon size="20" color="currentColor" />
            </DataTableSettings>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <DataTable loading={loading} table={table}>
          <EmptyList title={i18n.emptyText ?? null}>{children}</EmptyList>
        </DataTable>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 border-t pt-6">
        <DataTableSimplePagination table={table} />
      </CardFooter>
    </Card>
  );
}
