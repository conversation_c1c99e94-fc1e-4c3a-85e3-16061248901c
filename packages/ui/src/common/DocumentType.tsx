import type { LucideProps } from "lucide-react";

import {
  FileCodeIcon,
  FileIcon,
  FileImageIcon,
  FileMusicIcon,
  FileSpreadsheet,
  FileTextIcon,
  FileTypeIcon,
  FileVideo2Icon,
} from "lucide-react";

export interface DocumentTypeProps {
  type?: string;
  size?: LucideProps["size"];
  className?: string;
}

export default function DocumentType({
  type,
  size = 24,
  className,
}: DocumentTypeProps) {
  switch (type) {
    case "image":
    case "image/jpeg":
    case "image/png":
    case "image/gif":
      return <FileImageIcon size={size} className={className} />;
    case "audio":
      return <FileMusicIcon size={size} className={className} />;
    case "video":
      return <FileVideo2Icon size={size} className={className} />;
    case "csv":
    case "text/csv":
    case "excel":
    case "microsoft/excel":
      return <FileSpreadsheet size={size} className={className} />;
    case "code":
      return <FileCodeIcon size={size} className={className} />;
    case "powerpoint":
    case "pdf":
    case "application/pdf":
      return <FileTextIcon size={size} className={className} />;
    case "word":
    case "text":
      return <FileTypeIcon size={size} className={className} />;
    case "archive":
    case "other":
    case "unknown":
    default:
      return <FileIcon size={size} className={className} />;
  }
}
