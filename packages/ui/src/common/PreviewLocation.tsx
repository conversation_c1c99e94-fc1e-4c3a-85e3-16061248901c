import type { VariantProps } from "class-variance-authority";
import type { PropsWithChildren } from "react";

import Link from "next/link";
import { cva } from "class-variance-authority";
import { MapPinIcon } from "lucide-react";

import LocationAddress from "@/ui/common/LocationAddress";
import { cn } from "@/ui/lib";
import { Skeleton } from "@/ui/primitives/skeleton";

const i18n = {
  en: {
    generics: {
      location: "Location",
    },
  },
  links: {
    location: "/app/locations/[locationId]",
  },
};

const previewLocationVariants = cva(
  "flex w-full items-center justify-start gap-2 rounded-lg",
  {
    variants: {
      size: {
        sm: "h-12 rounded-md text-xs",
        md: "h-16 rounded-md",
        lg: "h-20",
        xl: "h-24 text-lg",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const previewLocationDescriptionVariants = cva(
  "overflow-hidden text-ellipsis text-muted-foreground",
  {
    variants: {
      size: {
        sm: "text-xs font-light",
        md: "text-sm font-light",
        lg: "text-base font-normal",
        xl: "text-lg font-normal",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const locationIconVariants = cva(
  "flex shrink-0 items-center justify-center rounded-sm bg-muted shadow-md transition-all duration-200",
  {
    variants: {
      size: {
        sm: "size-6 [&_svg]:size-4",
        md: "size-8 [&_svg]:size-5",
        lg: "size-10 [&_svg]:size-6",
        xl: "size-16 [&_svg]:size-10",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

export interface LocationPartial {
  location?: {
    id: string;
    name: string;
    address: {
      id?: string;
      formatted: string;
    };
    description?: string | null;
  } | null;
}

export interface PreviewLocationProps
  extends VariantProps<typeof previewLocationVariants>,
    LocationPartial,
    PropsWithChildren {
  loading?: boolean;
  link?: boolean;
  description?: string;
  showCopyButton?: boolean;
  className?: string;
}

export function LocationIcon({
  loading = false,
  size = "md",
  className,
}: {
  loading?: boolean;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}) {
  return loading ? (
    <Skeleton
      className={cn(
        "flex shrink-0 items-center justify-center rounded-sm",
        {
          "size-6": size === "sm",
          "size-8": size === "md",
          "size-10": size === "lg",
          "size-16": size === "xl",
        },
        className,
      )}
    />
  ) : (
    <div className={cn(locationIconVariants({ size }), className)}>
      <MapPinIcon />
    </div>
  );
}

export default function PreviewLocation({
  loading = false,
  link = false,
  size = "md",
  className,
  location,
  description,
  showCopyButton = true,
  children,
}: PreviewLocationProps) {
  return (
    <div
      className={cn(previewLocationVariants({ size: size ?? "md", className }))}
    >
      <LocationIcon loading={loading} size={size ?? "md"} />

      {loading ? (
        <div className="flex w-full min-w-32 grow flex-col gap-0.5">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-3 w-[70%]" />
        </div>
      ) : (
        <div className="flex h-fit grow flex-col items-start justify-start text-nowrap">
          {link ? (
            <Link
              className="overflow-hidden text-ellipsis font-semibold hover:underline"
              href={i18n.links.location.replace(
                "[locationId]",
                location?.id ?? "",
              )}
            >
              {location?.name}
            </Link>
          ) : (
            <p className="overflow-hidden text-ellipsis font-semibold">
              {location?.name}
            </p>
          )}
          {(location?.description ?? description) ? (
            <p
              className={cn(
                previewLocationDescriptionVariants({ size }),
                "mt-0",
              )}
            >
              {location?.description ?? description}
            </p>
          ) : (
            <LocationAddress
              loading={loading}
              link={link}
              showCopyButton={showCopyButton}
              address={location?.address.formatted}
              size={size}
              className={cn(
                previewLocationDescriptionVariants({ size }),
                "mt-0",
              )}
            />
          )}
        </div>
      )}

      {children ? <div>{children}</div> : null}
    </div>
  );
}
