import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/primitives/select";

const i18n = {
  en: {
    weight: {
      label: "Weight",
      description: "Enter the weight value",
      placeholder: "Enter weight",
      validation: {
        type: "Please enter a valid weight",
        min: "Weight must be at least {min} {unit}",
        max: "Weight must be at most {max} {unit}",
      },
      units: {
        kg: "Kilograms (kg)",
        lb: "Pounds (lb)",
        g: "Grams (g)",
        oz: "Ounces (oz)",
        mt: "Metric Tons (MT)",
      },
    },
  },
};

export type WeightUnit = keyof typeof i18n.en.weight.units;
// eslint-disable-next-line react-refresh/only-export-components
export const WEIGHT_UNITS = ["kg", "lb", "g", "oz", "mt"] as const;

const UNIT_CONVERSIONS = {
  kg: { to: { lb: 2.20462, g: 1000, oz: 35.274, mt: 0.001 } },
  lb: { to: { kg: 0.453592, g: 453.592, oz: 16, mt: 0.000453592 } },
  g: { to: { kg: 0.001, lb: 0.00220462, oz: 0.035274, mt: 0.000001 } },
  oz: { to: { kg: 0.0283495, lb: 0.0625, g: 28.3495, mt: 0.0000283495 } },
  mt: { to: { kg: 1000, lb: 2204.62, g: 1000000, oz: 35274 } },
} as const;

const convertWeight = (value: number, from: WeightUnit, to: WeightUnit) => {
  if (from === to) return value;
  // Access the conversion directly with proper typing
  return (
    value *
    UNIT_CONVERSIONS[from].to[
      to as keyof (typeof UNIT_CONVERSIONS)[typeof from]["to"]
    ]
  );
};

export interface WeightFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  unitName?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  defaultUnit?: WeightUnit;
  allowedUnits?: WeightUnit[];
}

export function WeightField({
  name = "weight",
  unitName,
  label = i18n.en.weight.label,
  description = i18n.en.weight.description,
  placeholder = i18n.en.weight.placeholder,
  min = 0,
  max,
  defaultUnit = "kg",
  allowedUnits = WEIGHT_UNITS as unknown as WeightUnit[],
  ...props
}: WeightFieldProps) {
  const form = useFormContext();

  const unit = form.watch(unitName ?? "unit") || defaultUnit;

  return (
    <div className="space-y-2">
      <FormField
        control={form.control}
        name={name}
        rules={{
          validate: {
            isNumber: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return !isNaN(num) || i18n.en.weight.validation.type;
            },
            isPositive: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return num >= 0 || "Weight must be a positive number";
            },
            minValue: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              const minInUnit = convertWeight(min, "kg", unit);
              return (
                num >= minInUnit ||
                i18n.en.weight.validation.min
                  .replace("{min}", minInUnit.toString())
                  .replace("{unit}", unit)
              );
            },
            maxValue: (value) => {
              if (value === undefined || value === "" || max === undefined)
                return true;
              const num = Number(value);
              const maxInUnit = convertWeight(max, "kg", unit);
              return (
                num <= maxInUnit ||
                i18n.en.weight.validation.max
                  .replace("{max}", maxInUnit.toString())
                  .replace("{unit}", unit)
              );
            },
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex gap-2">
              <FormControl>
                <Input
                  type="number"
                  inputMode="decimal"
                  step="0.01"
                  min={0}
                  placeholder={placeholder}
                  className="w-[180px]"
                  {...field}
                  {...props}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      field.onChange("");
                      return;
                    }

                    const value = parseFloat(e.target.value);
                    if (!isNaN(value)) {
                      field.onChange(value.toString());
                    }
                  }}
                />
              </FormControl>
              {unitName && (
                <FormField
                  control={form.control}
                  name={unitName}
                  defaultValue={defaultUnit}
                  render={({ field: unitField }) => (
                    <Select
                      value={unitField.value}
                      onValueChange={(value: WeightUnit) => {
                        const oldUnit = unitField.value as WeightUnit;
                        const newUnit = value;
                        const currentValue = field.value;

                        // Convert the current value to the new unit
                        if (currentValue !== "") {
                          const converted = convertWeight(
                            parseFloat(currentValue),
                            oldUnit,
                            newUnit,
                          );
                          field.onChange(converted.toFixed(3));
                        }

                        unitField.onChange(value);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[130px]">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allowedUnits.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {i18n.en.weight.units[unit]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              )}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
