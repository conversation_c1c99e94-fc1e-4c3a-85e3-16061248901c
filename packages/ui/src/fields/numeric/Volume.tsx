import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/ui/primitives/select";

const i18n = {
  en: {
    volume: {
      label: "Volume",
      description: "Enter the volume value",
      placeholder: "Enter volume",
      validation: {
        type: "Please enter a valid volume",
        min: "Volume must be at least {min} {unit}",
        max: "Volume must be at most {max} {unit}",
      },
      units: {
        ml: "Milliliters (ml)",
        l: "Liters (l)",
        gal: "Gallons (gal)",
        "cu.ft": "Cubic Feet (cu.ft)",
        "cu.m": "Cubic Meters (cu.m)",
      },
    },
  },
};

export type VolumeUnit = keyof typeof i18n.en.volume.units;
// eslint-disable-next-line react-refresh/only-export-components
export const VOLUME_UNITS = ["ml", "l", "gal", "cu.ft", "cu.m"] as const;

const UNIT_CONVERSIONS = {
  ml: {
    to: { l: 0.001, gal: 0.000264172, "cu.ft": 0.0000353147, "cu.m": 0.000001 },
  },
  l: { to: { ml: 1000, gal: 0.264172, "cu.ft": 0.0353147, "cu.m": 0.001 } },
  gal: {
    to: { ml: 3785.41, l: 3.78541, "cu.ft": 0.133681, "cu.m": 0.00378541 },
  },
  "cu.ft": { to: { ml: 28316.8, l: 28.3168, gal: 7.48052, "cu.m": 0.0283168 } },
  "cu.m": { to: { ml: 1000000, l: 1000, gal: 264.172, "cu.ft": 35.3147 } },
};

const convertVolume = (value: number, from: VolumeUnit, to: VolumeUnit) => {
  if (from === to) return value;
  return (
    value *
    UNIT_CONVERSIONS[from].to[
      to as keyof (typeof UNIT_CONVERSIONS)[typeof from]["to"]
    ]
  );
};

export interface VolumeFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  unitName?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  defaultUnit?: VolumeUnit;
  allowedUnits?: VolumeUnit[];
}

export function VolumeField({
  name = "volume",
  unitName,
  label = i18n.en.volume.label,
  description = i18n.en.volume.description,
  placeholder = i18n.en.volume.placeholder,
  min = 0,
  max,
  defaultUnit = "l",
  allowedUnits = VOLUME_UNITS as unknown as VolumeUnit[],
  ...props
}: VolumeFieldProps) {
  const form = useFormContext();

  const unit = form.watch(unitName ?? "unit") || defaultUnit;

  return (
    <div className="space-y-2">
      <FormField
        control={form.control}
        name={name}
        rules={{
          validate: {
            isNumber: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return !isNaN(num) || i18n.en.volume.validation.type;
            },
            isPositive: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);
              return num >= 0 || "Volume must be a positive number";
            },
            minValue: (value) => {
              if (value === undefined || value === "") return true;
              const num = Number(value);

              const minInUnit = convertVolume(min, "l", unit);
              return (
                num >= minInUnit ||
                i18n.en.volume.validation.min
                  .replace("{min}", minInUnit.toFixed(2))
                  .replace("{unit}", unit)
              );
            },
            maxValue: (value) => {
              if (value === undefined || value === "" || max === undefined)
                return true;
              const num = Number(value);
              const maxInUnit = convertVolume(max, "l", unit);
              return (
                num <= maxInUnit ||
                i18n.en.volume.validation.max
                  .replace("{max}", maxInUnit.toFixed(2))
                  .replace("{unit}", unit)
              );
            },
          },
        }}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex gap-2">
              <FormControl>
                <Input
                  type="number"
                  inputMode="decimal"
                  step="0.01"
                  min={0}
                  placeholder={placeholder}
                  className="w-[180px]"
                  {...field}
                  {...props}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      field.onChange("");
                      return;
                    }

                    const value = parseFloat(e.target.value);
                    if (!isNaN(value)) {
                      field.onChange(value.toString());
                    }
                  }}
                />
              </FormControl>
              {unitName && (
                <FormField
                  control={form.control}
                  name={unitName}
                  defaultValue={defaultUnit}
                  render={({ field: unitField }) => (
                    <Select
                      value={unitField.value}
                      onValueChange={(value: VolumeUnit) => {
                        const oldUnit = unitField.value as VolumeUnit;
                        const newUnit = value;
                        const currentValue = field.value;

                        // Convert the current value to the new unit
                        if (currentValue !== "") {
                          const converted = convertVolume(
                            parseFloat(currentValue),
                            oldUnit,
                            newUnit,
                          );
                          field.onChange(converted.toFixed(3));
                        }

                        unitField.onChange(value);
                      }}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[130px]">
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allowedUnits.map((unit) => (
                          <SelectItem key={unit} value={unit}>
                            {i18n.en.volume.units[unit]}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              )}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
