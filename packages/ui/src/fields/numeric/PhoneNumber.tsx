import type { ChangeEvent, LegacyRef } from "react";

import { forwardRef, useCallback } from "react";
import { useMaskito } from "@maskito/react";
import { mergeRefs } from "@react-aria/utils";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import { mask, transformPhoneNumber } from "@/ui/lib/masks/phone";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    label: "Phone Number",
    description: "The phone number",
    placeholder: "Enter the phone number",
  },
};

export interface PhoneNumberInputProps extends InputProps {
  placeholder?: string;
}

export const PhoneNumberInput = forwardRef<
  HTMLInputElement,
  PhoneNumberInputProps
>(function PhoneNumberCombined({ value, onChange, ...props }, ref) {
  const inputRef = useMaskito({ options: mask });

  const onInputChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const { value = "" } = event.target;
      const number = parseFloat(value.replace(/[\D]/g, "")).toString();
      onChange?.({
        ...event,
        target: {
          ...event.target,
          value: number,
        },
      });
    },
    [onChange],
  );

  return (
    <Input
      {...props}
      ref={mergeRefs(inputRef, ref) as LegacyRef<HTMLInputElement>}
      value={value ? transformPhoneNumber(value.toString()) : value}
      onInput={onInputChange}
      onChange={onInputChange}
    />
  );
});

PhoneNumberInput.displayName = "PhoneNumberInput";

export default PhoneNumberInput;

export interface PhoneNumberFieldProps extends PhoneNumberInputProps {
  name?: string;
  label?: string;
  description?: string;
}

export const PhoneNumberField = forwardRef<
  HTMLInputElement,
  PhoneNumberFieldProps
>(function PhoneNumberField(
  {
    name = "phone",
    label = i18n.en.label,
    description = i18n.en.description,
    ...props
  },
  ref,
) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <PhoneNumberInput {...props} {...field} ref={ref} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
});

PhoneNumberField.displayName = "PhoneNumberField";
