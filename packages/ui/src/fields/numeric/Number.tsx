import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    number: {
      label: "Number",
      description: "Enter a numeric value",
      placeholder: "Enter a number",
      validation: {
        type: "Please enter a valid number",
        min: "Value must be at least {min}",
        max: "Value must be at most {max}",
      },
    },
  },
};

export interface NumberFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
  allowNegative?: boolean;
  allowDecimals?: boolean;
}

export function NumberField({
  name = "",
  label = i18n.en.number.label,
  description = i18n.en.number.description,
  placeholder = i18n.en.number.placeholder,
  min,
  max,
  step = 1,
  allowNegative = false,
  allowDecimals = false,
  ...props
}: NumberFieldProps) {
  const form = useFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        validate: {
          isNumber: (value) => {
            if (value === undefined || value === "") return true;
            const num = Number(value);
            return !isNaN(num) || i18n.en.number.validation.type;
          },
          isInteger: (value) => {
            if (value === undefined || value === "" || allowDecimals)
              return true;
            const num = Number(value);
            return Number.isInteger(num) || "Please enter a whole number";
          },
          isPositive: (value) => {
            if (value === undefined || value === "" || allowNegative)
              return true;
            const num = Number(value);
            return num >= 0 || "Please enter a positive number";
          },
          minValue: (value) => {
            if (value === undefined || value === "" || min === undefined)
              return true;
            const num = Number(value);
            return (
              num >= min ||
              i18n.en.number.validation.min.replace("{min}", min.toString())
            );
          },
          maxValue: (value) => {
            if (value === undefined || value === "" || max === undefined)
              return true;
            const num = Number(value);
            return (
              num <= max ||
              i18n.en.number.validation.max.replace("{max}", max.toString())
            );
          },
        },
      }}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <Input
              type="number"
              inputMode={allowDecimals ? "decimal" : "numeric"}
              step={step}
              min={min}
              max={max}
              placeholder={placeholder}
              {...field}
              {...props}
              onChange={(e) => {
                // Handle empty string case
                if (e.target.value === "") {
                  field.onChange("");
                  return;
                }

                // Parse the number and apply validation
                const value = allowDecimals
                  ? parseFloat(e.target.value)
                  : parseInt(e.target.value);

                if (!isNaN(value)) {
                  // Apply min/max constraints
                  let finalValue = value;
                  if (min !== undefined) finalValue = Math.max(min, finalValue);
                  if (max !== undefined) finalValue = Math.min(max, finalValue);

                  field.onChange(finalValue.toString());
                }
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
