import { forwardRef } from "react";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    label: "Email",
    description: "Enter your email address",
    placeholder: "<EMAIL>",
  },
};

export const EmailInput = forwardRef<HTMLInputElement, InputProps>(
  (props, ref) => {
    return <Input {...props} type="email" ref={ref} />;
  },
);

EmailInput.displayName = "EmailInput";

export interface EmailFieldProps extends InputProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
}

export const EmailField = forwardRef<HTMLInputElement, EmailFieldProps>(
  (
    {
      name = "email",
      label = i18n.en.label,
      description = i18n.en.description,
      placeholder = i18n.en.placeholder,
      ...props
    },
    ref,
  ) => {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <EmailInput
                {...props}
                {...field}
                ref={ref}
                placeholder={placeholder}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

EmailField.displayName = "EmailField";
