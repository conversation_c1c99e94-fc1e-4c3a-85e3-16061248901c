import { forwardRef } from "react";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    label: "Name",
    description: "Enter your name",
    placeholder: "John Do<PERSON>",
  },
};

export const TextInput = forwardRef<HTMLInputElement, InputProps>(
  (props, ref) => {
    return <Input {...props} type="text" ref={ref} />;
  },
);

TextInput.displayName = "TextInput";

export interface TextFieldProps extends InputProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
}

export const TextField = forwardRef<HTMLInputElement, TextFieldProps>(
  (
    {
      name = "text",
      label = i18n.en.label,
      description = i18n.en.description,
      placeholder = i18n.en.placeholder,
      ...props
    },
    ref,
  ) => {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <TextInput
                {...props}
                {...field}
                ref={ref}
                placeholder={placeholder}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

TextField.displayName = "TextField";
