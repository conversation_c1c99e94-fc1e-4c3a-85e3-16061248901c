import { forwardRef } from "react";
import { useFormContext } from "react-hook-form";

import type { TextareaProps } from "@/ui/primitives/textarea";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Textarea } from "@/ui/primitives/textarea";

const i18n = {
  en: {
    label: "Name",
    description: "Enter your name",
    placeholder: "John Doe",
  },
};

export const DescriptionInput = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (props, ref) => {
    return <Textarea {...props} ref={ref} />;
  },
);

DescriptionInput.displayName = "DescriptionInput";

export interface DescriptionFieldProps extends TextareaProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
}

export const DescriptionField = forwardRef<
  HTMLTextAreaElement,
  DescriptionFieldProps
>(
  (
    {
      name = "description",
      label = i18n.en.label,
      description = i18n.en.description,
      placeholder = i18n.en.placeholder,
      ...props
    },
    ref,
  ) => {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <DescriptionInput
                {...props}
                {...field}
                ref={ref}
                placeholder={placeholder}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

DescriptionField.displayName = "DescriptionField";
