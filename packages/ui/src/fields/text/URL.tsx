import { forwardRef } from "react";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";

const i18n = {
  en: {
    label: "Name",
    description: "Enter your name",
    placeholder: "<PERSON>",
  },
};

export const URLInput = forwardRef<HTMLInputElement, InputProps>(
  (props, ref) => {
    return <Input {...props} type="url" ref={ref} />;
  },
);

URLInput.displayName = "URLInput";

export interface URLFieldProps extends InputProps {
  name?: string;
  label?: string;
  description?: string;
  placeholder?: string;
}

export const URLField = forwardRef<HTMLInputElement, URLFieldProps>(
  (
    {
      name = "email",
      label = i18n.en.label,
      description = i18n.en.description,
      placeholder = i18n.en.placeholder,
      ...props
    },
    ref,
  ) => {
    const form = useFormContext();

    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <FormControl>
              <URLInput
                {...props}
                {...field}
                ref={ref}
                placeholder={placeholder}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  },
);

URLField.displayName = "URLField";
