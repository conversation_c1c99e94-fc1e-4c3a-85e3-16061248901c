import { addDays, format, isBefore, isValid, parse } from "date-fns";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import { cn } from "@/ui/lib";
import { Calendar } from "@/ui/primitives/calendar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { Input } from "@/ui/primitives/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/ui/primitives/popover";

const i18n = {
  en: {
    dateRange: {
      label: "Date Range",
      description: "Select start and end dates",
      validation: {
        invalidStart: "Please enter a valid start date",
        invalidEnd: "Please enter a valid end date",
        startBeforeEnd: "Start date must be before end date",
        startAfterMin: "Start date must be after {min}",
        endBeforeMax: "End date must be before {max}",
        minDuration: "Duration must be at least {min} days",
        maxDuration: "Duration must be at most {max} days",
      },
      start: {
        label: "Start Date",
        placeholder: "Select start date",
      },
      end: {
        label: "End Date",
        placeholder: "Select end date",
      },
    },
  },
};

export interface DateRangeFieldProps
  extends Omit<InputProps, "name" | "type" | "disabled"> {
  startName?: string;
  endName?: string;
  label?: string;
  description?: string;
  minDate?: Date;
  maxDate?: Date;
  minDuration?: number; // in days
  maxDuration?: number; // in days
  disabled?: boolean | { start?: boolean; end?: boolean };
  format?: string;
}

export function DateRangeField({
  startName = "startDate",
  endName = "endDate",
  label = i18n.en.dateRange.label,
  description = i18n.en.dateRange.description,
  minDate,
  maxDate,
  minDuration = 0,
  maxDuration,
  disabled = false,
  format: dateFormat = "yyyy-MM-dd",
  className,
  ...props
}: DateRangeFieldProps) {
  const form = useFormContext();

  const isStartDisabled =
    typeof disabled === "boolean" ? disabled : disabled.start;
  const isEndDisabled = typeof disabled === "boolean" ? disabled : disabled.end;

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex flex-col gap-2">
        <FormLabel>{label}</FormLabel>
        <FormDescription>{description}</FormDescription>
      </div>
      <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
        <FormField
          control={form.control}
          name={startName}
          rules={{
            validate: {
              isValid: (value) => {
                if (!value) return true;
                const date = parse(value, dateFormat, new Date());
                return (
                  isValid(date) || i18n.en.dateRange.validation.invalidStart
                );
              },
              afterMinDate: (value) => {
                if (!value || !minDate) return true;
                const date = parse(value, dateFormat, new Date());
                return (
                  isBefore(minDate, date) ||
                  i18n.en.dateRange.validation.startAfterMin.replace(
                    "{min}",
                    format(minDate, dateFormat),
                  )
                );
              },
              beforeEndDate: (value) => {
                if (!value) return true;
                const endDate = form.watch(endName);
                if (!endDate) return true;
                const startDate = parse(value, dateFormat, new Date());
                const end = parse(endDate, dateFormat, new Date());
                return (
                  isBefore(startDate, end) ||
                  i18n.en.dateRange.validation.startBeforeEnd
                );
              },
            },
          }}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormLabel>{i18n.en.dateRange.start.label}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Input
                      {...field}
                      {...props}
                      disabled={isStartDisabled}
                      placeholder={i18n.en.dateRange.start.placeholder}
                      value={field.value || ""}
                    />
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={
                      field.value
                        ? parse(field.value, dateFormat, new Date())
                        : undefined
                    }
                    onSelect={(date) =>
                      field.onChange(date ? format(date, dateFormat) : "")
                    }
                    disabled={(date) =>
                      isStartDisabled ||
                      (minDate ? isBefore(date, minDate) : false) ||
                      (maxDate ? !isBefore(date, maxDate) : false)
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={endName}
          rules={{
            validate: {
              isValid: (value) => {
                if (!value) return true;
                const date = parse(value, dateFormat, new Date());
                return isValid(date) || i18n.en.dateRange.validation.invalidEnd;
              },
              beforeMaxDate: (value) => {
                if (!value || !maxDate) return true;
                const date = parse(value, dateFormat, new Date());
                return (
                  isBefore(date, maxDate) ||
                  i18n.en.dateRange.validation.endBeforeMax.replace(
                    "{max}",
                    format(maxDate, dateFormat),
                  )
                );
              },
              afterStartDate: (value) => {
                if (!value) return true;
                const startDate = form.watch(startName);
                if (!startDate) return true;
                const start = parse(startDate, dateFormat, new Date());
                const endDate = parse(value, dateFormat, new Date());
                return (
                  isBefore(start, endDate) ||
                  i18n.en.dateRange.validation.startBeforeEnd
                );
              },
              minDuration: (value) => {
                if (!value || minDuration === 0) return true;
                const startDate = form.watch(startName);
                if (!startDate) return true;
                const start = parse(startDate, dateFormat, new Date());
                const end = parse(value, dateFormat, new Date());
                const minEnd = addDays(start, minDuration);
                return (
                  !isBefore(end, minEnd) ||
                  i18n.en.dateRange.validation.minDuration.replace(
                    "{min}",
                    minDuration.toString(),
                  )
                );
              },
              maxDuration: (value) => {
                if (!value || !maxDuration) return true;
                const startDate = form.watch(startName);
                if (!startDate) return true;
                const start = parse(startDate, dateFormat, new Date());
                const end = parse(value, dateFormat, new Date());
                const maxEnd = addDays(start, maxDuration);
                return (
                  !isBefore(maxEnd, end) ||
                  i18n.en.dateRange.validation.maxDuration.replace(
                    "{max}",
                    maxDuration.toString(),
                  )
                );
              },
            },
          }}
          render={({ field }) => (
            <FormItem className="flex-1">
              <FormLabel>{i18n.en.dateRange.end.label}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Input
                      {...field}
                      {...props}
                      disabled={isEndDisabled}
                      placeholder={i18n.en.dateRange.end.placeholder}
                      value={field.value || ""}
                    />
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={
                      field.value
                        ? parse(field.value, dateFormat, new Date())
                        : undefined
                    }
                    onSelect={(date) =>
                      field.onChange(date ? format(date, dateFormat) : "")
                    }
                    disabled={(date) => {
                      if (isEndDisabled) return true;
                      if (maxDate && !isBefore(date, maxDate)) return true;
                      const startDate = form.watch(startName);
                      if (!startDate) return false;
                      const start = parse(startDate, dateFormat, new Date());
                      if (isBefore(date, start)) return true;
                      if (minDuration) {
                        const minEnd = addDays(start, minDuration);
                        if (isBefore(date, minEnd)) return true;
                      }
                      if (maxDuration) {
                        const maxEnd = addDays(start, maxDuration);
                        if (!isBefore(date, maxEnd)) return true;
                      }
                      return false;
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
