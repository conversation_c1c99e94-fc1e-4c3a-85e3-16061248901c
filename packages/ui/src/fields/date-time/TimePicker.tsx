import { useFormContext } from "react-hook-form";

import type { TimePickerProps } from "@/ui/primitives/time-picker";

import { cn } from "@/ui/lib";
import { getTimeFromInteger, setTimeToInteger } from "@/ui/lib/dates";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { TimePicker } from "@/ui/primitives/time-picker";

const i18n = {
  en: {
    label: "Time",
    description: "Time of the day",
  },
};

export interface TimePickerFieldProps extends TimePickerProps {
  label?: string;
  description?: string;
  className?: string;
}

export default function TimePickerField({
  name = "startTime",
  label = i18n.en.label,
  description = i18n.en.description,
  className,
  ...props
}: TimePickerFieldProps) {
  const form = useFormContext<{
    startTime?: number;
    endTime?: number;
  }>();

  return (
    <FormField
      control={form.control}
      name={name as "startTime" | "endTime"}
      render={({ field }) => (
        <FormItem className={cn("w-full", className)}>
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <TimePicker
              {...props}
              {...field}
              value={field.value ? getTimeFromInteger(field.value) : null}
              onChange={(value) => {
                field.onChange(setTimeToInteger(value));
              }}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
