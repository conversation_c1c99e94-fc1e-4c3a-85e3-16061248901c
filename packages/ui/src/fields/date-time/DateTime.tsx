import { useState } from "react";
import { CalendarIcon } from "@radix-ui/react-icons";
import { format, isValid } from "date-fns";
import { useFormContext } from "react-hook-form";

import type { InputProps } from "@/ui/primitives/input";

import { cn } from "@/ui/lib";
import { Button } from "@/ui/primitives/button";
import { Calendar } from "@/ui/primitives/calendar";
import { DatetimePicker } from "@/ui/primitives/datetime-picker";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/ui/primitives/popover";

const i18n = {
  en: {
    dateTime: {
      label: "Date & Time",
      description: "Select date and time",
      validation: {
        invalidDate: "Please enter a valid date",
        invalidTime: "Please enter a valid time",
        beforeMin: "Must be after {min}",
        afterMax: "Must be before {max}",
      },
      date: {
        label: "Date",
        placeholder: "Select date",
      },
      time: {
        label: "Time",
      },
    },
  },
};

export interface DateTimeFieldProps extends Omit<InputProps, "name" | "type"> {
  name?: string;
  label?: string;
  description?: string;
  minDateTime?: Date;
  maxDateTime?: Date;
  disabled?: boolean;
  dateFormat?: string;
}

export function DateTimeField({
  name = "dateTime",
  label = i18n.en.dateTime.label,
  description = i18n.en.dateTime.description,
  minDateTime,
  maxDateTime,
  disabled = false,
  dateFormat = "yyyy-MM-dd",
  className,
}: DateTimeFieldProps) {
  const form = useFormContext();
  // State to control the popover
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  return (
    <FormField
      control={form.control}
      name={name}
      rules={{
        validate: {
          isValid: (value: Date | string | undefined) => {
            if (!value) return true;
            const date =
              value instanceof Date ? value : new Date(String(value));
            return isValid(date) || i18n.en.dateTime.validation.invalidDate;
          },
          afterMin: (value: Date | string | undefined) => {
            if (!value || !minDateTime) return true;
            const date =
              value instanceof Date ? value : new Date(String(value));
            return (
              date >= minDateTime ||
              i18n.en.dateTime.validation.beforeMin.replace(
                "{min}",
                format(minDateTime, dateFormat),
              )
            );
          },
          beforeMax: (value: Date | string | undefined) => {
            if (!value || !maxDateTime) return true;
            const date =
              value instanceof Date ? value : new Date(String(value));
            return (
              date <= maxDateTime ||
              i18n.en.dateTime.validation.afterMax.replace(
                "{max}",
                format(maxDateTime, dateFormat),
              )
            );
          },
        },
      }}
      render={({ field }) => {
        // Safely convert the value to a Date object if it's not already
        const dateValue = field.value
          ? field.value instanceof Date
            ? field.value
            : new Date(field.value)
          : undefined;

        // Handle date selection from calendar
        const handleCalendarSelect = (date: Date | undefined) => {
          console.log("handleCalendarSelect", date);
          if (!date) return;

          // Create a new Date object to avoid modifying the original
          const newDate = new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
          );

          // Preserve the time from the current value if it exists
          if (dateValue) {
            newDate.setHours(
              dateValue.getHours(),
              dateValue.getMinutes(),
              dateValue.getSeconds(),
              dateValue.getMilliseconds(),
            );
          } else {
            // Set default time to noon if no previous time exists
            newDate.setHours(12, 0, 0, 0);
          }

          // Update the form value with the new Date object
          field.onChange(newDate);

          // Close the calendar popover
          setIsCalendarOpen(false);
        };

        // Handle datetime picker change
        const handleDateTimeChange = (date: Date | undefined) => {
          if (!date) return;

          // Create a new Date object to avoid modifying the original
          const newDate = new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds(),
            date.getMilliseconds(),
          );

          // Update the form value with the new Date object
          field.onChange(newDate);
        };

        return (
          <FormItem className={cn("space-y-2", className)}>
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <div className="flex items-center gap-2">
              <FormControl className="flex-1">
                <DatetimePicker
                  value={dateValue}
                  onChange={handleDateTimeChange}
                  className="w-full"
                  // Default format is [["months", "days", "years"], ["hours", "minutes", "am/pm"]]
                  format={[
                    ["months", "days", "years"],
                    ["hours", "minutes", "am/pm"],
                  ]}
                  dtOptions={{
                    date: dateValue ?? new Date(),
                    hour12: true,
                    ...(minDateTime && { minDate: minDateTime }),
                    ...(maxDateTime && { maxDate: maxDateTime }),
                    // Disable the picker if needed
                    wheelControl: !disabled,
                  }}
                />
              </FormControl>
              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    disabled={disabled}
                    className="size-10"
                    aria-label="Select date from calendar"
                  >
                    <CalendarIcon className="size-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    mode="single"
                    selected={dateValue}
                    onSelect={handleCalendarSelect}
                    disabled={(date) =>
                      (minDateTime ? date < minDateTime : false) ||
                      (maxDateTime ? date > maxDateTime : false)
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
