"use client";

import type { DateRange } from "react-day-picker";

import { useState } from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import type { CalendarProps } from "@/ui/primitives/calendar";

import { cn } from "@/ui/lib";
import { displayDateForTimeZone, setDateForTimeZone } from "@/ui/lib/dates";
import { Button } from "@/ui/primitives/button";
import { Calendar } from "@/ui/primitives/calendar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/ui/primitives/popover";

const i18n = {
  en: {
    label: "Date",
    description: "The date on which the work order will be scheduled.",
    placeholder: "Pick a date",
  },
};

export interface DateFieldProps {
  timeZone?: string;
  name?:
    | "date"
    | "startDate"
    | "endDate"
    | "dueDate"
    | "issueDate"
    | "expirationDate";
  rangeName?: ["startDate", "endDate"];
  label?: string;
  description?: string;
  placeholder?: string;
  type?: CalendarProps["mode"];
  numberOfMonths?: CalendarProps["numberOfMonths"];
  disabled?: CalendarProps["disabled"];
  initialFocus?: CalendarProps["initialFocus"];
  locale?: CalendarProps["locale"];
}

export function DateField({
  timeZone = "local",
  name = "date",
  rangeName = ["startDate", "endDate"],
  label = i18n.en.label,
  description = i18n.en.description,
  placeholder = i18n.en.placeholder,
  type = "default",
  numberOfMonths = 2,
  disabled = (date) => date < new Date(),
  initialFocus = false,
  ...props
}: DateFieldProps) {
  const [calendarOpen, setCalendarOpen] = useState(false);
  const form = useFormContext<{
    date: Date | undefined;
    startDate: Date | undefined;
    endDate: Date | undefined;
    dueDate: Date | undefined;
    issueDate: Date | undefined;
    expirationDate: Date | undefined;
  }>();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        let text: string;
        let value: Date | string | DateRange | undefined;

        if (type === "range") {
          const [startDate, endDate] = rangeName;
          const start = form.getValues(startDate) ?? field.value;
          const end = form.getValues(endDate);
          value = {
            from: start ? displayDateForTimeZone(start, timeZone) : undefined,
            to: end ? displayDateForTimeZone(end, timeZone) : undefined,
          };
          text =
            [
              value.from ? format(value.from, "PPP") : undefined,
              value.to ? format(value.to, "PPP") : undefined,
            ]
              .filter(Boolean)
              .join(" - ")
              .trim() || placeholder;
        } else if (field.value instanceof Date) {
          value = displayDateForTimeZone(field.value, timeZone);
          text = format(value, "PPP");
        } else {
          text = placeholder;
          value = undefined;
        }

        return (
          <FormItem className="flex flex-col gap-1">
            <FormLabel>{label}</FormLabel>
            <FormDescription>{description}</FormDescription>
            <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
              <div className="flex w-full items-center justify-between gap-2">
                <PopoverTrigger asChild className="flex-1">
                  <FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      className={cn(
                        "pl-3 text-left font-normal",

                        !field.value && "text-muted-foreground",
                      )}
                    >
                      <span>{text}</span>
                      <CalendarIcon className="ml-auto size-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
              </div>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  {...props}
                  mode="single"
                  selected={field.value}
                  // onSelect={field.onChange}
                  // mode={type as CalendarProps["mode"]}
                  // selected={value}
                  onSelect={(_value: Date | DateRange | undefined) => {
                    const setOptions = {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    };

                    if (_value) {
                      if (type === "range") {
                        const [startDate, endDate] = rangeName;
                        const { from, to } = (_value ?? {}) as DateRange;

                        const start = from
                          ? setDateForTimeZone(from, timeZone)
                          : undefined;
                        const end = to
                          ? setDateForTimeZone(to, timeZone)
                          : undefined;

                        if (start) form.setValue(startDate, start, setOptions);
                        if (end) form.setValue(endDate, end, setOptions);
                      } else {
                        const date: Date | undefined = _value
                          ? setDateForTimeZone(_value as Date, timeZone)
                          : undefined;

                        field.onChange(date);
                        setCalendarOpen(false);
                      }
                    }
                  }}
                  disabled={disabled}
                  initialFocus={initialFocus}
                  numberOfMonths={numberOfMonths}
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
