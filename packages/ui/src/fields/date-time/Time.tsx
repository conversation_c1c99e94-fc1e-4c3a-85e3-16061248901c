"use client";

import type { TimeValue } from "react-aria";

import { EllipsisVerticalIcon, InfoIcon } from "lucide-react";
import { useFormContext } from "react-hook-form";

import { But<PERSON> } from "@/ui/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/ui/primitives/dropdown-menu";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { TimePicker } from "@/ui/primitives/time-picker";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/ui/primitives/tooltip";

import { Input } from "../../primitives/input";

const i18n = {
  en: {
    startTime: {
      label: "Start Time",
      description: "The time the shift will start",
    },
    endTime: {
      label: "End Time",
      description: "The time the shift will end",
    },
    hours: {
      label: "Hours",
      description: "The number of hours for the shift",
      placeholder: "Enter the number of hours",
    },
    labels: {
      presets: "Time Presets",
      dayTime: "Daytime Hours",
      nightTime: "Nighttime Hours",
    },
    presets: {
      daytime: [
        {
          label: "8:00 AM - 4:00 PM",
          start: {
            hour: 8,
            minute: 0,
          },
          end: {
            hour: 16,
            minute: 0,
          },
        },
        {
          label: "9:00 AM - 5:00 PM",
          start: {
            hour: 9,
            minute: 0,
          },
          end: {
            hour: 17,
            minute: 0,
          },
        },
        {
          label: "10:00 AM - 6:00 PM",
          start: {
            hour: 10,
            minute: 0,
          },
          end: {
            hour: 18,
            minute: 0,
          },
        },
      ],
      nighttime: [
        {
          label: "6:00 PM - 2:00 AM",
          start: {
            hour: 18,
            minute: 0,
          },
          end: {
            hour: 2,
            minute: 0,
          },
        },
        {
          label: "7:00 PM - 3:00 AM",
          start: {
            hour: 19,
            minute: 0,
          },
          end: {
            hour: 3,
            minute: 0,
          },
        },
        {
          label: "8:00 PM - 4:00 AM",
          start: {
            hour: 20,
            minute: 0,
          },
          end: {
            hour: 4,
            minute: 0,
          },
        },
      ],
    },
  },
};

const setOptions = {
  shouldValidate: true,
  shouldDirty: true,
  shouldTouch: true,
};

export interface TimeFieldProps {
  label?: string;
  description?: string;
  showHours?: boolean;
}

export function TimeField({
  // label = i18n.en.label,
  // description = i18n.en.description,
  ...props
}: TimeFieldProps) {
  const form = useFormContext<{
    startTime: TimeValue;
    endTime: TimeValue;
    hours: number;
  }>();
  return (
    <div className="flex w-full flex-col gap-4">
      <div className="flex items-end justify-between gap-2">
        <div className="mt-2 grid flex-1 grid-cols-2 gap-4">
          <div>
            <FormField
              control={form.control}
              name="startTime"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <FormLabel>{i18n.en.startTime.label}</FormLabel>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <InfoIcon className="size-4" />
                      </TooltipTrigger>
                      <TooltipContent>
                        {i18n.en.startTime.description}
                      </TooltipContent>
                    </Tooltip>
                    <FormDescription className="sr-only">
                      {i18n.en.startTime.description}
                    </FormDescription>
                  </div>

                  <FormControl>
                    <TimePicker
                      {...field}
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div>
            <FormField
              control={form.control}
              name="endTime"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <FormLabel>{i18n.en.endTime.label}</FormLabel>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <InfoIcon className="size-4" />
                      </TooltipTrigger>
                      <TooltipContent>
                        {i18n.en.endTime.description}
                      </TooltipContent>
                    </Tooltip>
                    <FormDescription className="sr-only">
                      {i18n.en.endTime.description}
                    </FormDescription>
                  </div>

                  <FormControl>
                    <TimePicker
                      {...field}
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" type="button">
              <EllipsisVerticalIcon className="size-4" />
              <span className="sr-only">{i18n.en.labels.presets}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuGroup>
              <DropdownMenuLabel>{i18n.en.labels.dayTime}</DropdownMenuLabel>
              {i18n.en.presets.daytime.map((preset) => (
                <DropdownMenuItem
                  key={preset.label}
                  onClick={() => {
                    form.setValue(
                      "startTime",
                      preset.start as TimeValue,
                      setOptions,
                    );
                    form.setValue(
                      "endTime",
                      preset.end as TimeValue,
                      setOptions,
                    );
                    form.setValue(
                      "hours",
                      computeHours(
                        preset.start as TimeValue,
                        preset.end as TimeValue,
                      ),
                      setOptions,
                    );
                  }}
                >
                  {preset.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
            <DropdownMenuGroup>
              <DropdownMenuLabel>{i18n.en.labels.nightTime}</DropdownMenuLabel>
              {i18n.en.presets.nighttime.map((preset) => (
                <DropdownMenuItem
                  key={preset.label}
                  onClick={() => {
                    form.setValue(
                      "startTime",
                      preset.start as TimeValue,
                      setOptions,
                    );
                    form.setValue(
                      "endTime",
                      preset.end as TimeValue,
                      setOptions,
                    );
                    form.setValue(
                      "hours",
                      computeHours(
                        preset.start as TimeValue,
                        preset.end as TimeValue,
                      ),
                      setOptions,
                    );
                  }}
                >
                  {preset.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {props.showHours && (
        <FormField
          control={form.control}
          name="hours"
          render={({ field }) => (
            <FormItem className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <FormLabel>{i18n.en.hours.label}</FormLabel>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <InfoIcon className="size-4" />
                  </TooltipTrigger>
                  <TooltipContent>{i18n.en.hours.description}</TooltipContent>
                </Tooltip>
                <FormDescription className="sr-only">
                  {i18n.en.hours.description}
                </FormDescription>
              </div>

              <FormControl>
                <Input
                  type="number"
                  {...field}
                  onChange={(e) => {
                    field.onChange(parseFloat(e.target.value));
                  }}
                  placeholder={i18n.en.hours.placeholder}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      )}
    </div>
  );
}

export function computeHours(start: TimeValue, end: TimeValue) {
  const startInteger = setTimeToInteger(start);
  const endInteger = setTimeToInteger(end);

  if (endInteger < startInteger) {
    return (24 * 60 - startInteger + endInteger) / 60;
  }

  return (endInteger - startInteger) / 60;
}

export function setTimeToInteger(time?: {
  hour: number;
  minute: number;
}): number {
  if (time) {
    return time.hour * 60 + time.minute;
  }

  return 0;
}
