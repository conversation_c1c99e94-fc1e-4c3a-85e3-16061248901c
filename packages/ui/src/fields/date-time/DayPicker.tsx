import { useFormContext } from "react-hook-form";

import type { RadioGroupProps } from "@/ui/primitives/radio-group";

import { cn } from "@/ui/lib";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/ui/primitives/form";
import { RadioGroup, RadioGroupItem } from "@/ui/primitives/radio-group";

const i18n = {
  en: {
    label: "Day",
    description: "Select the day of the week",
    days: [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ],
  },
};

export interface DayPickerFieldProps
  extends Omit<RadioGroupProps, "value" | "onValueChange"> {
  name?: string;
  label?: string;
  description?: string;
}

export default function DayPickerField({
  name = "day",
  label = i18n.en.label,
  description = i18n.en.description,
  ...props
}: DayPickerFieldProps) {
  const form = useFormContext<Record<typeof name, number | undefined>>();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-3">
          <FormLabel>{label}</FormLabel>
          <FormDescription>{description}</FormDescription>
          <FormControl>
            <RadioGroup
              {...props}
              value={field.value?.toString()}
              onValueChange={field.onChange}
              className="grid grid-cols-7 gap-4"
            >
              {Array.from({ length: 7 }).map((_, index) => (
                <FormItem key={index}>
                  <FormControl>
                    <RadioGroupItem
                      value={index.toString()}
                      id={index.toString()}
                      className="peer sr-only"
                    />
                  </FormControl>
                  <FormLabel
                    htmlFor={index.toString()}
                    className={cn(
                      "flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary",
                    )}
                  >
                    <span className="text-xs font-medium sm:text-sm md:text-base">
                      {/* TODO: get the day of the week from locale and format it */}
                      {i18n.en.days[index]?.slice(0, 2)}
                    </span>
                  </FormLabel>
                </FormItem>
              ))}
            </RadioGroup>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
