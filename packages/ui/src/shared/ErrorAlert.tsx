import { TriangleAlertIcon } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "@/ui/primitives/alert";
import { Button } from "@/ui/primitives/button";

export function ErrorAlert({
  error,
  retry,
}: {
  error:
    | (Error & { digest?: string })
    | {
        message: string;
      };
  retry?: () => void;
}) {
  return (
    <Alert variant="destructive">
      <TriangleAlertIcon className="size-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        <span>{error.message}</span>
        {retry && (
          <div className="flex flex-row gap-2">
            <Button variant="secondary" onClick={retry}>
              Retry
            </Button>
          </div>
        )}
      </AlertDescription>
    </Alert>
  );
}
