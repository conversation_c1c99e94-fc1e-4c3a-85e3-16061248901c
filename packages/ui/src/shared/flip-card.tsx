"use client";

import type { VariantProps } from "class-variance-authority";

import { useState } from "react";
import { cva } from "class-variance-authority";

import { useFloatingHover } from "@/ui/hooks/use-floating-hover";
import { cn } from "@/ui/lib";

const flipCardVariants = cva(
  "relative cursor-pointer transition-all duration-700 ease-out",
  {
    variants: {
      aspectRatio: {
        square: "aspect-square",
        portrait: "aspect-[3/4]",
        landscape: "aspect-[4/3]",
        wide: "aspect-[16/9]",
        card: "aspect-[1.6/1]", // Credit card / ID card ratio
        photo: "aspect-[5/4]",
      },
      size: {
        sm: "min-h-32 min-w-48 max-w-xs",
        md: "min-h-40 min-w-64 max-w-sm",
        lg: "min-h-48 min-w-80 max-w-md",
        xl: "min-h-56 min-w-96 max-w-lg",
        "2xl": "min-h-64 min-w-[448px] max-w-xl",
      },
    },
    defaultVariants: {
      aspectRatio: "card",
      size: "md",
    },
  },
);

const cardFaceVariants = cva(
  "absolute inset-0 size-full overflow-hidden rounded-xl shadow-lg dark:shadow-black/25",
  {
    variants: {
      face: {
        front: "",
        back: "",
      },
    },
  },
);

export interface FlipCardProps extends VariantProps<typeof flipCardVariants> {
  className?: string;
  frontContent: React.ReactNode;
  backContent: React.ReactNode;
  customWidth?: number;
  customHeight?: number;
  maxRotation?: number;
  perspective?: number;
  style?: React.CSSProperties;
  isFlipped?: boolean;
  onFlip?: (isFlipped: boolean) => void;
  flipOnClick?: boolean;
  flipOnHover?: boolean;
}

export default function FlipCard({
  aspectRatio,
  size,
  className,
  frontContent,
  backContent,
  customWidth,
  customHeight,
  maxRotation = 10,
  perspective = 1000,
  style,
  isFlipped: controlledFlipped,
  onFlip,
  flipOnClick = true,
  flipOnHover = false,
  ...props
}: FlipCardProps) {
  const [internalFlipped, setInternalFlipped] = useState(false);
  const isControlled = controlledFlipped !== undefined;
  const isFlipped = isControlled ? controlledFlipped : internalFlipped;

  const { elementRef, handlers, getTransform, getBoxShadow } = useFloatingHover(
    {
      maxRotation,
      perspective,
    },
  );

  const handleFlip = () => {
    if (!flipOnClick) return;

    const newFlipped = !isFlipped;
    if (!isControlled) {
      setInternalFlipped(newFlipped);
    }
    onFlip?.(newFlipped);
  };

  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    handlers.onMouseEnter();
    if (flipOnHover && !isFlipped) {
      const newFlipped = true;
      if (!isControlled) {
        setInternalFlipped(newFlipped);
      }
      onFlip?.(newFlipped);
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    handlers.onMouseLeave();
    if (flipOnHover && isFlipped) {
      const newFlipped = false;
      if (!isControlled) {
        setInternalFlipped(newFlipped);
      }
      onFlip?.(newFlipped);
    }
  };

  const customStyles: React.CSSProperties = {
    transform: `${getTransform()} ${isFlipped ? "rotateY(180deg)" : "rotateY(0deg)"}`,
    transformStyle: "preserve-3d",
    ...(customWidth && {
      width: `${customWidth}px`,
      minWidth: `${customWidth}px`,
    }),
    ...(customHeight && {
      height: `${customHeight}px`,
      minHeight: `${customHeight}px`,
    }),
    ...style,
  };

  const boxShadow = getBoxShadow();

  return (
    <div className="mx-auto w-full" style={{ perspective: `${perspective}px` }}>
      <div
        ref={elementRef}
        className={cn(
          flipCardVariants({
            aspectRatio: customWidth || customHeight ? undefined : aspectRatio,
            size: customWidth || customHeight ? undefined : size,
          }),
          className,
        )}
        style={customStyles}
        onClick={handleFlip}
        onMouseMove={handlers.onMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...props}
      >
        {/* Front Face */}
        <div
          className={cn(cardFaceVariants({ face: "front" }))}
          style={{
            boxShadow,
            backfaceVisibility: "hidden",
          }}
        >
          {frontContent}
        </div>

        {/* Back Face */}
        <div
          className={cn(cardFaceVariants({ face: "back" }))}
          style={{
            boxShadow,
            backfaceVisibility: "hidden",
            transform: "rotateY(180deg)",
          }}
        >
          {backContent}
        </div>
      </div>
    </div>
  );
}
