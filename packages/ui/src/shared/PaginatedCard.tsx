"use client";

import React, { useCallback } from "react";

import type { useSearchPagination } from "@axa/ui/search";
import { Button } from "@axa/ui/primitives/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>itle,
} from "@axa/ui/primitives/card";
import { Separator } from "@axa/ui/primitives/separator";
import { Skeleton } from "@axa/ui/primitives/skeleton";

import { ErrorAlert } from "./ErrorAlert";

// --- Paginated Count Component ---
interface PaginatedCountProps {
  count: number;
}

function PaginatedCount({ count }: PaginatedCountProps) {
  const displayValue = count >= 99 ? "99+" : count.toString();

  return (
    <span className="ml-2 inline-flex h-5 items-center justify-center rounded-full bg-muted px-2 text-xs font-medium text-muted-foreground">
      {displayValue}
    </span>
  );
}

// --- Root Component ---

interface PaginatedCardRootProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  loading?: boolean;
}

export function PaginatedCardRoot({
  children,
  className,
  loading,
  ...props
}: PaginatedCardRootProps) {
  if (loading) {
    return <PaginatedCardSkeleton />;
  }

  return (
    <Card className={`flex flex-col gap-4 ${className || ""}`} {...props}>
      {children}
    </Card>
  );
}

// --- Header Component ---

interface PaginatedCardHeaderProps {
  icon?: React.ReactNode;
  title: string;
  count?: number;
  description?: string;
  actions?: React.ReactNode;
  filters?: React.ReactNode;
  loading?: boolean;
}

export function PaginatedCardHeader({
  icon,
  title,
  count,
  description,
  actions,
  filters,
  loading,
}: PaginatedCardHeaderProps) {
  if (loading) {
    return (
      <>
        <CardHeader className="space-y-4">
          {/* Title and Actions Row */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {icon && <Skeleton className="size-9 rounded-lg" />}
              <div>
                <Skeleton className="h-7 w-48" />
                <Skeleton className="mt-1 h-4 w-64" />
              </div>
            </div>
            {actions && <Skeleton className="h-9 w-32" />}
          </div>

          {/* Filters Row */}
          {filters && <Skeleton className="h-10 w-full" />}
        </CardHeader>
        <Separator />
      </>
    );
  }

  return (
    <>
      <CardHeader className="space-y-4">
        {/* Title and Actions Row */}
        <div className="flex items-center justify-between">
          {/* Left side: Icon, Title, and Description grouped together */}
          <div className="flex items-center gap-2">
            {icon && (
              <div className="flex items-center gap-2 rounded-lg border border-border p-2">
                {icon}
              </div>
            )}
            <div>
              <div className="flex items-center">
                <CardTitle>{title}</CardTitle>
                {count !== undefined && count > 0 && (
                  <PaginatedCount count={count} />
                )}
              </div>
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
            </div>
          </div>

          {/* Right side: Actions */}
          {actions && <div>{actions}</div>}
        </div>

        {/* Filters Row (full width below title/actions) */}
        {filters && <div>{filters}</div>}
      </CardHeader>
      <Separator />
    </>
  );
}

// --- Content Component ---

interface PaginatedCardContentProps {
  isLoading?: boolean;
  error?: Error | null;
  isEmpty: boolean;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  children: React.ReactNode;
}

export function PaginatedCardContent({
  isLoading,
  error,
  isEmpty,
  loadingComponent,
  errorComponent,
  emptyComponent,
  children,
}: PaginatedCardContentProps) {
  if (isLoading) {
    return <CardContent>{loadingComponent || <ContentSkeleton />}</CardContent>;
  }

  return (
    <CardContent className="flex flex-1 flex-col gap-4">
      {error && (errorComponent || <ErrorAlert error={error} />)}
      {isEmpty ? emptyComponent : children}
    </CardContent>
  );
}

function ContentSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-24 w-full" />
      <Skeleton className="h-24 w-full" />
    </div>
  );
}

// --- Footer Component (Pagination) ---

interface PaginatedCardFooterProps {
  pagination: ReturnType<typeof useSearchPagination>["pagination"];
  setPagination: ReturnType<typeof useSearchPagination>["setPagination"];
  totalItems: number;
  itemNoun?: { singular: string; plural: string };
  showEntryCount?: boolean;
  loading?: boolean;
}

const defaultItemNoun = { singular: "entry", plural: "entries" };

export function PaginatedCardFooter({
  pagination,
  setPagination,
  totalItems,
  itemNoun = defaultItemNoun,
  showEntryCount = true,
  loading,
}: PaginatedCardFooterProps) {
  const { pageIndex: page, pageSize } = pagination;
  const totalPages = Math.ceil(totalItems / pageSize);

  const handlePageChange = useCallback(
    (newPage: number) => {
      setPagination((prev: typeof pagination) => ({
        ...prev,
        pageIndex: newPage,
      }));
    },
    [setPagination],
  );

  const startItem = totalItems === 0 ? 0 : page * pageSize + 1;
  const endItem = Math.min((page + 1) * pageSize, totalItems);
  const noun = totalItems === 1 ? itemNoun.singular : itemNoun.plural;

  if (loading) {
    return (
      <CardFooter>
        <div className="flex w-full flex-col items-center justify-between gap-4 pt-4 sm:flex-row">
          <Skeleton className="h-5 w-40" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-[70px]" />
            <Skeleton className="h-9 w-[70px]" />
          </div>
        </div>
      </CardFooter>
    );
  }

  if (totalItems === 0 && !showEntryCount) return null; // Hide footer if nothing to show unless explicitly told

  return (
    <CardFooter>
      <div className="flex w-full flex-col items-center justify-between gap-4 pt-4 sm:flex-row">
        {showEntryCount && (
          <p className="text-sm text-muted-foreground">
            {totalItems > 0
              ? `Showing ${startItem}–${endItem} of ${totalItems} ${noun}`
              : `0 ${noun}`}
          </p>
        )}
        {!showEntryCount && <div />} {/* Placeholder for spacing */}
        {totalPages > 1 && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(Math.max(0, page - 1))}
              disabled={page === 0}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                handlePageChange(Math.min(totalPages - 1, page + 1))
              }
              disabled={page >= totalPages - 1}
            >
              Next
            </Button>
          </div>
        )}
        {totalPages <= 1 && <div />} {/* Placeholder for spacing */}
      </div>
    </CardFooter>
  );
}

// --- Skeleton ---

interface PaginatedCardSkeletonProps {
  showIcon?: boolean;
  showActions?: boolean;
  showFilters?: boolean;
  gridCols?: 1 | 2 | 3 | 4; // Number of columns for item skeletons
  itemHeight?: string; // e.g., "h-40"
}

export function PaginatedCardSkeleton({
  showIcon = true,
  showActions = true,
  showFilters = false, // Assuming filters might be added later above content
  gridCols = 3,
  itemHeight = "h-40",
}: PaginatedCardSkeletonProps) {
  const colClass = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
  }[gridCols];

  return (
    <Card>
      <CardHeader className="space-y-1">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {showIcon && <Skeleton className="size-9 rounded-lg" />}
            <div>
              <Skeleton className="h-7 w-48" />
              <Skeleton className="mt-1 h-4 w-64" />
            </div>
          </div>
          {showActions && <Skeleton className="size-9" />}
        </div>
      </CardHeader>
      <Separator className="mb-6" />
      <CardContent>
        <div className="space-y-4">
          {showFilters && (
            <div className="flex items-center justify-between gap-4">
              <Skeleton className="h-10 flex-1" />
              <Skeleton className="h-10 w-40" />
            </div>
          )}
          <div className={`grid ${colClass} gap-4`}>
            {[...Array(gridCols)].map((_, i) => (
              <Skeleton key={i} className={itemHeight} />
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-center justify-between gap-4 pt-4">
          <Skeleton className="h-5 w-40" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-9 w-[70px]" />
            <Skeleton className="h-9 w-[70px]" />
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
