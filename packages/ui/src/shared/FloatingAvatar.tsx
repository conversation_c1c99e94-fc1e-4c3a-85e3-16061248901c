import type { PropsWithChildren } from "react";

import Image from "next/image";

import { cn } from "@/ui/lib";
import { Avatar, AvatarFallback, AvatarImage } from "@/ui/primitives/avatar";
import { Skeleton } from "@/ui/primitives/skeleton";

const i18n = {
  en: {
    generics: {
      avatar: "Organization Avatar",
    },
  },
};

export interface PreviewOrganizationProps extends PropsWithChildren {
  size?: "sm" | "md" | "lg" | "xl" | null;
  loading?: boolean;
  link?: boolean;
  shadow?: boolean;
  className?: string;
  avatar?: string | null;
  label?: string;
  children?: React.ReactNode;
}

export default function FloatingAvatar({
  loading = false,
  link = false,
  size = "md",
  shadow = true,
  className,
  avatar,
  label,
  children,
}: PreviewOrganizationProps) {
  return loading ? (
    <Skeleton
      className={cn("size-7 shrink-0 rounded-sm", className, {
        "shadow-md": shadow,
        "size-6": size === "sm",
        "size-10": size === "lg",
        "size-16": size === "xl",
      })}
    />
  ) : (
    <Avatar
      className={cn("size-8 rounded-sm", className, {
        "shadow-md": shadow,
        "cursor-pointer": link,
        "size-6": size === "sm",
        "size-10": size === "lg",
        "size-16": size === "xl",
      })}
    >
      <AvatarImage asChild src={avatar ?? ""} alt={i18n.en.generics.avatar}>
        {children ?? (
          <Image
            loading="eager"
            src={avatar ?? ""}
            alt={i18n.en.generics.avatar}
            width={32}
            height={32}
          />
        )}
      </AvatarImage>
      <AvatarFallback className="rounded-sm">{label}</AvatarFallback>
    </Avatar>
  );
}
