"use client";

import type { VariantProps } from "class-variance-authority";

import { cva } from "class-variance-authority";

import { useFloatingHover } from "@/ui/hooks/use-floating-hover";
import { cn } from "@/ui/lib";

const floatingCardVariants = cva(
  "relative cursor-pointer overflow-hidden rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 p-6 shadow-2xl transition-all duration-200 ease-out dark:from-gray-800 dark:to-gray-900 dark:shadow-black/50",
  {
    variants: {
      aspectRatio: {
        square: "aspect-square",
        portrait: "aspect-[3/4]",
        landscape: "aspect-[4/3]",
        wide: "aspect-[16/9]",
        ultrawide: "aspect-[21/9]",
        photo: "aspect-[5/4]",
        video: "aspect-video",
      },
      size: {
        sm: "min-h-[200px] max-w-xs",
        md: "min-h-[300px] max-w-sm",
        lg: "min-h-[400px] max-w-md",
        xl: "min-h-[500px] max-w-lg",
        "2xl": "min-h-[600px] max-w-xl",
      },
    },
    defaultVariants: {
      aspectRatio: "portrait",
      size: "md",
    },
  },
);

const backgroundVariants = cva("absolute inset-0", {
  variants: {
    aspectRatio: {
      square: "",
      portrait: "",
      landscape: "",
      wide: "",
      ultrawide: "",
      photo: "",
      video: "",
    },
  },
  defaultVariants: {
    aspectRatio: "portrait",
  },
});

export interface FloatingCardBackgroundProps
  extends VariantProps<typeof backgroundVariants> {
  className?: string;
  opacity?: number;
}

export function FloatingCardBackground({
  aspectRatio,
  className,
  opacity = 1,
}: FloatingCardBackgroundProps) {
  // Calculate viewBox dimensions based on aspect ratio
  const getViewBoxDimensions = () => {
    switch (aspectRatio) {
      case "square":
        return { width: 400, height: 400 };
      case "landscape":
        return { width: 400, height: 300 };
      case "wide":
        return { width: 400, height: 225 };
      case "ultrawide":
        return { width: 400, height: 171 };
      case "photo":
        return { width: 400, height: 320 };
      case "video":
        return { width: 400, height: 225 };
      case "portrait":
      default:
        return { width: 300, height: 400 };
    }
  };

  const { width: viewWidth, height: viewHeight } = getViewBoxDimensions();
  const viewBox = `0 0 ${viewWidth} ${viewHeight}`;

  // Calculate pattern density based on dimensions
  const patternDensity = Math.min(viewWidth, viewHeight) / 400;
  const waveCount = Math.floor(30 * patternDensity);
  const flowCount = Math.floor(15 * patternDensity);
  const diagonalCount = Math.floor(20 * patternDensity);

  return (
    <div
      className={cn(backgroundVariants({ aspectRatio }), className)}
      style={{ opacity }}
    >
      {/* Topographic background effect */}
      <div className="absolute inset-0 opacity-20">
        <svg className="size-full" viewBox={viewBox}>
          <defs>
            {/* Light mode gradients */}
            <radialGradient id="topo1-light" cx="30%" cy="20%">
              <stop
                offset="0%"
                stopColor="rgb(255 255 255)"
                stopOpacity="0.3"
              />
              <stop
                offset="100%"
                stopColor="rgb(255 255 255)"
                stopOpacity="0"
              />
            </radialGradient>
            <radialGradient id="topo2-light" cx="70%" cy="80%">
              <stop offset="0%" stopColor="rgb(0 0 0)" stopOpacity="0.1" />
              <stop offset="100%" stopColor="rgb(0 0 0)" stopOpacity="0" />
            </radialGradient>
            {/* Dark mode gradients */}
            <radialGradient id="topo1-dark" cx="30%" cy="20%">
              <stop
                offset="0%"
                stopColor="rgb(229 231 235)"
                stopOpacity="0.3"
              />
              <stop
                offset="100%"
                stopColor="rgb(229 231 235)"
                stopOpacity="0"
              />
            </radialGradient>
            <radialGradient id="topo2-dark" cx="70%" cy="80%">
              <stop
                offset="0%"
                stopColor="rgb(249 250 251)"
                stopOpacity="0.1"
              />
              <stop
                offset="100%"
                stopColor="rgb(249 250 251)"
                stopOpacity="0"
              />
            </radialGradient>
          </defs>
          <ellipse
            cx={viewWidth * 0.3}
            cy={viewHeight * 0.2}
            rx="60"
            ry="40"
            fill="url(#topo1-light)"
            className="dark:hidden"
          />
          <ellipse
            cx={viewWidth * 0.3}
            cy={viewHeight * 0.2}
            rx="60"
            ry="40"
            fill="url(#topo1-dark)"
            className="hidden dark:block"
          />
          <ellipse
            cx={viewWidth * 0.7}
            cy={viewHeight * 0.8}
            rx="80"
            ry="50"
            fill="url(#topo2-light)"
            className="dark:hidden"
          />
          <ellipse
            cx={viewWidth * 0.7}
            cy={viewHeight * 0.8}
            rx="80"
            ry="50"
            fill="url(#topo2-dark)"
            className="hidden dark:block"
          />
          <ellipse
            cx={viewWidth * 0.5}
            cy={viewHeight * 0.5}
            rx="40"
            ry="30"
            fill="url(#topo1-light)"
            className="dark:hidden"
          />
          <ellipse
            cx={viewWidth * 0.5}
            cy={viewHeight * 0.5}
            rx="40"
            ry="30"
            fill="url(#topo1-dark)"
            className="hidden dark:block"
          />
        </svg>
      </div>

      {/* Enhanced wavy lines pattern */}
      <div className="absolute inset-0">
        <svg className="size-full opacity-60" viewBox={viewBox}>
          {Array.from({ length: waveCount }).map((_, i) => {
            const y = (viewHeight / waveCount) * i + 10;
            const amplitude = 6 + (i % 4) * 3;
            const frequency = 0.015 + (i % 3) * 0.008;
            const phase = (i % 5) * 0.5;
            const segments = Math.floor(viewWidth / 5);

            return (
              <path
                key={i}
                d={`M 0 ${y + Math.sin(phase) * amplitude} ${Array.from({
                  length: segments + 1,
                })
                  .map((_, x) => {
                    const xPos = (x * viewWidth) / segments;
                    const yPos =
                      y +
                      Math.sin(
                        (xPos / viewWidth) * frequency * viewWidth + phase,
                      ) *
                        amplitude;
                    return `L ${xPos} ${yPos}`;
                  })
                  .join(" ")}`}
                className="stroke-gray-500 dark:stroke-gray-400"
                strokeWidth="0.8"
                fill="none"
                opacity={0.3 + (i % 4) * 0.15}
              />
            );
          })}
        </svg>
      </div>

      {/* Additional flowing wavy lines */}
      <div className="absolute inset-0">
        <svg className="size-full opacity-50" viewBox={viewBox}>
          {Array.from({ length: flowCount }).map((_, i) => {
            const y = (viewHeight / flowCount) * i + 25;
            const midPoint1 = viewWidth * 0.25;
            const midPoint2 = viewWidth * 0.5;
            const endPoint = viewWidth;

            return (
              <path
                key={`flow-${i}`}
                d={`M 0 ${y} Q ${midPoint1} ${y - 10} ${midPoint2} ${y} T ${endPoint} ${y}`}
                className="stroke-gray-600 dark:stroke-gray-300"
                strokeWidth="1.2"
                fill="none"
                opacity={0.4}
              />
            );
          })}
        </svg>
      </div>

      {/* Diagonal flowing lines */}
      <div className="absolute inset-0">
        <svg className="size-full opacity-30" viewBox={viewBox}>
          {Array.from({ length: diagonalCount }).map((_, i) => {
            const startY = (viewHeight / diagonalCount) * i;
            const amplitude = 8;
            const frequency = 0.02;
            const segments = Math.floor(viewWidth / 10);

            return (
              <path
                key={`diagonal-${i}`}
                d={`M 0 ${startY} ${Array.from({ length: segments + 1 })
                  .map((_, x) => {
                    const xPos = (x * viewWidth) / segments;
                    const yPos =
                      startY +
                      (xPos / viewWidth) * viewHeight * 0.8 +
                      Math.sin((xPos / viewWidth) * frequency * viewWidth) *
                        amplitude;
                    return `L ${xPos} ${yPos}`;
                  })
                  .join(" ")}`}
                className="stroke-gray-400 dark:stroke-gray-500"
                strokeWidth="0.6"
                fill="none"
                opacity={0.25}
              />
            );
          })}
        </svg>
      </div>

      {/* Center teal circle with enhanced shadow */}
      <div className="absolute left-1/2 top-1/2 size-24 -translate-x-1/2 -translate-y-1/2">
        <div
          className="size-full rounded-full bg-gradient-to-br from-teal-400 to-emerald-500 shadow-xl dark:from-teal-500 dark:to-emerald-600"
          style={{
            boxShadow:
              "0 10px 25px -5px rgba(20, 184, 166, 0.4), 0 4px 6px -2px rgba(20, 184, 166, 0.2)",
          }}
        ></div>
      </div>
    </div>
  );
}

export interface FloatingCardProps
  extends VariantProps<typeof floatingCardVariants> {
  className?: string;
  children?: React.ReactNode;
  customWidth?: number;
  customHeight?: number;
  maxRotation?: number;
  perspective?: number;
  style?: React.CSSProperties;
  backgroundOpacity?: number;
  showBackground?: boolean;
}

export default function FloatingCard({
  aspectRatio,
  size,
  className,
  children,
  customWidth,
  customHeight,
  maxRotation = 5,
  perspective = 1000,
  style,
  backgroundOpacity = 1,
  showBackground = true,
  ...props
}: FloatingCardProps) {
  const { elementRef, isHovering, handlers, getTransform, getBoxShadow } =
    useFloatingHover({
      maxRotation,
      perspective,
    });

  const customStyles: React.CSSProperties = {
    transform: getTransform(),
    boxShadow: getBoxShadow(),
    ...(customWidth && { width: `${customWidth}px` }),
    ...(customHeight && { height: `${customHeight}px` }),
    ...style,
  };

  return (
    <div className="mx-auto w-full">
      <div
        ref={elementRef}
        className={cn(
          floatingCardVariants({
            aspectRatio: customWidth || customHeight ? undefined : aspectRatio,
            size: customWidth || customHeight ? undefined : size,
          }),
          customWidth || customHeight
            ? "relative cursor-pointer overflow-hidden rounded-3xl bg-gradient-to-b from-gray-100 to-gray-200 p-6 shadow-2xl transition-all duration-200 ease-out dark:from-gray-800 dark:to-gray-900 dark:shadow-black/50"
            : "",
          className,
        )}
        style={customStyles}
        {...handlers}
        {...props}
      >
        {/* Background layer */}
        {showBackground && (
          <FloatingCardBackground
            aspectRatio={aspectRatio}
            opacity={backgroundOpacity}
          />
        )}

        {/* Content layer */}
        <div className="relative z-10">{children}</div>
      </div>
    </div>
  );
}
