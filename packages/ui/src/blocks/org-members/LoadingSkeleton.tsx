"use client";

import React from "react";

import { PaginatedCardSkeleton } from "../../shared/PaginatedCard";

export interface OrganizationMembersListSkeletonProps {
  gridCols?: 1 | 2 | 3 | 4;
  itemCount?: number;
}

export function OrganizationMembersListSkeleton({
  gridCols = 2,
  itemCount = 4,
}: OrganizationMembersListSkeletonProps) {
  return (
    <PaginatedCardSkeleton
      showIcon
      showActions
      itemHeight="h-20"
      gridCols={gridCols}
    />
  );
}
