"use client";

import type { PaginationState } from "../types";

// Generic interfaces for flexibility
export interface GenericMember {
  id: string;
  displayName: string;
  avatar?: string | null;
  initials?: string;
  role: string;
  status?: string;
  metadata?: Record<string, unknown>;
}

export interface GenericMemberWithOriginal<T = unknown> extends GenericMember {
  originalData: T;
}

export interface OrganizationMembersListProps<
  T extends GenericMember = GenericMember,
> {
  // Data
  members: T[];
  total: number;
  loading?: boolean;
  error?: Error | null;

  // Configuration
  title?: string;
  description?: string;
  emptyMessage?: string;
  itemNoun?: { singular: string; plural: string };

  // Customization
  renderMember?: (member: T) => React.ReactNode;
  renderActions?: () => React.ReactNode;
  renderMemberMenu?: (member: T) => React.ReactNode;

  // Behavior
  pagination?: PaginationState;
  onPaginationChange?: (pagination: PaginationState) => void;
  onMemberClick?: (member: T) => void;

  // Styling
  className?: string;
  gridCols?: 1 | 2 | 3 | 4;
}

// Default member card component
export interface MemberCardProps<T extends GenericMember = GenericMember> {
  member: T;
  renderMenu?: (member: T) => React.ReactNode;
  onClick?: (member: T) => void;
  className?: string;
}
