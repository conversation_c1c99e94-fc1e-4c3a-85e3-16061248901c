"use client";

import React from "react";
import { Users2 } from "lucide-react";

export interface EmptyMembersStateProps {
  message?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export function EmptyMembersState({
  message = "No members found",
  icon: Icon = Users2,
}: EmptyMembersStateProps) {
  return (
    <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <Icon className="size-8 text-muted-foreground" />
      <p className="mt-2 text-sm text-muted-foreground">{message}</p>
    </div>
  );
}
