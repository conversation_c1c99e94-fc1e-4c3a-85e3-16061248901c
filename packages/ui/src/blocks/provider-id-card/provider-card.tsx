"use client";

import { useState } from "react";
import {
  Calendar,
  ChevronDown,
  ChevronUp,
  EyeOff,
  BadgeIcon as IdCard,
  Info,
  Mail,
  MapPin,
  Phone,
  RotateCcw,
  Shield,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/ui/primitives/avatar";
import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Card, CardContent, CardHeader } from "@/ui/primitives/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/ui/primitives/collapsible";
import { Separator } from "@/ui/primitives/separator";
import { Skeleton } from "@/ui/primitives/skeleton";
import FlipCard from "@/ui/shared/flip-card";

export interface ProviderData {
  id: string;
  name: string;
  specialty: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  licenseExpiry: string;
  profileImage?: string;
  status: "active" | "inactive" | "pending";
}

export interface GovernmentID {
  licenseNumber: string;
  issueDate: string;
  expiryDate: string;
  issuingAuthority: string;
  npiNumber: string;
  deaNumber?: string;
  state: string;
  licenseType: string;
}

interface ProviderCardProps {
  provider: ProviderData;
  governmentId?: GovernmentID;
  onIdLoad?: () => Promise<GovernmentID>;
  className?: string;
}

// Default provider data for demonstration
const defaultProvider: ProviderData = {
  id: "prov-001",
  name: "Dr. Sarah Johnson",
  specialty: "Cardiology",
  title: "Senior Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  licenseExpiry: "2025-12-31",
  profileImage: "/placeholder.svg?height=120&width=100",
  status: "active",
};

const defaultGovernmentID: GovernmentID = {
  licenseNumber: "MD-NY-123456789",
  issueDate: "2020-01-15",
  expiryDate: "2025-12-31",
  issuingAuthority: "New York State Department of Health",
  npiNumber: "**********",
  deaNumber: "*********",
  state: "NEW YORK",
  licenseType: "MEDICAL LICENSE",
};

interface GovernmentIDCardProps {
  provider: ProviderData;
  governmentId: GovernmentID;
  showSensitiveData: boolean;
  onToggleSensitiveData: () => void;
}

function GovernmentIDCard({
  provider,
  governmentId,
  showSensitiveData,
  onToggleSensitiveData,
}: GovernmentIDCardProps) {
  const frontContent = (
    <div className="h-full bg-gradient-to-br from-gray-50 to-gray-200 p-4 dark:from-gray-700 dark:to-gray-800">
      {/* Background pattern lines */}
      <div className="absolute inset-0 opacity-10">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute h-px bg-blue-300 dark:bg-blue-400"
            style={{
              top: `${(i + 1) * 5}%`,
              left: "0",
              right: "0",
              transform: `skew(-15deg)`,
            }}
          />
        ))}
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="mb-3 flex items-start justify-between">
          <div>
            <h3 className="text-xs font-bold uppercase tracking-wider text-blue-900 dark:text-blue-100">
              {governmentId.state}
            </h3>
            <p className="text-xs font-semibold text-blue-800 dark:text-blue-200">
              {governmentId.licenseType}
            </p>
          </div>
          <div className="flex size-8 items-center justify-center rounded-full bg-blue-900 dark:bg-blue-600">
            <Shield className="size-4 text-white" />
          </div>
        </div>

        {/* Main Info */}
        <div className="space-y-2">
          <div>
            <p className="text-xs uppercase tracking-wide text-gray-600 dark:text-gray-300">
              Name
            </p>
            <p className="text-sm font-bold text-gray-900 dark:text-gray-100">
              {provider.name}
            </p>
          </div>

          <div>
            <p className="text-xs uppercase tracking-wide text-gray-600 dark:text-gray-300">
              Specialty
            </p>
            <p className="text-sm font-semibold text-gray-800 dark:text-gray-200">
              {provider.specialty}
            </p>
          </div>

          <div className="mt-3 grid grid-cols-2 gap-2">
            <div>
              <p className="text-xs uppercase tracking-wide text-gray-600 dark:text-gray-300">
                Issue
              </p>
              <p className="font-mono text-xs text-gray-800 dark:text-gray-200">
                {governmentId.issueDate}
              </p>
            </div>
            <div>
              <p className="text-xs uppercase tracking-wide text-gray-600 dark:text-gray-300">
                Expires
              </p>
              <p className="font-mono text-xs text-gray-800 dark:text-gray-200">
                {governmentId.expiryDate}
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-4 border-t border-gray-200 pt-2 dark:border-gray-600">
          <p className="text-center text-xs text-gray-500 dark:text-gray-400">
            {governmentId.issuingAuthority}
          </p>
        </div>
      </div>
    </div>
  );

  const backContent = (
    <div className="h-full bg-gradient-to-br from-blue-50 to-blue-200 p-4 dark:from-blue-900/50 dark:to-blue-800/50">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-700" />
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-px bg-blue-300 dark:bg-blue-400"
            style={{
              left: `${(i + 1) * 6.67}%`,
              top: "0",
              bottom: "0",
              transform: `skew(0deg, 15deg)`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 flex h-full flex-col">
        {/* Profile Info */}
        <div className="mb-4 flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Avatar className="size-16 border-2 border-white shadow-lg dark:border-gray-200">
              <AvatarImage
                src={provider.profileImage || "/placeholder.svg"}
                alt={provider.name}
              />
              <AvatarFallback className="bg-blue-100 font-semibold text-blue-600 dark:bg-blue-200 dark:text-blue-800">
                {provider.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-bold text-gray-900 dark:text-gray-100">
                {provider.name}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-300">
                {provider.specialty}
              </p>
            </div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onToggleSensitiveData();
            }}
            className="size-8 rounded-full p-0 hover:bg-blue-100 dark:hover:bg-blue-800/50"
          >
            <Info className="size-4 text-blue-600 dark:text-blue-400" />
          </Button>
        </div>

        {/* Sensitive Information */}
        {showSensitiveData && (
          <div className="space-y-3 rounded-lg border border-blue-200 bg-white/80 p-3 text-xs backdrop-blur-sm dark:border-blue-600 dark:bg-gray-800/80">
            <div className="mb-2 flex items-center gap-2">
              <Shield className="size-3 text-red-500 dark:text-red-400" />
              <span className="font-semibold uppercase tracking-wide text-red-600 dark:text-red-400">
                Sensitive Data
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleSensitiveData();
                }}
                className="ml-auto size-4 p-0"
              >
                <EyeOff className="size-3" />
              </Button>
            </div>

            <div className="space-y-2">
              <div>
                <span className="font-medium text-gray-500 dark:text-gray-400">
                  License #:
                </span>
                <p className="font-mono text-gray-900 dark:text-gray-100">
                  {governmentId.licenseNumber}
                </p>
              </div>
              <div>
                <span className="font-medium text-gray-500 dark:text-gray-400">
                  NPI:
                </span>
                <p className="font-mono text-gray-900 dark:text-gray-100">
                  {governmentId.npiNumber}
                </p>
              </div>
              {governmentId.deaNumber && (
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    DEA:
                  </span>
                  <p className="font-mono text-gray-900 dark:text-gray-100">
                    {governmentId.deaNumber}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-auto">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            <RotateCcw className="size-3" />
            <span>Click to flip back</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <FlipCard
      aspectRatio="card"
      size="md"
      frontContent={frontContent}
      backContent={backContent}
      maxRotation={8}
      perspective={1200}
    />
  );
}

export default function ProviderCard({
  provider = defaultProvider,
  governmentId,
  onIdLoad,
  className,
}: ProviderCardProps) {
  const [isIdVisible, setIsIdVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [idData, setIdData] = useState<GovernmentID | null>(
    governmentId || null,
  );
  const [showSensitiveData, setShowSensitiveData] = useState(false);

  const handleIdToggle = async () => {
    if (!isIdVisible) {
      setIsLoading(true);
      try {
        if (onIdLoad) {
          const data = await onIdLoad();
          setIdData(data);
        } else {
          // Fallback to default data if no loader provided
          await new Promise((resolve) => setTimeout(resolve, 1500));
          setIdData(defaultGovernmentID);
        }
      } catch (error) {
        console.error("Failed to load government ID:", error);
      } finally {
        setIsLoading(false);
      }
    }
    setIsIdVisible(!isIdVisible);
    if (isIdVisible) {
      setShowSensitiveData(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "inactive":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card
      className={`mx-auto w-full max-w-md shadow-lg transition-shadow duration-300 hover:shadow-xl ${className || ""}`}
    >
      <CardHeader className="pb-4">
        <div className="flex items-start gap-4">
          <Avatar className="size-16 border-2 border-blue-100">
            <AvatarImage
              src={provider.profileImage || "/placeholder.svg"}
              alt={provider.name}
            />
            <AvatarFallback className="bg-blue-50 text-lg font-semibold text-blue-600">
              {provider.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>

          <div className="min-w-0 flex-1">
            <div className="flex items-start justify-between gap-2">
              <div>
                <h3 className="truncate text-lg font-semibold text-gray-900">
                  {provider.name}
                </h3>
                <p className="text-sm font-medium text-gray-600">
                  {provider.title}
                </p>
                <p className="text-sm font-medium text-blue-600">
                  {provider.specialty}
                </p>
              </div>
              <Badge
                className={`text-xs font-medium ${getStatusColor(provider.status)}`}
              >
                {provider.status}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Contact Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-3 text-sm">
            <Mail className="size-4 shrink-0 text-gray-400" />
            <span className="truncate text-gray-600">{provider.email}</span>
          </div>

          <div className="flex items-center gap-3 text-sm">
            <Phone className="size-4 shrink-0 text-gray-400" />
            <span className="text-gray-600">{provider.phone}</span>
          </div>

          <div className="flex items-center gap-3 text-sm">
            <MapPin className="size-4 shrink-0 text-gray-400" />
            <span className="text-gray-600">{provider.location}</span>
          </div>

          <div className="flex items-center gap-3 text-sm">
            <Calendar className="size-4 shrink-0 text-gray-400" />
            <span className="text-gray-600">
              License expires: {provider.licenseExpiry}
            </span>
          </div>
        </div>

        <Separator />

        {/* Government ID Section */}
        <Collapsible open={isIdVisible} onOpenChange={setIsIdVisible}>
          <CollapsibleTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between border-blue-200 bg-transparent hover:bg-blue-50"
              onClick={handleIdToggle}
              disabled={isLoading}
            >
              <div className="flex items-center gap-2">
                <IdCard className="size-4" />
                <span>Government ID Information</span>
              </div>
              {isLoading ? (
                <div className="size-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
              ) : isIdVisible ? (
                <ChevronUp className="size-4" />
              ) : (
                <ChevronDown className="size-4" />
              )}
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent className="mt-4">
            <div className="flex flex-col items-center space-y-4">
              {isLoading ? (
                <div className="flex aspect-[1.6/1] w-full max-w-sm items-center justify-center rounded-lg bg-gray-100">
                  <div className="w-full space-y-3 p-6">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-5/6" />
                    <Skeleton className="h-4 w-2/3" />
                  </div>
                </div>
              ) : idData ? (
                <>
                  <GovernmentIDCard
                    provider={provider}
                    governmentId={idData}
                    showSensitiveData={showSensitiveData}
                    onToggleSensitiveData={() =>
                      setShowSensitiveData(!showSensitiveData)
                    }
                  />
                  <p className="text-center text-xs text-gray-500">
                    Click the card to view photo • Click info icon for sensitive
                    data
                  </p>
                </>
              ) : null}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
