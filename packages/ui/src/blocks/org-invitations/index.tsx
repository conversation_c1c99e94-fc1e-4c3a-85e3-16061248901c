"use client";

import type { GenericInvitationWithOriginal } from "./types";

// Import for default export
import { InvitationsList } from "./InvitationsList";

// Export types
export type {
  GenericInvitation,
  GenericInvitationWithOriginal,
  OrganizationInvitationsListProps,
  InvitationCardProps,
} from "./types";

// Export individual components
export { InvitationCard, createInvitationMenu } from "./InvitationCard";
export { EmptyInvitationsState } from "./EmptyState";
export { OrganizationInvitationsListSkeleton } from "./LoadingSkeleton";
export { InvitationsList } from "./InvitationsList";

// Re-export the main component as OrganizationInvitationsList for backwards compatibility
export { InvitationsList as OrganizationInvitationsList } from "./InvitationsList";

// Utility function to create adapters
export function createInvitationAdapter<T>(
  item: T,
  config: {
    getId: (item: T) => string;
    getEmailAddress: (item: T) => string;
    getRole: (item: T) => string;
    getStatus: (item: T) => string;
    getCreatedAt: (item: T) => string;
    getMetadata?: (item: T) => Record<string, unknown>;
  },
): GenericInvitationWithOriginal<T> {
  return {
    id: config.getId(item),
    emailAddress: config.getEmailAddress(item),
    role: config.getRole(item),
    status: config.getStatus(item),
    createdAt: config.getCreatedAt(item),
    metadata: config.getMetadata?.(item),
    originalData: item,
  };
}

// Export default as the main component
export default InvitationsList;
