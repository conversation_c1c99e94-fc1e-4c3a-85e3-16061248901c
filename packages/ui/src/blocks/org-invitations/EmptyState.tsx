"use client";

import React from "react";
import { Mail } from "lucide-react";

export interface EmptyInvitationsStateProps {
  message?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export function EmptyInvitationsState({
  message = "No invitations found",
  icon: Icon = Mail,
}: EmptyInvitationsStateProps) {
  return (
    <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <Icon className="size-8 text-muted-foreground" />
      <p className="mt-2 text-sm text-muted-foreground">{message}</p>
    </div>
  );
}
