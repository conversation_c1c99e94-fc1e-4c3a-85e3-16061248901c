"use client";

import React from "react";

import { PaginatedCardSkeleton } from "../../shared/PaginatedCard";

export interface OrganizationInvitationsListSkeletonProps {
  gridCols?: 1 | 2 | 3 | 4;
  itemCount?: number;
}

export function OrganizationInvitationsListSkeleton({
  gridCols = 2,
  itemCount = 4,
}: OrganizationInvitationsListSkeletonProps) {
  return (
    <PaginatedCardSkeleton
      showIcon
      showActions
      itemHeight="h-32"
      gridCols={gridCols}
    />
  );
}
