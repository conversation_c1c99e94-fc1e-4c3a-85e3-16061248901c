import type { FilterGroup } from "../../search/filter";
import type { PaginationState } from "../types";

// Generic interfaces for flexibility
export interface GenericInvitation {
  id: string;
  emailAddress: string;
  role: string;
  status: string;
  createdAt: string | Date;
  metadata?: Record<string, unknown>;
}

export interface GenericInvitationWithOriginal<T = unknown>
  extends GenericInvitation {
  originalData: T;
}

export interface OrganizationInvitationsListProps<
  T extends GenericInvitation = GenericInvitation,
> {
  // Data
  invitations: T[];
  total: number;
  loading?: boolean;
  error?: Error | null;

  // Configuration
  title?: string;
  description?: string;
  emptyMessage?: string;
  itemNoun?: { singular: string; plural: string };

  // Customization
  renderInvitation?: (invitation: T) => React.ReactNode;
  renderInvitationMenu?: (invitation: T) => React.ReactNode;

  // Actions - should be modal triggers, not forms
  onInviteClick?: () => void;
  inviteButtonLabel?: string;

  // Search and Filtering
  filters?: FilterGroup[];
  searchPlaceholder?: string;
  searchNamespace?: string;

  // Behavior
  pagination?: PaginationState;
  onPaginationChange?: (pagination: PaginationState) => void;
  onInvitationClick?: (invitation: T) => void;

  // Styling
  className?: string;
  gridCols?: 1 | 2 | 3 | 4;
}

// Default invitation card component
export interface InvitationCardProps<
  T extends GenericInvitation = GenericInvitation,
> {
  invitation: T;
  renderMenu?: (invitation: T) => React.ReactNode;
  onClick?: (invitation: T) => void;
  className?: string;
  // Role and status label mappings
  roleLabels?: Record<string, string>;
  statusLabels?: Record<string, string>;
}
