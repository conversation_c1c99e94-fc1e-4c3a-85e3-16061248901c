"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { Clock, Mail, MoreHorizontal } from "lucide-react";

import type { ButtonProps } from "@/ui/primitives/button";

import type { GenericInvitation, InvitationCardProps } from "./types";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";
import { Card, CardContent } from "@/ui/primitives/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/ui/primitives/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/ui/primitives/dropdown-menu";

const defaultRoleLabels = {
  "org:admin": "Admin",
  "org:member": "Member",
  admin: "Admin",
  member: "Member",
  ADMIN: "Admin",
  BILLING: "Billing",
  INTERNAL: "Internal",
  CLIENT: "Client",
  PROVIDER: "Provider",
  NONE: "None",
};

const defaultStatusLabels = {
  pending: "Pending",
  accepted: "Accepted",
  revoked: "Revoked",
  expired: "Expired",
};

// Confirmation dialog component
function ConfirmationDialog({
  open,
  onOpenChange,
  title = "Confirm Action",
  description,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  variant = "primary",
  onConfirm,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  description: string;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: ButtonProps["variant"];
  onConfirm: () => void;
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {cancelLabel}
          </Button>
          <Button
            variant={variant}
            onClick={() => {
              onConfirm();
              onOpenChange(false);
            }}
          >
            {confirmLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Utility function to create standard invitation menu
export function createInvitationMenu<T extends GenericInvitation>(
  actions: {
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
    onClick: (invitation: T) => void;
    variant?: ButtonProps["variant"];
    confirmMessage?: string | ((invitation: T) => string);
    confirmTitle?: string;
    confirmLabel?: string;
  }[],
) {
  return function InvitationMenu(invitation: T) {
    const [confirmDialog, setConfirmDialog] = useState<{
      open: boolean;
      action?: (typeof actions)[0];
      message?: string;
    }>({ open: false });

    const handleActionClick = (action: (typeof actions)[0]) => {
      if (action.confirmMessage) {
        const message =
          typeof action.confirmMessage === "function"
            ? action.confirmMessage(invitation)
            : action.confirmMessage;

        setConfirmDialog({
          open: true,
          action,
          message,
        });
      } else {
        action.onClick(invitation);
      }
    };

    const handleConfirm = () => {
      if (confirmDialog.action) {
        confirmDialog.action.onClick(invitation);
      }
    };

    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="size-8 p-0"
              aria-label="Invitation actions"
            >
              <MoreHorizontal className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {actions.map((action) => (
              <DropdownMenuItem
                key={action.label}
                onClick={() => handleActionClick(action)}
                className={
                  action.variant === "destructive"
                    ? "text-destructive focus:text-destructive"
                    : ""
                }
              >
                {action.icon && <action.icon className="mr-2 size-4" />}
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        <ConfirmationDialog
          open={confirmDialog.open}
          onOpenChange={(open) => setConfirmDialog({ ...confirmDialog, open })}
          title={confirmDialog.action?.confirmTitle || "Confirm Action"}
          description={
            confirmDialog.message || "Are you sure you want to proceed?"
          }
          confirmLabel={confirmDialog.action?.confirmLabel || "Confirm"}
          variant={confirmDialog.action?.variant || "primary"}
          onConfirm={handleConfirm}
        />
      </>
    );
  };
}

export function InvitationCard<
  T extends GenericInvitation = GenericInvitation,
>({
  invitation,
  renderMenu,
  onClick,
  className = "",
  roleLabels = defaultRoleLabels,
  statusLabels = defaultStatusLabels,
}: InvitationCardProps<T>) {
  const getStatusBadgeProps = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return {
          className:
            "bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800",
          variant: "outline" as const,
        };
      case "accepted":
        return {
          className:
            "bg-green-100 text-green-800 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800",
          variant: "outline" as const,
        };
      case "revoked":
      case "expired":
        return {
          className:
            "bg-red-100 text-red-800 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800",
          variant: "outline" as const,
        };
      default:
        return {
          className:
            "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700",
          variant: "outline" as const,
        };
    }
  };

  const getRoleBadgeProps = (role: string) => {
    const isAdmin = role.toLowerCase().includes("admin");
    if (isAdmin) {
      return {
        className:
          "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800 font-medium",
        variant: "outline" as const,
      };
    }
    return {
      className:
        "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800",
      variant: "outline" as const,
    };
  };

  // Handle both string and Date types for createdAt
  const formatCreatedAt = (createdAt: string | Date) => {
    try {
      const date =
        typeof createdAt === "string" ? new Date(createdAt) : createdAt;
      return format(date, "M/d/yyyy");
    } catch (error) {
      console.warn("Invalid date format:", createdAt);
      return "Invalid date";
    }
  };

  const statusBadgeProps = getStatusBadgeProps(invitation.status);
  const roleBadgeProps = getRoleBadgeProps(invitation.role);

  return (
    <Card
      className={`transition-shadow hover:shadow-md ${onClick ? "cursor-pointer" : ""} ${className}`}
      onClick={() => onClick?.(invitation)}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex min-w-0 flex-1 items-center gap-3">
            {/* Avatar/Icon */}
            <div className="shrink-0">
              <div className="flex size-10 items-center justify-center rounded-full bg-muted">
                <Mail className="size-5 text-muted-foreground" />
              </div>
            </div>

            {/* Content */}
            <div className="min-w-0 flex-1">
              <h3
                className="truncate text-sm font-semibold"
                title={invitation.emailAddress}
              >
                {invitation.emailAddress}
              </h3>

              {/* Badges with better contrast */}
              <div className="mt-1 flex items-center gap-2">
                <Badge
                  variant={roleBadgeProps.variant}
                  className={`text-xs font-medium ${roleBadgeProps.className}`}
                >
                  {roleLabels[invitation.role] || invitation.role}
                </Badge>
                <Badge
                  variant={statusBadgeProps.variant}
                  className={`text-xs ${statusBadgeProps.className}`}
                >
                  {statusLabels[invitation.status] || invitation.status}
                </Badge>
              </div>

              {/* Date */}
              <div className="mt-1 flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="size-3" />
                <span>Sent {formatCreatedAt(invitation.createdAt)}</span>
              </div>
            </div>
          </div>

          {/* Menu */}
          {renderMenu && (
            <div className="ml-2 shrink-0">{renderMenu(invitation)}</div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
