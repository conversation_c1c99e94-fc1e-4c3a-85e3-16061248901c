import React from "react";

// Block represents individual timeline items within milestones
export interface Block {
  id: string;
  type: string;
  timestamp: Date;
  data: unknown; // Block-specific data that will be passed to the renderer
}

// Registry item defines how to render each block type
export interface RegistryItem {
  type: string;
  render: (block: Block) => React.ReactNode;
}

// Registry maps block types to their renderers
export type Registry = Record<string, RegistryItem>;

interface TimelineBlockProps {
  block: Block;
  registry: Registry;
  isLast?: boolean;
}

function DefaultBlockRenderer({ block }: { block: Block }) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className="flex size-12 items-center justify-center rounded-full border-2 border-gray-300 bg-gray-100 dark:border-gray-600 dark:bg-gray-800">
          <span className="text-xs text-gray-500 dark:text-gray-400">?</span>
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Unknown Block Type: {block.type}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {block.timestamp.toLocaleTimeString()}
            </span>
          </div>
          {block.data != null && (
            <pre className="mt-2 overflow-x-auto text-xs text-gray-600 dark:text-gray-300">
              {JSON.stringify(block.data, null, 2)}
            </pre>
          )}
        </div>
      </div>
    </div>
  );
}

export function TimelineBlock({
  block,
  registry,
  isLast = false,
}: TimelineBlockProps) {
  const registryItem = registry[block.type];

  return (
    <div className="relative">
      {registryItem ? (
        registryItem.render(block)
      ) : (
        <>
          <DefaultBlockRenderer block={block} />
          {process.env.NODE_ENV === "development" &&
            console.warn(
              `No registry item found for block type: ${block.type}`,
            )}
        </>
      )}
    </div>
  );
}
