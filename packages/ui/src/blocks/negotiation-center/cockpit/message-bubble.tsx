import { Avatar } from "./avatar";

interface MessageBubbleProps {
  name: string;
  avatar: string;
  message: string;
  timestamp?: string;
  isHighlighted?: boolean;
}

export function MessageBubble({
  name,
  avatar,
  message,
  timestamp,
  isHighlighted,
}: MessageBubbleProps) {
  return (
    <div className="relative flex items-start py-3">
      {/* 80px left gutter for avatar, perfectly centered on timeline */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className="relative z-20">
          <Avatar src={avatar} alt={name} />
        </div>
      </div>

      {/* Content area */}
      <div className="min-w-0 flex-1 pr-4">
        <div className="mb-1 flex items-center gap-2">
          <span className="font-semibold text-gray-900">{name}</span>
          {timestamp && (
            <span className="text-sm text-gray-500">{timestamp}</span>
          )}
        </div>
        <div
          className={`${isHighlighted ? "inline-block rounded-lg bg-gray-100 px-3 py-2" : ""}`}
        >
          <p className="text-gray-700">{message}</p>
        </div>
      </div>
    </div>
  );
}
