"use client";

import { cn } from "@/ui/lib";

interface TimelineNavigatorProps {
  milestones: { id: string; title: string; timestamp: string }[];
  activeMilestone: string | null;
  onNavigate: (id: string) => void;
}

export function TimelineNavigator({
  milestones,
  activeMilestone,
  onNavigate,
}: TimelineNavigatorProps) {
  return (
    <div className="fixed right-4 top-1/2 z-50 -translate-y-1/2 rounded-lg bg-white p-2 shadow-lg">
      <div className="flex flex-col gap-2">
        {milestones.map((milestone) => (
          <button
            key={milestone.id}
            onClick={() => onNavigate(milestone.id)}
            className={cn(
              "h-3 w-3 rounded-full transition-all duration-200",
              activeMilestone === milestone.id
                ? "scale-125 bg-teal-600"
                : "bg-gray-300 hover:bg-gray-400",
            )}
            title={milestone.title}
          />
        ))}
      </div>
    </div>
  );
}
