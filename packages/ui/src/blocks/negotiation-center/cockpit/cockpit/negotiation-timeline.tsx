"use client";

import { useState } from "react";

import { MessageBubble } from "../message-bubble";
import { MessageInput } from "../message-input";
import { RateOfferCard } from "../rate-offer-card";
import { TimelineSection } from "../timeline-section";

const milestones = [
  {
    id: "offer-created",
    title: "Offer Created",
    timestamp: "Mar 19: 10:24 AM",
  },
  { id: "contract-created", title: "Contract Created", timestamp: "11:17 AM" },
];

export function NegotiationTimeline() {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(["offer-created", "contract-created"]),
  );

  const handleSendMessage = (message: string) => {
    console.log("Sending message:", message);
  };

  const handleAcceptRate = () => {
    console.log("Rate accepted");
  };

  const handleCounterRate = () => {
    console.log("Counter offer requested");
  };

  const handleToggleSection = (id: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const isExpanded = (id: string) => {
    return expandedSections.has(id);
  };

  return (
    <div className="flex h-full flex-col rounded-lg border border-gray-200 bg-white shadow-sm">
      {/* Fixed Header */}
      <div className="flex shrink-0 items-center justify-between border-b border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900">Negotiation</h2>
        <button className="text-gray-400 hover:text-gray-600">
          <span className="text-lg">⋯</span>
        </button>
      </div>

      {/* Scrollable Timeline Container */}
      <div className="flex-1 overflow-y-auto">
        <div className="relative px-6 py-4">
          {/* Central Timeline Line - 2px wide, properly positioned to start/end at chevron edges */}
          <div className="absolute inset-y-8 left-[62px] z-0 w-[2px] rounded-full bg-gray-200"></div>

          <div className="space-y-0">
            {/* Offer Created Section */}
            <TimelineSection
              id="offer-created"
              title="Offer Created"
              timestamp="Mar 19: 10:24 AM"
              isExpanded={isExpanded("offer-created")}
              onToggle={handleToggleSection}
            >
              <div className="space-y-0">
                <MessageBubble
                  name="Dr. Rose Chen"
                  avatar="/placeholder.svg?height=48&width=48"
                  message="Hi there, thanks for the offer!"
                  timestamp="10:36 AM"
                />

                <RateOfferCard
                  amount="$120"
                  timestamp="Rate Agreed"
                  onAccept={handleAcceptRate}
                  onCounter={handleCounterRate}
                />

                <MessageBubble
                  name="Dr. Mark Chen"
                  avatar="/placeholder.svg?height=48&width=48"
                  message="Sounds good, I'll proceed with drafting the contract."
                  timestamp="11:12 AM"
                />
              </div>
            </TimelineSection>

            {/* Contract Created Section */}
            <TimelineSection
              id="contract-created"
              title="Contract Created"
              timestamp="11:17 AM"
              isExpanded={isExpanded("contract-created")}
              onToggle={handleToggleSection}
            >
              <div className="space-y-0">
                <MessageBubble
                  name="Dr. Rose Chen"
                  avatar="/placeholder.svg?height=48&width=48"
                  message="I've signed the contract."
                  timestamp="11:23 AM"
                />

                <MessageBubble
                  name="Rose Chen"
                  avatar="/placeholder.svg?height=48&width=48"
                  message="Looking forward to working together!"
                  timestamp="11:25 AM"
                />

                <MessageBubble
                  name="Dr. Mark Chen"
                  avatar="/placeholder.svg?height=48&width=48"
                  message="Excellent! Welcome to the team."
                  timestamp="11:30 AM"
                />

                <MessageBubble
                  name="Dr. Rose Chen"
                  avatar="/placeholder.svg?height=48&width=48"
                  message="Thank you! I'm excited to get started."
                  timestamp="11:32 AM"
                />
              </div>
            </TimelineSection>
          </div>
        </div>
      </div>

      {/* Fixed Footer - Message Input */}
      <div className="shrink-0 border-t border-gray-200">
        <MessageInput onSend={handleSendMessage} />
      </div>
    </div>
  );
}
