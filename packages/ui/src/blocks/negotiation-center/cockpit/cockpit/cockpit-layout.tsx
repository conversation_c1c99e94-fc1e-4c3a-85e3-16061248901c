"use client";

import { CommunicationCard } from "./communication-card";
import { JobCard } from "./job-card";
import { NegotiationTimeline } from "./negotiation-timeline";
import { ProviderCard } from "./provider-card";
import { RateInsightsPanel } from "./rate-insights-panel";
import { UrgencyCard } from "./urgency-card";

export function CockpitLayout() {
  return (
    <div className="flex h-screen flex-col bg-gray-50 p-4 lg:p-6">
      <div className="mx-auto flex max-w-7xl flex-1 flex-col">
        {/* Mobile Layout */}
        <div className="flex flex-1 flex-col space-y-4 lg:hidden">
          {/* Mobile Header Cards */}
          <div className="grid shrink-0 grid-cols-1 gap-4 sm:grid-cols-2">
            <ProviderCard
              name="Dr. <PERSON>"
              avatar="/placeholder.svg?height=48&width=48"
              specialty="Emergency Medicine"
              experience="10 years"
            />
            <RateInsightsPanel
              currentRate={120}
              comparison="Above average"
              demandLevel="medium"
            />
          </div>

          {/* Mobile Timeline - Takes remaining height */}
          <div className="min-h-0 flex-1">
            <NegotiationTimeline />
          </div>

          {/* Mobile Bottom Cards */}
          <div className="grid shrink-0 grid-cols-1 gap-4 sm:grid-cols-3">
            <JobCard
              specialty="Emergency Medicine"
              location="Atlanta, GA"
              schedule="7:00 AM — 7:00 PM"
            />
            <CommunicationCard responseTime="about an hour" />
            <UrgencyCard startTime="Start in 2 days" />
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden flex-1 gap-6 lg:grid lg:grid-cols-12">
          {/* Left Sidebar */}
          <div className="col-span-3 space-y-4 overflow-y-auto">
            <ProviderCard
              name="Dr. Rose Chen"
              avatar="/placeholder.svg?height=48&width=48"
              specialty="Emergency Medicine"
              experience="10 years"
            />

            <JobCard
              specialty="Emergency Medicine"
              location="Atlanta, GA"
              schedule="7:00 AM — 7:00 PM"
            />

            <CommunicationCard responseTime="about an hour" />

            <UrgencyCard startTime="Start in 2 days" />
          </div>

          {/* Center Panel - Negotiation Timeline */}
          <div className="col-span-6 min-h-0">
            <NegotiationTimeline />
          </div>

          {/* Right Sidebar */}
          <div className="col-span-3 overflow-y-auto">
            <RateInsightsPanel
              currentRate={120}
              comparison="Above average"
              demandLevel="medium"
            />
          </div>
        </div>

        {/* Tablet Layout */}
        <div className="hidden flex-1 gap-4 md:grid md:grid-cols-8 lg:hidden">
          {/* Left Column */}
          <div className="col-span-3 space-y-4 overflow-y-auto">
            <ProviderCard
              name="Dr. Rose Chen"
              avatar="/placeholder.svg?height=48&width=48"
              specialty="Emergency Medicine"
              experience="10 years"
            />

            <div className="grid grid-cols-1 gap-4">
              <JobCard
                specialty="Emergency Medicine"
                location="Atlanta, GA"
                schedule="7:00 AM — 7:00 PM"
              />
              <CommunicationCard responseTime="about an hour" />
              <UrgencyCard startTime="Start in 2 days" />
            </div>
          </div>

          {/* Right Column */}
          <div className="col-span-5 flex min-h-0 flex-col space-y-4">
            <div className="min-h-0 flex-1">
              <NegotiationTimeline />
            </div>
            <div className="shrink-0">
              <RateInsightsPanel
                currentRate={120}
                comparison="Above average"
                demandLevel="medium"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
