interface RateInsightsPanelProps {
  currentRate: number;
  comparison: string;
  demandLevel: "low" | "medium" | "high";
}

export function RateInsightsPanel({
  currentRate,
  comparison,
  demandLevel,
}: RateInsightsPanelProps) {
  const getDemandPosition = () => {
    switch (demandLevel) {
      case "low":
        return "20%";
      case "medium":
        return "50%";
      case "high":
        return "80%";
      default:
        return "50%";
    }
  };

  return (
    <div className="h-fit rounded-lg border border-gray-200 bg-white p-4 shadow-sm lg:p-6">
      <div className="mb-4 flex items-center justify-between lg:mb-6">
        <h2 className="text-base font-semibold text-gray-900 lg:text-lg">
          Rate insights
        </h2>
        <button className="text-gray-400 hover:text-gray-600">
          <span className="text-lg">⋯</span>
        </button>
      </div>

      {/* Rate Chart */}
      <div className="mb-4 lg:mb-6">
        <div className="mb-4 rounded-lg bg-teal-50 p-3 lg:p-4">
          <div className="flex h-16 items-end gap-1 lg:h-20 lg:gap-2">
            <div className="h-8 w-6 rounded-sm bg-teal-200 lg:h-12 lg:w-8"></div>
            <div className="h-12 w-6 rounded-sm bg-teal-300 lg:h-16 lg:w-8"></div>
            <div className="h-16 w-6 rounded-sm bg-teal-400 lg:h-20 lg:w-8"></div>
            <div className="h-10 w-6 rounded-sm bg-teal-500 lg:h-14 lg:w-8"></div>
          </div>
        </div>

        <div className="text-center">
          <div className="text-xl font-bold text-gray-900 lg:text-2xl">
            ${currentRate}{" "}
            <span className="text-sm font-normal text-gray-600">per hour</span>
          </div>
          <div className="text-sm text-gray-600">{comparison}</div>
        </div>
      </div>

      {/* Demand Gauge */}
      <div className="mb-4 lg:mb-6">
        <h3 className="mb-3 font-semibold text-gray-900">Demand</h3>
        <div className="relative">
          <div className="h-2 w-full rounded-full bg-gray-200">
            <div
              className="h-2 rounded-full bg-teal-500"
              style={{ width: "70%" }}
            ></div>
          </div>
          <div
            className="absolute top-0 size-3 -translate-x-1.5 -translate-y-0.5 rounded-full bg-teal-600"
            style={{ left: getDemandPosition() }}
          ></div>
          <div className="mt-1 flex justify-between text-xs text-gray-500">
            <span>Low</span>
            <span>High</span>
          </div>
        </div>
      </div>

      {/* Action Items */}
      <div>
        <h3 className="mb-3 font-semibold text-teal-600">Action</h3>
        <div className="space-y-2">
          <div className="h-3 rounded-full bg-gray-200"></div>
          <div className="h-3 w-3/4 rounded-full bg-gray-200"></div>
        </div>
      </div>
    </div>
  );
}
