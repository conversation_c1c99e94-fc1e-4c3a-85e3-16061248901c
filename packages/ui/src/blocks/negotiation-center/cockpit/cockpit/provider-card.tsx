import { ChevronDown } from "lucide-react";

import { Avatar } from "../avatar";

interface ProviderCardProps {
  name: string;
  avatar: string;
  specialty: string;
  experience: string;
}

export function ProviderCard({
  name,
  avatar,
  specialty,
  experience,
}: ProviderCardProps) {
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <div className="mb-4 flex items-center gap-2">
        <div className="flex size-6 items-center justify-center rounded-full bg-teal-600">
          <span className="text-xs font-bold text-white">—</span>
        </div>
        <span className="font-semibold text-gray-900">Provider</span>
      </div>

      <div className="mb-3 flex items-center gap-3">
        <Avatar src={avatar} alt={name} size="md" />
        <div>
          <h3 className="font-semibold text-gray-900">{name}</h3>
        </div>
      </div>

      <div className="mb-4 space-y-1">
        <p className="text-gray-700">{specialty}</p>
        <p className="text-gray-700">{experience}</p>
      </div>

      <button className="flex w-full items-center justify-between rounded-lg bg-gray-50 px-3 py-2 transition-colors hover:bg-gray-100">
        <span className="text-gray-700">View profile</span>
        <ChevronDown className="size-4 text-gray-500" />
      </button>
    </div>
  );
}
