interface CommunicationCardProps {
  responseTime: string;
}

export function CommunicationCard({ responseTime }: CommunicationCardProps) {
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <div className="mb-4 flex items-center gap-2">
        <div className="flex size-6 items-center justify-center rounded-full bg-teal-600">
          <span className="text-xs text-white">💬</span>
        </div>
        <span className="font-semibold text-gray-900">Communication</span>
      </div>

      <div className="space-y-2">
        <p className="font-medium text-gray-700">Response time</p>
        <p className="text-gray-900">{responseTime}</p>
      </div>
    </div>
  );
}
