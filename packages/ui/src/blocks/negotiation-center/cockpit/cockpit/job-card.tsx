interface JobCardProps {
  specialty: string;
  location: string;
  schedule: string;
}

export function JobCard({ specialty, location, schedule }: JobCardProps) {
  return (
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <div className="mb-4 flex items-center gap-2">
        <div className="flex size-6 items-center justify-center rounded-full bg-teal-600">
          <span className="text-xs font-bold text-white">—</span>
        </div>
        <span className="font-semibold text-gray-900">Job</span>
      </div>

      <div className="mb-4 space-y-2">
        <h3 className="font-semibold text-gray-900">{specialty}</h3>
        <p className="text-gray-700">{location}</p>
        <p className="text-gray-700">{schedule}</p>
      </div>

      <button className="font-medium text-teal-600 hover:text-teal-700">
        View full details
      </button>
    </div>
  );
}
