import { cn } from "@/ui/lib";

interface AvatarProps {
  src: string;
  alt: string;
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function Avatar({ src, alt, size = "md", className }: AvatarProps) {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };

  return (
    <div
      className={cn(
        "flex-shrink-0 overflow-hidden rounded-full",
        sizeClasses[size],
        className,
      )}
    >
      <img
        src={src || "/placeholder.svg"}
        alt={alt}
        className="size-full object-cover"
      />
    </div>
  );
}
