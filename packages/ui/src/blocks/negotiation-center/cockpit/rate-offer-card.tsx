"use client";

import { But<PERSON> } from "@/ui/primitives/button";

interface RateOfferCardProps {
  amount: string;
  timestamp: string;
  onAccept?: () => void;
  onCounter?: () => void;
}

export function RateOfferCard({
  amount,
  timestamp,
  onAccept,
  onCounter,
}: RateOfferCardProps) {
  return (
    <div className="relative py-4">
      {/* Card positioned to overlap timeline, starting at 60px to create elegant overlap */}
      <div className="relative z-20 ml-[60px] mr-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-start justify-between">
          <h3 className="font-semibold text-gray-900">{timestamp}</h3>
          <span className="text-sm text-gray-500">6 hours ago</span>
        </div>

        <div className="mb-6">
          <span className="text-3xl font-bold text-gray-900">{amount}</span>
          <span className="text-gray-600">/hr</span>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            className="flex-1 border-teal-600 bg-transparent text-teal-600 hover:bg-teal-50"
            onClick={onAccept}
          >
            Accept
          </Button>
          <Button
            className="flex-1 bg-teal-600 hover:bg-teal-700"
            onClick={onCounter}
          >
            Counter
          </Button>
        </div>
      </div>
    </div>
  );
}
