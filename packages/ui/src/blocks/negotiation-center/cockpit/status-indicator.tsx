import { Check } from "lucide-react";

import { cn } from "@/ui/lib";

interface StatusIndicatorProps {
  title: string;
  timestamp: string;
  variant?: "success" | "info";
  className?: string;
}

export function StatusIndicator({
  title,
  timestamp,
  variant = "success",
  className,
}: StatusIndicatorProps) {
  return (
    <div
      className={cn(
        "relative z-10 flex items-center justify-between rounded-lg p-4",
        variant === "success" ? "bg-teal-100" : "bg-blue-100",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <div
          className={cn(
            "relative z-20 flex h-8 w-8 items-center justify-center rounded-full",
            variant === "success" ? "bg-teal-600" : "bg-blue-600",
          )}
        >
          <Check className="size-4 text-white" />
        </div>
        <span className="font-semibold text-gray-900">{title}</span>
      </div>
      <span className="font-medium text-teal-600">{timestamp}</span>
    </div>
  );
}
