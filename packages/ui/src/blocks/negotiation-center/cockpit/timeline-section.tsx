"use client";

import type React from "react";

import { ChevronDown, ChevronRight } from "lucide-react";

import { cn } from "@/ui/lib";

interface TimelineSectionProps {
  id: string;
  title: string;
  timestamp: string;
  children: React.ReactNode;
  isExpanded: boolean;
  onToggle?: (id: string) => void;
}

export function TimelineSection({
  id,
  title,
  timestamp,
  children,
  isExpanded,
  onToggle,
}: TimelineSectionProps) {
  return (
    <div className="relative">
      {/* Milestone Header with 80px left gutter system */}
      <div className="relative flex items-center">
        {/* Chevron icon positioned in the 80px gutter, perfectly centered */}
        <div className="flex w-20 shrink-0 items-center justify-center">
          <div className="relative z-20 flex size-12 items-center justify-center rounded-full bg-teal-600">
            {isExpanded ? (
              <ChevronDown className="size-5 text-background" />
            ) : (
              <ChevronRight className="size-5 text-background" />
            )}
          </div>
        </div>

        {/* Milestone content with background - only spans the content area */}
        <div
          className={cn(
            "relative z-10 mr-4 flex flex-1 cursor-pointer items-center justify-between rounded-lg p-4 transition-all duration-300",
            "bg-teal-100 hover:bg-teal-200",
          )}
          onClick={() => onToggle?.(id)}
        >
          <div className="flex items-center gap-3">
            <span className="font-semibold text-gray-900">{title}</span>
          </div>
          <span className="font-medium text-teal-600">{timestamp}</span>
        </div>
      </div>

      {/* Collapsible Content */}
      <div
        className={cn(
          "overflow-hidden transition-all duration-500 ease-in-out",
          isExpanded ? "max-h-[2000px] opacity-100" : "max-h-0 opacity-0",
        )}
      >
        {/* Section Content */}
        <div className="relative z-10">{children}</div>
      </div>
    </div>
  );
}
