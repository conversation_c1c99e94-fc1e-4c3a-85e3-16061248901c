"use client";

import { useState } from "react";

import { MessageBubble } from "./message-bubble";
import { MessageInput } from "./message-input";
import { RateOfferCard } from "./rate-offer-card";
import { TimelineSection } from "./timeline-section";

// const milestones = [
//   { id: "rate-negotiated", title: "Rate Negotiated", timestamp: "2 hours ago" },
//   {
//     id: "contract-created-1",
//     title: "Contract Created",
//     timestamp: "6 hours ago",
//   },
//   {
//     id: "contract-created-2",
//     title: "Contract Created",
//     timestamp: "6 hours ago",
//   },
// ];

export function ChatInterface() {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(["rate-negotiated"]),
  );

  const handleSendMessage = (message: string) => {
    console.log("Sending message:", message);
  };

  const handleAcceptRate = () => {
    console.log("Rate accepted");
  };

  const handleCounterRate = () => {
    console.log("Counter offer requested");
  };

  const handleToggleSection = (id: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const isExpanded = (id: string) => {
    return expandedSections.has(id);
  };

  return (
    <div className="mx-auto max-h-[80vh] max-w-2xl overflow-hidden overflow-y-auto rounded-2xl bg-white shadow-lg">
      {/* Timeline Container with 80px left gutter */}
      <div className="relative">
        {/* Central Timeline Line - 2px wide, starts and ends at avatar/chevron edges */}
        <div className="absolute inset-y-6 left-[40px] z-0 w-[2px] rounded-full bg-gray-200"></div>

        <div className="space-y-0">
          {/* Rate Negotiated Section */}
          <TimelineSection
            id="rate-negotiated"
            title="Rate Negotiated"
            timestamp="2 hours ago"
            isExpanded={isExpanded("rate-negotiated")}
            onToggle={handleToggleSection}
          >
            <div className="space-y-0">
              <MessageBubble
                name="Dr. Mark Chen"
                avatar="/placeholder.svg?height=48&width=48"
                message="agreed to $120.00/hr"
              />

              <MessageBubble
                name="Dr. Mark Chen"
                avatar="/placeholder.svg?height=48&width=48"
                message="Let's proceed with these terms."
                isHighlighted={true}
              />

              <MessageBubble
                name="Dr. Emily Carter"
                avatar="/placeholder.svg?height=48&width=48"
                message="Sounds good."
                timestamp="3 hours ago"
              />

              <RateOfferCard
                amount="$120.00"
                timestamp="6 hours ago"
                onAccept={handleAcceptRate}
                onCounter={handleCounterRate}
              />
            </div>
          </TimelineSection>

          {/* Contract Created Section 1 */}
          <TimelineSection
            id="contract-created-1"
            title="Contract Created"
            timestamp="6 hours ago"
            isExpanded={isExpanded("contract-created-1")}
            onToggle={handleToggleSection}
          >
            <div className="space-y-0">
              <div className="flex items-start py-4">
                <div className="flex w-20 justify-center">
                  <div className="flex size-12 items-center justify-center rounded-full bg-gray-100">
                    <span className="text-xs text-gray-500">OH</span>
                  </div>
                </div>
                <div className="flex-1">
                  <p className="text-gray-700">Ohio Health created</p>
                </div>
              </div>

              <MessageBubble
                name="Dr. Mark Chen"
                avatar="/placeholder.svg?height=48&width=48"
                message="Contract signed"
                timestamp="4 hours ago"
              />
            </div>
          </TimelineSection>

          {/* Contract Created Section 2 */}
          <TimelineSection
            id="contract-created-2"
            title="Contract Created"
            timestamp="6 hours ago"
            isExpanded={isExpanded("contract-created-2")}
            onToggle={handleToggleSection}
          >
            <div className="space-y-0">
              <div className="flex items-start py-4">
                <div className="flex w-20 justify-center">
                  <div className="flex size-12 items-center justify-center rounded-full bg-gray-100">
                    <span className="text-xs text-gray-500">📄</span>
                  </div>
                </div>
                <div className="flex-1">
                  <p className="text-gray-700">
                    Additional contract details...
                  </p>
                </div>
              </div>
            </div>
          </TimelineSection>
        </div>
      </div>

      {/* Message Input */}
      <MessageInput onSend={handleSendMessage} />
    </div>
  );
}
