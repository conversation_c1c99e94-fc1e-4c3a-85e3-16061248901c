import type { VariantProps } from "class-variance-authority";

import React, { useState } from "react";
import { cva } from "class-variance-authority";

import type { Block, Registry, RegistryItem } from "./timeline-block";
import type { Milestone } from "./timeline-milestone";

import { TimelineInput } from "./timeline-input";
import { TimelineMilestone } from "./timeline-milestone";

// Timeline container variants
const timelineVariants = cva(
  "mx-auto max-h-[80vh] max-w-2xl overflow-hidden overflow-y-auto rounded-2xl",
  {
    variants: {
      variant: {
        primary: "bg-background shadow-lg",
        ghost: "bg-transparent",
        outline: "border border-border bg-background",
      },
    },
    defaultVariants: {
      variant: "primary",
    },
  },
);

// Main component props
interface NegotiationTimelineProps
  extends VariantProps<typeof timelineVariants> {
  milestones: Milestone[];
  registry: Registry;
  defaultExpanded?: boolean;
  showMessageInput?: boolean;
  onSendMessage?: (message: string) => void;
}

export function NegotiationTimeline({
  milestones,
  registry,
  variant,
  defaultExpanded = true,
  showMessageInput = true,
  onSendMessage,
}: NegotiationTimelineProps) {
  const [expandedMilestones, setExpandedMilestones] = useState<Set<string>>(
    new Set(defaultExpanded ? milestones.map((m) => m.id) : []),
  );

  const toggleMilestone = (milestoneId: string) => {
    const newExpanded = new Set(expandedMilestones);
    if (newExpanded.has(milestoneId)) {
      newExpanded.delete(milestoneId);
    } else {
      newExpanded.add(milestoneId);
    }
    setExpandedMilestones(newExpanded);
  };

  return (
    <div className={timelineVariants({ variant })}>
      {/* Timeline Container with 80px left gutter */}
      <div className="relative">
        {/* Central Timeline Line - 2px wide, starts and ends at avatar/chevron edges */}
        <div className="absolute inset-y-6 left-[40px] z-0 w-[2px] rounded-full bg-border"></div>

        {/* Content container */}
        <div className="grid grid-cols-1 gap-2 p-2">
          {milestones.map((milestone, index) => (
            <TimelineMilestone
              key={milestone.id}
              milestone={milestone}
              registry={registry}
              isExpanded={expandedMilestones.has(milestone.id)}
              onToggle={toggleMilestone}
              isFirst={index === 0}
              isLast={index === milestones.length - 1}
            />
          ))}
        </div>
      </div>

      {/* Message input area */}
      {showMessageInput && (
        <TimelineInput onSend={onSendMessage} disabled={!onSendMessage} />
      )}
    </div>
  );
}

// Export types for external use
export type {
  Block,
  Milestone,
  RegistryItem,
  Registry,
  NegotiationTimelineProps,
};

// Export the timelineVariants for external use if needed
export { timelineVariants };
