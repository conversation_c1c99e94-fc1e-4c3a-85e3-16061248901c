import { CheckCircle } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";

interface RateNegotiatedBlockData {
  doctor: string;
  rate: string;
  message?: string;
}

interface RateNegotiatedBlockProps {
  data: RateNegotiatedBlockData;
}

export function RateNegotiatedBlock({ data }: RateNegotiatedBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className="flex size-12 items-center justify-center rounded-full bg-green-500">
          <CheckCircle className="size-6 text-white" />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-900/20">
          <div className="flex flex-wrap items-center gap-2">
            <p className="font-semibold text-green-800 dark:text-green-400">
              {data.doctor} agreed to {data.rate}
            </p>
            <Badge
              variant="secondary"
              className="bg-green-100 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-400"
            >
              Completed
            </Badge>
          </div>
          {data.message && (
            <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
              {data.message}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export type { RateNegotiatedBlockData, RateNegotiatedBlockProps };
