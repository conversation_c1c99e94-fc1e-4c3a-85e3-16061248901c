import { CheckCircle } from "lucide-react";

import { Card } from "@/ui/primitives/card";

interface ContractSignedBlockData {
  signer: string;
  signedAt?: Date;
}

interface ContractSignedBlockProps {
  data: ContractSignedBlockData;
}

export function ContractSignedBlock({ data }: ContractSignedBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className="flex size-12 items-center justify-center rounded-full bg-gray-400">
          <CheckCircle className="size-6 text-white" />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800/50">
          <div className="flex-1">
            <p className="text-gray-700 dark:text-gray-300">Contract signed</p>
            {data.signedAt && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Signed {data.signedAt.toLocaleDateString()}
              </p>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}

export type { ContractSignedBlockData, ContractSignedBlockProps };
