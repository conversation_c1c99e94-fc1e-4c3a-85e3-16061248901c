import { FileText } from "lucide-react";

import { Card } from "@/ui/primitives/card";

interface ContractCreatedBlockData {
  organization: string;
  status: string;
  contractId?: string;
}

interface ContractCreatedBlockProps {
  data: ContractCreatedBlockData;
}

export function ContractCreatedBlock({ data }: ContractCreatedBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className="flex size-12 items-center justify-center rounded-full bg-purple-500">
          <FileText className="size-6 text-white" />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <Card className="border-purple-200 bg-purple-50 p-4 dark:border-purple-800 dark:bg-purple-900/20">
          <div className="flex-1">
            <p className="font-semibold text-purple-800 dark:text-purple-400">
              Contract Created
            </p>
            <p className="text-gray-600 dark:text-gray-400">
              {data.organization} created
            </p>
            {data.contractId && (
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Contract ID: {data.contractId}
              </p>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}

export type { ContractCreatedBlockData, ContractCreatedBlockProps };
