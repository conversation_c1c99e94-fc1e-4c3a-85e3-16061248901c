import { DollarSign } from "lucide-react";

import { Badge } from "@/ui/primitives/badge";
import { Button } from "@/ui/primitives/button";

interface RateOfferBlockData {
  rate: string;
  status: "pending" | "accepted" | "countered";
  onAccept?: () => void;
  onCounter?: () => void;
}

interface RateOfferBlockProps {
  data: RateOfferBlockData;
}

export function RateOfferBlock({ data }: RateOfferBlockProps) {
  return (
    <div className="flex items-start py-4">
      {/* 80px left gutter with centered icon */}
      <div className="flex w-20 shrink-0 items-center justify-center">
        <div className="flex size-12 items-center justify-center rounded-full bg-green-500">
          <DollarSign className="size-6 text-white" />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 pr-4">
        <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
          <div className="text-center">
            <div className="mb-2 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Rate Offer
              </h4>
              {data.status !== "pending" && (
                <Badge
                  variant={data.status === "accepted" ? "default" : "secondary"}
                  className={
                    data.status === "accepted"
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                  }
                >
                  {data.status === "accepted" ? "Accepted" : "Countered"}
                </Badge>
              )}
            </div>

            <div className="mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100">
              {data.rate}
            </div>

            {data.status === "pending" && (
              <div className="flex justify-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-teal-300 text-teal-700 hover:bg-teal-50 dark:border-teal-600 dark:text-teal-400 dark:hover:bg-teal-900/30"
                  onClick={data.onAccept}
                >
                  Accept
                </Button>
                <Button
                  size="sm"
                  className="bg-teal-600 hover:bg-teal-700 dark:bg-teal-700 dark:hover:bg-teal-600"
                  onClick={data.onCounter}
                >
                  Counter
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export type { RateOfferBlockData, RateOfferBlockProps };
