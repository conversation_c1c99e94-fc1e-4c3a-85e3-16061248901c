"use client";

import type React from "react";

import { useState } from "react";

import { But<PERSON> } from "@/ui/primitives/button";
import { Input } from "@/ui/primitives/input";

interface TimelineInputProps {
  onSend?: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function TimelineInput({
  onSend,
  placeholder = "Write a message...",
}: TimelineInputProps) {
  const [message, setMessage] = useState("");

  const handleSend = () => {
    if (message.trim() && onSend) {
      onSend(message);
      setMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSend();
    }
  };

  return (
    <div className="flex gap-3 p-4">
      <Input
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyPress={handleKeyPress}
        placeholder={placeholder}
        className="flex-1"
      />
      <Button onClick={handleSend} className="bg-teal-600 hover:bg-teal-700">
        Send
      </Button>
    </div>
  );
}
