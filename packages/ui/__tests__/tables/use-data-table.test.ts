import { renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { useDataTable } from "../../src/tables";

// Sample data for testing
interface TestData {
  id: number;
  name: string;
  age: number;
}

const testData: TestData[] = [
  { id: 1, name: "<PERSON>", age: 30 },
  { id: 2, name: "<PERSON>", age: 25 },
  { id: 3, name: "<PERSON>", age: 40 },
];

// Sample column definitions
const testColumns = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "age",
    header: "Age",
  },
];

describe("useDataTable hook", () => {
  it("should initialize with provided data", () => {
    const { result } = renderHook(() =>
      useDataTable({
        columns: testColumns,
        data: testData,
      }),
    );

    expect(result.current.table).toBeDefined();
    expect(result.current.table.getRowModel().rows).toHaveLength(
      testData.length,
    );
  });

  it("should handle pagination", () => {
    const { result } = renderHook(() =>
      useDataTable({
        columns: testColumns,
        data: testData,
        pagination: { pageIndex: 0, pageSize: 2 },
      }),
    );

    // With pageSize 2, we should only see 2 rows
    const paginatedRows = result.current.table.getRowModel().rows;
    expect(paginatedRows).toHaveLength(2);
    expect(paginatedRows[0]?.original).toEqual(testData[0]);
  });

  it("should handle sorting", () => {
    const setPaginationMock = vi.fn();

    const { result } = renderHook(() =>
      useDataTable({
        columns: testColumns,
        data: testData,
        pagination: { pageIndex: 0, pageSize: 10 },
        setPagination: setPaginationMock,
      }),
    );

    // Get the column to sort
    const nameColumn = result.current.table.getColumn("name");
    expect(nameColumn).toBeDefined();

    // Toggle sorting
    if (nameColumn) {
      nameColumn.toggleSorting(true);
    }

    // Check sorting state was updated
    const sortingState = result.current.table.getState().sorting;
    expect(sortingState).toHaveLength(1);
    expect(sortingState[0]?.id).toEqual("name");
  });

  it("should handle row selection", () => {
    const { result } = renderHook(() =>
      useDataTable({
        columns: testColumns,
        data: testData,
      }),
    );

    // Select first row
    const firstRow = result.current.table.getRowModel().rows[0];
    firstRow?.toggleSelected(true);

    // Check selection state
    expect(firstRow?.getIsSelected()).toBe(true);
    expect(result.current.table.getSelectedRowModel().rows).toHaveLength(1);
  });
});
