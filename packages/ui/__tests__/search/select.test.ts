import type { ReactNode } from "react";

import React from "react";
import { act, renderHook, waitFor } from "@testing-library/react";
import mockRouter from "next-router-mock";
import { beforeEach, describe, expect, it, vi } from "vitest";

import SearchParams from "@/ui/search/SearchParams";
import {
  SEARCH_SELECT_NAME,
  useSearchSelect,
  useSearchSelectValue,
} from "@/ui/search/select";

// Test wrapper with real SearchParams provider
function TestWrapper({ children }: { children: ReactNode }) {
  return React.createElement(SearchParams, null, children);
}

describe("Search Select Hooks", () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Reset router to clean state
    await mockRouter.push("/test-page");
  });

  describe("useSearchSelectValue", () => {
    it("should return undefined when no search params are present", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchSelectValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBeUndefined();
    });

    it("should return value from search params", async () => {
      await mockRouter.push("/test-page?select=option1");

      const { result } = renderHook(() => useSearchSelectValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBe("option1");
    });

    it("should handle grouped select with custom name", async () => {
      await mockRouter.push("/test-page?customSelect-users=user1");

      const { result } = renderHook(
        () => useSearchSelectValue("users", "customSelect"),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current).toBe("user1");
    });

    it("should handle numeric values", async () => {
      await mockRouter.push("/test-page?select=123");

      const { result } = renderHook(() => useSearchSelectValue<number>(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBe("123"); // Note: searchParams values are always strings
    });
  });

  describe("useSearchSelect", () => {
    it("should return default value when no search params", async () => {
      await mockRouter.push("/test-page");

      const defaultValue = "default-option";
      const { result } = renderHook(
        () => useSearchSelect(undefined, SEARCH_SELECT_NAME, defaultValue),
        {
          wrapper: TestWrapper,
        },
      );

      const [value] = result.current;
      expect(value).toBe(defaultValue);
    });

    it("should return value from search params over default", async () => {
      await mockRouter.push("/test-page?select=url-option");

      const defaultValue = "default-option";
      const { result } = renderHook(
        () => useSearchSelect(undefined, SEARCH_SELECT_NAME, defaultValue),
        {
          wrapper: TestWrapper,
        },
      );

      const [value] = result.current;
      expect(value).toBe("url-option");
    });

    it("should handle value changes and call onChange", async () => {
      await mockRouter.push("/test-page");

      const onChange = vi.fn();
      const { result } = renderHook(
        () => useSearchSelect("users", "testSelect", undefined, onChange),
        {
          wrapper: TestWrapper,
        },
      );

      const [, handleValueChange] = result.current;

      await act(async () => {
        await handleValueChange("new-value");
      });

      expect(onChange).toHaveBeenCalledWith("new-value");

      // Wait for URL update
      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: "/test-page?testSelect-users=new-value",
          pathname: "/test-page",
          query: {
            "testSelect-users": "new-value",
          },
        });
      });
    });

    it("should handle debounced value updates", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () => useSearchSelect("products", "productSelect"),
        {
          wrapper: TestWrapper,
        },
      );

      const [, handleValueChange] = result.current;

      // Make rapid changes
      await act(async () => {
        await handleValueChange("value1");
        await handleValueChange("value2");
        await handleValueChange("final-value");
      });

      // Wait for debounce and URL update
      await waitFor(
        () => {
          expect(mockRouter).toMatchObject({
            asPath: "/test-page?productSelect-products=final-value",
            pathname: "/test-page",
            query: {
              "productSelect-products": "final-value",
            },
          });
        },
        { timeout: 1000 },
      );
    });
  });

  describe("Constants", () => {
    it("should have expected constant values", () => {
      expect(SEARCH_SELECT_NAME).toBe("select");
    });
  });
});
