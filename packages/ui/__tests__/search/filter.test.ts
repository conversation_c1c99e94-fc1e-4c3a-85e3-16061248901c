import type { ReactNode } from "react";

import React from "react";
import { act, renderHook, waitFor } from "@testing-library/react";
import mockRouter from "next-router-mock";
import { beforeEach, describe, expect, it, vi } from "vitest";

import type { FilterGroup } from "@/ui/search/filter";

import {
  SEARCH_FILTER_NAME,
  useSearchFilter,
  useSearchFilterValue,
} from "@/ui/search/filter";
import SearchParams from "@/ui/search/SearchParams";

// Test wrapper with real SearchParams provider
function TestWrapper({ children }: { children: ReactNode }) {
  return React.createElement(SearchParams, null, children);
}

describe("Search Filter Hooks", () => {
  const mockFilterGroups: FilterGroup[] = [
    {
      id: "status",
      label: "Status",
      options: [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
        { value: null, label: "All" },
      ],
    },
    {
      id: "category",
      label: "Category",
      options: [
        { value: "tech", label: "Technology" },
        { value: "business", label: "Business" },
        { value: "design", label: "Design" },
      ],
    },
  ];

  beforeEach(async () => {
    vi.clearAllMocks();
    // Reset router to clean state
    await mockRouter.push("/test-page");
  });

  describe("useSearchFilterValue", () => {
    it("should return undefined when no search params are present", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchFilterValue("status"), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBeUndefined();
    });

    it("should return value from search params", async () => {
      await mockRouter.push("/test-page?filter-status=active");

      const { result } = renderHook(() => useSearchFilterValue("status"), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBe("active");
    });

    it("should handle custom filter name", async () => {
      await mockRouter.push("/test-page?customFilter-category=tech");

      const { result } = renderHook(
        () => useSearchFilterValue("category", "customFilter"),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current).toBe("tech");
    });

    it("should handle numeric values", async () => {
      await mockRouter.push("/test-page?filter-priority=1");

      const { result } = renderHook(
        () => useSearchFilterValue<number>("priority"),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current).toBe("1"); // Note: searchParams values are always strings
    });
  });

  describe("useSearchFilter", () => {
    it("should return filter functions", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () =>
          useSearchFilter({
            groups: mockFilterGroups,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      expect(typeof result.current.handleValueChange).toBe("function");
      expect(typeof result.current.getFilterValue).toBe("function");
    });

    it("should handle value changes and update URL", async () => {
      await mockRouter.push("/test-page");

      const onChange = vi.fn();
      const { result } = renderHook(
        () =>
          useSearchFilter({
            groups: mockFilterGroups,
            onChange,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      const statusGroup = mockFilterGroups[0]!;

      act(() => {
        result.current.handleValueChange("active", statusGroup);
      });

      expect(onChange).toHaveBeenCalledWith("active", statusGroup);

      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: "/test-page?filter-status=active",
          pathname: "/test-page",
          query: {
            "filter-status": "active",
          },
        });
      });
    });

    it("should get filter values from URL", async () => {
      await mockRouter.push(
        "/test-page?filter-status=active&filter-category=tech",
      );

      const { result } = renderHook(
        () =>
          useSearchFilter({
            groups: mockFilterGroups,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current.getFilterValue("status")).toBe("active");
      expect(result.current.getFilterValue("category")).toBe("tech");
      expect(result.current.getFilterValue("nonexistent")).toBeUndefined();
    });

    it("should handle custom filter name", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () =>
          useSearchFilter({
            name: "customFilter",
            groups: mockFilterGroups,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      const statusGroup = mockFilterGroups[0]!;

      act(() => {
        result.current.handleValueChange("active", statusGroup);
      });

      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: "/test-page?customFilter-status=active",
          pathname: "/test-page",
          query: {
            "customFilter-status": "active",
          },
        });
      });

      expect(result.current.getFilterValue("status")).toBe("active");
    });

    it("should preserve other search params when updating filters", async () => {
      await mockRouter.push("/test-page?search=test&pagination-index=1");

      const { result } = renderHook(
        () =>
          useSearchFilter({
            groups: mockFilterGroups,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      const statusGroup = mockFilterGroups[0]!;

      act(() => {
        result.current.handleValueChange("active", statusGroup);
      });

      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath:
            "/test-page?search=test&pagination-index=1&filter-status=active",
          pathname: "/test-page",
          query: {
            search: "test",
            "pagination-index": "1",
            "filter-status": "active",
          },
        });
      });
    });
  });

  describe("Constants", () => {
    it("should have expected constant values", () => {
      expect(SEARCH_FILTER_NAME).toBe("filter");
    });
  });
});
