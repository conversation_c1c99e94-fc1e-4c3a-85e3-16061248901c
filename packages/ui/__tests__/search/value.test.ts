import type { ReactNode } from "react";

import React from "react";
import { act, renderHook, waitFor } from "@testing-library/react";
import mockRouter from "next-router-mock";
import { beforeEach, describe, expect, it, vi } from "vitest";

import SearchParams from "@/ui/search/SearchParams";
import {
  SEARCH_VALUE_GROUP,
  SEARCH_VALUE_NAME,
  useSearchValue,
  useSearchValueResult,
} from "@/ui/search/value";

// Test wrapper with real SearchParams provider
function TestWrapper({ children }: { children: ReactNode }) {
  return React.createElement(SearchParams, null, children);
}

describe("Search Value Hooks", () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Reset router to clean state
    await mockRouter.push("/test-page");
  });

  describe("useSearchValueResult", () => {
    it("should return undefined when no search params are present", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchValueResult(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBeUndefined();
    });

    it("should return value from search params", async () => {
      await mockRouter.push("/test-page?value-value=test-value");

      const { result } = renderHook(() => useSearchValueResult(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toBe("test-value");
    });

    it("should handle custom group and name", async () => {
      await mockRouter.push("/test-page?users-customName=user-value");

      const { result } = renderHook(
        () => useSearchValueResult("users", "customName"),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current).toBe("user-value");
    });
  });

  describe("useSearchValue", () => {
    it("should return default state when no search params", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () => useSearchValue({ name: "testValue" }),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current.value).toBeUndefined();
      expect(result.current.selection).toBeNull();
      expect(typeof result.current.onSelectionChange).toBe("function");
      expect(typeof result.current.onValueChange).toBe("function");
      expect(typeof result.current.onClear).toBe("function");
    });

    it("should handle selection change and update URL", async () => {
      await mockRouter.push("/test-page");

      const onSelect = vi.fn();
      const { result } = renderHook(
        () =>
          useSearchValue({
            name: "testValue",
            group: "users",
            onSelect,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      const mockSelection = { value: "user1", label: "User 1", id: "1" };

      act(() => {
        result.current.onSelectionChange(mockSelection);
      });

      expect(onSelect).toHaveBeenCalledWith(mockSelection);
      expect(result.current.selection).toBe(mockSelection);

      // Check URL update
      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: "/test-page?users-testValue=user1",
          pathname: "/test-page",
          query: {
            "users-testValue": "user1",
          },
        });
      });
    });

    it("should handle value change with debouncing", async () => {
      await mockRouter.push("/test-page");

      const onChange = vi.fn();
      const { result } = renderHook(
        () =>
          useSearchValue({
            name: "testValue",
            onChange,
            debounce: 100,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      act(() => {
        result.current.onValueChange("test-input");
      });

      expect(onChange).toHaveBeenCalledWith("test-input");

      // Wait for debounce
      await waitFor(
        () => {
          expect(result.current.value).toBe("test-input");
        },
        { timeout: 500 },
      );
    });

    it("should clear selection and value", async () => {
      await mockRouter.push("/test-page?users-testValue=user1");

      const onChange = vi.fn();
      const onSelect = vi.fn();

      const { result } = renderHook(
        () =>
          useSearchValue({
            name: "testValue",
            group: "users",
            onChange,
            onSelect,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      act(() => {
        result.current.onClear();
      });

      expect(onSelect).toHaveBeenCalledWith(undefined);
      expect(onChange).toHaveBeenCalledWith(undefined);
      expect(result.current.selection).toBeNull();

      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: "/test-page",
          pathname: "/test-page",
          query: {},
        });
      });
    });
  });

  describe("Constants", () => {
    it("should have expected constant values", () => {
      expect(SEARCH_VALUE_NAME).toBe("value");
      expect(SEARCH_VALUE_GROUP).toBe("value");
    });
  });
});
