import type { ReactNode } from "react";

import React from "react";
import { act, renderHook } from "@testing-library/react";
import mockRouter from "next-router-mock";
import { beforeEach, describe, expect, it, vi } from "vitest";

import {
  SEARCH_PAGINATION_NAME,
  useSearchPagination,
  useSearchPaginationValue,
} from "@/ui/search/pagination";
import SearchParams from "@/ui/search/SearchParams";

// Test wrapper with real SearchParams provider
function TestWrapper({ children }: { children: ReactNode }) {
  return React.createElement(SearchParams, null, children);
}

describe("Pagination Hooks", () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Reset router to clean state
    await mockRouter.push("/test-page");
  });

  describe("useSearchPaginationValue", () => {
    it("should return default values when no search params are present", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: 0,
        pageSize: 10,
      });
    });

    it("should return values from search params", async () => {
      await mockRouter.push("/test-page?pagination-index=2&pagination-size=20");

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: 2,
        pageSize: 20,
      });
    });

    it("should handle grouped pagination", async () => {
      await mockRouter.push(
        "/test-page?pagination-users-index=1&pagination-users-size=15",
      );

      const { result } = renderHook(() => useSearchPaginationValue("users"), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: 1,
        pageSize: 15,
      });
    });

    it("should handle custom name", async () => {
      await mockRouter.push("/test-page?customPag-index=3&customPag-size=25");

      const { result } = renderHook(
        () => useSearchPaginationValue(undefined, "customPag"),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current).toEqual({
        pageIndex: 3,
        pageSize: 25,
      });
    });

    it("should handle invalid numeric values gracefully", async () => {
      await mockRouter.push(
        "/test-page?pagination-index=invalid&pagination-size=also-invalid",
      );

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      // Invalid values should fall back to defaults
      expect(result.current).toEqual({
        pageIndex: 0, // falls back to default
        pageSize: 10, // falls back to default
      });
    });

    it("should handle partial search params", async () => {
      await mockRouter.push("/test-page?pagination-index=5");

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: 5,
        pageSize: 10, // default value
      });
    });

    it("should handle empty string values", async () => {
      await mockRouter.push("/test-page?pagination-index=&pagination-size=");

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      // Empty strings should fall back to defaults
      expect(result.current).toEqual({
        pageIndex: 0, // falls back to default
        pageSize: 10, // falls back to default
      });
    });
  });

  describe("useSearchPagination", () => {
    it("should return default pagination state", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      expect(result.current.pagination).toEqual({
        pageIndex: 0,
        pageSize: 10,
      });
      expect(typeof result.current.setPagination).toBe("function");
    });

    it("should return pagination state from search params", async () => {
      await mockRouter.push("/test-page?pagination-index=2&pagination-size=20");

      const { result } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      expect(result.current.pagination).toEqual({
        pageIndex: 2,
        pageSize: 20,
      });
    });

    it("should handle custom defaults", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () =>
          useSearchPagination({
            defaultPageIndex: 1,
            defaultPageSize: 25,
          }),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current.pagination).toEqual({
        pageIndex: 1,
        pageSize: 25,
      });
    });

    it("should update URL when setPagination is called with object", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        result.current.setPagination({
          pageIndex: 3,
          pageSize: 15,
        });
      });

      expect(mockRouter).toMatchObject({
        asPath: "/test-page?pagination-index=3&pagination-size=15",
        pathname: "/test-page",
        query: {
          "pagination-index": "3",
          "pagination-size": "15",
        },
      });
    });

    it("should update URL when setPagination is called with function", async () => {
      await mockRouter.push("/test-page?pagination-index=1&pagination-size=10");

      const { result } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        result.current.setPagination((prev) => ({
          ...prev,
          pageIndex: prev.pageIndex + 1,
        }));
      });

      expect(mockRouter).toMatchObject({
        asPath: "/test-page?pagination-index=2&pagination-size=10",
        pathname: "/test-page",
        query: {
          "pagination-index": "2",
          "pagination-size": "10",
        },
      });
    });

    it("should call onChange callback when provided", async () => {
      await mockRouter.push("/test-page");

      const onChange = vi.fn();

      const { result } = renderHook(() => useSearchPagination({ onChange }), {
        wrapper: TestWrapper,
      });

      const newPagination = { pageIndex: 2, pageSize: 20 };

      await act(async () => {
        result.current.setPagination(newPagination);
      });

      expect(onChange).toHaveBeenCalledWith(newPagination);
    });

    it("should handle grouped pagination with correct URL keys", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () => useSearchPagination({ group: "users" }),
        {
          wrapper: TestWrapper,
        },
      );

      await act(async () => {
        result.current.setPagination({
          pageIndex: 1,
          pageSize: 15,
        });
      });

      expect(mockRouter).toMatchObject({
        asPath: "/test-page?pagination-users-index=1&pagination-users-size=15",
        pathname: "/test-page",
        query: {
          "pagination-users-index": "1",
          "pagination-users-size": "15",
        },
      });
    });

    it("should handle custom name with correct URL keys", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () => useSearchPagination({ name: "customPag" }),
        {
          wrapper: TestWrapper,
        },
      );

      await act(async () => {
        result.current.setPagination({
          pageIndex: 4,
          pageSize: 30,
        });
      });

      expect(mockRouter).toMatchObject({
        asPath: "/test-page?customPag-index=4&customPag-size=30",
        pathname: "/test-page",
        query: {
          "customPag-index": "4",
          "customPag-size": "30",
        },
      });
    });

    it("should handle both group and custom name", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(
        () =>
          useSearchPagination({
            group: "products",
            name: "customPag",
          }),
        {
          wrapper: TestWrapper,
        },
      );

      await act(async () => {
        result.current.setPagination({
          pageIndex: 0,
          pageSize: 5,
        });
      });

      expect(mockRouter).toMatchObject({
        asPath:
          "/test-page?customPag-products-index=0&customPag-products-size=5",
        pathname: "/test-page",
        query: {
          "customPag-products-index": "0",
          "customPag-products-size": "5",
        },
      });
    });

    it("should preserve existing search params when updating pagination", async () => {
      await mockRouter.push(
        "/test-page?search=test-query&filter=active&pagination-index=0&pagination-size=10",
      );

      const { result } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        result.current.setPagination({
          pageIndex: 2,
          pageSize: 20,
        });
      });

      expect(mockRouter).toMatchObject({
        asPath:
          "/test-page?search=test-query&filter=active&pagination-index=2&pagination-size=20",
        pathname: "/test-page",
        query: {
          search: "test-query",
          filter: "active",
          "pagination-index": "2",
          "pagination-size": "20",
        },
      });
    });

    it("should maintain referential stability of pagination object when values unchanged", async () => {
      await mockRouter.push("/test-page?pagination-index=1&pagination-size=10");

      const { result, rerender } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      const firstPagination = result.current.pagination;

      rerender();

      const secondPagination = result.current.pagination;

      expect(firstPagination).toBe(secondPagination);
    });

    it("should update pagination when URL changes externally", async () => {
      await mockRouter.push("/test-page?pagination-index=1&pagination-size=10");

      const { result, rerender } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      expect(result.current.pagination).toEqual({
        pageIndex: 1,
        pageSize: 10,
      });

      // Simulate external URL change - wrap in act since it causes state updates
      await act(async () => {
        await mockRouter.push(
          "/test-page?pagination-index=5&pagination-size=25",
        );
      });

      rerender();

      expect(result.current.pagination).toEqual({
        pageIndex: 5,
        pageSize: 25,
      });
    });
  });

  describe("SEARCH_PAGINATION_NAME constant", () => {
    it("should have the expected value", () => {
      expect(SEARCH_PAGINATION_NAME).toBe("pagination");
    });
  });

  describe("Edge cases and error handling", () => {
    it("should handle negative page numbers", async () => {
      await mockRouter.push(
        "/test-page?pagination-index=-1&pagination-size=-5",
      );

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: -1,
        pageSize: -5,
      });
    });

    it("should handle very large numbers", async () => {
      await mockRouter.push(
        "/test-page?pagination-index=999999&pagination-size=1000000",
      );

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: 999999,
        pageSize: 1000000,
      });
    });

    it("should handle decimal numbers by truncating", async () => {
      await mockRouter.push(
        "/test-page?pagination-index=2.7&pagination-size=10.9",
      );

      const { result } = renderHook(() => useSearchPaginationValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current).toEqual({
        pageIndex: 2,
        pageSize: 10,
      });
    });

    it("should handle URL changes that remove pagination params", async () => {
      await mockRouter.push(
        "/test-page?search=test&pagination-index=2&pagination-size=20",
      );

      const { result, rerender } = renderHook(() => useSearchPagination(), {
        wrapper: TestWrapper,
      });

      expect(result.current.pagination).toEqual({
        pageIndex: 2,
        pageSize: 20,
      });

      // Remove pagination params - wrap in act since it causes state updates
      await act(async () => {
        await mockRouter.push("/test-page?search=test");
      });

      rerender();

      expect(result.current.pagination).toEqual({
        pageIndex: 0,
        pageSize: 10,
      });
    });
  });
});
