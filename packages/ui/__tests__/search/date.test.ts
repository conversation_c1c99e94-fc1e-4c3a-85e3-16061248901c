import type { ReactNode } from "react";

import React from "react";
import { act, renderHook, waitFor } from "@testing-library/react";
import mockRouter from "next-router-mock";
import { beforeEach, describe, expect, it, vi } from "vitest";

import {
  SEARCH_DATE_GROUP,
  SEARCH_DATE_NAME,
  SEARCH_END_DATE_NAME,
  SEARCH_START_DATE_NAME,
  useSearchDateRange,
  useSearchDateRangeValue,
} from "@/ui/search/date";
import SearchParams from "@/ui/search/SearchParams";

// Test wrapper with real SearchParams provider
function TestWrapper({ children }: { children: ReactNode }) {
  return React.createElement(SearchParams, null, children);
}

describe("Search Date Hooks", () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Reset router to clean state
    await mockRouter.push("/test-page");
  });

  describe("useSearchDateRangeValue", () => {
    it("should return undefined dates when no search params are present", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchDateRangeValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current.startDate).toBeUndefined();
      expect(result.current.endDate).toBeUndefined();
    });

    it("should return dates from search params with timezone handling", async () => {
      const startDate = "2024-01-01T00:00:00.000Z";
      const endDate = "2024-01-31T23:59:59.999Z";
      await mockRouter.push(
        `/test-page?range-date-startDate=${encodeURIComponent(startDate)}&range-date-endDate=${encodeURIComponent(endDate)}`,
      );

      const { result } = renderHook(() => useSearchDateRangeValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current.startDate).toEqual(expect.any(Date));
      expect(result.current.endDate).toEqual(expect.any(Date));
      // Note: startOfDay and endOfDay apply timezone adjustments, so we just verify they're Date objects
      // The exact ISO strings may differ based on the local timezone
    });

    it("should handle custom group and name", async () => {
      const startDate = "2024-02-01T00:00:00.000Z";
      await mockRouter.push(
        `/test-page?events-calendar-startDate=${encodeURIComponent(startDate)}`,
      );

      const { result } = renderHook(
        () => useSearchDateRangeValue("events", "calendar"),
        {
          wrapper: TestWrapper,
        },
      );

      expect(result.current.startDate).toEqual(expect.any(Date));
      expect(result.current.endDate).toBeUndefined();
    });

    it("should handle partial date ranges", async () => {
      const endDate = "2024-01-31T23:59:59.999Z";
      await mockRouter.push(
        `/test-page?range-date-endDate=${encodeURIComponent(endDate)}`,
      );

      const { result } = renderHook(() => useSearchDateRangeValue(), {
        wrapper: TestWrapper,
      });

      expect(result.current.startDate).toBeUndefined();
      expect(result.current.endDate).toEqual(expect.any(Date));
    });
  });

  describe("useSearchDateRange", () => {
    it("should return default state when no search params", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchDateRange(), {
        wrapper: TestWrapper,
      });

      expect(result.current.values.from).toBeUndefined();
      expect(result.current.values.to).toBeUndefined();
      expect(result.current.hasValue).toBe(false);
      expect(result.current.text).toBe("");
      expect(typeof result.current.onSelect).toBe("function");
      expect(typeof result.current.onClear).toBe("function");
    });

    it("should handle date selection and update URL", async () => {
      await mockRouter.push("/test-page");

      const { result } = renderHook(() => useSearchDateRange(), {
        wrapper: TestWrapper,
      });

      const dateRange = {
        from: new Date("2024-01-01"),
        to: new Date("2024-01-31"),
      };

      act(() => {
        result.current.onSelect(dateRange);
      });

      await waitFor(() => {
        expect(mockRouter.query["range-date-startDate"]).toBe(
          dateRange.from.toISOString(),
        );
        expect(mockRouter.query["range-date-endDate"]).toBe(
          dateRange.to.toISOString(),
        );
      });
    });

    it("should display formatted date text from URL params", async () => {
      const startDate = "2024-01-01T00:00:00.000Z";
      const endDate = "2024-01-31T23:59:59.999Z";
      await mockRouter.push(
        `/test-page?range-date-startDate=${encodeURIComponent(startDate)}&range-date-endDate=${encodeURIComponent(endDate)}`,
      );

      const { result } = renderHook(() => useSearchDateRange(), {
        wrapper: TestWrapper,
      });

      expect(result.current.hasValue).toBe(true);
      // The text formatting uses the raw Date constructor, not startOfDay/endOfDay
      // so it will show the actual date from the ISO string
      expect(result.current.text).toMatch(
        /\d{2}\/\d{2}\/\d{4} - \d{2}\/\d{2}\/\d{4}/,
      );
      expect(result.current.values.from).toEqual(expect.any(Date));
      expect(result.current.values.to).toEqual(expect.any(Date));
    });

    it("should clear date range and remove from URL", async () => {
      const startDate = "2024-01-01T00:00:00.000Z";
      const endDate = "2024-01-31T23:59:59.999Z";
      await mockRouter.push(
        `/test-page?range-date-startDate=${encodeURIComponent(startDate)}&range-date-endDate=${encodeURIComponent(endDate)}`,
      );

      const { result } = renderHook(() => useSearchDateRange(), {
        wrapper: TestWrapper,
      });

      expect(result.current.hasValue).toBe(true);

      act(() => {
        result.current.onClear();
      });

      await waitFor(() => {
        expect(mockRouter).toMatchObject({
          asPath: "/test-page",
          pathname: "/test-page",
          query: {},
        });
      });

      expect(result.current.hasValue).toBe(false);
      expect(result.current.text).toBe("");
    });
  });

  describe("Constants", () => {
    it("should have expected constant values", () => {
      expect(SEARCH_DATE_GROUP).toBe("range");
      expect(SEARCH_DATE_NAME).toBe("date");
      expect(SEARCH_START_DATE_NAME).toBe("startDate");
      expect(SEARCH_END_DATE_NAME).toBe("endDate");
    });
  });
});
