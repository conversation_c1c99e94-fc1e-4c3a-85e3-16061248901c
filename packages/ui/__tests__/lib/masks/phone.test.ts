import { describe, expect, it } from "vitest";

import { mask, transformPhoneNumber } from "@/ui/lib/masks/phone";

describe("Phone Mask", () => {
  describe("mask configuration", () => {
    it("should have a valid mask structure", () => {
      expect(mask).toBeDefined();
      expect(mask.mask).toBeDefined();
      expect(Array.isArray(mask.mask)).toBe(true);
    });

    it("should have the correct mask pattern", () => {
      const maskArray = mask.mask as unknown[];
      expect(maskArray[0]).toBe("+");
      expect(maskArray[1]).toBe("1");
      expect(maskArray[2]).toBe(" ");
      expect(maskArray[3]).toBe("(");
    });
  });

  describe("transformPhoneNumber", () => {
    it("should transform empty string", () => {
      const result = transformPhoneNumber("");
      expect(typeof result).toBe("string");
    });

    it("should transform numeric input to phone format", () => {
      const result = transformPhoneNumber("1234567890");
      expect(result).toContain("+1");
      expect(result).toContain("(");
      expect(result).toContain(")");
      expect(result).toContain("-");
    });

    it("should handle partial phone numbers", () => {
      const result = transformPhoneNumber("123");
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThanOrEqual(0);
    });

    it("should handle already formatted phone numbers", () => {
      const input = "+****************";
      const result = transformPhoneNumber(input);
      expect(typeof result).toBe("string");
    });
  });
});
