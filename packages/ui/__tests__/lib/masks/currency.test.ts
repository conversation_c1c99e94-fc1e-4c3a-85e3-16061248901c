import { describe, expect, it } from "vitest";

import { mask, transformCurrency } from "@/ui/lib/masks/currency";

describe("Currency Mask", () => {
  describe("mask configuration", () => {
    it("should have a valid mask structure", () => {
      expect(mask).toBeDefined();
      expect(typeof mask).toBe("object");
    });

    it("should be a valid Maskito options object", () => {
      expect(mask.mask).toBeDefined();
      expect(mask.postprocessors).toBeDefined();
    });
  });

  describe("transformCurrency", () => {
    it("should transform empty string", () => {
      const result = transformCurrency("");
      expect(typeof result).toBe("string");
    });

    it("should transform numeric input to currency format", () => {
      const result = transformCurrency("123");
      expect(result).toContain("$");
      expect(typeof result).toBe("string");
    });

    it("should handle decimal values", () => {
      const result = transformCurrency("123.45");
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
    });

    it("should handle already formatted currency", () => {
      const input = "$123.45";
      const result = transformCurrency(input);
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
    });

    it("should handle large numbers with thousands separator", () => {
      const result = transformCurrency("1234567");
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
    });
  });
});
