import { describe, expect, it } from "vitest";

import {
  computeHours,
  displayDateForTimeZone,
  getTimeFromInteger,
  setDateForTimeZone,
  setTimeToInteger,
} from "@/ui/lib/dates";

describe("Date Utilities", () => {
  describe("setDateForTimeZone", () => {
    it("should return a Date object", () => {
      const inputDate = new Date("2024-01-01T12:00:00Z");
      const result = setDateForTimeZone(inputDate, "America/New_York");
      expect(result).toBeInstanceOf(Date);
    });

    it("should handle UTC timezone", () => {
      const inputDate = new Date("2024-01-01T12:00:00Z");
      const result = setDateForTimeZone(inputDate, "UTC");
      expect(result).toBeInstanceOf(Date);
    });

    it("should handle same timezone gracefully", () => {
      const inputDate = new Date("2024-01-01T12:00:00Z");
      const result = setDateForTimeZone(inputDate, "UTC");
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe("displayDateForTimeZone", () => {
    it("should return a Date object", () => {
      const inputDate = new Date("2024-01-01T12:00:00Z");
      const result = displayDateForTimeZone(inputDate, "America/New_York");
      expect(result).toBeInstanceOf(Date);
    });

    it("should handle different timezones", () => {
      const inputDate = new Date("2024-01-01T12:00:00Z");
      const result = displayDateForTimeZone(inputDate, "Asia/Tokyo");
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe("setTimeToInteger", () => {
    it("should return 0 for undefined input", () => {
      const result = setTimeToInteger();
      expect(result).toBe(0);
    });

    it("should return 0 for null input", () => {
      const result = setTimeToInteger(null);
      expect(result).toBe(0);
    });

    it("should convert time to integer minutes", () => {
      // Using getTimeFromInteger to create a valid TimeValue
      const time = getTimeFromInteger(150); // 2 hours 30 minutes
      const result = setTimeToInteger(time);
      expect(result).toBe(150);
    });

    it("should handle midnight", () => {
      const time = getTimeFromInteger(0);
      const result = setTimeToInteger(time);
      expect(result).toBe(0);
    });
  });

  describe("getTimeFromInteger", () => {
    it("should convert integer minutes to time", () => {
      const result = getTimeFromInteger(150);
      expect(result.hour).toBe(2);
      expect(result.minute).toBe(30);
    });

    it("should handle zero minutes", () => {
      const result = getTimeFromInteger(0);
      expect(result.hour).toBe(0);
      expect(result.minute).toBe(0);
    });

    it("should handle full day", () => {
      const result = getTimeFromInteger(1440); // 24 hours * 60 minutes
      expect(result.hour).toBe(24);
      expect(result.minute).toBe(0);
    });
  });

  describe("computeHours", () => {
    it("should compute hours between two times", () => {
      const start = getTimeFromInteger(540); // 9:00 AM
      const end = getTimeFromInteger(1020); // 5:00 PM
      const result = computeHours(start, end);
      expect(result).toBe(8);
    });

    it("should handle overnight shifts", () => {
      const start = getTimeFromInteger(1380); // 11:00 PM
      const end = getTimeFromInteger(420); // 7:00 AM
      const result = computeHours(start, end);
      expect(result).toBe(8);
    });

    it("should handle same start and end times", () => {
      const start = getTimeFromInteger(720); // 12:00 PM
      const end = getTimeFromInteger(720); // 12:00 PM
      const result = computeHours(start, end);
      expect(result).toBe(0);
    });

    it("should handle partial hours", () => {
      const start = getTimeFromInteger(570); // 9:30 AM
      const end = getTimeFromInteger(765); // 12:45 PM
      const result = computeHours(start, end);
      expect(result).toBe(3.25); // 3 hours and 15 minutes
    });
  });
});
