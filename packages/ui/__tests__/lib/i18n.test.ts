import { describe, expect, it } from "vitest";

import { formatCurrency, formatTimeStamp } from "@/ui/lib/i18n";

describe("i18n Utilities", () => {
  describe("formatCurrency", () => {
    it("should format currency with default options", () => {
      const result = formatCurrency(1234.56);
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
      expect(result).toContain("1,234.56");
    });

    it("should format currency with custom locale", () => {
      const result = formatCurrency(1234.56, { locale: "en-GB" });
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
    });

    it("should format currency with custom currency", () => {
      const result = formatCurrency(1234.56, { currency: "EUR" });
      expect(typeof result).toBe("string");
      expect(result).toContain("€");
    });

    it("should format as decimal", () => {
      const result = formatCurrency(1234.56, { style: "decimal" });
      expect(typeof result).toBe("string");
      expect(result).toContain("1,234.56");
      expect(result).not.toContain("$");
    });

    it("should format as percent", () => {
      const result = formatCurrency(0.1234, { style: "percent" });
      expect(typeof result).toBe("string");
      expect(result).toContain("%");
    });

    it("should handle zero value", () => {
      const result = formatCurrency(0);
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
      expect(result).toContain("0");
    });

    it("should handle negative values", () => {
      const result = formatCurrency(-123.45);
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
    });

    it("should handle large numbers", () => {
      const result = formatCurrency(1234567.89);
      expect(typeof result).toBe("string");
      expect(result).toContain("$");
      expect(result).toContain("1,234,567.89");
    });

    it("should handle custom locale and currency together", () => {
      const result = formatCurrency(1234.56, {
        locale: "de-DE",
        currency: "EUR",
      });
      expect(typeof result).toBe("string");
      expect(result).toContain("€");
    });
  });

  describe("formatTimeStamp", () => {
    it("should format timestamp as relative time", () => {
      const now = new Date();
      const result = formatTimeStamp(now);
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle past dates", () => {
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // 1 day ago
      const result = formatTimeStamp(pastDate);
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle future dates", () => {
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day from now
      const result = formatTimeStamp(futureDate);
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle dates from different years", () => {
      const oldDate = new Date("2020-01-01");
      const result = formatTimeStamp(oldDate);
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });

    it("should handle very recent dates", () => {
      const recentDate = new Date(Date.now() - 60000); // 1 minute ago
      const result = formatTimeStamp(recentDate);
      expect(typeof result).toBe("string");
      expect(result.length).toBeGreaterThan(0);
    });
  });
});
