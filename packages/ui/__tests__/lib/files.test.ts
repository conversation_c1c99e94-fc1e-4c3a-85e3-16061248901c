import { beforeEach, describe, expect, it, vi } from "vitest";

import { arrayToCsv, downloadBlob, exportCSV } from "@/ui/lib/files";

// Mock DOM methods for downloadBlob tests
Object.defineProperty(global, "URL", {
  value: {
    createObjectURL: vi.fn(() => "mock-url"),
    revokeObjectURL: vi.fn(),
  },
});

Object.defineProperty(global, "Blob", {
  value: class MockBlob {
    constructor(content: unknown[], options: { type: string }) {
      // Mock implementation
    }
  },
});

describe("File Utilities", () => {
  describe("arrayToCsv", () => {
    it("should convert simple array to CSV", () => {
      const data = [
        ["Name", "Age"],
        ["John", "30"],
        ["Jane", "25"],
      ];
      const result = arrayToCsv(data);
      expect(typeof result).toBe("string");
      expect(result).toContain('"Name","Age"');
      expect(result).toContain('"<PERSON>","30"');
      expect(result).toContain('"<PERSON>","25"');
    });

    it("should handle empty array", () => {
      const data: (string | number)[][] = [];
      const result = arrayToCsv(data);
      expect(result).toBe("");
    });

    it("should escape double quotes", () => {
      const data = [['He said "Hello"', "Test"]];
      const result = arrayToCsv(data);
      expect(result).toContain('""Hello""');
    });

    it("should handle numbers", () => {
      const data = [
        ["Name", "Score"],
        ["Alice", 95],
        ["Bob", 87],
      ];
      const result = arrayToCsv(data);
      expect(result).toContain('"95"');
      expect(result).toContain('"87"');
    });

    it("should separate rows with CRLF", () => {
      const data = [["Row1"], ["Row2"]];
      const result = arrayToCsv(data);
      expect(result).toContain("\r\n");
    });
  });

  describe("exportCSV", () => {
    it("should export data with headers", () => {
      const data = [
        ["John", 30],
        ["Jane", 25],
      ];
      const headers = ["Name", "Age"];
      const result = exportCSV({ data, headers });
      expect(typeof result).toBe("string");
      expect(result).toContain('"Name","Age"');
    });

    it("should export data without headers", () => {
      const data = [
        ["John", 30],
        ["Jane", 25],
      ];
      const result = exportCSV({ data });
      expect(typeof result).toBe("string");
      expect(result).toContain('"John","30"');
    });

    it("should use default filename", () => {
      const data = [["test"]];
      const result = exportCSV({ data });
      expect(typeof result).toBe("string");
    });

    it("should handle custom filename", () => {
      const data = [["test"]];
      const result = exportCSV({ data, fileName: "custom.csv" });
      expect(typeof result).toBe("string");
    });

    it("should handle empty data", () => {
      const data: (string | number)[][] = [];
      const result = exportCSV({ data });
      expect(result).toBe("");
    });
  });

  describe("downloadBlob", () => {
    beforeEach(() => {
      // Mock createElement and click
      const mockLink = {
        href: "",
        setAttribute: vi.fn(),
        click: vi.fn(),
        remove: vi.fn(),
      };
      vi.spyOn(document, "createElement").mockReturnValue(
        mockLink as unknown as HTMLElement,
      );
    });

    it("should create and trigger download", () => {
      downloadBlob("test content", "test.txt", "text/plain");
      expect(document.createElement).toHaveBeenCalledWith("a");
    });

    it("should handle different content types", () => {
      downloadBlob("csv content", "data.csv", "text/csv");
      expect(document.createElement).toHaveBeenCalledWith("a");
    });

    it("should handle empty content", () => {
      downloadBlob("", "empty.txt", "text/plain");
      expect(document.createElement).toHaveBeenCalledWith("a");
    });
  });
});
