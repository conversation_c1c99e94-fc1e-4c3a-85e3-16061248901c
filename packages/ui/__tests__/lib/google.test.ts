import { describe, expect, it } from "vitest";

import type { Address } from "@/ui/lib/google";

import { normalizeGeocodeResults, structureComponents } from "@/ui/lib/google";

describe("Google Utilities", () => {
  describe("structureComponents", () => {
    it("should handle empty components array", () => {
      const components: google.maps.GeocoderAddressComponent[] = [];
      const result = structureComponents(components);
      expect(result).toBeDefined();
      expect(result.country).toBeNull();
      expect(result.street).toBeNull();
      expect(result.city).toBeNull();
    });

    it("should structure address components with short format", () => {
      const components: google.maps.GeocoderAddressComponent[] = [
        {
          long_name: "123",
          short_name: "123",
          types: ["street_number"],
        },
        {
          long_name: "Main Street",
          short_name: "Main St",
          types: ["route"],
        },
        {
          long_name: "New York",
          short_name: "NY",
          types: ["administrative_area_level_1", "political"],
        },
      ];

      const result = structureComponents(components, {
        componentFormat: "short",
      });

      expect(result.street).toBe("123 Main St");
      expect(result.state).toBe("NY");
    });

    it("should structure address components with long format", () => {
      const components: google.maps.GeocoderAddressComponent[] = [
        {
          long_name: "United States",
          short_name: "US",
          types: ["country", "political"],
        },
        {
          long_name: "Manhattan",
          short_name: "Manhattan",
          types: ["locality", "political"],
        },
      ];

      const result = structureComponents(components, {
        componentFormat: "long",
      });

      expect(result.country).toBe("United States");
      expect(result.city).toBe("Manhattan");
    });

    it("should handle street number without route", () => {
      const components: google.maps.GeocoderAddressComponent[] = [
        {
          long_name: "123",
          short_name: "123",
          types: ["street_number"],
        },
      ];

      const result = structureComponents(components);
      expect(result.street).toBe("123");
    });

    it("should handle route without street number", () => {
      const components: google.maps.GeocoderAddressComponent[] = [
        {
          long_name: "Main Street",
          short_name: "Main St",
          types: ["route"],
        },
      ];

      const result = structureComponents(components);
      expect(result.street).toBe("Main St");
    });
  });

  describe("normalizeGeocodeResults", () => {
    it("should return null for undefined result", () => {
      const result = normalizeGeocodeResults(undefined);
      expect(result).toBeNull();
    });

    it("should normalize complete geocode result", () => {
      const mockLocation = {
        lat: () => 40.7128,
        lng: () => -74.006,
      };

      const mockResult: google.maps.GeocoderResult = {
        types: ["street_address"],
        formatted_address: "123 Main St, New York, NY 10001, USA",
        address_components: [
          {
            long_name: "123",
            short_name: "123",
            types: ["street_number"],
          },
        ],
        geometry: {
          location: mockLocation as google.maps.LatLng,
          location_type: "ROOFTOP" as google.maps.GeocoderLocationType,
          viewport: {} as google.maps.LatLngBounds,
        },
        place_id: "test-place-id",
      };

      const result = normalizeGeocodeResults(mockResult);

      expect(result).toBeDefined();
      expect(result?.types).toEqual(["street_address"]);
      expect(result?.address.formatted).toBe(
        "123 Main St, New York, NY 10001, USA",
      );
      expect(result?.coordinates.latitude).toBe(40.7128);
      expect(result?.coordinates.longitude).toBe(-74.006);
    });

    it("should handle result without geometry", () => {
      const mockResult: google.maps.GeocoderResult = {
        types: ["street_address"],
        formatted_address: "Test Address",
        address_components: [],
        geometry: {} as google.maps.GeocoderGeometry,
        place_id: "test-place-id",
      };

      const result = normalizeGeocodeResults(mockResult);

      expect(result).toBeDefined();
      expect(result?.coordinates.latitude).toBeUndefined();
      expect(result?.coordinates.longitude).toBeUndefined();
    });

    it("should pass options to structureComponents", () => {
      const mockLocation = {
        lat: () => 40.7128,
        lng: () => -74.006,
      };

      const mockResult: google.maps.GeocoderResult = {
        types: ["street_address"],
        formatted_address: "Test Address",
        address_components: [
          {
            long_name: "New York",
            short_name: "NY",
            types: ["administrative_area_level_1", "political"],
          },
        ],
        geometry: {
          location: mockLocation as google.maps.LatLng,
          location_type: "ROOFTOP" as google.maps.GeocoderLocationType,
          viewport: {} as google.maps.LatLngBounds,
        },
        place_id: "test-place-id",
      };

      const result = normalizeGeocodeResults(mockResult, {
        componentFormat: "long",
      });

      expect(result?.address.state).toBe("New York");
    });
  });

  describe("Type Exports", () => {
    it("should export Address type", () => {
      // This test ensures the Address type is properly exported
      const address: Address = {
        country: "US",
        neighborhood: null,
        street: "123 Main St",
        borough: null,
        city: "New York",
        county: null,
        state: "NY",
        postal: "10001",
      };

      expect(address).toBeDefined();
      expect(address.country).toBe("US");
      expect(address.street).toBe("123 Main St");
    });
  });
});
