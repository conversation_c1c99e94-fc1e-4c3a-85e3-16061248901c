import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useEffect, useState } from "react";

import type { Point } from "@/ui/maps/Markers";

import GoogleMap from "@/ui/maps/GoogleMap";
import Markers, { <PERSON><PERSON><PERSON><PERSON>, PinMarker } from "@/ui/maps/Markers";
import { But<PERSON> } from "@/ui/primitives/button";

const meta: Meta<typeof Markers> = {
  component: Markers,
  title: "Maps/Markers",
  decorators: [
    (Story, { args }) => (
      // Ensure GoogleMap has a defined height for visibility
      <GoogleMap
        defaultCenter={{ lat: 34.0522, lng: -118.2437 }}
        defaultZoom={10}
        height="500px"
      >
        <Story args={args} />
      </GoogleMap>
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          "Displays multiple markers on a Google Map, with support for clustering and custom marker components. Requires `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`.",
      },
    },
  },
  argTypes: {
    points: {
      control: "object",
      description:
        "An array of points (latitude, longitude, and a unique key) to display as markers.",
    },
    Marker: {
      control: false, // Control for component type is complex, better to show via separate stories
      description:
        "Optional custom component to use for rendering each marker.",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Markers>;

const samplePoints: Point[] = [
  { key: "la1", lat: 34.0522, lng: -118.2437 }, // Los Angeles
  { key: "la2", lat: 34.055, lng: -118.25 }, // Near LA
  { key: "sf1", lat: 37.7749, lng: -122.4194 }, // San Francisco
];

export const DefaultMarkers: Story = {
  name: "Default Markers",
  args: {
    points: samplePoints,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays multiple markers with default rendering. The map auto-fits to show all markers.",
      },
    },
  },
};

export const CustomPinMarkers: Story = {
  name: "Custom Pin Markers",
  args: {
    points: samplePoints,
    Marker: PinMarker, // Using the simple PinMarker from Markers.tsx
  },
  parameters: {
    docs: {
      description: {
        story: "Displays multiple markers using a custom PinMarker component.",
      },
    },
  },
};

export const CustomHouseMarkers: Story = {
  name: "Custom House Markers",
  args: {
    points: samplePoints,
    Marker: HouseMarker, // Using the HouseMarker
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays multiple markers using a custom HouseMarker component.",
      },
    },
  },
};

// For clustering, we need more points, some close to each other
const clusteredPoints: Point[] = [
  // Los Angeles Area
  { key: "c_la1", lat: 34.0522, lng: -118.2437 },
  { key: "c_la2", lat: 34.0532, lng: -118.2447 },
  { key: "c_la3", lat: 34.0542, lng: -118.2457 },
  { key: "c_la4", lat: 34.0552, lng: -118.2467 },
  { key: "c_la5", lat: 34.0562, lng: -118.2477 },
  // New York Area
  { key: "c_ny1", lat: 40.7128, lng: -74.006 },
  { key: "c_ny2", lat: 40.7138, lng: -74.007 },
  { key: "c_ny3", lat: 40.7148, lng: -74.008 },
  // A bit further out
  { key: "c_chicago", lat: 41.8781, lng: -87.6298 },
  { key: "c_miami", lat: 25.7617, lng: -80.1918 },
];

export const ClusteredMarkers: Story = {
  name: "Clustered Markers",
  args: {
    points: clusteredPoints,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays a larger set of markers to demonstrate automatic clustering when markers are close together.",
      },
    },
  },
};

const DynamicMarkersComponent = () => {
  const initialPoints: Point[] = [
    { key: "initial1", lat: 34.0522, lng: -118.2437 },
    { key: "initial2", lat: 34.055, lng: -118.25 },
  ];
  const [points, setPoints] = useState<Point[]>(initialPoints);

  const addPoint = () => {
    const newKey = `dynamic${points.length + 1}`;
    // Add a point near the last one, or a new area
    const lastPoint = points[points.length - 1] || { lat: 34, lng: -118 };
    const newPoint: Point = {
      key: newKey,
      lat: lastPoint.lat + Math.random() * 0.1 - 0.05,
      lng: lastPoint.lng + Math.random() * 0.1 - 0.05,
    };
    setPoints([...points, newPoint]);
  };

  const removePoint = () => {
    if (points.length > 0) {
      setPoints(points.slice(0, -1));
    }
  };

  return (
    <div style={{ position: "relative" }}>
      <GoogleMap
        defaultCenter={{ lat: 34.0522, lng: -118.2437 }}
        defaultZoom={10}
        height="450px"
      >
        <Markers points={points} Marker={PinMarker} />
      </GoogleMap>
      <div style={{ marginTop: "1rem", display: "flex", gap: "0.5rem" }}>
        <Button onClick={addPoint}>Add Marker</Button>
        <Button
          onClick={removePoint}
          disabled={points.length === 0}
          variant="destructive"
        >
          Remove Last Marker
        </Button>
      </div>
    </div>
  );
};

export const DynamicallyUpdatingMarkers: Story = {
  name: "Dynamically Updating Markers",
  render: () => <DynamicMarkersComponent />,
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates adding and removing markers dynamically. The map should update to fit the new set of markers.",
      },
    },
  },
};
