import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import GoogleMap from "@/ui/maps/GoogleMap";
import { Typography } from "@/ui/pages/Typography";

const meta: Meta<typeof GoogleMap> = {
  component: GoogleMap,
  title: "Maps/GoogleMap",
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "Displays a Google Map. Requires `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` to be set in the environment for the map to render correctly.",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof GoogleMap>;

export const Default: Story = {
  name: "Default Map",
  args: {
    // Default center is NYC, default zoom is 10
    // Default size is 100% width, 300px height
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays the map with default settings (New York City, zoom 10).",
      },
    },
  },
};

export const CustomLocationAndZoom: Story = {
  name: "Custom Location & Zoom",
  args: {
    defaultCenter: { lat: 34.0522, lng: -118.2437 }, // Los Angeles
    defaultZoom: 12,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays the map centered on Los Angeles with a zoom level of 12.",
      },
    },
  },
};

export const CustomSize: Story = {
  name: "Custom Size",
  args: {
    width: "100%",
    height: "500px",
  },
  parameters: {
    docs: {
      description: {
        story: "Displays the map with a custom height of 500px.",
      },
    },
  },
};

export const WithPlaceholderChild: Story = {
  name: "With Placeholder Child (for Markers)",
  args: {
    children: (
      <div
        style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          color: "red",
          fontWeight: "bold",
        }}
      >
        <Typography variant="h5">Markers would go here</Typography>
      </div>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Illustrates where map children like markers would be placed. Specific marker stories will follow.",
      },
    },
  },
};
