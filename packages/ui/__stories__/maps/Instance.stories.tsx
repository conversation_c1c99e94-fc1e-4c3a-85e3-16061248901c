import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { useCallback, useState } from "react";

import type { MapInstance } from "@/ui/maps/Instance";

import GoogleMap from "@/ui/maps/GoogleMap";
import Instance from "@/ui/maps/Instance";
import { Typography } from "@/ui/pages/Typography";
import { Button } from "@/ui/primitives/button";

const meta: Meta<typeof Instance> = {
  component: Instance,
  title: "Maps/Instance (Map Control)",
  // `Instance` itself doesn't render, so we provide a custom render for its stories
  // to show it in context with a GoogleMap and controls.
  parameters: {
    docs: {
      description: {
        component:
          "A utility component to get direct access to the Google Maps instance for imperative control. It does not render any UI itself. Requires `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`.",
      },
    },
  },
  argTypes: {
    set: {
      control: false,
      description: "Callback function that receives the map instance.",
    },
  },
};

export default meta;

// Define a type for our story that uses a custom render function
interface InstanceStoryProps {
  // We don't have direct args for Instance, but Storybook might pass some
}

const MapControllerComponent: React.FC<InstanceStoryProps> = () => {
  const [mapInstance, setMapInstance] = useState<MapInstance | null>(null);
  const [mapCenter, setMapCenter] = useState<string>("N/A");
  const [mapZoom, setMapZoom] = useState<string>("N/A");

  const handleMapLoad = useCallback((map: MapInstance) => {
    setMapInstance(map);
    if (map) {
      setMapCenter(JSON.stringify(map.getCenter()?.toJSON(), null, 2));
      setMapZoom(map.getZoom()?.toString() ?? "N/A");

      map.addListener("center_changed", () => {
        setMapCenter(JSON.stringify(map.getCenter()?.toJSON(), null, 2));
      });
      map.addListener("zoom_changed", () => {
        setMapZoom(map.getZoom()?.toString() ?? "N/A");
      });
    }
  }, []);

  const panToNewYork = () => {
    mapInstance?.panTo({ lat: 40.7128, lng: -74.006 });
  };

  const zoomIn = () => {
    const currentZoom = mapInstance?.getZoom() ?? 10;
    mapInstance?.setZoom(currentZoom + 1);
  };

  const setSatelliteView = () => {
    mapInstance?.setMapTypeId("satellite");
  };

  const setRoadmapView = () => {
    mapInstance?.setMapTypeId("roadmap");
  };

  return (
    <div>
      <GoogleMap
        defaultCenter={{ lat: 34.0522, lng: -118.2437 }}
        defaultZoom={10}
        height="400px"
      >
        <Instance set={handleMapLoad} />
      </GoogleMap>
      <div
        style={{
          marginTop: "1rem",
          display: "flex",
          flexDirection: "column",
          gap: "0.5rem",
        }}
      >
        <Typography variant="h6">Map Controls & Info</Typography>
        <div style={{ display: "flex", gap: "0.5rem", flexWrap: "wrap" }}>
          <Button onClick={panToNewYork} disabled={!mapInstance}>
            Pan to New York
          </Button>
          <Button onClick={zoomIn} disabled={!mapInstance}>
            Zoom In
          </Button>
          <Button onClick={setSatelliteView} disabled={!mapInstance}>
            Satellite View
          </Button>
          <Button onClick={setRoadmapView} disabled={!mapInstance}>
            Roadmap View
          </Button>
        </div>
        <div>
          <Typography variant="body1">Current Center:</Typography>
          <pre
            style={{
              fontSize: "0.8em",
              background: "#f0f0f0",
              padding: "0.5em",
              borderRadius: "4px",
            }}
          >
            {mapCenter}
          </pre>
        </div>
        <div>
          <Typography variant="body1">Current Zoom: {mapZoom}</Typography>
        </div>
      </div>
    </div>
  );
};

export const AccessAndControlMapInstance: StoryObj<InstanceStoryProps> = {
  name: "Access & Control Map Instance",
  render: () => <MapControllerComponent />,
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates using the `Instance` component to get the map object and control it programmatically. Map interactions (drag, zoom) will update the displayed center/zoom.",
      },
    },
  },
};
