import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useState } from "react";

import { AddressMarker } from "@/ui/maps/AddressMarker";
import GoogleMap from "@/ui/maps/GoogleMap";
import { <PERSON><PERSON> } from "@/ui/primitives/button";

const meta: Meta<typeof AddressMarker> = {
  component: AddressMarker,
  title: "Maps/AddressMarker",
  decorators: [
    (Story, { args }) => (
      <GoogleMap defaultCenter={args.position} defaultZoom={14} height="400px">
        <Story />
      </GoogleMap>
    ),
  ],
  parameters: {
    docs: {
      description: {
        component:
          "Displays a single, styled marker on a Google Map at a given position. The map will pan to the marker's location. Requires `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`.",
      },
    },
  },
  argTypes: {
    position: {
      control: "object",
      description: "The latitude and longitude for the marker.",
    },
  },
};

export default meta;

type Story = StoryObj<typeof AddressMarker>;

export const DefaultMarker: Story = {
  name: "Default Marker",
  args: {
    position: { lat: 34.0522, lng: -118.2437 }, // Los Angeles
  },
  parameters: {
    docs: {
      description: {
        story:
          "Displays a single marker at a predefined location (Los Angeles).",
      },
    },
  },
};

const InteractiveMarkerComponent = () => {
  const locations = [
    { lat: 34.0522, lng: -118.2437, name: "Los Angeles" },
    { lat: 40.7128, lng: -74.006, name: "New York" },
    { lat: 48.8566, lng: 2.3522, name: "Paris" },
  ];
  const [currentLocation, setCurrentLocation] = useState<(typeof locations)[0]>(
    locations[0]!,
  );

  return (
    <div style={{ position: "relative" }}>
      <GoogleMap
        defaultCenter={currentLocation}
        defaultZoom={12}
        height="400px"
      >
        <AddressMarker position={currentLocation} />
      </GoogleMap>
      <div style={{ marginTop: "1rem", display: "flex", gap: "0.5rem" }}>
        {locations.map((loc) => (
          <Button
            key={loc.name}
            onClick={() => setCurrentLocation(loc)}
            variant={currentLocation.name === loc.name ? "primary" : "outline"}
          >
            Pan to {loc.name}
          </Button>
        ))}
      </div>
    </div>
  );
};

export const InteractiveMarkerPosition: Story = {
  name: "Interactive Marker Position",
  render: () => <InteractiveMarkerComponent />,
  parameters: {
    docs: {
      description: {
        story:
          "Click buttons to change the marker's position and see the map pan accordingly.",
      },
    },
  },
};
