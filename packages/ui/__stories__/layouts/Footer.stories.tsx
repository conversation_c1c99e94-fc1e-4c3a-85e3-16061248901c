import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { ConnectBar, Footer, FooterBase } from "@/ui/layouts/Footer";
import { Typography } from "@/ui/pages/Typography";

const meta: Meta<typeof Footer> = {
  component: Footer,
  title: "Layouts/Footer",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Footer>;

export const Default: Story = {
  name: "Footer",
  args: {},
};

export const ConnectBarStory: StoryObj<typeof ConnectBar> = {
  name: "Connect Bar",
  render: () => <ConnectBar />,
};

export const FooterBaseStory: StoryObj<typeof FooterBase> = {
  name: "Footer Base",
  render: () => <FooterBase />,
};

export const CustomizedFooter: Story = {
  name: "Customized Footer",
  args: {
    brand: <Typography variant="h4">Custom Brand</Typography>,
    links: [
      { label: "Careers", href: "#" },
      { label: "Blog", href: "#" },
      { label: "Help Center", href: "#" },
    ],
  },
};
