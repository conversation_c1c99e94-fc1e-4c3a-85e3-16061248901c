import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import AppView from "@/ui/layouts/AppView";
import { Section } from "@/ui/pages/Section";
import { Typography } from "@/ui/pages/Typography";
import { Button } from "@/ui/primitives/button";

const meta: Meta<typeof AppView> = {
  component: AppView,
  title: "Layouts/AppView",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof AppView>;

const defaultArgs = {
  title: "Page Title",
  description: "A brief description of the page content.",
  children: (
    <Section>
      <Typography variant="body1">
        This is the main content of the view. It can be any React node.
      </Typography>
    </Section>
  ),
};

export const Default: Story = {
  args: {
    ...defaultArgs,
  },
};

export const WithGoBackButton: Story = {
  args: {
    ...defaultArgs,
    goBackUrl: "#",
  },
};

export const WithHeaderActions: Story = {
  args: {
    ...defaultArgs,
    children: [
      <Button key="action">Primary Action</Button>,
      <Section key="content">
        <Typography variant="body1">
          This view has a button in the header area.
        </Typography>
      </Section>,
    ],
  },
};

export const FullWidthSize: Story = {
  args: {
    ...defaultArgs,
    size: "full",
    title: "Full Width Page",
    description: "This view uses the full width of the screen.",
  },
};

export const Loading: Story = {
  args: {
    ...defaultArgs,
    loading: true,
    goBackUrl: "#",
  },
};
