import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import PageLayout from "@/ui/layouts/PageLayout";
import { Container } from "@/ui/pages/Container";
import { Section } from "@/ui/pages/Section";
import { Typography } from "@/ui/pages/Typography";

const meta: Meta<typeof PageLayout> = {
  component: PageLayout,
  title: "Layouts/PageLayout",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof PageLayout>;

export const Default: Story = {
  args: {
    children: (
      <Container>
        <Section>
          <Typography variant="h1">Page Title</Typography>
          <Typography variant="body1">
            This is the main content of the page, wrapped in a PageLayout.
          </Typography>
        </Section>
      </Container>
    ),
  },
};

export const WithNavigationLinks: Story = {
  args: {
    links: [
      { label: "Home", href: "#" },
      { label: "About", href: "#" },
      { label: "Contact", href: "#" },
    ],
    children: (
      <Container>
        <Section>
          <Typography variant="h1">Page with Navigation</Typography>
          <Typography variant="body1">
            This page has navigation links in the header and footer.
          </Typography>
        </Section>
      </Container>
    ),
  },
};
