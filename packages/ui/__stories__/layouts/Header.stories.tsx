import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Header } from "@/ui/layouts/Header";
import { Typography } from "@/ui/pages/Typography";
import { Input } from "@/ui/primitives/input";

const meta: Meta<typeof Header> = {
  component: Header,
  title: "Layouts/Header",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Header>;

export const Default: Story = {
  args: {},
};

export const WithNavigationLinks: Story = {
  args: {
    links: [
      { label: "Home", href: "#" },
      { label: "About", href: "#" },
      { label: "Contact", href: "#" },
    ],
  },
};

export const WithCustomChildren: Story = {
  args: {
    children: (
      <div className="flex w-full items-center justify-center">
        <Input placeholder="Search..." className="max-w-sm" />
      </div>
    ),
  },
};

export const WithoutLoginButton: Story = {
  args: {
    showLogin: false,
  },
};

export const WithCustomBrand: Story = {
  args: {
    brand: <Typography variant="h4">Custom Brand</Typography>,
  },
};
