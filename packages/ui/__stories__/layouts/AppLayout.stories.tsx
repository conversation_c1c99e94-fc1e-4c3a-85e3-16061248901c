import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Bell, Home, Package, Settings, Users } from "lucide-react";

import AppLayout from "@/ui/layouts/AppLayout";
import { Section } from "@/ui/pages/Section";
import { Typography } from "@/ui/pages/Typography";
import { Badge } from "@/ui/primitives/badge";

const meta: Meta<typeof AppLayout> = {
  component: AppLayout,
  title: "Layouts/AppLayout",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof AppLayout>;

const defaultNavLinks = [
  [
    { href: "#", label: "Dashboard", icon: Home },
    { href: "#", label: "Users", icon: Users },
    { href: "#", label: "Products", icon: Package },
  ],
  [{ href: "#", label: "Settings", icon: Settings }],
];

const defaultChildren = [
  <Typography key="org" variant="h5">
    Org Name
  </Typography>,
  <Typography key="header" variant="h5">
    Page Title
  </Typography>,
  <Section key="content">
    <Typography variant="body1">
      This is the main content of the application.
    </Typography>
  </Section>,
];

export const Default: Story = {
  args: {
    navLinks: defaultNavLinks,
    children: defaultChildren,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    navLinks: [],
    children: defaultChildren,
  },
};

export const WithBanner: Story = {
  args: {
    navLinks: defaultNavLinks,
    children: [
      ...defaultChildren,
      <div key="banner" className="rounded-lg border p-4 text-center">
        <Typography variant="h6">Upgrade Now</Typography>
        <Typography variant="body2">Get more features!</Typography>
      </div>,
    ],
  },
};

export const WithActiveLinkAndBadges: Story = {
  args: {
    navLinks: [
      [
        { href: "#", label: "Dashboard", icon: Home, isActive: true },
        {
          href: "#",
          label: "Notifications",
          icon: Bell,
          badge: <Badge className="ms-auto">5</Badge>,
        },
      ],
      [{ href: "#", label: "Settings", icon: Settings }],
    ],
    children: defaultChildren,
  },
};

export const WithMultipleLinkGroups: Story = {
  args: {
    navLinks: [
      [{ href: "#", label: "Dashboard", icon: Home }],
      [
        { href: "#", label: "Users", icon: Users },
        { href: "#", label: "Products", icon: Package },
      ],
      [{ href: "#", label: "Settings", icon: Settings }],
    ],
    children: defaultChildren,
  },
};
