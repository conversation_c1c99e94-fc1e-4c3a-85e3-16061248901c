import { Meta } from "@storybook/addon-docs/blocks";

import { RoadmapCard } from "./RoadmapCard";
import { RoadmapChecklistItem } from "./RoadmapChecklistItem";

<Meta title="Roadmap" />

# Roadmap

## Overview

This is the roadmap for the project.

### General Roadmap

#### Changes

**Selectors & Search**

- [ ] Support more selector types used across apps
- [ ] Standardize selector/search component patterns
- [ ] Add clear button to all selectors
- [ ] Fix selector value selection behavior
- [ ] Enhance search component library

**Scheduling**

- [ ] Scheduling forms and validation
- [ ] Schedule presentation components
- [ ] Calendar views and navigation
- [ ] Time slot management
- [ ] Availability display

**Stripe & Payments**

- [ ] Payment history display
- [ ] Payment methods management
- [ ] Payment capture workflows
- [ ] Connect account verification
- [ ] Invoice generation

**UI Components Library**

- [ ] Preview Person component (name, title, avatar)
- [ ] Enhanced table components
- [ ] Form validation patterns
- [ ] Loading states standardization
- [ ] Error handling components

#### Common (and shared) Patterns

- [ ] Incident
- [ ] Credential
- [ ] Cookie

#### Shared Components Consolidation

**Components that exist in multiple versions across apps and need unified implementations**

**Messages & Communication**

- [ ] Consolidate Messages/Messenger components (currently ~3 versions)
- [ ] Unified message thread interface
- [ ] Standardized message composition
- [ ] Consistent notification handling
- [ ] Shared message state management

**Scheduling & Calendar**

- [ ] Consolidate scheduling logic components
- [ ] Unified schedule display patterns
- [ ] Shared event input/creation forms
- [ ] Consistent calendar view components
- [ ] Standardized availability management

**Forms & Validation**

- [ ] Consolidate form validation patterns
- [ ] Unified input component variants
- [ ] Shared form state management
- [ ] Consistent error display patterns
- [ ] Use shared fields

**Data Display**

- [ ] Consolidate list/grid view patterns
- [ ] Unified pagination components
- [ ] Shared loading state components
- [ ] Consistent empty state displays

## Features

### Blocks

We are building out "Blocks" as a pre-packaged way of handling common UI patterns that we see throughout both apps. This will allow for faster development and more consistent user experiences.

<div className="my-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
  <RoadmapCard
    checked
    title="Modular Table Block"
    description="A single, reusable table component that can be configured for various data display needs, including sorting, filtering, and pagination."
  >
    <RoadmapChecklistItem checked>Core table structure</RoadmapChecklistItem>
    <RoadmapChecklistItem checked>
      Configurable actions for selection
    </RoadmapChecklistItem>
    <RoadmapChecklistItem checked>
      improved selection across pages (bug)
    </RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Authentication Blocks"
    description="A set of components for handling user sign-in, sign-up, and password recovery flows."
  >
    <RoadmapChecklistItem>Create Clerk Element forms</RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Sign Up and Sign In layout panels
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>Customizable for white-labeling</RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Entity Command Bar"
    description="A universal search component that can be integrated into any page to provide consistent search functionality."
  >
    <RoadmapChecklistItem>
      Define API for multi-entity search
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Component for entity type selection
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Backend query logic post-selection
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Visual component for displaying results
    </RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Payments and Stripe"
    description="Components for handling payments, payment history, and Stripe Connect functionality."
  >
    <RoadmapChecklistItem>Payment History</RoadmapChecklistItem>
    <RoadmapChecklistItem>Payment Methods</RoadmapChecklistItem>
    <RoadmapChecklistItem>Capture Payment</RoadmapChecklistItem>
    <RoadmapChecklistItem>Connect and Verify</RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Messaging and Activity"
    description="Components for handling user messages, threads, and activity logs."
  >
    <RoadmapChecklistItem>Clean message UI</RoadmapChecklistItem>
    <RoadmapChecklistItem>Thread manager</RoadmapChecklistItem>
    <RoadmapChecklistItem>Activity monitor/logs</RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Developer Tools"
    description="Conditionally rendered components and tools to aid in development and testing."
  >
    <RoadmapChecklistItem>Visual Dev Panel component</RoadmapChecklistItem>
    <RoadmapChecklistItem>System for quick Dev Buttons</RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Clear labeling for non-technical users
    </RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="AI & Specialized Loaders"
    description="Components and utilities for rich loading experiences, especially for AI-generated content."
  >
    <RoadmapChecklistItem>
      Streaming text display component
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>Dynamic content skeletons</RoadmapChecklistItem>
    <RoadmapChecklistItem>Generative UI placeholders</RoadmapChecklistItem>
  </RoadmapCard>
</div>

### Charts

We will create a variety of charts to visualize data for different scenarios.

<div className="my-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
  <RoadmapCard
    title="Abstract Existing Charts"
    description="Move existing charts into a more abstracted form and store them in our component library."
  >
    <RoadmapChecklistItem>
      Define new chart conventions (e.g., compact form)
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>Migrate existing charts</RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Financial Charts"
    description="Create charts for various financial use cases."
  >
    <RoadmapChecklistItem>Profit and Loss</RoadmapChecklistItem>
    <RoadmapChecklistItem>Cash Flow Projections</RoadmapChecklistItem>
    <RoadmapChecklistItem>Revenue by Service</RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Operations Charts"
    description="Create charts for various operational use cases."
  >
    <RoadmapChecklistItem>Service Delivery Times</RoadmapChecklistItem>
    <RoadmapChecklistItem>Client Satisfaction Scores</RoadmapChecklistItem>
    <RoadmapChecklistItem>Team Utilization Rates</RoadmapChecklistItem>
  </RoadmapCard>
</div>

### Improvements

<div className="my-4 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
  <RoadmapCard title="Selector" description="Improve selector functionality">
    <RoadmapChecklistItem>
      Multi-select rendering and logic
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>Support for maximum limit</RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Bolster our form and fields"
    description="Enhance form usability and field consistency across the application."
  >
    <RoadmapChecklistItem>
      Create a stronger scheduling form UI
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Streamline specialized fields (date, weight, etc.)
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Implement form presets for common actions
    </RoadmapChecklistItem>
  </RoadmapCard>
  <RoadmapCard
    title="Abstract Actions & Data Binding Patterns"
    description="Define new conventions for connecting UI to data, improving separation of concerns."
  >
    <RoadmapChecklistItem>
      Define 'views' folder convention
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Structure for forms, actions, selectors within 'views'
    </RoadmapChecklistItem>
    <RoadmapChecklistItem>
      Dedicated 'hooks' subfolder in 'views'
    </RoadmapChecklistItem>
  </RoadmapCard>
</div>
