import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { CockpitLayout } from "@/ui/blocks/negotiation-center/cockpit/cockpit/cockpit-layout";

const meta: Meta<typeof CockpitLayout> = {
  title: "Blocks/Negotiation Center/Cockpit Layout",
  component: CockpitLayout,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A flexible, reusable component for displaying organization invitations with support for pagination, custom rendering, and actions.",
      },
    },
  },
  tags: ["autodocs"],
  // argTypes: {},
};

export default meta;

type Story = StoryObj<typeof CockpitLayout>;

export const Default: Story = {
  // render: () => <CockpitLayout />,
};
