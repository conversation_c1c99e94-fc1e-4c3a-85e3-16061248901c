import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs-vite";

import type {
  Block,
  Milestone,
  Registry,
} from "@/ui/blocks/negotiation-timeline";
import type {
  ContractCreatedBlockData,
  ContractSignedBlockData,
  MessageBlockData,
  RateNegotiatedBlockData,
  RateOfferBlockData,
} from "@/ui/blocks/negotiation-timeline/templates";

import { NegotiationTimeline } from "@/ui/blocks/negotiation-timeline";
import {
  ContractCreatedBlock,
  ContractSignedBlock,
  MessageBlock,
  RateNegotiatedBlock,
  RateOfferBlock,
} from "@/ui/blocks/negotiation-timeline/templates";

// Enhanced registry using template components
const registry: Registry = {
  "rate-negotiated": {
    type: "rate-negotiated",
    render: (block: Block) => {
      const data = block.data as RateNegotiatedBlockData;
      return <RateNegotiatedBlock data={data} />;
    },
  },
  message: {
    type: "message",
    render: (block: Block) => {
      const data = block.data as MessageBlockData;
      return <MessageBlock data={data} />;
    },
  },
  "rate-offer": {
    type: "rate-offer",
    render: (block: Block) => {
      const data = block.data as RateOfferBlockData;
      return <RateOfferBlock data={data} />;
    },
  },
  "contract-created": {
    type: "contract-created",
    render: (block: Block) => {
      const data = block.data as ContractCreatedBlockData;
      return <ContractCreatedBlock data={data} />;
    },
  },
  "contract-signed": {
    type: "contract-signed",
    render: (block: Block) => {
      const data = block.data as ContractSignedBlockData;
      return <ContractSignedBlock data={data} />;
    },
  },
};

const meta: Meta<typeof NegotiationTimeline> = {
  title: "Blocks/Negotiation Timeline/Timeline",
  component: NegotiationTimeline,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A chat-widget style negotiation timeline with collapsible milestones, message input, and integrated conversation flow",
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

// Sample data matching the image design
const completeMilestones: Milestone[] = [
  {
    id: "milestone-1",
    title: "Rate Negotiated",
    status: "completed",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    blocks: [
      {
        id: "block-1",
        type: "rate-negotiated",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        data: {
          doctor: "Dr. Mark Chen",
          rate: "$120.00/hr",
          message: "Let's proceed with these terms.",
        },
      },
      {
        id: "block-2",
        type: "message",
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        data: {
          author: "Dr. Emily Carter",
          message: "Sounds good.",
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
        },
      },
      {
        id: "block-3",
        type: "rate-offer",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        data: {
          rate: "$120.00/hr",
          status: "accepted" as const,
        },
      },
    ],
  },
  {
    id: "milestone-2",
    title: "Contract Created",
    status: "completed",
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    blocks: [
      {
        id: "block-4",
        type: "contract-signed",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        data: {
          signer: "Dr. Mark Chen",
          signedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        },
      },
      {
        id: "block-5",
        type: "contract-created",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        data: {
          organization: "Ohio Health",
          status: "created",
          contractId: "CT-2024-001",
        },
      },
    ],
  },
];

const activeMilestones: Milestone[] = [
  {
    id: "milestone-active-1",
    title: "Rate Negotiation",
    status: "active",
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
    blocks: [
      {
        id: "block-active-1",
        type: "rate-offer",
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        data: {
          rate: "$150.00/hr",
          status: "pending" as const,
          onAccept: () => alert("Rate accepted! 🎉"),
          onCounter: () => alert("Counter offer dialog would open"),
        },
      },
      {
        id: "block-active-2",
        type: "message",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        data: {
          author: "Dr. Sarah Johnson",
          message: "I'd like to discuss the rate for this position.",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        },
      },
    ],
  },
  {
    id: "milestone-pending-2",
    title: "Contract Creation",
    status: "pending",
    timestamp: new Date(),
    blocks: [],
  },
];

// Chat Widget - matches the image exactly
export const Default: Story = {
  args: {
    milestones: completeMilestones,
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`Sending message: "${message}"`),
  },
};

// Chat Widget without message input
export const WithoutMessageInput: Story = {
  args: {
    milestones: completeMilestones,
    registry: registry,
    defaultExpanded: true,
    showMessageInput: false,
  },
};

// Initially collapsed milestones
export const InitiallyCollapsed: Story = {
  args: {
    milestones: completeMilestones,
    registry: registry,
    defaultExpanded: false,
    showMessageInput: true,
    onSendMessage: (message: string) => console.log("Message:", message),
  },
};

// Active negotiation with pending offers
export const ActiveNegotiation: Story = {
  args: {
    milestones: activeMilestones,
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`New message: "${message}"`),
  },
};

// Full conversation flow
export const FullConversation: Story = {
  args: {
    milestones: [...completeMilestones, ...activeMilestones],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) => alert(`Message sent: "${message}"`),
  },
};

// Interactive demo with all features
export const InteractiveDemo: Story = {
  args: {
    milestones: [
      {
        id: "demo-milestone",
        title: "Live Negotiation",
        status: "active",
        timestamp: new Date(),
        blocks: [
          {
            id: "demo-offer",
            type: "rate-offer",
            timestamp: new Date(),
            data: {
              rate: "$135.00/hr",
              status: "pending" as const,
              onAccept: () =>
                alert(
                  "🎉 Rate accepted! Contract will be generated automatically.",
                ),
              onCounter: () =>
                alert(
                  "💬 Counter offer form would appear here with rate input.",
                ),
            },
          },
          {
            id: "demo-message-2",
            type: "message",
            timestamp: new Date(Date.now() - 15 * 60 * 1000),
            data: {
              author: "Hiring Manager",
              message: "We're flexible on the rate. What works for you?",
              timestamp: new Date(Date.now() - 15 * 60 * 1000),
            },
          },
          {
            id: "demo-message-1",
            type: "message",
            timestamp: new Date(Date.now() - 30 * 60 * 1000),
            data: {
              author: "Dr. Alex Rivera",
              message:
                "I'm interested in this position. Can we discuss the compensation?",
              timestamp: new Date(Date.now() - 30 * 60 * 1000),
            },
          },
        ],
      },
    ],
    registry: registry,
    defaultExpanded: true,
    showMessageInput: true,
    onSendMessage: (message: string) =>
      alert(`📨 Your message: "${message}" has been sent to the conversation!`),
  },
};
