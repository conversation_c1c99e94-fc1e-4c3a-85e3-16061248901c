import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import type { AnimatedTestimonialsProps } from "@/ui/blocks/testimonials/animated-testimonials";

import { AnimatedTestimonials } from "@/ui/blocks/testimonials/animated-testimonials";

const meta: Meta<typeof AnimatedTestimonials> = {
  title: "Blocks/Testimonials/AnimatedTestimonials",
  component: AnimatedTestimonials,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof AnimatedTestimonials>;

const testimonials: AnimatedTestimonialsProps["testimonials"] = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Full Stack Developer",
    company: "TechFlow",
    content:
      "This starter template saved me weeks of setup time. The Supabase integration is flawless, and the UI components are beautiful and easy to customize. Worth every penny!",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Frontend Engineer",
    company: "DesignHub",
    content:
      "I've used many starter templates, but this one stands out for its clean architecture and attention to detail. The TypeScript support is excellent, and the documentation is comprehensive.",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
  },
  {
    id: 3,
    name: "<PERSON> <PERSON>",
    role: "Product Manager",
    company: "InnovateLabs",
    content:
      "Our team was able to launch our MVP in record time thanks to this template. The authentication flow and user management features worked right out of the box. Highly recommended!",
    rating: 5,
    avatar: "https://randomuser.me/api/portraits/men/46.jpg",
  },
];

export const Basic: Story = {
  args: {
    testimonials,
    trustedCompanies: ["Google", "Microsoft", "Airbnb", "Spotify", "Netflix"],
  },
};
