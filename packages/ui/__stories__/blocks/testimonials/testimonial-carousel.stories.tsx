import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { TestimonialCarousel } from "@/ui/blocks/testimonials/testimonial-carousel";

const meta: Meta<typeof TestimonialCarousel> = {
  title: "Blocks/Testimonials/TestimonialCarousel",
  component: TestimonialCarousel,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof TestimonialCarousel>;

const testimonials = [
  {
    company: "google",
    avatar: "men/32.jpg",
    name: "<PERSON>",
    role: "Full Stack Developer",
    review:
      "This carousel is fantastic! The UI is clean and the experience is smooth.",
  },
  {
    company: "microsoft",
    avatar: "women/44.jpg",
    name: "<PERSON>",
    role: "Frontend Engineer",
    review:
      "I love how easy it is to integrate and customize. Highly recommended!",
  },
  {
    company: "airbnb",
    avatar: "men/46.jpg",
    name: "<PERSON>",
    role: "Product Manager",
    review:
      "Our team was able to showcase testimonials beautifully with this component.",
  },
];

export const Basic: Story = {
  args: {
    testimonials,
    companyLogoPath: "/logos/", // Adjust as needed for your static assets
    avatarPath: "https://randomuser.me/api/portraits/",
  },
};
