import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import React from "react";

import type {
  GovernmentID,
  ProviderData,
} from "@/ui/blocks/provider-id-card/provider-card";

import ProviderCard from "@/ui/blocks/provider-id-card/provider-card";

// Mock provider data for different examples
const mockProviders: Record<string, ProviderData> = {
  cardiologist: {
    id: "prov-001",
    name: "Dr. <PERSON>",
    specialty: "Cardiology",
    title: "Senior Cardiologist",
    email: "<EMAIL>",
    phone: "+****************",
    location: "New York, NY",
    licenseExpiry: "2025-12-31",
    profileImage: "/placeholder.svg?height=120&width=100",
    status: "active",
  },
  pediatrician: {
    id: "prov-002",
    name: "Dr. <PERSON>",
    specialty: "Pediatrics",
    title: "Chief of Pediatric Medicine",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
    licenseExpiry: "2026-06-15",
    profileImage: "/placeholder.svg?height=120&width=100",
    status: "active",
  },
  neurologist: {
    id: "prov-003",
    name: "Dr. <PERSON> <PERSON>",
    specialty: "Neurology",
    title: "Attending Neurologist",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Boston, MA",
    licenseExpiry: "2024-09-30",
    profileImage: "/placeholder.svg?height=120&width=100",
    status: "pending",
  },
  orthopedist: {
    id: "prov-004",
    name: "Dr. James Wilson",
    specialty: "Orthopedic Surgery",
    title: "Orthopedic Surgeon",
    email: "<EMAIL>",
    phone: "+****************",
    location: "Chicago, IL",
    licenseExpiry: "2024-03-15",
    profileImage: "/placeholder.svg?height=120&width=100",
    status: "inactive",
  },
};

const mockGovernmentIDs: Record<string, GovernmentID> = {
  cardiologist: {
    licenseNumber: "MD-NY-123456789",
    issueDate: "2020-01-15",
    expiryDate: "2025-12-31",
    issuingAuthority: "New York State Department of Health",
    npiNumber: "**********",
    deaNumber: "*********",
    state: "NEW YORK",
    licenseType: "MEDICAL LICENSE",
  },
  pediatrician: {
    licenseNumber: "MD-CA-987654321",
    issueDate: "2019-06-15",
    expiryDate: "2026-06-15",
    issuingAuthority: "California Medical Board",
    npiNumber: "**********",
    deaNumber: "*********",
    state: "CALIFORNIA",
    licenseType: "MEDICAL LICENSE",
  },
  neurologist: {
    licenseNumber: "MD-MA-456789123",
    issueDate: "2021-09-30",
    expiryDate: "2024-09-30",
    issuingAuthority: "Massachusetts Board of Registration in Medicine",
    npiNumber: "**********",
    state: "MASSACHUSETTS",
    licenseType: "MEDICAL LICENSE",
  },
  orthopedist: {
    licenseNumber: "MD-IL-789123456",
    issueDate: "2018-03-15",
    expiryDate: "2024-03-15",
    issuingAuthority:
      "Illinois Department of Financial and Professional Regulation",
    npiNumber: "**********",
    deaNumber: "*********",
    state: "ILLINOIS",
    licenseType: "MEDICAL LICENSE",
  },
};

const meta: Meta<typeof ProviderCard> = {
  title: "Blocks/Provider ID Card/ProviderCard",
  component: ProviderCard,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A comprehensive provider card component that displays healthcare provider information with an optional government ID section using the FlipCard component. Features dynamic loading, customizable data, and interactive ID verification.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    provider: {
      description:
        "Provider data object containing personal and professional information",
      control: { type: "object" },
    },
    governmentId: {
      description:
        "Optional government ID data. If not provided, uses async loading via onIdLoad",
      control: { type: "object" },
    },
    onIdLoad: {
      description: "Async function to load government ID data when needed",
      action: "onIdLoad",
    },
    className: {
      description: "Additional CSS classes for the card container",
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ProviderCard>;

export const Default: Story = {
  args: {
    provider: mockProviders.cardiologist,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Default provider card with simulated async loading of government ID data.",
      },
    },
  },
};

export const WithPreloadedGovernmentID: Story = {
  args: {
    provider: mockProviders.cardiologist,
    governmentId: mockGovernmentIDs.cardiologist,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Provider card with government ID data already loaded, bypassing the loading state.",
      },
    },
  },
};

export const CustomAsyncLoader: Story = {
  args: {
    provider: mockProviders.pediatrician!,
    onIdLoad: async (): Promise<GovernmentID> => {
      // Simulate API call with custom loading time
      await new Promise((resolve) => setTimeout(resolve, 2000));
      return mockGovernmentIDs.pediatrician!;
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Provider card with custom async loader function for government ID data.",
      },
    },
  },
};

export const DifferentSpecialties: Story = {
  render: () => (
    <div className="grid gap-6 lg:grid-cols-2">
      <ProviderCard
        provider={mockProviders.cardiologist!}
        governmentId={mockGovernmentIDs.cardiologist}
      />
      <ProviderCard
        provider={mockProviders.neurologist!}
        governmentId={mockGovernmentIDs.neurologist}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Multiple provider cards showing different medical specialties and status indicators.",
      },
    },
  },
};

export const DifferentStatuses: Story = {
  render: () => (
    <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
      <div>
        <h3 className="mb-4 text-center text-sm font-medium text-green-600">
          Active Provider
        </h3>
        <ProviderCard
          provider={mockProviders.cardiologist!}
          governmentId={mockGovernmentIDs.cardiologist}
        />
      </div>
      <div>
        <h3 className="mb-4 text-center text-sm font-medium text-yellow-600">
          Pending Provider
        </h3>
        <ProviderCard
          provider={mockProviders.neurologist!}
          governmentId={mockGovernmentIDs.neurologist}
        />
      </div>
      <div>
        <h3 className="mb-4 text-center text-sm font-medium text-red-600">
          Inactive Provider
        </h3>
        <ProviderCard
          provider={mockProviders.orthopedist!}
          governmentId={mockGovernmentIDs.orthopedist}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Provider cards showing different status states: active, pending, and inactive.",
      },
    },
  },
};

export const MinimalProvider: Story = {
  args: {
    provider: {
      id: "prov-min",
      name: "Dr. Jane Smith",
      specialty: "Family Medicine",
      title: "Family Physician",
      email: "<EMAIL>",
      phone: "+****************",
      location: "Rural Clinic, TX",
      licenseExpiry: "2025-01-01",
      status: "active",
      // No profile image provided
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Provider card with minimal data and no profile image, showing fallback initials.",
      },
    },
  },
};

export const LoadingError: Story = {
  args: {
    provider: mockProviders.pediatrician!,
    onIdLoad: async (): Promise<GovernmentID> => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      throw new Error("Failed to load government ID data");
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Provider card demonstrating error handling when government ID loading fails.",
      },
    },
  },
};

export const CustomStyling: Story = {
  args: {
    provider: mockProviders.cardiologist!,
    governmentId: mockGovernmentIDs.cardiologist!,
    className: "border-2 border-blue-200 bg-blue-50/50",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Provider card with custom styling applied through the className prop.",
      },
    },
  },
};

export const InteractiveDemo: Story = {
  render: () => {
    const providers = Object.values(mockProviders);
    const governmentIds = Object.values(mockGovernmentIDs);
    const [currentIndex, setCurrentIndex] = React.useState(0);

    const handleNext = () => {
      setCurrentIndex((prev) => (prev + 1) % providers.length);
    };

    const handlePrev = () => {
      setCurrentIndex(
        (prev) => (prev - 1 + providers.length) % providers.length,
      );
    };

    const currentProvider = providers[currentIndex];
    const currentGovId = governmentIds[currentIndex];

    if (!currentProvider || !currentGovId) {
      return <div>No data available</div>;
    }

    return (
      <div className="space-y-4">
        <div className="flex justify-center gap-2">
          <button
            onClick={handlePrev}
            className="rounded bg-blue-500 px-3 py-1 text-white hover:bg-blue-600"
          >
            Previous
          </button>
          <button
            onClick={handleNext}
            className="rounded bg-blue-500 px-3 py-1 text-white hover:bg-blue-600"
          >
            Next
          </button>
        </div>
        <ProviderCard
          key={currentProvider.id}
          provider={currentProvider}
          governmentId={currentGovId}
        />
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive demo allowing navigation between different provider examples.",
      },
    },
  },
};
