import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ider from "@/ui/authentication/ClerkProvider";
import SignUpPage from "@/ui/authentication/SignUp";

const meta: Meta<typeof SignUpPage> = {
  component: SignUpPage,
  title: "Authentication/SignUp Page",
  decorators: [
    (Story) => (
      <ClerkCustomProvider>
        <div style={{ maxWidth: "400px", margin: "auto", paddingTop: "2rem" }}>
          <Story />
        </div>
      </ClerkCustomProvider>
    ),
  ],
  parameters: {
    nextjs: {
      appDirectory: true,
    },
    docs: {
      description: {
        component:
          "Displays the full Clerk Sign Up flow. Requires Clerk environment variables (e.g., `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`) to be set for full functionality. Styling is provided by `@/ui/styles/clerk.css` and theme-aware appearance settings.",
      },
    },
    layout: "fullscreen", // Use fullscreen to better represent a page
  },
};

export default meta;

export const Default: StoryObj<typeof SignUpPage> = {
  name: "Default Sign Up Flow",
  parameters: {
    docs: {
      description: {
        story:
          "This story shows the default Clerk Sign Up component. Interact with the form to see different steps like email/username input, password creation, and verification.",
      },
    },
  },
};
