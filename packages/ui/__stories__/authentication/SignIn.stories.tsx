import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import <PERSON><PERSON><PERSON><PERSON>Provider from "@/ui/authentication/ClerkProvider";
import SignInPage from "@/ui/authentication/SignIn";

const meta: Meta<typeof SignInPage> = {
  component: SignInPage,
  title: "Authentication/SignIn Page",
  decorators: [
    (Story) => (
      <ClerkCustomProvider>
        <div style={{ maxWidth: "400px", margin: "auto", paddingTop: "2rem" }}>
          <Story />
        </div>
      </ClerkCustomProvider>
    ),
  ],
  parameters: {
    nextjs: {
      appDirectory: true,
    },
    docs: {
      description: {
        component:
          "Displays the full Clerk Sign In flow. Requires Clerk environment variables (e.g., `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`) to be set for full functionality. Styling is provided by `@/ui/styles/clerk.css` and theme-aware appearance settings.",
      },
    },
    layout: "fullscreen", // Use fullscreen to better represent a page
  },
};

export default meta;

export const Default: StoryObj<typeof SignInPage> = {
  name: "Default Sign In Flow",
  parameters: {
    docs: {
      description: {
        story:
          "This story shows the default Clerk Sign In component. Interact with the form to see different steps like email input, verification, and password recovery.",
      },
    },
  },
};
