import React from "react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/ui/primitives/card";
import { Checkbox } from "@/ui/primitives/checkbox";

export interface RoadmapCardProps {
  checked?: boolean;
  title: string;
  description: string;
  children?: React.ReactNode;
}

export const RoadmapCard: React.FC<RoadmapCardProps> = ({
  checked,
  title,
  description,
  children,
}) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-4">
          <Checkbox checked={checked} />
          <CardTitle>{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="ml-8 text-sm text-muted-foreground">{description}</p>
        {children && <div className="ml-8 mt-4 flex flex-col">{children}</div>}
      </CardContent>
    </Card>
  );
};
