import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Section } from "@/ui/pages/Section";
import { Typography } from "@/ui/pages/Typography";

const meta: Meta<typeof Section> = {
  component: Section,
  title: "Pages/Section",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Section>;

export const Default: Story = {
  args: {
    children: (
      <>
        <Typography variant="h2">Section Title</Typography>
        <Typography variant="body1">
          This is a section of content. It provides vertical spacing to separate
          different parts of a page.
        </Typography>
      </>
    ),
  },
};

export const WithCustomClassName: Story = {
  args: {
    className: "bg-muted",
    children: (
      <>
        <Typography variant="h2">Section with Custom Background</Typography>
        <Typography variant="body1">
          This section has a custom background color applied via the className
          prop.
        </Typography>
      </>
    ),
  },
};
