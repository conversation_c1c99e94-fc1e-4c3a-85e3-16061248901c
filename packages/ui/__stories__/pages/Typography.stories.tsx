import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Typography } from "@/ui/pages/Typography";

const meta: Meta<typeof Typography> = {
  component: Typography,
  title: "Pages/Typography",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Typography>;

export const H1: Story = {
  args: {
    variant: "h1",
    children: "Heading 1",
  },
};

export const H2: Story = {
  args: {
    variant: "h2",
    children: "Heading 2",
  },
};

export const H3: Story = {
  args: {
    variant: "h3",
    children: "Heading 3",
  },
};

export const H4: Story = {
  args: {
    variant: "h4",
    children: "Heading 4",
  },
};

export const H5: Story = {
  args: {
    variant: "h5",
    children: "Heading 5",
  },
};

export const H6: Story = {
  args: {
    variant: "h6",
    children: "Heading 6",
  },
};

export const Body1: Story = {
  args: {
    variant: "body1",
    children:
      "This is body1 text. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
};

export const Body2: Story = {
  args: {
    variant: "body2",
    children:
      "This is body2 text. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
  },
};

export const Caption: Story = {
  args: {
    variant: "caption",
    children: "This is caption text. Used for smaller, secondary information.",
  },
};
