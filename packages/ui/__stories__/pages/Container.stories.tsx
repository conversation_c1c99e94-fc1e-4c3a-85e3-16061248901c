import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Container } from "@/ui/pages/Container";
import { Section } from "@/ui/pages/Section";
import { Typography } from "@/ui/pages/Typography";

const meta: Meta<typeof Container> = {
  component: Container,
  title: "Pages/Container",
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Container>;

export const Default: Story = {
  args: {
    children: (
      <>
        <Section>
          <Typography variant="h1">Page Title</Typography>
          <Typography variant="body1">
            This content is inside a container, which centers it and adds
            horizontal padding.
          </Typography>
        </Section>
        <Section className="bg-muted">
          <Typography variant="h2">Another Section</Typography>
          <Typography variant="body1">
            This demonstrates how containers can hold multiple sections to build
            a complete page layout.
          </Typography>
        </Section>
      </>
    ),
  },
};

export const WithCustomClassName: Story = {
  args: {
    className: "max-w-4xl", // Example of a custom class
    children: (
      <>
        <Section>
          <Typography variant="h1">Contained to 4xl</Typography>
          <Typography variant="body1">
            This container has a custom `max-w-4xl` class applied to make it
            narrower than the default.
          </Typography>
        </Section>
      </>
    ),
  },
};
