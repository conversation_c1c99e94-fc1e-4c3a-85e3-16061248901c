import type { Meta, StoryObj } from "@storybook/react";

import DateTime from "../../src/shared/DateTime";

const meta = {
  title: "Shared/DateTime",
  component: DateTime,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof DateTime>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default DateTime display with current date
 */
export const Default: Story = {
  args: {
    date: new Date(),
  },
};

/**
 * DateTime with calendar view
 */
export const Calendar: Story = {
  args: {
    date: new Date(),
    mode: "calendar",
  },
  render: (args) => (
    <div className="p-4">
      <DateTime {...args} />
    </div>
  ),
};

/**
 * DateTime showing time
 */
export const WithTime: Story = {
  args: {
    date: new Date(),
    showTime: true,
  },
};

/**
 * DateTime with custom format
 */
export const CustomFormat: Story = {
  args: {
    date: new Date(),
    format: "MMMM dd, yyyy",
  },
};

/**
 * DateTime with time only
 */
export const TimeOnly: Story = {
  args: {
    time: {
      hour: 14,
      minute: 30,
    },
  },
};

/**
 * DateTime in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
  },
};
