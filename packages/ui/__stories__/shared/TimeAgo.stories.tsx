import type { <PERSON>a, StoryObj } from "@storybook/react";

import TimeAgo from "../../src/shared/TimeAgo";

const meta = {
  title: "Shared/TimeAgo",
  component: TimeAgo,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof TimeAgo>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default TimeAgo showing time since now
 */
export const Default: Story = {
  args: {
    date: new Date(),
  },
};

/**
 * TimeAgo for a date in the past
 */
export const PastDate: Story = {
  args: {
    date: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
  },
};

/**
 * TimeAgo for a date further in the past
 */
export const OlderDate: Story = {
  args: {
    date: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 1 week ago
  },
};

/**
 * TimeAgo in loading state
 */
export const Loading: Story = {
  args: {
    date: new Date(),
    loading: true,
  },
};
