import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useState } from "react";
import { AlertCircleIcon, TrashIcon } from "lucide-react";

import { But<PERSON> } from "../../src/primitives/button";
import DialogConfirmation from "../../src/shared/DialogConfirmation";

const meta = {
  title: "Shared/DialogConfirmation",
  component: DialogConfirmation,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof DialogConfirmation>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Basic confirmation dialog with default delete icon
 */
export const Default: Story = {
  args: {
    title: "Delete Item?",
    description:
      "This action cannot be undone. Are you sure you want to delete this item?",
    onClick: () => console.log("Confirmed action"),
  },
};

/**
 * Confirmation dialog with custom icon
 */
export const WithCustomIcon: Story = {
  args: {
    title: "Warning",
    description: "This action will mark the item as inactive. Are you sure?",
    Icon: AlertCircleIcon,
    label: "Mark as Inactive",
    action: "Confirm",
    onClick: () => console.log("Confirmed action"),
  },
};

/**
 * Confirmation dialog with custom actions
 */
export const CustomActions: Story = {
  args: {
    title: "Archive Document?",
    description:
      "This document will be moved to the archive. You can restore it later if needed.",
    action: "Archive",
    cancel: "Keep Active",
    label: "Archive",
    onClick: () => console.log("Archived"),
  },
};

/**
 * Controlled open state dialog example
 */
export const ControlledState = () => {
  const [open, setOpen] = useState(false);

  return (
    <div className="flex flex-col items-center gap-4">
      <Button onClick={() => setOpen(true)}>Open Dialog</Button>
      <div>Dialog is {open ? "open" : "closed"}</div>

      <DialogConfirmation
        open={open}
        onOpenChange={setOpen}
        title="Confirm Action"
        description="Are you sure you want to proceed with this action?"
        onClick={() => console.log("Action confirmed")}
        useTrigger={false}
      />
    </div>
  );
};

/**
 * Dialog with custom body content
 */
export const WithCustomBody: Story = {
  args: {
    title: "Confirm Deletion",
    description: "Please review the details before confirming:",
    body: (
      <div className="my-4 rounded-md bg-muted p-4">
        <p className="font-semibold">Item: Customer Record #12345</p>
        <p className="text-sm text-muted-foreground">
          This will delete all associated data.
        </p>
      </div>
    ),
    onClick: () => console.log("Confirmed deletion"),
  },
};
