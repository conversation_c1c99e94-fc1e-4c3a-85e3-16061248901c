import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import DashboardGreeting from "../../src/shared/DashboardGreeting";

const meta = {
  title: "Shared/DashboardGreeting",
  component: DashboardGreeting,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof DashboardGreeting>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * DashboardGreeting with user name
 */
export const WithUser: Story = {
  args: {
    user: { firstName: "John" },
    loading: false,
  },
  render: (args) => (
    <div className="w-full max-w-2xl p-4">
      <DashboardGreeting {...args} />
    </div>
  ),
};

/**
 * DashboardGreeting without user name
 */
export const WithoutUser: Story = {
  args: {
    user: undefined,
    loading: false,
  },
  render: (args) => (
    <div className="w-full max-w-2xl p-4">
      <DashboardGreeting {...args} />
    </div>
  ),
};

/**
 * DashboardGreeting in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
  },
  render: (args) => (
    <div className="w-full max-w-2xl p-4">
      <DashboardGreeting {...args} />
    </div>
  ),
};
