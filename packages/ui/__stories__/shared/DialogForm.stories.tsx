import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import type { ComponentType } from "react";

import { useState } from "react";
import { CirclePlusIcon, PenBoxIcon } from "lucide-react";

import { But<PERSON> } from "../../src/primitives/button";
import { Input } from "../../src/primitives/input";
import { Label } from "../../src/primitives/label";
import DialogForm from "../../src/shared/DialogForm";

// Simple form component for demo purposes
const ExampleForm = ({
  onSubmit = (values: unknown) => console.log(values),
}) => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    onSubmit({ name, email });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your name"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email"
        />
      </div>

      <Button type="submit" className="w-full">
        Submit
      </Button>
    </form>
  );
};

const meta = {
  title: "Shared/DialogForm",
  component: DialogForm,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  args: {
    Component: ExampleForm as ComponentType<unknown>,
    onSubmit: (values) => console.log("Form submitted:", values),
  },
} satisfies Meta<typeof DialogForm>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Add type dialog form
 */
export const AddType: Story = {
  args: {
    title: "Add User",
    description: "Fill out the form to add a new user.",
    type: "add",
    label: "Add User",
  },
};

/**
 * Update type dialog form
 */
export const UpdateType: Story = {
  args: {
    title: "Update User",
    description: "Update user details",
    type: "update",
    label: "Edit User",
    Icon: PenBoxIcon,
  },
};

/**
 * With custom trigger button
 */
export const CustomTrigger: Story = {
  args: {
    title: "Custom Dialog",
    description: "Dialog with custom trigger button",
  },
  render: (args) => (
    <DialogForm {...args}>
      <Button variant="outline" className="border-dashed">
        <CirclePlusIcon className="mr-2 size-4" />
        Custom Trigger Button
      </Button>
    </DialogForm>
  ),
};

/**
 * Controlled state dialog form
 */
export const ControlledState = () => {
  const [open, setOpen] = useState(false);

  return (
    <div className="flex flex-col items-center gap-4">
      <Button onClick={() => setOpen(true)}>Open Form Dialog</Button>
      <div>Dialog is {open ? "open" : "closed"}</div>

      <DialogForm
        open={open}
        onOpenChange={setOpen}
        useTrigger={false}
        title="Controlled Dialog"
        description="This dialog's open state is controlled externally"
        Component={ExampleForm}
        onSubmit={(values) => {
          console.log("Form submitted:", values);
          setOpen(false);
        }}
      />
    </div>
  );
};
