import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ErrorAlert } from "../../src/shared/ErrorAlert";

const meta = {
  title: "Shared/ErrorAlert",
  component: ErrorAlert,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof ErrorAlert>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Basic error alert with a simple message
 */
export const Default: Story = {
  args: {
    error: { message: "An unexpected error occurred" },
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <ErrorAlert {...args} />
    </div>
  ),
};

/**
 * Error alert with a more specific message
 */
export const SpecificError: Story = {
  args: {
    error: { message: "Failed to load user data: Network timeout" },
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <ErrorAlert {...args} />
    </div>
  ),
};

/**
 * Error alert from Error object
 */
export const FromErrorObject: Story = {
  args: {
    error: new Error("Permission denied"),
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <ErrorAlert {...args} />
    </div>
  ),
};
