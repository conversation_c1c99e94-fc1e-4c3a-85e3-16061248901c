import type { Meta, StoryObj } from "@storybook/react";

import Currency from "../../src/shared/Currency";

const meta = {
  title: "Shared/Currency",
  component: Currency,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Currency>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default Currency display for USD
 */
export const Default: Story = {
  args: {
    value: 1234.56,
  },
};

/**
 * Currency with Euro format
 */
export const Euro: Story = {
  args: {
    value: 1234.56,
    currency: "EUR",
  },
};

/**
 * Currency with high amount
 */
export const HighAmount: Story = {
  args: {
    value: 9999999.99,
  },
};

/**
 * Currency in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
    value: 1234.56,
  },
};
