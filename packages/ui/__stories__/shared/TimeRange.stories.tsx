import type { <PERSON>a, <PERSON>Obj } from "@storybook/react";

import TimeRange from "../../src/shared/TimeRange";

const meta = {
  title: "Shared/TimeRange",
  component: TimeRange,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof TimeRange>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * TimeRange with only start time
 */
export const StartTimeOnly: Story = {
  args: {
    startTime: { hour: 9, minute: 0 },
  },
  render: (args) => (
    <div className="p-4">
      <TimeRange {...args} />
    </div>
  ),
};

/**
 * TimeRange with start and end time
 */
export const StartAndEndTime: Story = {
  args: {
    startTime: { hour: 9, minute: 0 },
    endTime: { hour: 17, minute: 0 },
  },
  render: (args) => (
    <div className="p-4">
      <TimeRange {...args} />
    </div>
  ),
};

/**
 * TimeRange showing time
 */
export const WithTime: Story = {
  args: {
    startTime: { hour: 9, minute: 30 },
    endTime: { hour: 17, minute: 30 },
    showTime: true,
  },
  render: (args) => (
    <div className="p-4">
      <TimeRange {...args} />
    </div>
  ),
};

/**
 * TimeRange in loading state
 */
export const Loading: Story = {
  args: {
    startTime: { hour: 9, minute: 0 },
    endTime: { hour: 17, minute: 0 },
    loading: true,
  },
  render: (args) => (
    <div className="p-4">
      <TimeRange {...args} />
    </div>
  ),
};
