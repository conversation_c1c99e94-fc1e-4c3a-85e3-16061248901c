import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { PlusCircleIcon } from "lucide-react";

import { But<PERSON> } from "../../src/primitives/button";
import EmptyList from "../../src/shared/EmptyList";

const meta = {
  title: "Shared/EmptyList",
  component: EmptyList,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof EmptyList>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Basic empty list display
 */
export const Default: Story = {
  args: {
    title: "No items found",
    description:
      "Try adjusting your search or filters to find what you're looking for.",
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <EmptyList {...args} />
    </div>
  ),
};

/**
 * Empty list with action button
 */
export const WithAction: Story = {
  args: {
    title: "No documents found",
    description: "Get started by creating your first document",
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <EmptyList {...args}>
        <Button>
          <PlusCircleIcon className="mr-2 h-4 w-4" />
          Add Document
        </Button>
      </EmptyList>
    </div>
  ),
};

/**
 * Empty list with custom styling
 */
export const CustomStyling: Story = {
  args: {
    title: "Empty Inbox",
    description: "You've processed all your messages",
    className: "bg-muted/20 border-primary/20",
  },
  render: (args) => (
    <div className="w-full max-w-md">
      <EmptyList {...args} />
    </div>
  ),
};
