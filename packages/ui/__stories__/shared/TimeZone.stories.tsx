import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import TimeZone from "../../src/shared/TimeZone";

const meta = {
  title: "Shared/TimeZone",
  component: TimeZone,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof TimeZone>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default TimeZone component
 */
export const Default: Story = {
  args: {
    timeZone: "America/New_York",
  },
};

/**
 * TimeZone with different format
 */
export const DifferentTimeZone: Story = {
  args: {
    timeZone: "Europe/London",
  },
};

/**
 * TimeZone with null value
 */
export const NullTimeZone: Story = {
  args: {
    timeZone: null,
  },
};

/**
 * TimeZone in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
  },
};
