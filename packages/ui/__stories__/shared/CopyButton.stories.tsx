import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Card } from "../../src/primitives/card";
import CopyButton from "../../src/shared/CopyButton";

const meta = {
  title: "Shared/CopyButton",
  component: CopyButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof CopyButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default CopyButton that copies text when clicked
 */
export const Default: Story = {
  args: {
    text: "Text to copy",
  },
  render: (args) => (
    <div className="group flex items-center gap-2 p-2">
      <span>Hover to see copy button:</span>
      <CopyButton {...args} />
    </div>
  ),
};

/**
 * CopyButton with custom label
 */
export const WithCustomLabel: Story = {
  args: {
    text: "Custom text to copy",
    label: "Copy to clipboard",
  },
  render: (args) => (
    <div className="group flex items-center gap-2 p-2">
      <span>Hover to see copy button:</span>
      <CopyButton {...args} />
    </div>
  ),
};
