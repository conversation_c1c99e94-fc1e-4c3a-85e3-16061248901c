import type { Meta, StoryObj } from "@storybook/react";

import PriceCounter from "../../src/shared/PriceCounter";

const meta = {
  title: "Shared/PriceCounter",
  component: PriceCounter,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof PriceCounter>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default PriceCounter component with default settings
 */
export const Default: Story = {
  args: {},
};

/**
 * PriceCounter with custom min/max range
 */
export const CustomRange: Story = {
  args: {
    min: 100,
    max: 500,
  },
};

/**
 * PriceCounter with custom step size
 */
export const CustomStep: Story = {
  args: {
    step: 25,
  },
};

/**
 * PriceCounter with narrow range
 */
export const NarrowRange: Story = {
  args: {
    min: 300,
    max: 350,
    step: 5,
  },
};
