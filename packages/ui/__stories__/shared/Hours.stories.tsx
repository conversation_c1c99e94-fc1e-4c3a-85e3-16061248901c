import type { <PERSON>a, StoryObj } from "@storybook/react";

import { Hours } from "../../src/shared/Hours";

const meta = {
  title: "Shared/Hours",
  component: Hours,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Hours>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default hours display
 */
export const Default: Story = {
  args: {
    hours: 24,
  },
};

/**
 * Hours with custom label
 */
export const CustomLabel: Story = {
  args: {
    hours: 8,
    label: "Working Hours",
  },
};

/**
 * Hours display with custom styling
 */
export const CustomStyling: Story = {
  args: {
    hours: 12,
    className: "text-primary",
  },
};

/**
 * Hours display in loading state
 */
export const Loading: Story = {
  args: {
    hours: 0,
    loading: true,
  },
};
