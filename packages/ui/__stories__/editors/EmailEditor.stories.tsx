import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import { EmailEditor } from "@/ui/editors/EmailEditor";

const meta = {
  title: "Editors/EmailEditor",
  component: EmailEditor,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  args: {
    className: "w-[900px]",
    editorHeight: "min-h-[400px]",
    sanitizeHtml: true,
    showSkeleton: true,
  },
} satisfies Meta<typeof EmailEditor>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithInitialContent: Story = {
  args: {
    value:
      "<h2>Welcome to AXA</h2><p>This is a sample email with <strong>rich text</strong> formatting.</p><ul><li>Item 1</li><li>Item 2</li></ul><p>You can edit this content directly in the editor.</p>",
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const CompactSize: Story = {
  args: {
    value: "<p>Compact email editor for smaller spaces.</p>",
    className: "w-[600px] h-[300px]",
    editorHeight: "min-h-[200px]",
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const LargeSize: Story = {
  args: {
    value:
      "<h1>Newsletter Template</h1><p>This is a large email editor perfect for detailed newsletter content creation.</p><h2>Section 1</h2><p>Content goes here...</p><h2>Section 2</h2><p>More content...</p>",
    className: "w-[1200px] h-[700px]",
    editorHeight: "min-h-[500px]",
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithoutSkeleton: Story = {
  args: {
    value: "<p>Clean email editor without skeleton placeholders.</p>",
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithCustomHeaderFooter: Story = {
  args: {
    value:
      "<h2>Email with Custom UI</h2><p>This demonstrates the basic header and footer slot functionality.</p>",
    header: (
      <div className="border-b bg-blue-50 p-4">
        <h4 className="font-medium text-blue-900">Custom Header</h4>
        <p className="text-sm text-blue-700">This is a simple custom header</p>
      </div>
    ),
    footer: (
      <div className="border-t bg-gray-50 p-4">
        <p className="text-sm text-gray-600">Custom footer content</p>
      </div>
    ),
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithLayeredSections: Story = {
  args: {
    value:
      "<h2>Main Email Content</h2><p>This demonstrates the new layered header and footer structure with separate control and content areas.</p>",
    header: (
      <div className="bg-blue-600 p-4 text-white">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">Controls Area (No Padding)</h3>
          <button className="rounded bg-white/20 px-3 py-1 text-sm">
            Action
          </button>
        </div>
      </div>
    ),
    headerContent: (
      <div className="text-sm text-muted-foreground">
        <p>
          <strong>Header Content Area (With Padding):</strong> This text appears
          as part of the email content with proper padding.
        </p>
      </div>
    ),
    footerContent: (
      <div className="text-sm text-muted-foreground">
        <p>
          <strong>Footer Content Area (With Padding):</strong> This text also
          appears as part of the email content.
        </p>
      </div>
    ),
    footer: (
      <div className="bg-gray-100 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">
            Footer Controls Area (No Padding)
          </span>
          <button className="rounded border border-gray-300 px-3 py-1 text-sm">
            Another Action
          </button>
        </div>
      </div>
    ),
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <EmailEditor {...args} value={value} onChange={handleChange} />;
  },
};
