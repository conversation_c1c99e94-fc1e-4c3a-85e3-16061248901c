import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import { EmailEditorComponent } from "@/ui/editors/Email";

const meta = {
  title: "Editors/EmailEditor",
  component: EmailEditorComponent,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof EmailEditorComponent>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return (
      <div style={{ width: "900px", height: "500px" }}>
        <EmailEditorComponent {...args} value={value} onChange={handleChange} />
      </div>
    );
  },
};

export const WithInitialContent: Story = {
  args: {
    value:
      "<h2>Welcome to AXA</h2><p>This is a sample email with <strong>rich text</strong> formatting.</p><ul><li>Item 1</li><li>Item 2</li></ul>",
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return (
      <div style={{ width: "900px", height: "500px" }}>
        <EmailEditorComponent {...args} value={value} onChange={handleChange} />
      </div>
    );
  },
};
