import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JobPostComposer } from "@/ui/editors/JobPostComposer";

const meta = {
  title: "Editors/JobPostComposer",
  component: JobPostComposer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  args: {
    className: "w-[1000px] h-[600px]",
    editorHeight: "min-h-[400px]",
    sanitizeHtml: true,
  },
} satisfies Meta<typeof JobPostComposer>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    value:
      "<h1>Software Engineer</h1><p>Start writing your job description here...</p>",
    metadata: {
      title: "Software Engineer",
      status: "draft",
      priority: "standard",
      jobType: "full-time",
      company: "TechCorp Inc.",
      location: "San Francisco, CA",
      salaryRange: "$120k - $160k",
      applicationCount: 0,
      lastSaved: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    },
    actions: {
      onPreview: () => console.log("Preview job post"),
      onSave: () => console.log("Save draft"),
      onPublish: () => console.log("Publish job post"),
      onSettings: () => console.log("Open settings"),
    },
  },
};

export const PublishedJob: Story = {
  args: {
    value: `
      <h1>Senior Full-Stack Developer</h1>
      <h2>About Us</h2>
      <p>We're a fast-growing startup building the future of developer tools. Our mission is to make software development more accessible and enjoyable for everyone.</p>
      
      <h2>What You'll Do</h2>
      <ul>
        <li>Build and maintain our core platform using React, Node.js, and TypeScript</li>
        <li>Collaborate with our design team to create intuitive user experiences</li>
        <li>Mentor junior developers and contribute to our engineering culture</li>
        <li>Work on challenging problems at scale</li>
      </ul>
      
      <h2>Requirements</h2>
      <ul>
        <li>5+ years of full-stack development experience</li>
        <li>Strong proficiency in React, TypeScript, and Node.js</li>
        <li>Experience with cloud platforms (AWS, GCP, or Azure)</li>
        <li>Excellent communication and collaboration skills</li>
      </ul>
      
      <h2>Benefits</h2>
      <ul>
        <li>Competitive salary and equity package</li>
        <li>Comprehensive health, dental, and vision insurance</li>
        <li>Flexible work arrangements and remote options</li>
        <li>$2000 annual learning and development budget</li>
      </ul>
    `,
    metadata: {
      title: "Senior Full-Stack Developer",
      status: "published",
      priority: "featured",
      jobType: "full-time",
      company: "DevTools Startup",
      location: "New York, NY",
      salaryRange: "$140k - $180k",
      applicationCount: 47,
      lastSaved: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRemote: true,
      isFeatured: true,
    },
    actions: {
      onPreview: () => console.log("Preview published job"),
      onSave: () => console.log("Update job post"),
      onSettings: () => console.log("Job settings"),
      onViewApplications: () => console.log("View 47 applications"),
    },
  },
};

export const UrgentPosition: Story = {
  args: {
    value: `
      <h1>URGENT: DevOps Engineer - Immediate Start</h1>
      <h2>Critical Role</h2>
      <p><strong>We need to fill this position immediately due to rapid growth and infrastructure demands.</strong></p>
      
      <h2>What We Need</h2>
      <ul>
        <li>Expert-level AWS/Kubernetes experience</li>
        <li>CI/CD pipeline management</li>
        <li>Infrastructure as Code (Terraform preferred)</li>
        <li>Monitoring and alerting systems</li>
        <li>Security best practices</li>
      </ul>
      
      <h2>Why Join Us</h2>
      <ul>
        <li>Immediate impact on growing platform</li>
        <li>Work with cutting-edge technology</li>
        <li>Competitive compensation + signing bonus</li>
        <li>Fast-track career progression</li>
      </ul>
    `,
    metadata: {
      title: "DevOps Engineer - Urgent Hire",
      status: "published",
      priority: "urgent",
      jobType: "full-time",
      company: "ScaleUp Technologies",
      location: "Austin, TX",
      salaryRange: "$130k - $170k + bonus",
      applicationCount: 23,
      lastSaved: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      isFeatured: true,
    },
    actions: {
      onPreview: () => console.log("Preview urgent job"),
      onSave: () => console.log("Update urgent job"),
      onPublish: () => console.log("Boost visibility"),
      onSettings: () => console.log("Urgent job settings"),
      onViewApplications: () => console.log("Review applications"),
    },
  },
};

export const RemoteInternship: Story = {
  args: {
    value: `
      <h1>Software Engineering Internship - Summer 2024</h1>
      <h2>Launch Your Career</h2>
      <p>Join our summer internship program and work alongside experienced engineers on real projects that impact millions of users.</p>
      
      <h2>What You'll Learn</h2>
      <ul>
        <li>Modern web development with React and TypeScript</li>
        <li>Backend development with Node.js and databases</li>
        <li>Agile development practices</li>
        <li>Code review and testing methodologies</li>
        <li>Product development lifecycle</li>
      </ul>
      
      <h2>Requirements</h2>
      <ul>
        <li>Currently pursuing a degree in Computer Science or related field</li>
        <li>Basic knowledge of JavaScript and web technologies</li>
        <li>Strong problem-solving skills</li>
        <li>Eagerness to learn and grow</li>
      </ul>
      
      <h2>Program Benefits</h2>
      <ul>
        <li>Mentorship from senior engineers</li>
        <li>Real project ownership</li>
        <li>Networking opportunities</li>
        <li>Potential for full-time offer</li>
      </ul>
    `,
    metadata: {
      title: "Software Engineering Internship",
      status: "published",
      priority: "standard",
      jobType: "internship",
      company: "Global Tech Corp",
      location: "Remote",
      salaryRange: "$25/hour",
      applicationCount: 156,
      lastSaved: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      isRemote: true,
    },
    actions: {
      onPreview: () => console.log("Preview internship"),
      onSave: () => console.log("Update internship"),
      onSettings: () => console.log("Internship settings"),
      onViewApplications: () => console.log("Review 156 applications"),
    },
    features: {
      showPriority: false,
      showFeaturedBadge: false,
    },
  },
};

export const ContractPosition: Story = {
  args: {
    value: `
      <h1>Frontend React Developer - 6 Month Contract</h1>
      <h2>Project Overview</h2>
      <p>We're building a new customer portal and need an experienced React developer to lead the frontend development.</p>
      
      <h2>Contract Details</h2>
      <ul>
        <li>6-month initial contract with extension possibility</li>
        <li>Remote work with quarterly in-person meetings</li>
        <li>Direct collaboration with our design team</li>
        <li>Modern tech stack: React 18, TypeScript, Tailwind CSS</li>
      </ul>
      
      <h2>Ideal Candidate</h2>
      <ul>
        <li>3+ years of React development experience</li>
        <li>Strong TypeScript skills</li>
        <li>Experience with modern CSS frameworks</li>
        <li>Portfolio of recent frontend work</li>
      </ul>
    `,
    metadata: {
      title: "Frontend React Developer Contract",
      status: "published",
      priority: "standard",
      jobType: "contract",
      company: "Digital Agency LLC",
      location: "Remote",
      salaryRange: "$80-100/hour",
      applicationCount: 12,
      lastSaved: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      isRemote: true,
    },
    actions: {
      onPreview: () => console.log("Preview contract job"),
      onSave: () => console.log("Update contract"),
      onPublish: () => console.log("Extend contract posting"),
      onSettings: () => console.log("Contract settings"),
      onViewApplications: () => console.log("Review applications"),
    },
  },
};

export const MinimalJobPost: Story = {
  args: {
    value: "<h2>Quick Job Posting</h2><p>Simple job description here...</p>",
    metadata: {
      title: "Junior Developer",
      status: "draft",
    },
    actions: {
      onSave: () => console.log("Save"),
      onPublish: () => console.log("Publish"),
    },
    features: {
      showCompany: false,
      showLocation: false,
      showSalaryRange: false,
      showApplicationCount: false,
      showLastSaved: false,
      showRemoteBadge: false,
      showFeaturedBadge: false,
      showPreviewButton: false,
      showSettingsButton: false,
      showApplicationsButton: false,
      showScheduleButton: false,
    },
    className: "w-[800px] h-[400px]",
    editorHeight: "min-h-[250px]",
  },
};

export const CustomLayout: Story = {
  args: {
    value:
      "<h1>Custom Job Post Interface</h1><p>This job post uses completely custom header and footer components.</p>",
    customHeader: (
      <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
        <h2 className="text-xl font-bold">Custom Job Portal</h2>
        <p className="text-green-100">Advanced job posting system</p>
        <div className="mt-4 flex gap-2">
          <button className="rounded bg-white/20 px-4 py-2 text-sm hover:bg-white/30">
            Custom Save
          </button>
          <button className="rounded bg-white/20 px-4 py-2 text-sm hover:bg-white/30">
            Custom Publish
          </button>
        </div>
      </div>
    ),
    customFooter: (
      <div className="bg-gray-100 p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <p>Custom Footer Area</p>
            <p>Job ID: custom-job-001 | Created: Today</p>
          </div>
          <div className="flex gap-2">
            <button className="rounded border border-gray-300 px-3 py-1 text-sm hover:bg-gray-50">
              Export
            </button>
            <button className="rounded bg-green-600 px-3 py-1 text-sm text-white hover:bg-green-700">
              Go Live
            </button>
          </div>
        </div>
      </div>
    ),
  },
};
