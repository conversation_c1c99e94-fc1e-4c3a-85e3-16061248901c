import type { Meta, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import { JobPostEditor } from "@/ui/editors/JobPostEditor";

const meta = {
  title: "Editors/JobPostEditor",
  component: JobPostEditor,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  args: {
    className: "w-[900px] h-[500px]",
    editorHeight: "min-h-[400px]",
    sanitizeHtml: true,
    showSkeleton: true,
  },
} satisfies Meta<typeof JobPostEditor>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <JobPostEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithInitialContent: Story = {
  args: {
    value: `
      <h1>Senior Software Engineer</h1>
      <h2>About the Role</h2>
      <p>We're looking for an experienced software engineer to join our growing team...</p>
      <h2>Requirements</h2>
      <ul>
        <li>5+ years of software development experience</li>
        <li>Strong knowledge of React and TypeScript</li>
        <li>Experience with modern development practices</li>
      </ul>
      <h2>What We Offer</h2>
      <ul>
        <li>Competitive salary and benefits</li>
        <li>Remote-first culture</li>
        <li>Professional development opportunities</li>
      </ul>
    `,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <JobPostEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithCustomHeader: Story = {
  args: {
    value:
      "<h1>Job Post with Custom Header</h1><p>This demonstrates basic header functionality for job posts.</p>",
    header: (
      <div className="border-b bg-blue-50 p-4">
        <h4 className="font-medium text-blue-900">Job Post Controls</h4>
        <p className="text-sm text-blue-700">
          Basic header area for job post metadata
        </p>
      </div>
    ),
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <JobPostEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const WithHeaderAndFooter: Story = {
  args: {
    value:
      "<h1>Full-Stack Developer Position</h1><p>Join our team and help build the future of web development.</p>",
    header: (
      <div className="bg-green-50 p-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-green-900">Job Post Header</h4>
          <button className="rounded bg-green-600 px-3 py-1 text-sm text-white">
            Save Draft
          </button>
        </div>
      </div>
    ),
    footer: (
      <div className="bg-gray-50 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Job Post Footer</span>
          <button className="rounded border border-gray-300 px-3 py-1 text-sm">
            Publish
          </button>
        </div>
      </div>
    ),
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <JobPostEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const LargeEditor: Story = {
  args: {
    value: `
      <h1>Director of Engineering</h1>
      <h2>Company Overview</h2>
      <p>Our company is at the forefront of innovation in the tech industry...</p>
      
      <h2>Role Description</h2>
      <p>As Director of Engineering, you will lead a team of talented engineers...</p>
      
      <h2>Key Responsibilities</h2>
      <ul>
        <li>Lead and mentor engineering teams</li>
        <li>Drive technical strategy and architecture decisions</li>
        <li>Collaborate with product and design teams</li>
        <li>Ensure code quality and development best practices</li>
      </ul>
      
      <h2>Qualifications</h2>
      <ul>
        <li>10+ years of software engineering experience</li>
        <li>5+ years in engineering leadership roles</li>
        <li>Strong technical background in modern web technologies</li>
        <li>Excellent communication and leadership skills</li>
      </ul>
    `,
    className: "w-[1200px] h-[700px]",
    editorHeight: "min-h-[500px]",
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <JobPostEditor {...args} value={value} onChange={handleChange} />;
  },
};

export const CompactEditor: Story = {
  args: {
    value:
      "<h2>Quick Job Post</h2><p>A compact editor for simple job postings.</p>",
    className: "w-[600px] h-[300px]",
    editorHeight: "min-h-[200px]",
    showSkeleton: false,
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return <JobPostEditor {...args} value={value} onChange={handleChange} />;
  },
};
