import Image from "next/image";

export const initialContent = `
<p>Thank you for accepting the work order. Upon arriving onsite, please text 
<a href="tel:+***********" style="color: #299487;">+****************</a> 
and notify us about your arrival. For any onsite support, please call 
<a href="tel:+***********" style="color: #299487;">+****************</a> 
or <a href="tel:+***********" style="color: #299487;">+****************</a>.</p>

<p>While onsite, you are <strong>representing AXA Professionals</strong>. Our dress code is 
<strong>business casual</strong>, with a long sleeve shirt <strong>(no jeans or sneakers)</strong>. 
Please make sure you bring:</p>

<ul>
  <li>Valid drivers license or government issued ID - electronic IDs are <strong>NOT ACCEPTED</strong> onsite</li>
</ul>
`.trim();

export const headerContent = (
  <div className="space-y-4">
    {/* Email <PERSON>er */}
    <div className="text-center">
      <Image
        src="https://tech.axapro.com/images/brand/logo.png"
        width="162"
        height="97"
        alt="AXA Professionals"
        className="mx-auto"
      />
    </div>

    {/* Work Order Heading */}
    <h1 className="mb-6 text-center text-2xl font-normal text-foreground">
      Work Order for{" "}
      <span className="inline-flex items-center rounded-lg bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
        organization.name
      </span>
    </h1>

    {/* Greeting */}
    <p className="text-sm text-foreground">
      Hi{" "}
      <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
        firstName
      </span>{" "}
      <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
        lastName
      </span>
      ,
    </p>
  </div>
);

export const footerContent = (
  <div className="space-y-6">
    {/* Work Order Details */}
    <div className="space-y-4">
      <div>
        <h2 className="mb-2 text-base font-medium text-foreground">Date</h2>
        <p className="text-sm text-foreground">
          <span className="inline-flex items-center rounded-lg bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            startTime
          </span>
        </p>
      </div>

      <div>
        <h2 className="mb-2 text-base font-medium text-foreground">Location</h2>
        <p className="text-sm text-foreground">
          <a
            href="#"
            className="text-teal-600 hover:underline dark:text-teal-400"
          >
            <span className="inline-flex items-center rounded-lg bg-orange-100 px-3 py-1 text-sm font-medium text-orange-800 dark:bg-orange-900 dark:text-orange-200">
              address
            </span>
          </a>
        </p>
      </div>

      <div>
        <h2 className="mb-2 text-base font-medium text-foreground">Contacts</h2>
        <p className="text-sm text-foreground">
          <span className="inline-flex items-center rounded-full bg-pink-100 px-2 py-1 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-200">
            contact.role
          </span>
          :{" "}
          <span className="inline-flex items-center rounded-full bg-pink-100 px-2 py-1 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-200">
            contact.name
          </span>{" "}
          <a
            href="#"
            className="text-teal-600 hover:underline dark:text-teal-400"
          >
            <span className="inline-flex items-center rounded-full bg-pink-100 px-2 py-1 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-200">
              contact.phone
            </span>
          </a>
        </p>
      </div>

      <div>
        <h2 className="mb-2 text-base font-medium text-foreground">
          Scope of Work
        </h2>
        <p className="text-sm font-bold text-foreground">
          <span className="inline-flex items-center rounded-lg bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
            summary
          </span>
        </p>
        <p className="mt-1 text-sm text-foreground">
          <span className="inline-flex items-center rounded-lg bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">
            scope
          </span>
        </p>
      </div>
    </div>

    {/* Code of Conduct */}
    <div className="rounded border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-950">
      <h3 className="mb-2 font-medium text-yellow-800 dark:text-yellow-200">
        Code of Conduct
      </h3>
      <div className="space-y-2 text-sm text-yellow-700 dark:text-yellow-300">
        <p>
          By accepting this work order you agree to the terms below. Failure to
          comply will result in immediate termination of your contract.
        </p>
        <p>
          <strong>NO</strong> weapons of any kind are permitted onsite, even
          concealed.
        </p>
        <p>
          <strong>DO NOT</strong> under any circumstances take any pictures
          onsite - even if you are asked to, we are not permitted to take any
          pictures on the premise.
        </p>
        <p>
          <strong>DO NOT</strong> bring or substitute any other person onsite
          without prior written approval or they will be{" "}
          <strong>considered trespassing</strong>.
        </p>
        <p>
          Please be <strong>professional at all times</strong>, as client
          locations are under constant video surveillance.
        </p>
      </div>
    </div>

    {/* Closing */}
    <p className="text-sm text-foreground">
      Thank you,
      <br />
      AXA Professionals
    </p>

    <hr className="border-border" />

    {/* Email Footer */}
    <div className="text-xs text-muted-foreground">
      <p>
        This email was intended for{" "}
        <strong>
          <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
            firstName
          </span>{" "}
          <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
            lastName
          </span>
        </strong>
        . If you were not expecting this email, you can ignore it. If you have
        any questions or concerns, please reach out to{" "}
        <a
          href="mailto:<EMAIL>"
          className="text-teal-600 hover:underline dark:text-teal-400"
        >
          <EMAIL>
        </a>
        .
      </p>
    </div>
  </div>
);
