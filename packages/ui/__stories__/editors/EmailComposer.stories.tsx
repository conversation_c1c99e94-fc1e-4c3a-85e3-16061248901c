import type { <PERSON>a, StoryObj } from "@storybook/react";

import { EmailComposer } from "@/ui/editors/EmailComposer";

const meta = {
  title: "Editors/EmailComposer",
  component: EmailComposer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  args: {
    className: "w-[1000px] h-[600px]",
    editorHeight: "min-h-[400px]",
    sanitizeHtml: true,
  },
} satisfies Meta<typeof EmailComposer>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    value: "<h1>Email Campaign</h1><p>Start composing your email here...</p>",
    metadata: {
      title: "Marketing Campaign",
      status: "draft",
      priority: "normal",
      recipientCount: 1247,
      lastSaved: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
    },
    actions: {
      onPreview: () => console.log("Preview email"),
      onSave: () => console.log("Save draft"),
      onSend: () => console.log("Send email"),
      onSettings: () => console.log("Open settings"),
    },
  },
};

export const HighPriorityWithAbTest: Story = {
  args: {
    value:
      "<h1>URGENT: Security Update Required</h1><p>This is a high-priority security notification that requires immediate attention from all users.</p><blockquote><p><strong>Action Required:</strong> Please update your password within 24 hours.</p></blockquote><p>Best regards,<br/>The Security Team</p>",
    metadata: {
      title: "Security Alert Campaign",
      status: "draft",
      priority: "urgent",
      recipientCount: 15420,
      lastSaved: new Date(Date.now() - 30 * 1000), // 30 seconds ago
      isAbTestReady: true,
      campaignType: "Security Alert",
    },
    actions: {
      onPreview: () => console.log("Preview security alert"),
      onSave: () => console.log("Save urgent draft"),
      onSend: () => console.log("Send security alert"),
      onSchedule: () => console.log("Schedule delivery"),
      onSettings: () => console.log("Configure A/B test"),
    },
    features: {
      showScheduleButton: true,
    },
  },
};

export const ScheduledNewsletter: Story = {
  args: {
    value:
      "<h1>Monthly Newsletter - March 2024</h1><h2>What's New This Month</h2><p>Discover the latest updates, features, and announcements from our team.</p><h3>Product Updates</h3><ul><li>New dashboard interface</li><li>Enhanced reporting tools</li><li>Mobile app improvements</li></ul><h3>Upcoming Events</h3><p>Join us for our quarterly webinar series...</p>",
    metadata: {
      title: "Monthly Newsletter - March 2024",
      status: "scheduled",
      priority: "normal",
      recipientCount: 8934,
      lastSaved: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
      campaignType: "Newsletter",
    },
    actions: {
      onPreview: () => console.log("Preview newsletter"),
      onSave: () => console.log("Update scheduled email"),
      onSettings: () => console.log("Modify schedule"),
    },
    features: {
      showSendButton: false,
      showScheduleButton: true,
    },
    className: "w-[1200px] h-[700px]",
    editorHeight: "min-h-[500px]",
  },
};

export const MinimalComposer: Story = {
  args: {
    value: "<p>Simple email composition</p>",
    metadata: {
      title: "Quick Email",
    },
    actions: {
      onSave: () => console.log("Save"),
      onSend: () => console.log("Send"),
    },
    features: {
      showStatus: false,
      showPriority: false,
      showLastSaved: false,
      showRecipientCount: false,
      showAbTestBadge: false,
      showPreviewButton: false,
      showSettingsButton: false,
      showScheduleButton: false,
    },
    className: "w-[800px] h-[400px]",
    editorHeight: "min-h-[250px]",
  },
};

export const CustomHeaderFooter: Story = {
  args: {
    value:
      "<h1>Custom Layout Email</h1><p>This email uses completely custom header and footer components.</p>",
    customHeader: (
      <div className="rounded-t-lg bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
        <h2 className="text-xl font-bold">Custom Email Builder</h2>
        <p className="text-blue-100">Advanced composition tools</p>
      </div>
    ),
    customFooter: (
      <div className="rounded-b-lg border-t bg-gray-50 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">
            Powered by Custom Editor
          </span>
          <div className="flex gap-2">
            <button className="rounded bg-blue-500 px-3 py-1 text-sm text-white">
              Custom Action
            </button>
            <button className="rounded border border-gray-300 px-3 py-1 text-sm">
              Another Action
            </button>
          </div>
        </div>
      </div>
    ),
  },
};

export const LowPriorityDraft: Story = {
  args: {
    value:
      "<h2>Internal Team Update</h2><p>This is a low-priority internal communication for team members.</p><p>Please review when convenient.</p>",
    metadata: {
      title: "Team Update - Internal",
      status: "draft",
      priority: "low",
      recipientCount: 45,
      lastSaved: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      campaignType: "Internal",
    },
    actions: {
      onPreview: () => console.log("Preview team update"),
      onSave: () => console.log("Save team draft"),
      onSend: () => console.log("Send to team"),
    },
    features: {
      showAbTestBadge: false,
      showScheduleButton: false,
    },
  },
};
