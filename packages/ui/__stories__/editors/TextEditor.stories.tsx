import type { Meta, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import { TextEditor } from "@/ui/editors/RichTextEditor";

const meta = {
  title: "Editors/TextEditor",
  component: TextEditor,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof TextEditor>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return (
      <div style={{ width: "700px", height: "400px" }}>
        <TextEditor {...args} value={value} onChange={handleChange} />
      </div>
    );
  },
};

export const WithInitialContent: Story = {
  args: {
    value:
      "<h2>AXA Professional Documentation</h2><p>This is an example of <strong>rich text</strong> content.</p><ul><li>Feature 1</li><li>Feature 2</li></ul>",
  },
  render: function Render(args) {
    const [{ value }, updateArgs] = useArgs();
    const handleChange = (newValue: string) => {
      updateArgs({ value: newValue });
    };

    return (
      <div style={{ width: "700px", height: "400px" }}>
        <TextEditor {...args} value={value} onChange={handleChange} />
      </div>
    );
  },
};
