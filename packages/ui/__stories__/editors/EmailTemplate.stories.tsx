import type { Meta, StoryObj } from "@storybook/react";

import { fn } from "storybook/test";

import { EmailTemplate } from "@/ui/editors/EmailTemplate";
import EntityForm from "@/ui/forms/Entity";

// real world template
import {
  footerContent,
  headerContent,
  initialContent,
} from "./templates/tech-dispatch";

const meta = {
  title: "Editors/EmailTemplate",
  component: EmailTemplate,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  args: {
    className: "w-[1000px]",
    editorHeight: "min-h-[300px]",
    sanitizeHtml: true,
  },
} satisfies Meta<typeof EmailTemplate>;

export default meta;

type Story = StoryObj<typeof meta>;

export const NewTemplate: Story = {
  args: {
    templateContent: {
      title: "",
      content: "<p>Start creating your email template here...</p>",
      additionalMetadata: {},
    },
    actions: {
      onSave: fn(),
      onCancel: fn(),
    },
    features: {
      showDeleteButton: false,
      showCancelButton: true,
    },
  },
};

export const WelcomeEmailTemplate: Story = {
  args: {
    templateMetadata: {
      id: "welcome-001",
      title: "Welcome Email Template",
      description: "Standard welcome email for new users",
      category: "Onboarding",
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      lastModified: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      usageCount: 157,
      tags: ["welcome", "onboarding", "new-user"],
      isPublic: true,
      author: "Design Team",
    },
    templateContent: {
      title: "Welcome to AXA - Let's Get Started!",
      content: `
        <h1>Welcome to AXA!</h1>
        <p>We're excited to have you join our community of professionals.</p>
        
        <h2>Getting Started</h2>
        <p>Here are your next steps:</p>
        <ol>
          <li>Complete your profile setup</li>
          <li>Explore our professional dashboard</li>
          <li>Connect with your team members</li>
        </ol>
        
        <p>If you have any questions, our support team is here to help.</p>
      `,
      additionalMetadata: {
        trigger: "account_activation",
        frequency: "once",
        segment: "new_users",
      },
    },
    headerContent: (
      <div className="text-sm text-muted-foreground">
        <p>
          <strong>Automated Message:</strong> This email is automatically sent
          to new users upon account activation.
        </p>
      </div>
    ),
    footerContent: (
      <div className="text-sm text-muted-foreground">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>
          For support, contact us at{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-blue-600 hover:underline"
          >
            <EMAIL>
          </a>
        </p>
      </div>
    ),
    actions: {
      onSave: fn(),
      onDelete: fn(),
      onDuplicate: fn(),
    },
  },
};

export const SecurityAlertTemplate: Story = {
  args: {
    templateMetadata: {
      id: "security-001",
      title: "Security Alert Template",
      description: "Critical security notifications for users",
      category: "Security",
      createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
      lastModified: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      usageCount: 43,
      tags: ["security", "alert", "urgent"],
      isPublic: false,
      author: "Security Team",
    },
    templateContent: {
      title: "URGENT: Security Alert - Action Required",
      content: `
        <div style="border-left: 4px solid #dc2626; padding-left: 16px; margin: 16px 0;">
          <h1 style="color: #dc2626;">Security Alert</h1>
          <p><strong>We detected unusual activity on your account.</strong></p>
        </div>
        
        <h2>Immediate Action Required</h2>
        <p>Please take the following steps immediately:</p>
        <ul>
          <li>Change your password</li>
          <li>Review recent account activity</li>
          <li>Enable two-factor authentication</li>
        </ul>
        
        <div style="background-color: #fef2f2; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <p><strong>Note:</strong> If you did not request this alert, please contact our security team immediately.</p>
        </div>
      `,
      additionalMetadata: {
        priority: "urgent",
        escalation_level: "high",
        response_required: "true",
      },
    },
    headerContent: (
      <div className="border-l-4 border-red-400 bg-red-50 p-3">
        <div className="text-sm text-red-700">
          <p>
            <strong>URGENT SECURITY NOTIFICATION</strong>
          </p>
          <p>
            This template is used for critical security notifications that
            require immediate user action.
          </p>
        </div>
      </div>
    ),
    footerContent: (
      <div className="text-sm text-gray-600">
        <p>
          <strong>Emergency Contact:</strong> For immediate assistance, contact
          our security team at{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-red-600 hover:underline"
          >
            <EMAIL>
          </a>{" "}
          or call ******-SECURITY.
        </p>
      </div>
    ),
    actions: {
      onSave: fn(),
      onDelete: fn(),
      onDuplicate: fn(),
    },
  },
};

export const NewsletterTemplate: Story = {
  args: {
    templateMetadata: {
      id: "newsletter-001",
      title: "Monthly Newsletter Template",
      description: "Monthly company newsletter layout",
      category: "Newsletter",
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
      lastModified: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      usageCount: 12,
      tags: ["newsletter", "monthly", "company-updates"],
      isPublic: true,
      author: "Marketing Team",
    },
    templateContent: {
      title: "AXA Monthly Newsletter - {MONTH} {YEAR}",
      content: `
        <h1>What's New at AXA</h1>
        <p>Your monthly update on company news, product updates, and industry insights.</p>
        
        <h2>This Month's Highlights</h2>
        <ul>
          <li>Product feature updates</li>
          <li>Team achievements</li>
          <li>Industry news and insights</li>
        </ul>
        
        <h2>Featured Article</h2>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        
        <h2>Upcoming Events</h2>
        <p>Mark your calendars for these upcoming events and webinars...</p>
      `,
      additionalMetadata: {
        frequency: "monthly",
        audience: "all_subscribers",
        template_variables: "month,year",
      },
    },
    headerContent: (
      <div className="border-l-4 border-blue-400 bg-blue-50 p-3">
        <div className="text-sm text-blue-700">
          <p>
            <strong>Template Variables:</strong> Replace {"{MONTH}"} and{" "}
            {"{YEAR}"} placeholders with current month and year before sending.
          </p>
        </div>
      </div>
    ),
    footerContent: (
      <div className="text-sm text-gray-600">
        <p>
          You're receiving this newsletter because you're subscribed to AXA
          updates.
        </p>
        <p>
          <a href="#" className="text-blue-600 hover:underline">
            Unsubscribe
          </a>{" "}
          |{" "}
          <a href="#" className="text-blue-600 hover:underline">
            Update preferences
          </a>
        </p>
      </div>
    ),
    actions: {
      onSave: fn(),
      onDelete: fn(),
      onDuplicate: fn(),
    },
    className: "w-[1200px] h-[800px]",
    editorHeight: "min-h-[400px]",
  },
};

export const MinimalTemplate: Story = {
  args: {
    templateMetadata: {
      title: "Simple Template",
    },
    templateContent: {
      title: "Simple Email",
      content: "<p>Basic email content</p>",
    },
    actions: {
      onSave: fn(),
    },
    features: {
      showDeleteButton: false,
      showDuplicateButton: false,
      showMetadataFields: false,
      showUsageStats: false,
      showTags: false,
    },
    className: "w-[800px] h-[500px]",
  },
};

export const TemplateWithAllFeatures: Story = {
  args: {
    templateMetadata: {
      id: "full-featured-001",
      title: "Full Featured Template",
      description: "Template showcasing all available features",
      category: "Marketing",
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      lastModified: new Date(Date.now() - 5 * 60 * 1000),
      usageCount: 89,
      tags: ["marketing", "promotional", "featured"],
      isPublic: true,
      author: "Template Team",
    },
    templateContent: {
      title: "Comprehensive Email Template",
      content: `
        <h1>Feature-Rich Email Template</h1>
        <p>This template showcases all the features available in the EmailTemplate component.</p>
        
        <h2>Key Features</h2>
        <ul>
          <li>Rich text editing</li>
          <li>Header and footer content areas</li>
          <li>Template metadata management</li>
          <li>Usage statistics tracking</li>
          <li>Tag-based organization</li>
        </ul>
      `,
      additionalMetadata: {
        version: "2.1",
        compatibility: "all_clients",
        responsive: "true",
      },
    },
    headerContent: (
      <div className="border-l-4 border-purple-400 bg-purple-50 p-3">
        <div className="text-sm text-purple-700">
          <p>
            <strong>Full Feature Demo:</strong> This template demonstrates all
            available features and customization options.
          </p>
        </div>
      </div>
    ),
    footerContent: (
      <div className="text-sm text-gray-600">
        <p>
          This footer demonstrates the footer content functionality with
          additional information.
        </p>
        <p>
          <strong>Template Info:</strong> Version 2.1 | Compatible with all
          clients | Responsive design enabled
        </p>
      </div>
    ),
    actions: {
      onSave: fn(),
      onDelete: fn(),
      onDuplicate: fn(),
      onCancel: fn(),
    },
    features: {
      showDeleteButton: true,
      showDuplicateButton: true,
      showCancelButton: true,
      showMetadataFields: true,
      showUsageStats: true,
      showTags: true,
      showTemplateFooter: true,
    },
  },
};

export const CustomLayout: Story = {
  args: {
    templateContent: {
      title: "Custom Layout Template",
      content:
        "<h1>Custom Header and Footer</h1><p>This template uses completely custom header and footer components.</p>",
    },
    headerContent: (
      <div className="border border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-3">
        <div className="text-sm text-green-800">
          <p>
            <strong>Custom Layout Demo:</strong> This example shows how header
            content appears with custom styling.
          </p>
        </div>
      </div>
    ),
    footerContent: (
      <div className="border border-orange-200 bg-gradient-to-r from-orange-50 to-red-50 p-3">
        <div className="text-sm text-orange-800">
          <p>
            <strong>Custom Footer Content:</strong> This footer content has
            independent styling from the template header/footer controls.
          </p>
        </div>
      </div>
    ),
    templateHeader: (
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
        <h1 className="text-2xl font-bold">Custom Template Builder</h1>
        <p className="text-purple-100">Advanced template management system</p>
        <div className="mt-4 flex gap-2">
          <button className="rounded bg-white/20 px-4 py-2 text-sm hover:bg-white/30">
            Custom Save
          </button>
          <button className="rounded bg-white/20 px-4 py-2 text-sm hover:bg-white/30">
            Custom Action
          </button>
        </div>
      </div>
    ),
    templateFooter: (
      <div className="bg-gray-100 p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            <p>Custom Footer Area</p>
            <p>Template ID: custom-001 | Version: 1.0</p>
          </div>
          <div className="flex gap-2">
            <button className="rounded border border-gray-300 px-3 py-1 text-sm hover:bg-gray-50">
              Export
            </button>
            <button className="rounded bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700">
              Publish
            </button>
          </div>
        </div>
      </div>
    ),
    features: {
      showTemplateFooter: true,
    },
  },
};

// New story demonstrating EmailEditor mode (without template management)
export const EmailEditorMode: Story = {
  args: {
    showTemplateManagement: false,
    templateContent: {
      title: "Simple Email Content",
      content: `
        <h1>Pure Email Editor</h1>
        <p>This demonstrates using EmailTemplate in pure EmailEditor mode without template management UI.</p>
        <p>All EmailEditor props pass through directly when showTemplateManagement is false.</p>
      `,
    },
    header: (
      <div className="border-b bg-blue-50 p-4">
        <h2 className="text-lg font-semibold text-blue-900">
          Custom Email Header
        </h2>
        <p className="text-sm text-blue-700">
          This header comes from the 'header' prop
        </p>
      </div>
    ),
    headerContent: (
      <div className="text-sm italic text-muted-foreground">
        <p>This appears in the headerContent area (with padding)</p>
      </div>
    ),
    footerContent: (
      <div className="text-sm italic text-muted-foreground">
        <p>This appears in the footerContent area (with padding)</p>
      </div>
    ),
    footer: (
      <div className="border-t bg-gray-50 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Custom Email Footer</span>
          <button className="rounded bg-blue-600 px-3 py-1 text-sm text-white">
            Send Email
          </button>
        </div>
      </div>
    ),
    className: "w-[900px] h-[600px]",
  },
};

export const RealWorldTemplate: Story = {
  args: {
    templateMetadata: {
      title: "AXA Professionals - Technician Dispatch",
      id: "full-featured-001",
      description:
        "Email template for dispatching technicians to work orders with complete instructions and contact information",
      category: "Technicians",
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      lastModified: new Date(Date.now() - 5 * 60 * 1000),
      usageCount: 89,
      tags: ["dispatch", "technician", "work-order"],
      isPublic: true,
      author: "AXA Professionals",
    },
    templateContent: {
      title: "Work Order Assignment - Technician Dispatch",
      content: initialContent,
    },
    headerContent,
    footerContent,
    actions: {
      onSave: fn(async () => {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }),
    },
    showTemplateManagement: true,
    features: {
      showDeleteButton: true,
      showDuplicateButton: false,
      showMetadataFields: false,
      showUsageStats: false,
      showTags: true,
    },
    className: "h-[800px] w-auto",
    editorHeight: "min-h-[200px]",
  },
};
