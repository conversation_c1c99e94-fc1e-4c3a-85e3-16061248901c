import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useArgs } from "storybook/preview-api";

import Editor from "@/ui/editors/Editor";

const meta = {
  title: "Editors/Editor",
  component: Editor,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Editor>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};
