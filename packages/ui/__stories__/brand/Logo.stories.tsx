import type { <PERSON>a, StoryObj } from "@storybook/react";

import { Logo } from "../../src/brand/Logo";

const meta = {
  title: "Brand/Logo",
  component: Logo,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Logo>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    width: 500,
    height: 150,
  },
};

export const WithDifferentColors: Story = {
  args: {
    width: 500,
    height: 150,
    darkColor: "#2C3E50",
    lightColor: "#3498DB",
  },
};

export const WithDifferentTextColor: Story = {
  args: {
    width: 500,
    height: 150,
    textColor: "#8E44AD",
  },
};

export const WithShadow: Story = {
  args: {
    width: 500,
    height: 150,
    shadow: true,
  },
};

export const WithClip: Story = {
  args: {
    width: 500,
    height: 150,
    clip: true,
  },
};
