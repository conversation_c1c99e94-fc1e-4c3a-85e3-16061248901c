import type { Meta, StoryObj } from "@storybook/react";

import { Emblem } from "../../src/brand/Emblem";

const meta = {
  title: "Brand/Emblem",
  component: Emblem,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Emblem>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    size: 200,
  },
};

export const WithDifferentColors: Story = {
  args: {
    size: 200,
    darkColor: "#2C3E50",
    lightColor: "#3498DB",
  },
};

export const WithShadow: Story = {
  args: {
    size: 200,
    shadow: true,
  },
};

export const WithClip: Story = {
  args: {
    size: 200,
    clip: true,
  },
};
