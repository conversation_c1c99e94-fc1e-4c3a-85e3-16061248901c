import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { SelectFieldProps, SelectOption } from "@/ui/fields/Select";

import type { BaseFormProviderArgs } from "./WithFormProvider";

import { SelectField } from "@/ui/fields/Select";

import { WithFormProvider } from "./WithFormProvider";

const sampleOptions: SelectOption[] = [
  { label: "Feature Request", value: "feature" },
  { label: "Bug Report", value: "bug" },
  { label: "General Inquiry", value: "inquiry" },
  { label: "Support Question", value: "support" },
];

// Define a Zod schema based on the sample options
const storyLevelSelectSchema = z.enum(
  sampleOptions.map((opt) => opt.value) as [string, ...string[]],
  { required_error: "Please select an option." },
);

// Define args type for this specific story
interface CurrentSelectStoryArgs
  extends SelectFieldProps,
    BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelSelectSchema;
  defaultValues?: { [key: string]: string | undefined };
}

const meta: Meta<CurrentSelectStoryArgs> = {
  title: "Fields/SelectField",
  component: SelectField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for SelectField component itself
    label: "Feedback Type",
    name: "feedbackType", // This will be the key in the form data
    placeholder: "Select a type...",
    description: "Choose the category that best describes your feedback.",
    options: sampleOptions,

    // Args for the WithFormProvider decorator
    fieldName: "feedbackType", // Must match the 'name' prop above
    fieldSchema: storyLevelSelectSchema,
    onFormSubmit: fn(),
    defaultValues: { feedbackType: undefined }, // Start with no selection to show placeholder
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentSelectStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByText("Select a type...")).toBeInTheDocument();
  },
};

export const WithInitialValue: Story = {
  args: {
    name: "initialFeedback",
    fieldName: "initialFeedback",
    defaultValues: { initialFeedback: "bug" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Check if the label for 'bug' is displayed as the selected value
    await expect(canvas.getByText("Bug Report")).toBeInTheDocument();
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveFeedback",
    fieldName: "interactiveFeedback",
    defaultValues: { interactiveFeedback: undefined },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const trigger = canvas.getByRole("combobox");

    await userEvent.click(trigger);
    const optionToSelect = await canvas.findByText("General Inquiry");
    await userEvent.click(optionToSelect);

    // Check if the selected value is displayed
    await expect(canvas.getByText("General Inquiry")).toBeInTheDocument();

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name as string]: "inquiry" },
      expect.anything(),
    );
  },
};

export const WithErrorRequired: Story = {
  args: {
    name: "requiredFeedback",
    fieldName: "requiredFeedback",
    defaultValues: { requiredFeedback: undefined }, // No initial selection
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Please select an option."),
    ).toBeInTheDocument();
  },
};
