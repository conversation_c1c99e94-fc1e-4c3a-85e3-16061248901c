import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { TemperatureFieldProps, TemperatureUnit } from "@/ui/fields/numeric/Temperature";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import {
  TEMPERATURE_UNITS,
  TemperatureField,
} from "@/ui/fields/numeric/Temperature";

import { WithFormProvider } from "../WithFormProvider";

// Zod schema for the numeric temperature value (string input converted to number)
const createStoryLevelTemperatureValueSchema = () =>
  z.preprocess(
    (val) => {
      if (typeof val === "string" && val.trim() === "") return undefined;
      if (val === undefined || val === null) return undefined;
      const num = Number(val);
      // If parsing results in NaN, pass the original string to let <PERSON><PERSON> catch it as not a number
      // Zod's .number() will coerce valid number strings.
      return isNaN(num) && typeof val === 'string' ? val : num;
    },
    z
      .number({
        invalid_type_error: "Temperature must be a valid number.",
      })
      .optional(), // .nullable() if you want to distinguish between empty and null
  );

// Keep old schema for reference or if TemperatureField itself needs it for internal validation
// const createTemperatureObjectSchema = (valueField: string, unitField: string) =>
//   z.object({
//     [valueField]: z.coerce
//       .number({
//         invalid_type_error: "Temperature value must be a number.",
//       })
//       .optional()
//       .or(z.literal(""))
//       .or(z.nan()),
//     [unitField]: z.enum(TEMPERATURE_UNITS, {
//       required_error: "Temperature unit is required.",
//     }),
//   });

interface CurrentTemperatureStoryArgs
  extends Omit<
      TemperatureFieldProps,
      "name" | "unitName" | "defaultValue" // defaultUnit is a valid prop we might want to set
    >,
    BaseFormProviderArgs<
      string, // TFieldName (the type of 'fieldName' itself)
      ReturnType<typeof createStoryLevelTemperatureValueSchema> // TFieldSchema (the Zod schema type)
    > {
  // fieldValue will be inferred as z.infer<ReturnType<typeof createStoryLevelTemperatureValueSchema>>
  // Props for TemperatureField component itself
  name: string; // RHF name for the numeric input part, matches fieldName
  // unitName is now typed as TemperatureUnit to align with TemperatureFieldProps
  // This means the RHF field name for the unit will be the unit itself (e.g., "C")
  unitName?: TemperatureUnit;

  // Initial value for the unit (used by TemperatureField's unit selector via RHF)
  // This should also align with the unitName if unitName is the RHF key
  initialUnitValue: TemperatureUnit;
}

const meta: Meta<CurrentTemperatureStoryArgs> = {
  title: "Fields/Numeric/Temperature",
  component: TemperatureField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for TemperatureField component itself
    label: "Ambient Temperature",
    placeholder: "e.g., 22.5",
    description: "Enter temperature and select unit.",
    min: 0, // Min temperature in Celsius
    max: 100, // Max temperature in Celsius
    allowedUnits: [...TEMPERATURE_UNITS],
    // Note: defaultUnit from TemperatureFieldProps is for the component's internal default
    // if the RHF field for unit is not registered or has no value.
    // initialUnitValue below sets the RHF default for the unit field.
    defaultUnit: "C", // Component's internal default if RHF unit field is empty

    // Args for WithFormProvider decorator & RHF
    fieldName: "ambientTemp", // RHF name for the numeric value part
    name: "ambientTemp", // MUST match fieldName for RHF registration
    unitName: "C", // Unit value, to match TemperatureFieldProps.unitName & RHF name
    fieldSchema: createStoryLevelTemperatureValueSchema(),
    onFormSubmit: fn(),
    fieldValue: 22, // Initial numeric value for RHF
    initialUnitValue: "C", // Initial unit value for RHF (should match unitName if it's the key)
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentTemperatureStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultCelsius: Story = {
  args: {
    label: "Default Celsius Temp",
    name: "defaultCTemp",
    unitName: "C", // Unit value
    fieldName: "defaultCTemp", // Match name
    fieldValue: 25,
    initialUnitValue: "C",
    // fieldSchema is inherited from meta.args
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByDisplayValue("25")).toBeInTheDocument();
    await expect(canvas.getByText("Celsius (°C)")).toBeInTheDocument();
  },
};

export const InitialFahrenheit: Story = {
  args: {
    label: "Initial Fahrenheit Temp",
    name: "initialFTemp",
    unitName: "F", // Unit value
    defaultUnit: "F", // Component's internal default
    fieldName: "initialFTemp", // Match name
    fieldValue: 77,
    initialUnitValue: "F", // RHF initial unit
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByDisplayValue("77")).toBeInTheDocument();
    await expect(canvas.getByText("Fahrenheit (°F)")).toBeInTheDocument();
  },
};

export const InteractiveConversion: Story = {
  args: {
    label: "Interactive Temp Conversion",
    name: "convTemp",
    unitName: "C", // Unit value
    min: -100, // Celsius
    max: 200, // Celsius
    fieldName: "convTemp",
    fieldValue: 10,
    initialUnitValue: "C",
    defaultUnit: "C",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const tempInput = canvas.getByPlaceholderText("e.g., 22.5");
    const unitSelectTrigger = canvas.getByRole("combobox");

    await expect(tempInput).toHaveValue(10);
    await expect(canvas.getByText("Celsius (°C)")).toBeInTheDocument();

    // Change unit to Fahrenheit
    await userEvent.click(unitSelectTrigger);
    const fahrenheitOption = await canvas.findByText("Fahrenheit (°F)");
    await userEvent.click(fahrenheitOption);

    // 10°C is 50°F. Component should convert and update input.
    await expect(tempInput).toHaveValue(50);
    await expect(canvas.getByText("Fahrenheit (°F)")).toBeInTheDocument();

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    // WithFormProvider now submits an object like { [fieldName]: value }
    // The unit is handled by TemperatureField internally via its own RHF registration
    // So, we expect { convTemp: 50 }. The unit part ('convUnit': 'F') will be separate in the form state.
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.fieldName]: 50 }, // fieldName from BaseFormProviderArgs (non-null assertion removed)
      expect.anything(),
    );
    // To assert the unit, you might need to access the form instance if WithFormProvider exposes it
    // or check the visual state if that's sufficient.
  },
};

export const WithErrorMinTemp: Story = {
  args: {
    label: "Min Temp Error",
    name: "minErrorTemp",
    unitName: "C", // Unit value
    min: 10, // Min 10°C
    fieldName: "minErrorTemp",
    fieldValue: 5, // 5°C, below min
    initialUnitValue: "C",
    defaultUnit: "C",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Component's internal validation message
    await expect(
      canvas.getByText("Temperature must be at least 10.0°C"),
    ).toBeInTheDocument();
  },
};

export const WithErrorInvalidInput: Story = {
  args: {
    label: "Invalid Input Error",
    name: "invalidInputTemp",
    unitName: "C", // Unit value
    fieldName: "invalidInputTemp",
    // @ts-expect-error testing invalid input type for Zod schema (fieldValue expects number | undefined)
    fieldValue: "abc",
    initialUnitValue: "C",
    defaultUnit: "C",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Zod validation message via WithFormProvider
    await expect(
      canvas.getByText("Temperature value must be a number."),
    ).toBeInTheDocument();
  },
};

export const RestrictedUnitsKelvinOnly: Story = {
  args: {
    label: "Kelvin Only Temp",
    name: "kelvinTemp",
    unitName: "K", // Unit value
    defaultUnit: "K",
    allowedUnits: ["K"],
    min: 0, // 0K
    fieldName: "kelvinTemp",
    fieldValue: 273.15,
    initialUnitValue: "K",
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByDisplayValue("273.15")).toBeInTheDocument();
    await expect(canvas.getByText("Kelvin (K)")).toBeInTheDocument();

    // Try to open select - only Kelvin should be there or it might not even be a select
    const unitSelectTrigger = canvas.getByRole("combobox");
    await userEvent.click(unitSelectTrigger);
    // Check that other units are not present
    await expect(canvas.queryByText("Celsius (°C)")).not.toBeInTheDocument();
    await expect(canvas.queryByText("Fahrenheit (°F)")).not.toBeInTheDocument();
    // Ensure Kelvin is there (it's already selected)
    await expect(await canvas.findByText("Kelvin (K)")).toBeVisible();
  },
};
