import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { WeightFieldProps, WeightUnit } from "@/ui/fields/numeric/Weight";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { WEIGHT_UNITS, WeightField } from "@/ui/fields/numeric/Weight";

import { WithFormProvider } from "../WithFormProvider";

// Minimal i18n object for validation messages
const i18n = {
  en: {
    weight: {
      validation: {
        type: "Please enter a valid weight.",
        min: "Weight must be at least {min} {unit}.",
        max: "Weight must be at most {max} {unit}.",
        required: "Weight value cannot be empty.",
      },
    },
  },
};

// Define a Zod schema factory for this story's form validation needs for the numeric value
const createStoryLevelWeightValueSchema = ({
  min,
  max,
  required = false,
}: { min?: number; max?: number; required?: boolean } = {}) =>
  z.preprocess(
    (val) => {
      if (val === "" || val === null) return undefined;
      if (typeof val === "string") {
        const num = parseFloat(val.replace(/,/g, ".")); // Allow comma as decimal separator
        return isNaN(num) ? val : num; // Keep original string if not a number for Zod to catch
      }
      return val;
    },
    z
      .number({ message: i18n.en.weight.validation.type })
      .optional()
      .refine((val) => val === undefined || val >= (min ?? -Infinity), {
        message: i18n.en.weight.validation.min
          .replace("{min}", (min ?? -Infinity).toFixed(2))
          .replace("{unit}", "kg (base for schema)"), // Assuming kg is base for schema validation
      })
      .refine((val) => val === undefined || val <= (max ?? Infinity), {
        message: i18n.en.weight.validation.max
          .replace("{max}", (max ?? Infinity).toFixed(2))
          .replace("{unit}", "kg (base for schema)"), // Assuming kg is base for schema validation
      })
      .refine((val) => !required || val !== undefined, {
        message: i18n.en.weight.validation.required,
      }),
  );

// Define args type for this specific story
interface CurrentWeightStoryArgs
  extends Omit<WeightFieldProps, "defaultValue">,
    BaseFormProviderArgs<
      string,
      ReturnType<typeof createStoryLevelWeightValueSchema>
    > {
  initialUnitValue: WeightUnit;
  required?: boolean;
}

const meta: Meta<CurrentWeightStoryArgs> = {
  title: "Fields/Numeric/Weight",
  component: WeightField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    label: "Default Weight",
    name: "weightValue",
    unitName: "weightUnit",
    placeholder: "Enter weight",
    description: "Specify the weight and its unit.",
    defaultUnit: "kg",
    allowedUnits: [...WEIGHT_UNITS],
    min: 0,
    fieldName: "weightValue",
    fieldSchema: createStoryLevelWeightValueSchema({ min: 0, required: false }),
    onFormSubmit: fn(),
    fieldValue: undefined,
    initialUnitValue: "kg",
    required: false,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentWeightStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultKilograms: Story = {
  args: {
    fieldName: "defaultKgWeight",
    name: "defaultKgWeight",
    unitName: "kg",
    fieldValue: 5,
    initialUnitValue: "kg",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter weight");
    await expect(input).toHaveValue("5");
    await expect(canvas.getByRole("combobox")).toHaveTextContent(
      "Kilograms (kg)",
    );
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 5,
      [args.unitName!]: "kg",
    });
  },
};

export const WithInitialValuePounds: Story = {
  args: {
    label: "Weight in Pounds",
    fieldName: "initialLbWeight",
    name: "initialLbWeight",
    unitName: "lb",
    fieldValue: 10,
    initialUnitValue: "lb",
    defaultUnit: "lb", // Component prop
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter weight");
    await expect(input).toHaveValue("10");
    await expect(canvas.getByRole("combobox")).toHaveTextContent("Pounds (lb)");
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 10,
      [args.unitName!]: "lb",
    });
  },
};

export const InteractiveUnitConversion: Story = {
  args: {
    label: "Convertible Weight",
    fieldName: "convertWeightVal",
    name: "convertWeightVal",
    unitName: "g", // Initial RHF unit field name
    fieldValue: 2500,
    initialUnitValue: "g",
    defaultUnit: "g", // Component prop
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter weight");
    const unitSelect = canvas.getByRole("combobox");

    await expect(input).toHaveValue("2500");
    await expect(unitSelect).toHaveTextContent("Grams (g)");

    // Change unit to Kilograms
    await userEvent.click(unitSelect);
    await userEvent.click(canvas.getByText("Kilograms (kg)"));
    await expect(input).toHaveValue("2.5"); // 2500g = 2.5kg
    await expect(unitSelect).toHaveTextContent("Kilograms (kg)");

    // Submit form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    // After conversion, the unitName in form state also changes if component updates RHF for unit
    // Assuming unitName is the RHF field for the unit selector
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 2.5, // Value is in kg
      [args.unitName!]: "kg", // Unit is now kg
    });
  },
};

export const WithErrorEmptyValue: Story = {
  args: {
    label: "Error Empty Weight",
    fieldName: "errorEmptyWeight",
    name: "errorEmptyWeight",
    unitName: "kg",
    fieldValue: undefined,
    initialUnitValue: "kg",
    fieldSchema: createStoryLevelWeightValueSchema({ required: true }),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText(i18n.en.weight.validation.required),
    ).toBeInTheDocument();
  },
};

export const WithErrorInvalidNumberInput: Story = {
  args: {
    label: "Error Invalid Weight",
    fieldName: "errorInvalidWeight",
    name: "errorInvalidWeight",
    unitName: "kg",
    fieldValue: "heavy" as any, // Test schema's handling of non-numeric
    initialUnitValue: "kg",
    // fieldSchema inherited from meta.args
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // No need to type, RHF sets value from args.fieldValue
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText(i18n.en.weight.validation.type),
    ).toBeInTheDocument();
  },
};

export const WithErrorMinWeight: Story = {
  args: {
    label: "Error Min Weight",
    fieldName: "errorMinWeight",
    name: "errorMinWeight",
    unitName: "g", // Display unit
    fieldValue: 500, // 500g
    initialUnitValue: "g",
    min: 2, // Prop for WeightField: 2 Kilograms (base unit for component's min/max logic)
    defaultUnit: "g", // Component's display unit
    fieldSchema: createStoryLevelWeightValueSchema({ min: 2 }), // Schema validates against base unit (kg)
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Component's min validation: "Weight must be at least {min} {unit}"
    // min is 2kg. Component converts this to current display unit 'g' -> 2000g.
    await expect(
      canvas.getByText("Weight must be at least 2000 g"),
    ).toBeInTheDocument();
  },
};

export const WithErrorMaxWeight: Story = {
  args: {
    label: "Error Max Weight",
    fieldName: "errorMaxWeight",
    name: "errorMaxWeight",
    unitName: "g", // Display unit
    fieldValue: 1500, // 1500g
    initialUnitValue: "g",
    max: 1, // Prop for WeightField: 1 Kilogram (base unit for component's min/max logic)
    defaultUnit: "g", // Component's display unit
    fieldSchema: createStoryLevelWeightValueSchema({ max: 1 }), // Schema validates against base unit (kg)
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Component's max validation: "Weight must be at most {max} {unit}"
    // max is 1kg. Component converts this to current display unit 'g' -> 1000g.
    await expect(
      canvas.getByText("Weight must be at most 1000 g"),
    ).toBeInTheDocument();
  },
};

export const RestrictedUnits: Story = {
  args: {
    label: "Restricted Weight Units",
    fieldName: "restrictedWeightVal",
    name: "restrictedWeightVal",
    unitName: "g",
    fieldValue: 100,
    initialUnitValue: "g",
    allowedUnits: ["g", "kg"] as WeightUnit[],
    defaultUnit: "g",
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const unitSelect = canvas.getByRole("combobox");
    await userEvent.click(unitSelect);
    await expect(canvas.getByText("Grams (g)")).toBeInTheDocument();
    await expect(canvas.getByText("Kilograms (kg)")).toBeInTheDocument();
    await expect(canvas.queryByText("Pounds (lb)")).not.toBeInTheDocument();
    await userEvent.click(canvas.getByText("Grams (g)")); // Close dropdown
  },
};
