import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { VolumeFieldProps, VolumeUnit } from "@/ui/fields/numeric/Volume";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { VOLUME_UNITS, VolumeField } from "@/ui/fields/numeric/Volume";

import { WithFormProvider } from "../WithFormProvider";

// Minimal i18n object for schema validation messages within stories
const i18n = {
  en: {
    volume: {
      validation: {
        type: "Please enter a valid volume.",
        min: "Volume must be at least {min} {unit}.",
        max: "Volume must be at most {max} {unit}.",
      },
    },
  },
};

// Zod schema factory for validating only the numeric volume value
const createStoryLevelVolumeValueSchema = ({
  min,
  max,
  required = false,
}: { min?: number; max?: number; required?: boolean } = {}) =>
  z.preprocess(
    (val) => {
      if (val === "" || val === null) return undefined;
      if (typeof val === "string") {
        const num = Number(val.trim());
        return isNaN(num) ? val : num; // Keep as string if not a number for Zod to catch
      }
      return val;
    },
    z
      .number({
        invalid_type_error: i18n.en.volume.validation.type,
      })
      .optional()
      .refine(
        (val) => val === undefined || val >= (min ?? 0),
        i18n.en.volume.validation.min
          .replace("{min}", (min ?? 0).toFixed(2))
          .replace("{unit}", "L (base for schema)"), // Unit context for schema error
      )
      .refine(
        (val) => val === undefined || max === undefined || val <= max,
        i18n.en.volume.validation.max
          .replace("{max}", (max ?? Infinity).toFixed(2))
          .replace("{unit}", "L (base for schema)"), // Unit context for schema error
      )
      .refine((val) => !required || val !== undefined, {
        message: "Value cannot be empty", // Generic required message
      }),
  );

// Define args type for this specific story
interface CurrentVolumeStoryArgs
  extends Omit<
      VolumeFieldProps,
      "name" | "unitName" | "defaultValue" // Omit to redefine with specific roles
    >,
    BaseFormProviderArgs<
      string, // TFieldName (the type of 'fieldName' itself)
      ReturnType<typeof createStoryLevelVolumeValueSchema> // TFieldSchema (the Zod schema type)
    > {
  // Props for VolumeField component itself, mapping to RHF
  name: string; // RHF name for the numeric input part, MUST match fieldName from BaseFormProviderArgs
  unitName?: VolumeUnit; // RHF name for the unit select part AND the actual unit for the component

  // Initial value for the unit (used by VolumeField's unit selector via RHF)
  initialUnitValue: VolumeUnit;
}

const meta: Meta<CurrentVolumeStoryArgs> = {
  title: "Fields/Numeric/Volume",
  component: VolumeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for VolumeField component itself (passed through)
    label: "Item Volume",
    placeholder: "Enter volume",
    description: "Specify the volume and its unit.",
    defaultUnit: "l", // Component's internal default if RHF doesn't provide one
    allowedUnits: [...VOLUME_UNITS],
    min: 0, // Default min for the component

    // Args for WithFormProvider decorator & RHF
    fieldName: "volumeValue", // RHF name for the numeric value part
    name: "volumeValue", // MUST match fieldName for RHF registration in VolumeField
    unitName: "l", // Actual unit for VolumeField, also RHF name for unit selector
    fieldSchema: createStoryLevelVolumeValueSchema(), // Schema for the numeric value
    onFormSubmit: fn(),
    fieldValue: undefined, // Initial numeric value for RHF (e.g., 10 or undefined)
    initialUnitValue: "l", // Initial unit value for RHF (for the unit selector)
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentVolumeStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultLiters: Story = {
  args: {
    label: "Default Liters Volume",
    fieldName: "defaultLitersVolume",
    name: "defaultLitersVolume",
    unitName: "l",
    fieldValue: 10,
    initialUnitValue: "l",
    // fieldSchema will be inherited from meta.args or use createStoryLevelVolumeValueSchema()
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter volume");
    await expect(input).toHaveValue("10");
    await expect(canvas.getByRole("combobox")).toHaveTextContent("Liters (l)");

    // Example of form submission check
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.fieldName]: 10 },
      expect.anything(),
    );
  },
};

export const WithInitialValueGallons: Story = {
  args: {
    label: "Initial Gallons Volume",
    fieldName: "initialGalVolume",
    name: "initialGalVolume",
    unitName: "gal", // This is VolumeUnit, also RHF key for unit
    defaultUnit: "gal", // VolumeFieldProp
    fieldValue: 5,
    initialUnitValue: "gal", // RHF initial value for unit
    // fieldSchema will use createStoryLevelVolumeValueSchema() from meta or specific if min/max needed
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter volume");
    await expect(input).toHaveValue("5");
    await expect(canvas.getByRole("combobox")).toHaveTextContent(
      "Gallons (gal)",
    );
  },
};

export const ConvertUnits: Story = {
  args: {
    label: "Convert Units Volume",
    fieldName: "convertVolumeValue",
    name: "convertVolumeValue",
    unitName: "ml", // RHF key for unit selector, also VolumeUnit
    defaultUnit: "ml",
    fieldValue: 1000,
    initialUnitValue: "ml", // RHF initial value for unit selector
    // fieldSchema will be inherited
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter volume");
    const unitSelector = canvas.getByRole("combobox");

    await expect(input).toHaveValue("1000");
    await expect(unitSelector).toHaveTextContent("Milliliters (ml)");

    await userEvent.click(unitSelector);
    await userEvent.click(canvas.getByText("Liters (l)"));
    await expect(input).toHaveValue("1"); // 1000ml should convert to 1l
    await expect(unitSelector).toHaveTextContent("Liters (l)");

    // Check form submission with the new unit
    // The VolumeField component itself handles the unit conversion internally.
    // The form will submit the numeric value associated with args.fieldName (name prop)
    // and the unit selector's value associated with args.unitName (unitName prop).
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      {
        [args.fieldName]: 1, // The numeric value field
        // The unit is also part of the form state if a unitName is provided to VolumeField
        // and WithFormProvider is set up to handle it, which it is by default.
        // However, our current fieldSchema in meta.args only validates args.fieldName.
        // For this test, we primarily care that the numeric value is correct after conversion.
      },
      expect.anything(),
    );
  },
};

export const Empty: Story = {
  args: {
    label: "Empty Volume Field",
    fieldName: "emptyVolumeValue",
    name: "emptyVolumeValue",
    unitName: "l",
    fieldValue: undefined, // Start with no value
    initialUnitValue: "l",
    // fieldSchema will be inherited, allowing optional undefined
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter volume");
    await expect(input).toHaveValue("");

    await userEvent.type(input, "123");
    await expect(input).toHaveValue("123");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.fieldName]: 123 }, // unitName is also in form state but schema only covers fieldName
      expect.anything(),
    );
  },
};

export const WithErrorEmptyValue: Story = {
  args: {
    label: "Error Empty Volume",
    fieldName: "errorEmptyVolume",
    name: "errorEmptyVolume",
    unitName: "l",
    fieldValue: undefined, // Intentionally empty to trigger validation
    initialUnitValue: "l",
    fieldSchema: createStoryLevelVolumeValueSchema({ required: true }),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(canvas.getByText("Value cannot be empty")).toBeInTheDocument();
  },
};

export const WithErrorInvalidNumberInput: Story = {
  args: {
    label: "Error Invalid Number Input",
    fieldName: "errorInvalidNumber",
    name: "errorInvalidNumber",
    unitName: "l",
    fieldValue: "abc" as any, // Intentionally invalid to trigger validation, cast to satisfy TS at arg level
    initialUnitValue: "l",
    // fieldSchema inherited from meta.args, which handles non-numeric input via preprocess and Zod's number type.
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // const input = canvas.getByPlaceholderText("Enter volume"); // Not needed as fieldValue sets it
    // Value is set via args, so no need to type 'abc' here if RHF populates it.
    // If fieldValue is "abc", RHF will pass "abc" to the Zod schema.
    // The schema's preprocess will keep "abc" as is because Number("abc") is NaN.
    // Then Zod's .number() will fail with 'Please enter a valid volume.'
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText(i18n.en.volume.validation.type),
    ).toBeInTheDocument();
  },
};

export const WithErrorMinVolume: Story = {
  args: {
    label: "Error Min Volume",
    fieldName: "errorMinVolume",
    name: "errorMinVolume",
    unitName: "ml",
    min: 10, // Prop for VolumeField: 10 Liters (base unit for component's min/max logic)
    defaultUnit: "ml", // Component's display unit
    fieldValue: 500, // 500ml (numeric value for RHF)
    initialUnitValue: "ml", // RHF unit selector value
    fieldSchema: createStoryLevelVolumeValueSchema({ min: 10 }), // Schema validates against base unit (Liters)
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // Value is 500ml. Min is 10L. Schema min is 10.
    // The schema's refine message for min is: "Volume must be at least {min} L (base for schema)"
    // The component's validation message is: "Volume must be at least {min} {unit}"
    // The component will convert its `min` prop (10L) to the current unit (ml) -> 10000ml.
    // The input value is 500ml. So the component's validation should trigger.
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Volume must be at least 10000.00 ml"),
    ).toBeInTheDocument();
  },
};

export const WithErrorMaxVolume: Story = {
  args: {
    label: "Error Max Volume",
    fieldName: "errorMaxVolume",
    name: "errorMaxVolume",
    unitName: "ml",
    max: 1, // Prop for VolumeField: 1 Liter (base unit for component's min/max logic)
    defaultUnit: "ml", // Component's display unit
    fieldValue: 1500, // 1500ml (numeric value for RHF)
    initialUnitValue: "ml", // RHF unit selector value
    fieldSchema: createStoryLevelVolumeValueSchema({ max: 1 }), // Schema validates against base unit (Liters)
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // Value is 1500ml. Max is 1L. Schema max is 1.
    // The schema's refine message for max is: "Volume must be at most {max} L (base for schema)"
    // The component's validation message is: "Volume must be at most {max} {unit}"
    // The component will convert its `max` prop (1L) to the current unit (ml) -> 1000ml.
    // The input value is 1500ml. So the component's validation should trigger.
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Volume must be at most 1000.00 ml"),
    ).toBeInTheDocument();
  },
};

export const RestrictedUnits: Story = {
  args: {
    label: "Restricted Units Volume",
    fieldName: "restrictedVolumeValue",
    name: "restrictedVolumeValue",
    unitName: "gal",
    defaultUnit: "gal",
    allowedUnits: ["gal", "l"],
    fieldValue: 2,
    initialUnitValue: "gal",
    // fieldSchema inherited
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter volume");
    const unitSelector = canvas.getByRole("combobox");

    await expect(input).toHaveValue("2");
    await expect(unitSelector).toHaveTextContent("Gallons (gal)");

    // Check that only allowed units are present
    await userEvent.click(unitSelector);
    await expect(canvas.queryByText("Milliliters (ml)")).toBeNull();
    await expect(canvas.getByText("Liters (l)")).toBeInTheDocument();
    await expect(canvas.getByText("Gallons (gal)")).toBeInTheDocument();
    // Close dropdown
    await userEvent.keyboard("{escape}");
  },
};
