import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type {
  DurationFieldProps,
  DurationUnit,
} from "@/ui/fields/numeric/Duration";
import { DURATION_UNITS, DurationField } from "@/ui/fields/numeric/Duration";
import type { BaseFormProviderArgs } from "../WithFormProvider";
import { WithFormProvider } from "../WithFormProvider";

// Minimal i18n object for validation messages
const i18n = {
  en: {
    duration: {
      validation: {
        type: "Duration must be a valid number.",
        nonNegative: "Duration must be a non-negative number.",
        integer: "Duration must be a whole number.",
        required: "Duration value cannot be empty.",
        // min/max messages can be added if schema handles them, or rely on component
      },
    },
  },
};

// UPDATED: Schema factory for the numeric value part only
const createStoryLevelValueSchema = ({
  showDecimal = false,
  required = false,
  // min, max can be added here if schema should validate base unit range
}: { showDecimal?: boolean; required?: boolean } = {}) =>
  z.preprocess(
    (val) => {
      if (val === "" || val === undefined || val === null) return undefined;
      if (typeof val === "string") {
        const num = parseFloat(val.replace(/,/g, "."));
        return isNaN(num) ? val : num;
      }
      return val;
    },
    z.number({ invalid_type_error: i18n.en.duration.validation.type })
      .nonnegative({ message: i18n.en.duration.validation.nonNegative })
      .refine((val) => showDecimal || Number.isInteger(val), {
        message: i18n.en.duration.validation.integer,
      })
      .optional() // Base schema is optional
      .refine((val) => !required || val !== undefined, { // Add required check if flag is true
        message: i18n.en.duration.validation.required,
      })
  );

// UPDATED: CurrentDurationStoryArgs for full consistency
interface CurrentDurationStoryArgs
  extends Omit<DurationFieldProps, "name" | "unitName" | "defaultValue">, // Omit RHF-managed props
    BaseFormProviderArgs<
      string, // TFieldName (the type of 'fieldName' itself)
      ReturnType<typeof createStoryLevelValueSchema> // TFieldSchema (the Zod schema type)
    > {
  // Props for DurationField component itself, mapping to RHF
  name: string; // RHF name for the numeric input part, MUST match fieldName from BaseFormProviderArgs
  unitName?: DurationUnit; // RHF name for the unit select part AND the actual unit for the component

  // Initial value for the unit (used by DurationField's unit selector via RHF)
  initialUnitValue: DurationUnit;
  // fieldValue is inherited from BaseFormProviderArgs if needed, or can be explicitly added if specific typing is required beyond z.infer
  // For consistency with Volume, let's ensure fieldValue is available if we need to set it directly in args
  fieldValue?: number; // Explicitly adding for clarity, matches schema output
}

// UPDATED: meta object for full consistency
const meta: Meta<CurrentDurationStoryArgs> = {
  title: "Fields/Numeric/Duration", // Standardized title
  component: DurationField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for DurationField component itself
    label: "Task Duration",
    placeholder: "Enter duration",
    description: "Specify the duration and its unit.",
    defaultUnit: "hours", // Component's internal default if RHF doesn't provide one
    allowedUnits: [...DURATION_UNITS],
    showDecimal: false,
    // min, max can be added here if needed by component directly

    // Args for WithFormProvider decorator and RHF setup
    fieldName: "durationValue",       // RHF name for the numeric value part
    name: "durationValue",           // Component's `name` prop, MUST match fieldName
    unitName: "hours",              // Component's `unitName` prop, AND RHF field name for unit selector
    fieldSchema: createStoryLevelValueSchema({ showDecimal: false }), // Default schema
    onFormSubmit: fn(),
    fieldValue: undefined,          // Initial numeric value for RHF
    initialUnitValue: "hours",      // Initial unit value for RHF (for the unit selector field named by unitName)
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentDurationStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

// UPDATED: DefaultHours story
export const DefaultHours: Story = {
  args: {
    // fieldName, name, unitName, fieldSchema are inherited from meta.args
    fieldValue: 8,
    initialUnitValue: "hours",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter duration");
    await expect(input).toHaveValue(8);
    await expect(canvas.getByRole("combobox")).toHaveTextContent("Hours");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 8,
      [args.unitName as string]: "hours", // initialUnitValue is used as the unit for submission
    });
  },
};

// UPDATED: WithInitialValueDays story
export const WithInitialValueDays: Story = {
  args: {
    label: "Initial Days Duration",
    name: "initialDaysValue",
    unitName: "days", // RHF key for unit selector & DurationUnit
    defaultUnit: "days", // Component prop
    
    fieldName: "initialDaysValue",
    fieldSchema: createStoryLevelValueSchema({ showDecimal: false }),
    fieldValue: 5,
    initialUnitValue: "days",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter duration");
    await expect(input).toHaveValue(5);
    await expect(canvas.getByRole("combobox")).toHaveTextContent("Days");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 5,
      [args.unitName as string]: "days",
    });
  },
};

// UPDATED: InteractiveUnitConversion story
export const InteractiveUnitConversion: Story = {
  args: {
    label: "Convertible Duration",
    name: "convertDurationValue",
    unitName: "minutes" as DurationUnit, // RHF key for unit selector & DurationUnit
    defaultUnit: "minutes", // Component prop

    fieldName: "convertDurationValue",
    fieldSchema: createStoryLevelValueSchema({ showDecimal: false }),
    fieldValue: 120, // 120 minutes
    initialUnitValue: "minutes" as DurationUnit,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter duration");
    const unitSelect = canvas.getByRole("combobox");

    await expect(input).toHaveValue(120);
    await userEvent.click(unitSelect);
    await userEvent.click(canvas.getByText("Hours")); // Assumes "Hours" is an option
    await expect(input).toHaveValue(2); // 120 minutes = 2 hours

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 2, // 120 minutes is 2 hours
      [args.unitName as string]: "hours", // Unit after conversion
    });
  },
};

// UPDATED: WithDecimal story
export const WithDecimal: Story = {
  args: {
    label: "Decimal Duration",
    name: "decimalDurationValue",
    unitName: "hours" as DurationUnit, // RHF key for unit selector & DurationUnit
    showDecimal: true,
    defaultUnit: "hours", // Component prop

    fieldName: "decimalDurationValue",
    fieldSchema: createStoryLevelValueSchema({ showDecimal: true }),
    fieldValue: 2.5,
    initialUnitValue: "hours" as DurationUnit,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Enter duration");
    await expect(input).toHaveValue(2.5);

    await userEvent.clear(input);
    await userEvent.type(input, "3.5");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit!).toHaveBeenCalledWith({
      [args.fieldName]: 3.5,
      [args.unitName as string]: "hours",
    });
  },
};

// UPDATED: WithErrorNotInteger story
export const WithErrorNotInteger: Story = {
  args: {
    label: "Integer Check Duration",
    name: "integerCheckValue",
    unitName: "hours", // RHF key for unit selector & DurationUnit
    showDecimal: false, // Expect integer
    defaultUnit: "hours", // Component prop

    fieldName: "integerCheckValue",
    fieldSchema: createStoryLevelValueSchema({ showDecimal: false }),
    fieldValue: 1.5 as any, // Invalid initial value, cast for TS
    initialUnitValue: "hours",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText(i18n.en.duration.validation.integer),
    ).toBeInTheDocument();
  },
};

// UPDATED: WithErrorMinDuration story
export const WithErrorMinDuration: Story = {
  args: {
    label: "Min Duration Check",
    name: "minDurationCheckValue",
    unitName: "hours", // RHF key for unit selector & DurationUnit
    min: 24, // Component prop: Min 24 of defaultUnit
    defaultUnit: "hours", // Component prop
    
    fieldName: "minDurationCheckValue",
    // Schema for numeric value type; component handles min/max logic with current unit
    fieldSchema: createStoryLevelValueSchema({ showDecimal: false }), 
    fieldValue: 20, // Less than min
    initialUnitValue: "hours",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // The DurationField itself has validation rules.
    // The fieldSchema for WithFormProvider is for the raw value type.
    // We need to trigger submit to see component's internal validation.
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled(); 
    await expect(
      canvas.getByText("Duration must be at least 24 hours"),
    ).toBeInTheDocument();
  },
};

// UPDATED: RestrictedUnits story
export const RestrictedUnits: Story = {
  args: {
    label: "Restricted Duration",
    name: "restrictedDurationValue",
    unitName: "minutes", // RHF key for unit selector & DurationUnit
    allowedUnits: ["minutes", "hours"] as DurationUnit[],
    defaultUnit: "minutes", // Component prop

    fieldName: "restrictedDurationValue",
    fieldSchema: createStoryLevelValueSchema({ showDecimal: false }),
    fieldValue: 30,
    initialUnitValue: "minutes",
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const unitSelect = canvas.getByRole("combobox");
    await userEvent.click(unitSelect);
    await expect(canvas.getByText("Minutes")).toBeInTheDocument();
    await expect(canvas.getByText("Hours")).toBeInTheDocument();
    await expect(canvas.queryByText("Days")).not.toBeInTheDocument();
    await userEvent.click(canvas.getByText("Minutes")); // Close dropdown
  },
};
