import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { CompanySizeFieldProps } from "@/ui/fields/company/CompanySize";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { CompanySizeField } from "@/ui/fields/company/CompanySize";

import { WithFormProvider } from "../WithFormProvider";

const storyLevelSchema = z.string().min(1, "Please select a company size.");

type CurrentStoryArgs = CompanySizeFieldProps & BaseFormProviderArgs<"size", z.ZodType<string>>;

const meta: Meta<CurrentStoryArgs> = {
  title: "Fields/Company/CompanySizeField",
  component: CompanySizeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    label: "Company Size",
    placeholder: "Select a size",
    description: "The size of the company.",
    name: "size",
    fieldName: "size",
    fieldSchema: storyLevelSchema,
    onFormSubmit: fn(),
  },
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "size",
    fieldSchema: z.string().min(1, "Size is required"),
    onFormSubmit: fn(),
  },
};

export const WithValue: Story = {
  args: {
    ...Default.args,
    fieldValue: "1-10",
  },
};

export const WithError: Story = {
  args: {
    ...Default.args,
    fieldValue: "",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // Attempt to submit without selection
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    // Assert onFormSubmit was NOT called because of validation
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Assert error message is shown
    await expect(
      canvas.getByText("Please select a company size."),
    ).toBeInTheDocument();
  },
};
