import type { Meta, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { CompanyIndustryFieldProps } from "@/ui/fields/company/CompanyIndustry";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { CompanyIndustryField } from "@/ui/fields/company/CompanyIndustry";

import { WithFormProvider } from "../WithFormProvider";

const storyLevelSchema = z.string().min(1, "Please select an industry.");

type CurrentStoryArgs = CompanyIndustryFieldProps & BaseFormProviderArgs<"industry", z.ZodType<string>>;

const meta: Meta<CurrentStoryArgs> = {
  title: "Fields/Company/CompanyIndustryField",
  component: CompanyIndustryField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    label: "Industry",
    placeholder: "Select an industry",
    description: "The industry of the company.",
    name: "industry",
    fieldName: "industry",
    fieldSchema: storyLevelSchema,
    onFormSubmit: fn(),
  },
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "industry",
    fieldSchema: z.string().min(1, "Industry is required"),
    onFormSubmit: fn(),
  },
};

export const WithValue: Story = {
  args: {
    ...Default.args,
    fieldValue: "Technology",
  },
};

export const WithError: Story = {
  args: {
    ...Default.args,
    fieldValue: "",
    fieldSchema: z.string().refine((val) => val === "technology", {
      message: "Only Technology is an acceptable industry.",
    }),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Only Technology is an acceptable industry."),
    ).toBeInTheDocument();
  },
};
