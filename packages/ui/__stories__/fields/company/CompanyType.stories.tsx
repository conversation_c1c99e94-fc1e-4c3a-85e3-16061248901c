import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { CompanyTypeFieldProps } from "@/ui/fields/company/CompanyType";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { CompanyTypeField } from "@/ui/fields/company/CompanyType";

import { WithFormProvider } from "../WithFormProvider";

const storyLevelSchema = z.string().min(1, "Please select a company type.");

type CurrentStoryArgs = CompanyTypeFieldProps & BaseFormProviderArgs<"type", z.ZodType<string>>;

const meta: Meta<CurrentStoryArgs> = {
  title: "Fields/Company/CompanyTypeField",
  component: CompanyTypeField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    label: "Company Type",
    placeholder: "Select a type",
    description: "The legal type of the company.",
    name: "type",
    fieldName: "type",
    fieldSchema: storyLevelSchema,
    onFormSubmit: fn(),
  },
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "type",
    fieldSchema: z.string().min(1, "Type is required"),
    onFormSubmit: fn(),
  },
};

export const WithValue: Story = {
  args: {
    ...Default.args,
    fieldValue: "private",
  },
};

export const WithError: Story = {
  args: {
    ...Default.args,
    fieldValue: "",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // Attempt to submit without selection
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));
    // Assert onFormSubmit was NOT called because of validation
    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    // Assert error message is shown
    await expect(
      canvas.getByText("Please select a company type."),
    ).toBeInTheDocument();
  },
};
