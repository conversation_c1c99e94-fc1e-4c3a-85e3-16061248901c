import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import type { z } from "zod";

import { expect, fn, userEvent, within } from "storybook/test";

import type { AddressAutocompleteFieldProps } from "@/ui/fields/AddressAutocomplete";

import type { BaseFormProviderArgs } from "./WithFormProvider";

import {
  AddressAutocompleteField,
  zAddress,
} from "@/ui/fields/AddressAutocomplete";

import { WithFormProvider } from "./WithFormProvider";

// Define args type for this specific story
interface CurrentAddressStoryArgs
  extends AddressAutocompleteFieldProps,
    BaseFormProviderArgs {
  fieldName: "address";
  fieldSchema: typeof zAddress;
  defaultValues: { address?: Partial<z.infer<typeof zAddress>> };
}

const meta: Meta<CurrentAddressStoryArgs> = {
  title: "Fields/AddressAutocompleteField",
  component: AddressAutocompleteField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for AddressAutocompleteField component itself
    label: "Location Address",
    placeholder: "Search for an address...",
    description: "Start typing to find an address.",

    // Args for the WithFormProvider decorator
    fieldName: "address",
    fieldSchema: zAddress,
    onFormSubmit: fn(),
    defaultValues: { address: { formatted: "" } },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentAddressStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // Default story uses meta.args
  },
};

export const WithInitialValue: Story = {
  args: {
    defaultValues: {
      address: {
        formatted: "1600 Amphitheatre Parkway, Mountain View, CA",
        country: "USA",
        city: "Mountain View",
        state: "CA",
        postal: "94043",
        latitude: 37.422,
        longitude: -122.084,
      },
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("Search for an address...");
    await expect(input).toHaveValue(
      "1600 Amphitheatre Parkway, Mountain View, CA",
    );
  },
};

// Interactive story might be complex due to Google Places API.
// For now, focusing on form submission aspects.
export const InteractiveSubmission: Story = {
  args: {
    // For this test, we'll assume a value is programmatically set or selected
    // and we're testing the submission of this selected value.
    defaultValues: {
      address: {
        formatted: "1 Infinite Loop, Cupertino, CA",
        country: "USA",
        city: "Cupertino",
        state: "CA",
        postal: "95014",
        latitude: 37.3318,
        longitude: -122.0312,
      },
    },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // Click submit (assuming the initial value is what we want to submit)
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert onFormSubmit was called with the correct address object
    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      {
        address: {
          formatted: "1 Infinite Loop, Cupertino, CA",
          country: "USA",
          city: "Cupertino",
          state: "CA",
          postal: "95014",
          latitude: 37.3318,
          longitude: -122.0312,
        },
      },
      expect.anything(),
    );
  },
};

export const WithError: Story = {
  args: {
    // fieldSchema (zAddress) makes 'formatted' required.
    // So submitting with empty formatted string should show an error.
    defaultValues: { address: { formatted: "" } },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // Attempt to submit the form
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    // Assert onFormSubmit was NOT called because of validation
    await expect(args.onFormSubmit).not.toHaveBeenCalled();

    // Assert error message is shown (from zAddress schema for 'formatted' field)
    // The exact error message is "Please enter an address"
    await expect(
      canvas.getByText("Please enter an address"),
    ).toBeInTheDocument();
  },
};
