import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { FileUploadFieldProps } from "@/ui/fields/files/FileUpload";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { FileUploadField } from "@/ui/fields/files/FileUpload";

import { WithFormProvider } from "../WithFormProvider";

// Helper to create a mock file
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File([new ArrayBuffer(size)], name, { type });
  return file;
};

const meta: Meta<typeof FileUploadField> = {
  component: FileUploadField,
  title: "Fields/Files/FileUploadField",
  decorators: [WithFormProvider],
  tags: ["autodocs"],
};

export default meta;

type StoryArgs = FileUploadFieldProps & BaseFormProviderArgs;

// Base story configuration
// @ts-expect-error - initialValue is not a valid prop
const baseArgs: StoryArgs = {
  name: "file",
  label: "Upload File",
  description: "Please upload your file.",
  fieldSchema: z.instanceof(File, { message: "A file is required." }),
};

// Stories
export const Default: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
  },
};

export const WithInitialValue: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    // @ts-expect-error - initialValue is not a valid prop
    initialValue: createMockFile("initial-file.txt", 1024, "text/plain"),
  },
};

export const ErrorOnSize: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    maxSize: 0.001, // 1KB
    description: "Upload a file. Max size is 1KB.",
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const file = createMockFile("large-file.txt", 2048, "text/plain"); // 2KB file
    const input = canvas.getByLabelText("Upload File", {
      selector: 'input[type="file"]',
    });

    await userEvent.upload(input, file);

    const errorMessage = await canvas.findByText(
      "File size must be less than 0.001MB",
    );
    await expect(errorMessage).toBeInTheDocument();
  },
};

export const ErrorOnType: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    accept: ["image/png"],
    description: "Only .png files are allowed.",
    fieldSchema: z
      .instanceof(File)
      .refine((file) => file.type === "image/png", "File must be a .png image"),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const invalidFile = createMockFile("invalid.txt", 1024, "text/plain");
    const input = canvas.getByLabelText("Upload File", {
      selector: 'input[type="file"]',
    });

    await userEvent.upload(input, invalidFile);

    const errorMessage = await canvas.findByText(
      "File type must be one of: image/png",
    );
    await expect(errorMessage).toBeInTheDocument();
  },
};

export const Multiple: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    name: "files",
    multiple: true,
    label: "Upload Files",
    description: "You can upload multiple files.",
    fieldSchema: z
      .array(z.instanceof(File))
      .min(1, "At least one file is required."),
  },
};

export const Disabled: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    disabled: true,
  },
};

export const WithoutPreview: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    showPreview: false,
  },
};

export const CustomValidation: StoryObj<StoryArgs> = {
  args: {
    ...baseArgs,
    description: "File name must contain the word 'invoice'.",
    fieldSchema: z
      .instanceof(File)
      .refine((file) => file.name.includes("invoice"), {
        message: "File name must contain 'invoice'",
      }),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const invalidFile = createMockFile("document.txt", 1024, "text/plain");
    const input = canvas.getByLabelText("Upload File", {
      selector: 'input[type="file"]',
    });

    await userEvent.upload(input, invalidFile);

    const errorMessage = await canvas.findByText(
      "File name must contain 'invoice'",
    );
    await expect(errorMessage).toBeInTheDocument();
  },
};
