import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import type { SubmitHandler } from "react-hook-form"; // Added import

import { expect, fn, userEvent, within } from "storybook/test"; // Added fn
import { z } from "zod";

import type { DocumentUploadFieldProps } from "@/ui/fields/files/DocumentUpload";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { DocumentUploadField } from "@/ui/fields/files/DocumentUpload";

import { WithFormProvider } from "../WithFormProvider";

// Helper to create a mock file
const createMockFile = (name: string, size: number, type: string): File => {
  const file = new File([new ArrayBuffer(size)], name, { type });
  return file;
};

const meta: Meta<StoryArgs> = {
  component: DocumentUploadField,
  title: "Fields/Files/DocumentUploadField",
  decorators: [WithFormProvider],
  tags: ["autodocs"],
};

export default meta;

type StoryArgs<TSchema extends z.ZodTypeAny = z.ZodTypeAny> =
  Omit<DocumentUploadFieldProps, 'defaultValue' | 'value'> & // Omit conflicting/HTML attributes
  BaseFormProviderArgs<string, TSchema> & {
    onFormSubmit?: SubmitHandler<Record<string, z.infer<TSchema>>>;
    defaultValue?: z.infer<TSchema>; // This is for RHF initial value, typed by schema
    // If DocumentUploadFieldProps has its own 'value' prop for controlled state (e.g. File | File[]), 
    // it should also be Omit-ed and re-added here if needed for stories, typed by TSchema.
    // For now, assuming 'value' from InputHTMLAttributes is the one to omit.
  };

// Base story configuration
const singleFileSchema = z.instanceof(File, { message: "A document is required." });
const baseArgs: StoryArgs<typeof singleFileSchema> = {
  name: "document",
  fieldName: "document", // Added fieldName
  label: "Upload Document",
  description: "Please upload your document.",
  fieldSchema: singleFileSchema,
  onFormSubmit: fn() as SubmitHandler<Record<string, File>>,
};

// Stories
export const Default: StoryObj<StoryArgs<typeof singleFileSchema>> = {
  args: {
    ...baseArgs,
  },
};

export const WithInitialValue: StoryObj<StoryArgs<typeof singleFileSchema>> = {
  args: {
    ...baseArgs,
    defaultValue: createMockFile( // Changed defaultValues to defaultValue
      "initial-document.pdf",
      1024,
      "application/pdf",
    ),
  },
};

export const Error: StoryObj<StoryArgs<typeof singleFileSchema>> = {
  args: {
    ...baseArgs,
    maxSize: 0.001, // 1KB
    description: "Upload a file. Max size is 1KB.",
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const file = createMockFile("large-file.pdf", 2048, "application/pdf"); // 2KB file
    const input = canvas.getByLabelText("Upload Document", {
      selector: 'input[type="file"]',
    });

    await userEvent.upload(input, file);

    const errorMessage = await canvas.findByText(
      "File size must be less than 0.001MB",
    );
    await expect(errorMessage).toBeInTheDocument();
  },
};

const multipleFileSchema = z.array(z.instanceof(File)).min(1, "At least one document is required.");
export const Multiple: StoryObj<StoryArgs<typeof multipleFileSchema>> = {
  args: {
    // Explicitly define args for Multiple to avoid type conflicts from baseArgs spread
    name: "documents",
    fieldName: "documents",
    label: "Upload Documents",
    description: "You can upload multiple documents.",
    multiple: true,
    fieldSchema: multipleFileSchema,
    onFormSubmit: fn() as SubmitHandler<Record<string, File[]>>,
    defaultValue: [], // For RHF: initial value for the 'documents' field
    // common props from baseArgs if needed, ensuring they don't conflict:
    // e.g. allowedTypes: baseArgs.allowedTypes (if it were defined on baseArgs)
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const files = [
      createMockFile("document1.pdf", 1024, "application/pdf"),
      createMockFile(
        "document2.docx",
        1500,
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ),
    ];
    const input = canvas.getByLabelText("Upload Documents", {
      selector: 'input[type="file"]',
    });

    await userEvent.upload(input, files);

    await expect(canvas.getByText("document1.pdf")).toBeInTheDocument();
    await expect(canvas.getByText("document2.docx")).toBeInTheDocument();
  },
};

export const Disabled: StoryObj<StoryArgs<typeof singleFileSchema>> = {
  args: {
    ...baseArgs,
    disabled: true,
  },
};

const customAllowedTypesSchema = z.instanceof(File).refine(
  (file) => ["text/plain", "application/rtf"].includes(file.type),
  "File must be a .txt or .rtf",
);
export const CustomAllowedTypes: StoryObj<StoryArgs<typeof customAllowedTypesSchema>> = {
  args: {
    ...baseArgs, // Spreading baseArgs here will bring its onFormSubmit. It's compatible (File vs File).
    allowedTypes: ["text/plain", "application/rtf"],
    description: "Only .txt and .rtf files are allowed.",
    fieldSchema: customAllowedTypesSchema,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const invalidFile = createMockFile("invalid.pdf", 1024, "application/pdf");
    const input = canvas.getByLabelText("Upload Document", {
      selector: 'input[type="file"]',
    });

    await userEvent.upload(input, invalidFile);

    const errorMessage = await canvas.findByText("File must be a .txt or .rtf");
    await expect(errorMessage).toBeInTheDocument();
  },
};
