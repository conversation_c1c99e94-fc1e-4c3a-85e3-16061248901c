import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";
import { z } from "zod";

import type { EmailFieldProps } from "@/ui/fields/text/Email";

import type { BaseFormProviderArgs } from "../WithFormProvider";

import { EmailField } from "@/ui/fields/text/Email";

import { WithFormProvider } from "../WithFormProvider";

// Define a Zod schema for this story's form validation needs
const storyLevelEmailSchema = z
  .string()
  .min(1, "Email address cannot be empty.")
  .email("Please enter a valid email address.");

// Define args type for this specific story
interface CurrentEmailStoryArgs extends EmailFieldProps, BaseFormProviderArgs {
  fieldName: string; // To match the 'name' prop for the decorator
  fieldSchema: typeof storyLevelEmailSchema;
  defaultValues?: Record<string, string | undefined>;
}

const meta: Meta<CurrentEmailStoryArgs> = {
  title: "Fields/Text/Email",
  component: EmailField,
  tags: ["autodocs"],
  decorators: [WithFormProvider],
  args: {
    // Props for EmailField component itself
    label: "User Email",
    name: "userEmail", // This will be the key in the form data
    placeholder: "<EMAIL>",
    description: "The primary email address for the user.",

    // Args for the WithFormProvider decorator
    fieldName: "userEmail", // Must match the 'name' prop above
    fieldSchema: storyLevelEmailSchema,
    onFormSubmit: fn(),
    defaultValues: { userEmail: "" },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<CurrentEmailStoryArgs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithInitialValue: Story = {
  args: {
    name: "initialUserEmail",
    fieldName: "initialUserEmail",
    defaultValues: { initialUserEmail: "<EMAIL>" },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("<EMAIL>");
    await expect(input).toHaveValue("<EMAIL>");
  },
};

export const Interactive: Story = {
  args: {
    name: "interactiveUserEmail",
    fieldName: "interactiveUserEmail",
    defaultValues: { interactiveUserEmail: "" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("<EMAIL>");

    await userEvent.type(input, "<EMAIL>");
    await expect(input).toHaveValue("<EMAIL>");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onFormSubmit).toHaveBeenCalledWith(
      { [args.name!]: "<EMAIL>" },
      expect.anything(),
    );
  },
};

export const WithErrorEmpty: Story = {
  args: {
    name: "emptyUserEmail",
    fieldName: "emptyUserEmail",
    defaultValues: { emptyUserEmail: "" }, // Intentionally empty
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Email address cannot be empty."),
    ).toBeInTheDocument();
  },
};

export const WithErrorInvalid: Story = {
  args: {
    name: "invalidUserEmail",
    fieldName: "invalidUserEmail",
    defaultValues: { invalidUserEmail: "not-an-email" },
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const input = canvas.getByPlaceholderText("<EMAIL>");
    await expect(input).toHaveValue("not-an-email");

    await userEvent.click(canvas.getByRole("button", { name: /submit/i }));

    await expect(args.onFormSubmit).not.toHaveBeenCalled();
    await expect(
      canvas.getByText("Please enter a valid email address."),
    ).toBeInTheDocument();
  },
};
