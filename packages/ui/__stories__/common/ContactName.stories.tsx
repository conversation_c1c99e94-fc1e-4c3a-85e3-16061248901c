import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import ContactName from "@/ui/common/ContactName";

const meta = {
  title: "Common/ContactName",
  component: ContactName,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ContactName>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: "<PERSON>",
    size: "md",
  },
};

export const Large: Story = {
  args: {
    name: "John Doe",
    size: "lg",
  },
};

export const WithLink: Story = {
  args: {
    name: "<PERSON>",
    size: "md",
    link: "/contacts/john-doe",
  },
};

export const LongName: Story = {
  args: {
    name: "<PERSON>",
    size: "md",
  },
};

export const WithCopyButton: Story = {
  args: {
    name: "<PERSON>",
    size: "md",
    showCopyButton: true,
  },
};
