import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import PreviewDocument from "@/ui/common/PreviewDocument";

const meta: Meta<typeof PreviewDocument> = {
  title: "Common/PreviewDocument",
  component: PreviewDocument,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof PreviewDocument>;

const sampleDocument = {
  id: "1",
  name: "Sample Document",
  type: "pdf",
  url: "https://example.com/document.pdf",
};

export const Default: Story = {
  args: {
    document: sampleDocument,
  },
};

export const Loading: Story = {
  args: {
    document: sampleDocument,
    loading: true,
  },
};

export const WithDescription: Story = {
  args: {
    document: {
      ...sampleDocument,
      description: "A sample PDF document for testing",
    },
  },
};

export const Error: Story = {
  args: {
    document: {
      ...sampleDocument,
      url: "https://example.com/invalid.pdf",
    },
  },
};
