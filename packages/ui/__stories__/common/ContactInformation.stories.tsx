import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import ContactInformation from "@/ui/common/ContactInformation";

const meta: Meta<typeof ContactInformation> = {
  title: "Common/ContactInformation",
  component: ContactInformation,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ContactInformation>;

export const Default: Story = {
  args: {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
  },
};

export const WithoutPhone: Story = {
  args: {
    name: "<PERSON>",
    email: "<EMAIL>",
  },
};

export const WithoutEmail: Story = {
  args: {
    name: "<PERSON>",
    phone: "+****************",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithLink: Story = {
  args: {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    link: "/contacts/john-doe",
  },
};
