import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs-vite";

import PreviewPerson from "@/ui/common/PreviewPerson";

const meta: Meta<typeof PreviewPerson> = {
  title: "Common/PreviewPerson",
  component: PreviewPerson,
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj<typeof PreviewPerson>;

const samplePerson = {
  id: "1",
  name: "<PERSON>",
  email: "<EMAIL>",
  avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
};

export const Default: Story = {
  args: {
    person: samplePerson,
  },
};

export const Loading: Story = {
  args: {
    person: samplePerson,
    loading: true,
  },
};

export const WithDescription: Story = {
  args: {
    person: samplePerson,
    description: "Senior Software Engineer",
  },
};

export const WithoutAvatar: Story = {
  args: {
    person: {
      ...samplePerson,
      avatar: null,
    },
    description: "Product Manager",
  },
};

export const SmallSize: Story = {
  args: {
    person: samplePerson,
    description: "Designer",
    size: "sm",
  },
};

export const LargeSize: Story = {
  args: {
    person: samplePerson,
    description: "Team Lead",
    size: "lg",
  },
};
