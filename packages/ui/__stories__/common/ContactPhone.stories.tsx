import type { Meta, StoryObj } from "@storybook/nextjs-vite";

import ContactPhone from "@/ui/common/ContactPhone";

const meta = {
  title: "Common/ContactPhone",
  component: ContactPhone,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof ContactPhone>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    phone: "+****************",
    showCopy: true,
  },
};

export const WithoutCopyButton: Story = {
  args: {
    phone: "+****************",
    showCopy: false,
  },
};

export const NoPhoneNumber: Story = {
  args: {
    phone: null,
    showCopy: true,
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};
