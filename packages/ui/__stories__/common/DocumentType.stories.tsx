import type { Meta, StoryObj } from "@storybook/nextjs-vite";

import DocumentType from "@/ui/common/DocumentType";

const meta = {
  title: "Common/DocumentType",
  component: DocumentType,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof DocumentType>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    type: "pdf",
  },
};

export const AllTypes: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "1rem" }}>
      <DocumentType type="pdf" />
      <DocumentType type="doc" />
      <DocumentType type="xls" />
      <DocumentType type="img" />
    </div>
  ),
};

export const WithLabel: Story = {
  args: {
    type: "pdf",
  },
};
