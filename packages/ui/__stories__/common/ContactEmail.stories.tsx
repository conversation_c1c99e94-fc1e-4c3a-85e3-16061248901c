import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import ContactEmail from "@/ui/common/ContactEmail";

const meta: Meta<typeof ContactEmail> = {
  title: "Common/ContactEmail",
  component: ContactEmail,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof ContactEmail>;

export const Default: Story = {
  args: {
    email: "<EMAIL>",
  },
};

export const WithCustomClassName: Story = {
  args: {
    email: "<EMAIL>",
    className: "custom-email-class",
  },
};
