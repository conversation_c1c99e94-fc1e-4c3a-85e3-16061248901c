import type { Meta, StoryObj } from "@storybook/nextjs-vite";

import DocumentViewer from "@/ui/common/DocumentViewer";

const meta = {
  title: "Common/DocumentViewer",
  component: DocumentViewer,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof DocumentViewer>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleDocument = {
  id: "1",
  name: "Sample Document",
  type: "pdf",
  url: "https://example.com/sample.pdf",
};

export const Default: Story = {
  args: {
    document: sampleDocument,
  },
};

export const Loading: Story = {
  args: {
    document: sampleDocument,
  },
};

export const WithError: Story = {
  args: {
    document: {
      ...sampleDocument,
      url: "https://example.com/invalid.pdf",
    },
  },
};

export const CustomSize: Story = {
  args: {
    document: {
      ...sampleDocument,
      url: "https://example.com/sample.pdf",
    },
  },
};
