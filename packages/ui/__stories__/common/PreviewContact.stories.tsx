import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import PreviewContact from "@/ui/common/PreviewContact";

const meta: Meta<typeof PreviewContact> = {
  title: "Common/PreviewContact",
  component: PreviewContact,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof PreviewContact>;

export const Default: Story = {
  args: {
    firstName: "<PERSON>",
    lastName: "Do<PERSON>",
    email: "<EMAIL>",
    phone: "****** 567 8900",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithoutPhone: Story = {
  args: {
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
  },
};

export const WithoutEmail: Story = {
  args: {
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    phone: "****** 654 3210",
  },
};
