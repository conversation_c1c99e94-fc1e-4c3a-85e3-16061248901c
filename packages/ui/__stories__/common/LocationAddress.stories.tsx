import type { Meta, StoryObj } from "@storybook/nextjs-vite";

import LocationAddress from "@/ui/common/LocationAddress";

const meta = {
  title: "Common/LocationAddress",
  component: LocationAddress,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof LocationAddress>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    address: "123 Main Street, San Francisco, CA 94105",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const WithLink: Story = {
  args: {
    address: "123 Main Street, San Francisco, CA 94105",
    link: true,
  },
};

export const Truncated: Story = {
  args: {
    address:
      "45 Westminster Bridge Road, South Bank, London SE1 7GP, United Kingdom",
    truncate: true,
  },
};
