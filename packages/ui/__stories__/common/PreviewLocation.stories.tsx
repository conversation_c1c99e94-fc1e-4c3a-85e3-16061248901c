import type { <PERSON>a, StoryObj } from "@storybook/nextjs-vite";

import PreviewLocation from "@/ui/common/PreviewLocation";

const meta = {
  title: "Common/PreviewLocation",
  component: PreviewLocation,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof PreviewLocation>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    location: {
      id: "1",
      name: "San Francisco Office",
      address: {
        formatted: "123 Main St, San Francisco, CA 94105",
      },
    },
  },
};

export const WithDescription: Story = {
  args: {
    location: {
      id: "1",
      name: "Headquarters",
      address: {
        formatted: "123 Main St, San Francisco, CA 94105",
      },
    },
    description: "Main office building with 500+ employees",
  },
};

export const LongAddress: Story = {
  args: {
    location: {
      id: "2",
      name: "London Branch",
      address: {
        formatted:
          "45 Westminster Bridge Road, South Bank, London SE1 7GP, United Kingdom",
      },
    },
  },
};

export const SmallSize: Story = {
  args: {
    location: {
      id: "3",
      name: "Remote Office",
      address: {
        formatted: "456 Oak Ave, Austin, TX 78701",
      },
    },
    description: "Co-working space",
    size: "sm",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};
