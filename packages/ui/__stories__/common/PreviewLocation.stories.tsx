import type { Meta, StoryObj } from "@storybook/nextjs-vite";

import PreviewLocation from "@/ui/common/PreviewLocation";

const meta = {
  title: "Common/PreviewLocation",
  component: PreviewLocation,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof PreviewLocation>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    location: {
      id: "1",
      name: "San Francisco Office",
      address: {
        formatted: "123 Main St, San Francisco, CA 94105",
      },
    },
  },
};

export const LongAddress: Story = {
  args: {
    location: {
      id: "2",
      name: "London Branch",
      address: {
        formatted:
          "45 Westminster Bridge Road, South Bank, London SE1 7GP, United Kingdom",
      },
    },
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};
