import type { Meta, StoryObj } from "@storybook/nextjs-vite";

import PreviewOrganization from "@/ui/common/PreviewOrganization";

const meta = {
  title: "Common/PreviewOrganization",
  component: PreviewOrganization,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof PreviewOrganization>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleOrg = {
  id: "1",
  name: "AXA Medical Group",
  avatar: "https://example.com/avatar.jpg",
};

export const Default: Story = {
  args: {
    organization: sampleOrg,
    size: "md",
  },
};

export const WithLink: Story = {
  args: {
    organization: sampleOrg,
    link: true,
    size: "md",
  },
};

export const Small: Story = {
  args: {
    organization: sampleOrg,
    size: "sm",
  },
};

export const Large: Story = {
  args: {
    organization: sampleOrg,
    size: "lg",
  },
};

export const ExtraLarge: Story = {
  args: {
    organization: sampleOrg,
    size: "xl",
  },
};

export const NoAvatar: Story = {
  args: {
    organization: {
      id: "2",
      name: "Medical Center Inc",
    },
    size: "md",
  },
};

export const AXAProfessionals: Story = {
  args: {
    organization: {
      id: "3",
      name: "AXA Professionals",
    },
    size: "md",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    size: "md",
  },
};

export const WithChildren: Story = {
  args: {
    organization: sampleOrg,
    size: "md",
    children: (
      <div className="text-sm text-muted-foreground">Additional content</div>
    ),
  },
};
