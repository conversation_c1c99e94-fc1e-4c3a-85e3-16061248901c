import { Meta } from "@storybook/addon-docs";

# AXA Professionals Design System

<Meta title="Design System" />

<div className="space-y-8 p-6 bg-background border border-border rounded-lg mb-8">
  
  {/* Type Scale Preview */}
  <div className="space-y-4">
    <h3 className="text-lg font-semibold mb-4">Typography Scale</h3>
    <div className="space-y-3">
      <div className="flex items-baseline gap-4">
        <span className="text-xs text-muted-foreground w-16">Text XS</span>
        <span className="text-xs">The quick brown fox jumps over the lazy dog</span>
        <span className="text-xs text-muted-foreground">0.75rem (12px)</span>
      </div>
      <div className="flex items-baseline gap-4">
        <span className="text-xs text-muted-foreground w-16">Text SM</span>
        <span className="text-sm">The quick brown fox jumps over the lazy dog</span>
        <span className="text-xs text-muted-foreground">0.875rem (14px)</span>
      </div>
      <div className="flex items-baseline gap-4">
        <span className="text-xs text-muted-foreground w-16">Text Base</span>
        <span className="text-base">The quick brown fox jumps over the lazy dog</span>
        <span className="text-xs text-muted-foreground">1rem (16px)</span>
      </div>
      <div className="flex items-baseline gap-4">
        <span className="text-xs text-muted-foreground w-16">Text LG</span>
        <span className="text-lg">The quick brown fox jumps over the lazy dog</span>
        <span className="text-xs text-muted-foreground">1.125rem (18px)</span>
      </div>
      <div className="flex items-baseline gap-4">
        <span className="text-xs text-muted-foreground w-16">Text XL</span>
        <span className="text-xl">The quick brown fox jumps over the lazy dog</span>
        <span className="text-xs text-muted-foreground">1.25rem (20px)</span>
      </div>
      <div className="flex items-baseline gap-4">
        <span className="text-xs text-muted-foreground w-16">Text 2XL</span>
        <span className="text-2xl">The quick brown fox jumps over the lazy dog</span>
        <span className="text-xs text-muted-foreground">1.5rem (24px)</span>
      </div>
    </div>
  </div>

{/* Font Weight Preview */}

<div className="space-y-4">
  <h3 className="mb-4 text-lg font-semibold">Font Weights</h3>
  <div className="grid grid-cols-2 gap-4">
    <div className="space-y-2">
      <div className="flex items-center gap-3">
        <span className="w-20 text-sm text-muted-foreground">Normal</span>
        <span className="font-normal">Regular text content</span>
      </div>
      <div className="flex items-center gap-3">
        <span className="w-20 text-sm text-muted-foreground">Medium</span>
        <span className="font-medium">Form labels and emphasis</span>
      </div>
    </div>
    <div className="space-y-2">
      <div className="flex items-center gap-3">
        <span className="w-20 text-sm text-muted-foreground">Semibold</span>
        <span className="font-semibold">Button text and headings</span>
      </div>
      <div className="flex items-center gap-3">
        <span className="w-20 text-sm text-muted-foreground">Bold</span>
        <span className="font-bold">Critical information</span>
      </div>
    </div>
  </div>
</div>

{/* Color Palette Preview */}

<div className="space-y-4">
  <h3 className="mb-4 text-lg font-semibold">Color Tokens</h3>
  <div className="grid grid-cols-2 gap-4 lg:grid-cols-4">
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Primary</h4>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-primary"></div>
        <span className="text-sm">primary</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="flex h-6 w-6 items-center justify-center rounded border border-border bg-primary text-xs text-primary-foreground">
          A
        </div>
        <span className="text-sm">primary-foreground</span>
      </div>
    </div>
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Background</h4>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-background"></div>
        <span className="text-sm">background</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-muted"></div>
        <span className="text-sm">muted</span>
      </div>
    </div>
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Accent</h4>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-accent"></div>
        <span className="text-sm">accent</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-secondary"></div>
        <span className="text-sm">secondary</span>
      </div>
    </div>
    <div className="space-y-2">
      <h4 className="text-sm font-medium">Status</h4>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-destructive"></div>
        <span className="text-sm">destructive</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="h-6 w-6 rounded border border-border bg-border"></div>
        <span className="text-sm">border</span>
      </div>
    </div>
  </div>
</div>

{/* Spacing Scale Preview */}

<div className="space-y-4">
  <h3 className="mb-4 text-lg font-semibold">Spacing Scale</h3>
  <div className="space-y-3">
    <div className="flex items-center gap-4">
      <span className="w-8 text-xs text-muted-foreground">0.5</span>
      <div className="h-4 w-0.5 bg-primary"></div>
      <span className="text-sm">0.125rem (2px)</span>
    </div>
    <div className="flex items-center gap-4">
      <span className="w-8 text-xs text-muted-foreground">1</span>
      <div className="h-4 w-1 bg-primary"></div>
      <span className="text-sm">0.25rem (4px)</span>
    </div>
    <div className="flex items-center gap-4">
      <span className="w-8 text-xs text-muted-foreground">2</span>
      <div className="h-4 w-2 bg-primary"></div>
      <span className="text-sm">0.5rem (8px)</span>
    </div>
    <div className="flex items-center gap-4">
      <span className="w-8 text-xs text-muted-foreground">4</span>
      <div className="h-4 w-4 bg-primary"></div>
      <span className="text-sm">1rem (16px)</span>
    </div>
    <div className="flex items-center gap-4">
      <span className="w-8 text-xs text-muted-foreground">6</span>
      <div className="h-4 w-6 bg-primary"></div>
      <span className="text-sm">1.5rem (24px)</span>
    </div>
    <div className="flex items-center gap-4">
      <span className="w-8 text-xs text-muted-foreground">8</span>
      <div className="h-4 w-8 bg-primary"></div>
      <span className="text-sm">2rem (32px)</span>
    </div>
  </div>
</div>

{/* Focus Ring Preview */}

<div className="space-y-4">
  <h3 className="mb-4 text-lg font-semibold">Focus & Interaction States</h3>
  <div className="flex flex-wrap gap-4">
    <button className="rounded-md bg-primary px-4 py-2 text-primary-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
      Focused Button
    </button>
    <div className="rounded-md border border-input px-3 py-2 focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
      <input
        placeholder="Focus within input"
        className="bg-transparent outline-none"
      />
    </div>
    <div className="cursor-pointer rounded-md bg-accent px-4 py-2 text-accent-foreground hover:bg-accent/80">
      Hover state
    </div>
  </div>
</div>

</div>

---

This document outlines the core design principles, tokens, and patterns that power the AXA Professionals UI component library.

## Overview

The AXA design system is built on semantic design tokens that automatically adapt between light and dark themes. All components follow consistent accessibility standards with keyboard navigation, focus management, and screen reader support.

## Typography

### Font Family

- **Primary**: System font stack optimizing for readability across platforms
- **Monospace**: Used for code blocks, technical content, and data display

### Type Scale

- **Text XS**: 0.75rem (12px) - Fine print, captions
- **Text SM**: 0.875rem (14px) - Secondary content, form labels
- **Text Base**: 1rem (16px) - Body text, primary content
- **Text LG**: 1.125rem (18px) - Subheadings, emphasis
- **Text XL**: 1.25rem (20px) - Section headers
- **Text 2XL**: 1.5rem (24px) - Page titles, major headings

### Font Weights

- **Normal**: 400 - Standard body text
- **Medium**: 500 - Form labels, secondary emphasis
- **Semibold**: 600 - Button text, strong emphasis
- **Bold**: 700 - Headings, critical information

## Color System

### Semantic Color Tokens

The color system uses semantic naming that automatically adapts to light/dark themes:

#### Primary Colors

- `primary` - Brand accent color for buttons, links, and key actions
- `primary-foreground` - Text color for content on primary backgrounds

#### Background Colors

- `background` - Main page background
- `foreground` - Primary text color
- `muted` - Subtle background for cards, secondary areas
- `muted-foreground` - Secondary text color
- `accent` - Highlight background for hover states
- `accent-foreground` - Text on accent backgrounds

#### UI Element Colors

- `card` - Card background color
- `card-foreground` - Text color for card content
- `popover` - Overlay background (tooltips, dropdowns)
- `popover-foreground` - Text color for overlay content
- `border` - Default border color
- `input` - Input field border color

#### Status Colors

- `destructive` - Error states, dangerous actions
- `destructive-foreground` - Text on destructive backgrounds
- `secondary` - Alternative actions, less prominent buttons
- `secondary-foreground` - Text on secondary backgrounds

### Dark Mode Support

All color tokens automatically switch between light and dark variants based on the user's theme preference. The system uses CSS custom properties to enable seamless theme transitions.

## Spacing & Layout

### Spacing Scale

Based on a 4px grid system using rem units:

- **0.5**: 0.125rem (2px) - Minimal spacing
- **1**: 0.25rem (4px) - Tight spacing
- **1.5**: 0.375rem (6px) - Small gaps
- **2**: 0.5rem (8px) - Standard small spacing
- **3**: 0.75rem (12px) - Medium spacing
- **4**: 1rem (16px) - Large spacing
- **6**: 1.5rem (24px) - Section spacing
- **8**: 2rem (32px) - Major layout spacing

### Border Radius

- **SM**: 0.125rem (2px) - Subtle rounding
- **Default**: 0.375rem (6px) - Standard component rounding
- **MD**: 0.375rem (6px) - Cards, buttons
- **LG**: 0.5rem (8px) - Larger components
- **Full**: 9999px - Pills, avatars, fully rounded elements

## Focus & Interaction States

### Focus Ring System

All interactive elements use a consistent focus ring pattern for accessibility:

- **Ring Width**: 2px solid outline
- **Ring Color**: `hsl(var(--ring))` - Semantic ring color token
- **Ring Offset**: 2px separation from element
- **Border Radius**: Matches component's border radius

### Focus Classes

- `focus-visible:outline-none` - Removes default browser outline
- `focus-visible:ring-2` - Applies 2px ring width
- `focus-visible:ring-ring` - Uses semantic ring color
- `focus-visible:ring-offset-2` - Adds 2px offset
- `focus-visible:ring-offset-background` - Ensures proper contrast

### Focus-Within Pattern

For composite components (like input groups), focus-within provides container highlighting:

- `focus-within:ring-2` - Ring appears when any child receives focus
- `focus-within:ring-ring` - Consistent ring color
- `focus-within:ring-offset-2` - Standard offset spacing

### Hover States

- `hover:bg-accent` - Background highlight on hover
- `hover:text-accent-foreground` - Text color change for contrast
- `hover:opacity-100` - Reveals hidden elements (like action buttons)

## Component Size Variants

### Standard Size Scale

Most components support consistent size variants:

- **SM**: Compact sizing for dense layouts
- **MD** (Default): Standard comfortable sizing
- **LG**: Larger sizing for prominence
- **XL**: Maximum sizing for hero elements

**Compact** and **Panel** (large) are common variant size (modes) that are used in different contexts.

### Icon Sizing

- **Size 3**: 0.75rem (12px) - Small icons in buttons
- **Size 4**: 1rem (16px) - Standard interface icons
- **Size 5**: 1.25rem (20px) - Larger icons in headers
- **Size 6**: 1.5rem (24px) - Prominent icons

## Technology Stack

### Tailwind CSS Foundation

- **CSS Custom Properties**: Enable seamless theme switching
- **Utility Classes**: Provide consistent spacing, typography, and colors
- **Responsive Design**: Built-in breakpoint system for mobile-first design
- **Dark Mode**: Automatic theme detection and switching

### Component Architecture

- **Radix UI Primitives**: Unstyled, accessible component foundations
- **Class Variance Authority (CVA)**: Type-safe component variant management
- **Tailwind Merge**: Intelligent class conflict resolution
- **CSS Variables**: Dynamic theming and customization

### Accessibility Standards

- **WCAG 2.1 AA Compliance**: Color contrast, keyboard navigation
- **Screen Reader Support**: Proper ARIA labels and semantic markup
- **Focus Management**: Logical tab order and focus trapping
- **Reduced Motion**: Respects user accessibility preferences

### Theme Provider

The `ThemeProvider` component manages:

- Theme persistence across sessions
- System theme detection
- CSS variable injection
- Component theme inheritance

This design system ensures consistency, accessibility, and maintainability across all AXA Professionals applications.
