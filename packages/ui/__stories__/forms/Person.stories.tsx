import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { PersonFormValues } from "../../src/forms/Person";

import PersonForm from "../../src/forms/Person";

const meta = {
  title: "Forms/PersonForm",
  component: PersonForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    // Provide default values that satisfy the schema for a basic story
    defaultValues: {
      organizationId: "org123",
      firstName: "", // Required, will be filled by user
      lastName: "", // Required, will be filled by user
    } as Partial<PersonFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof PersonForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testData = {
      firstName: "Jane",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "1234567890",
    };

    // 1. Fill in the form fields
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the first name of the person"),
      testData.firstName,
    );
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the last name of the person"),
      testData.lastName,
    );
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the email address of the person"),
      testData.email,
    );
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the phone number of the person"),
      testData.phone,
    );

    // 2. Get the submit button and click it
    const submitButton = await canvas.findByRole("button", { name: /submit/i });
    await userEvent.click(submitButton);

    // 3. Assert onSubmit was called with the correct data
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        organizationId: "org123",
        firstName: testData.firstName,
        lastName: testData.lastName,
        email: testData.email,
        phone: testData.phone,
      }),
      expect.anything(), // Second argument to handleSubmit is the event
    );
  },
};
