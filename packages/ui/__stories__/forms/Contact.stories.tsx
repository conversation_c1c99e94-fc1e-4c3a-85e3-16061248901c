import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { ContactFormValues } from "../../src/forms/Contact";

import ContactForm from "../../src/forms/Contact";

const mockRoles = [
  { id: "1", value: "Admin" },
  { id: "2", value: "Editor" },
  { id: "3", value: "Viewer" },
];

const meta = {
  title: "Forms/ContactForm",
  component: ContactForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    roles: mockRoles,
    // Provide default values that satisfy the schema for a basic story
    defaultValues: {
      personId: "mockPerson123", // Required by schema
      role: "", // Will be selected by user in play function
      person: { id: "mockPerson123" }, // Required by schema
    } as Partial<ContactFormValues>, // Use Partial for defaultValues
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ContactForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // 1. Find the Role select trigger and click it
    // The FormLabel is "Role"
    const roleSelectTrigger = canvas.getByRole("button", {
      name: /select the role of the contact/i, // Placeholder text
    });
    await userEvent.click(roleSelectTrigger);

    // 2. Select a role from the list (e.g., "Admin")
    const roleOptionToSelect = await canvas.findByRole("option", {
      name: "Admin",
    });
    await userEvent.click(roleOptionToSelect);

    // 3. Get the submit button and click it
    const submitButton = await canvas.findByRole("button", { name: /submit/i });
    await userEvent.click(submitButton);

    // 4. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    // Check if it was called with the selected role and the default personId
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        role: "Admin",
        personId: "mockPerson123",
        person: expect.objectContaining({ id: "mockPerson123" }),
      }),
      expect.anything(), // Second argument to handleSubmit is the event
    );
  },
};
