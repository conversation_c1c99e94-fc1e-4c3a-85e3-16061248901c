import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { MemberFormValues } from "@/ui/forms/organizations/Member";

import MemberForm from "@/ui/forms/organizations/Member";

const meta = {
  title: "Forms/Organizations/MemberForm",
  component: MemberForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    defaultValues: {
      organizationId: "org789",
      personId: "person456",
      role: "", // Required, will be selected by user
    } as Partial<MemberFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof MemberForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // 1. Find the Role select trigger and click it
    // The placeholder is "Select the role for the member"
    await userEvent.click(
      canvas.getByRole("button", { name: /select the role for the member/i }),
    );

    // 2. Select a role from the list (e.g., "Admin")
    // The options are dynamically generated but we know "Admin" is one
    await userEvent.click(await canvas.findByRole("option", { name: "Admin" }));

    // 3. Get the submit button and click it
    await userEvent.click(
      await canvas.findByRole("button", { name: /submit/i }),
    );

    // 4. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        organizationId: "org789",
        personId: "person456",
        role: "org:admin", // The value for the "Admin" option
      }),
      expect.anything(), // Second argument to handleSubmit is the event
    );
  },
};
