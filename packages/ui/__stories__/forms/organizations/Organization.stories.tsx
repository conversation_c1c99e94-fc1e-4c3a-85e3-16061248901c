import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { OrganizationFormValues } from "@/ui/forms/organizations/Organization";

import OrganizationForm from "@/ui/forms/organizations/Organization";

const meta = {
  title: "Forms/Organizations/OrganizationForm",
  component: OrganizationForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    // parentId is required by the schema but not rendered in the form,
    // so it must be provided in defaultValues.
    defaultValues: {
      name: "", // Required, will be filled by user
      parentId: "parent-org-123",
      organizationId: "org789",
      fieldNationId: "field-nation-123",
    } satisfies OrganizationFormValues,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof OrganizationForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testOrgName = "NewCo Inc.";

    // 1. Find the name input and type into it
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the name of the organization"),
      testOrgName,
    );

    // 2. Get the submit button and click it
    await userEvent.click(
      await canvas.findByRole("button", { name: /submit/i }),
    );

    // 3. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        parentId: "parent-org-123",
        name: testOrgName,
      }),
      expect.anything(),
    );
  },
};
