import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { InvitationFormValues } from "@/ui/forms/organizations/Invitation";

import InvitationForm from "@/ui/forms/organizations/Invitation";

const meta = {
  title: "Forms/Organizations/InvitationForm",
  component: InvitationForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    defaultValues: {
      email: "", // Required, will be filled by user
      role: "", // Required, will be filled by user
    } as Partial<InvitationFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof InvitationForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testEmail = "<EMAIL>";

    // 1. Find the email input and type into it
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the email for the member"),
      testEmail,
    );

    // 2. Find the Role select trigger and click it
    await userEvent.click(
      canvas.getByRole("button", { name: /select the role for the member/i }),
    );

    // 3. Select a role from the list (e.g., "Admin")
    await userEvent.click(await canvas.findByRole("option", { name: "Admin" }));

    // 4. Get the submit button and click it
    await userEvent.click(
      await canvas.findByRole("button", { name: /submit/i }),
    );

    // 5. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        email: testEmail,
        role: "org:admin", // The value for the "Admin" option
      }),
      expect.anything(),
    );
  },
};
