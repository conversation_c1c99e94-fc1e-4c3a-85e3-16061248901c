import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { NewsletterFormValues } from "@/ui/forms/public/Newsletter";

import NewsletterForm from "@/ui/forms/public/Newsletter";

const meta = {
  title: "Forms/Public/NewsletterForm",
  component: NewsletterForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    defaultValues: {
      email: "",
    } as Partial<NewsletterFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof NewsletterForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testEmail = "<EMAIL>";

    // 1. Find the email input and type into it
    await userEvent.type(
      canvas.getByPlaceholderText(
        "Enter the email address to best contact you",
      ),
      testEmail,
    );

    // 2. Get the submit button and click it
    await userEvent.click(
      await canvas.findByRole("button", { name: /subscribe/i }),
    );

    // 3. Assert onSubmit was called with the correct data
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({ email: testEmail }),
      expect.anything(),
    );
  },
};
