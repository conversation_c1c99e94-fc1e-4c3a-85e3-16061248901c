import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, userEvent, within } from "storybook/test";

import CookieSettingsForm from "@/ui/forms/public/CookieSettings";

const meta = {
  title: "Forms/Public/CookieSettingsForm",
  component: CookieSettingsForm,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof CookieSettingsForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Interact with Functional Cookies switch
    const functionalSwitch = canvas.getByRole("switch", {
      name: /functional cookies/i,
    });
    await expect(functionalSwitch).not.toBeChecked();
    await userEvent.click(functionalSwitch);
    await expect(functionalSwitch).toBeChecked();

    // Interact with Performance Cookies switch
    const performanceSwitch = canvas.getByRole("switch", {
      name: /performance cookies/i,
    });
    await expect(performanceSwitch).not.toBeChecked();
    await userEvent.click(performanceSwitch);
    await expect(performanceSwitch).toBeChecked();

    // Check the Strictly Necessary switch (should be checked by default and disabled for unchecking in a real scenario, but clickable here)
    const necessarySwitch = canvas.getByRole("switch", {
      name: /strictly necessary/i,
    });
    await expect(necessarySwitch).toBeChecked();
    // We can click it, though in a real app it might be disabled or prevent unchecking
    // For story purposes, we'll just confirm it's there and checked.

    // Click the Save preferences button
    const saveButton = canvas.getByRole("button", {
      name: /save preferences/i,
    });
    await userEvent.click(saveButton);
    // No specific onSubmit to assert here as the component doesn't have one
  },
};
