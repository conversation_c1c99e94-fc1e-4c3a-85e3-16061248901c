import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { ContactUsFormValues } from "@/ui/forms/public/ContactUs";

import ContactUsForm from "@/ui/forms/public/ContactUs";

const meta = {
  title: "Forms/Public/ContactUsForm",
  component: ContactUsForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    defaultValues: {
      companyName: "",
      name: "",
      email: "",
      phone: "",
      message: "",
    } as Partial<ContactUsFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof ContactUsForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testData = {
      companyName: "Test Corp",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "5551234567",
      message: "This is a test inquiry.",
    };

    // 1. Fill in the form fields
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the name of the company"),
      testData.companyName,
    );
    await userEvent.type(
      canvas.getByPlaceholderText("Enter your name"),
      testData.name,
    );
    await userEvent.type(
      canvas.getByPlaceholderText(
        "Enter the email address to best contact you",
      ),
      testData.email,
    );
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the phone number to best contact you"),
      testData.phone,
    );
    await userEvent.type(
      canvas.getByPlaceholderText("Send us a brief message about your needs"),
      testData.message,
    );

    // 2. Get the submit button and click it
    await userEvent.click(
      await canvas.findByRole("button", { name: /submit/i }),
    );

    // 3. Assert onSubmit was called with the correct data
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining(testData),
      expect.anything(),
    );
  },
};
