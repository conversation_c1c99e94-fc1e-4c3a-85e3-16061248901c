import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import AddressForm from "../../src/forms/Address";

const meta = {
  title: "Forms/AddressForm",
  component: AddressForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
  },
  parameters: {
    layout: "centered", // Optional: Adjust as needed
  },
} satisfies Meta<typeof AddressForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // 1. Find the address combobox trigger by its associated label and click it
    const addressCombobox = canvas.getByRole("combobox", {
      name: /address/i, // The FormLabel is "Address"
    });
    await userEvent.click(addressCombobox);

    // 2. Select an address from the list (e.g., "English")
    // CommandItems typically have role 'option'
    const optionToSelect = await canvas.findByRole("option", {
      name: "English",
    });
    await userEvent.click(optionToSelect);

    // 3. Get the submit button and click it
    // The default submit button is rendered by AddressForm and has text "Submit"
    const submitButton = await canvas.findByRole("button", { name: /submit/i });
    await userEvent.click(submitButton);

    // 4. Assert onSubmit was called (optional explicit assertion)
    // Storybook's fn() wrapper on onSubmit automatically tracks calls.
    // For more explicit testing, you can use expect:
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({ address: "en" }), // 'en' is the value for 'English'
      expect.anything(), // Second argument to handleSubmit is the event
    );
  },
};
