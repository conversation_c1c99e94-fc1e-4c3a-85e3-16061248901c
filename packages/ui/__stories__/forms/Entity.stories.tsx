import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { EntityFormValues } from "../../src/forms/Entity";

import EntityForm from "../../src/forms/Entity";

const meta = {
  title: "Forms/EntityForm",
  component: EntityForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    defaultValues: {
      title: "", // Required, will be filled by user
      description: "", // Optional, will be filled by user
    } as Partial<EntityFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof EntityForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testEntityTitle = "My Awesome Entity";
    const testEntityDescription =
      "This entity is truly awesome and has a great description.";

    // 1. Find the Title input and type into it
    const titleInput = canvas.getByPlaceholderText("Enter the title");
    await userEvent.type(titleInput, testEntityTitle);

    // 2. Find the Description textarea and type into it
    const descriptionTextarea = canvas.getByPlaceholderText(
      "Enter a description (optional)",
    );
    await userEvent.type(descriptionTextarea, testEntityDescription);

    // 3. Get the submit button (text is "Save") and click it
    const submitButton = await canvas.findByRole("button", { name: /save/i });
    await userEvent.click(submitButton);

    // 4. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        title: testEntityTitle,
        description: testEntityDescription,
      }),
      expect.anything(), // Second argument to handleSubmit is the event
    );
  },
};
