import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { UserInvitationFormValues } from "@/ui/forms/users/UserInvitation";

import UserInvitationForm from "@/ui/forms/users/UserInvitation";

const meta = {
  title: "Forms/Users/<USER>",
  component: UserInvitationForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    defaultValues: {
      organizationId: "org-invite-789",
      email: "", // Required, will be filled by user
      role: undefined, // Required, will be selected by user
    } as Partial<UserInvitationFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof UserInvitationForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testData = {
      email: "<EMAIL>",
      role: "CLIENT",
      roleLabel: "Client",
    };

    // 1. Fill in the email field
    await userEvent.type(
      canvas.getByPlaceholderText("Enter the email for the member"),
      testData.email,
    );

    // 2. Select a role
    await userEvent.click(
      canvas.getByRole("button", { name: /select the role for the member/i }),
    );
    await userEvent.click(
      await canvas.findByRole("option", { name: testData.roleLabel }),
    );

    // 3. Click the submit button
    await userEvent.click(
      await canvas.findByRole("button", { name: /submit/i }),
    );

    // 4. Assert onSubmit was called correctly
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        organizationId: "org-invite-789",
        email: testData.email,
        role: testData.role,
      }),
      expect.anything(),
    );
  },
};
