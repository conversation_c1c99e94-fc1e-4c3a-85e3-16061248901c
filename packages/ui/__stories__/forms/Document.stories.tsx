import type { <PERSON>a, StoryObj } from "@storybook/react";

import { expect, fn, userEvent, within } from "storybook/test";

import type { DocumentFormValues } from "../../src/forms/Document";

import DocumentForm from "../../src/forms/Document";

const meta = {
  title: "Forms/DocumentForm",
  component: DocumentForm,
  tags: ["autodocs"],
  args: {
    onSubmit: fn(),
    // Provide default values that satisfy the schema for a basic story
    defaultValues: {
      organizationId: "org123",
      name: "", // Will be filled by user
      description: "", // Will be filled by user
      size: 0, // Required by schema, default for story
      type: "application/pdf", // Required by schema, default for story
    } as Partial<DocumentFormValues>,
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof DocumentForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    const testDocumentName = "My Test Document";
    const testDocumentDescription =
      "This is a detailed description for the test document.";

    // 1. Find the Name input and type into it
    const nameInput = canvas.getByPlaceholderText("Enter the document name");
    await userEvent.type(nameInput, testDocumentName);

    // 2. Find the Description textarea and type into it
    const descriptionTextarea = canvas.getByPlaceholderText(
      "Enter the document description",
    );
    await userEvent.type(descriptionTextarea, testDocumentDescription);

    // 3. Get the submit button and click it
    const submitButton = await canvas.findByRole("button", { name: /submit/i });
    await userEvent.click(submitButton);

    // 4. Assert onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalledTimes(1);
    await expect(args.onSubmit).toHaveBeenCalledWith(
      expect.objectContaining({
        organizationId: "org123",
        name: testDocumentName,
        description: testDocumentDescription,
        size: 0,
        type: "application/pdf",
      }),
      expect.anything(), // Second argument to handleSubmit is the event
    );
  },
};
