# Reusable Tables Components Guide

The AXA UI library provides a comprehensive set of table components designed for displaying and interacting with data efficiently. Built on top of [TanStack Table](https://tanstack.com/table/latest), these components offer powerful features like sorting, filtering, pagination, row selection, and bulk actions while maintaining consistent styling and accessibility.

## Key Features

- **Flexible Architecture**: Choose between `Table` (full-featured) and `DataTable` (simplified) components
- **Built-in State Management**: Automatic handling of sorting, filtering, pagination, and selection
- **Bulk Actions**: Export, delete, and status update operations for selected rows
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Accessibility**: Full keyboard navigation and screen reader support
- **Customizable**: Extensive styling options and component composition

## Installation & Setup

```bash
npm install @axa/ui
```

The tables components are exported from the main package:

```tsx
import {
  Table,
  DataTable,
  useDataTable,
  DataTableSettings,
  DataTablePagination,
  DataTableColumnHeader,
  DataTableFacetedFilter,
  createExportCSVAction,
  createBulkDeleteAction,
} from "@axa/ui/tables";
```

## Quick Start

### Basic Table (Recommended)

The `Table` component is the primary solution for most use cases:

```tsx
import { Table, DataTableColumnHeader } from "@axa/ui/tables";
import { ColumnDef } from "@tanstack/react-table";

interface User {
  id: string;
  name: string;
  email: string;
  status: string;
}

const columns: ColumnDef<User>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
  },
];

export function UsersTable({ users, total, isLoading }: Props) {
  const config = {
    groupName: "users",
    enableSelection: true,
    manualPagination: true,
    i18n: {
      noData: "No users found",
      selection: "selected",
      actions: {
        tableSettings: "Table Settings",
        tableActions: "Actions",
        search: "Search users...",
      },
    },
  };

  const data = {
    items: users,
    total,
  };

  return (
    <Table
      data={data}
      columns={columns}
      config={config}
      loading={isLoading}
      defaultPageSize={20}
    />
  );
}
```

### Production Example: Complete Users Table

Here's a real-world production example demonstrating the full Table implementation pattern used in AXA applications:

#### 1. Component Definition (`ListUsers.tsx`)

First, define your table component with proper types, configuration, and features:

```tsx
"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PropsWithChildren } from "react";
import { useMemo } from "react";
import Image from "next/image";

import type { ActionContext, TableConfig } from "@axa/ui/tables/table";
import { PersonRole } from "@axa/database-tech";
import ContactEmail from "@axa/ui/common/ContactEmail";
import ContactName from "@axa/ui/common/ContactName";
import ContactPhone from "@axa/ui/common/ContactPhone";
import PreviewOrganization from "@axa/ui/common/PreviewOrganization";
import { Avatar, AvatarFallback, AvatarImage } from "@axa/ui/primitives/avatar";
import { Badge } from "@axa/ui/primitives/badge";
import { SearchText } from "@axa/ui/search";
import { createTypedExportCSVAction } from "@axa/ui/tables/actions";
import { DataTableColumnHeader } from "@axa/ui/tables/helpers";
import Table from "@axa/ui/tables/table";
import type { RouterOutputs } from "@/api";
import { SearchOrganizations } from "@/widgets/actions/search";

// Type definitions from API router outputs
export type UserQueryResult = RouterOutputs["user"]["getMany"];
export type UserType = UserQueryResult["users"];
export type PersonType = UserType[number];

// Table configuration constants
export const groupName = "user";

const i18n = {
  en: {
    noData: "There are no users yet",
    selection: "Selection",
    actions: {
      tableSettings: "Table Settings",
      tableActions: "Table Actions",
      search: "Search users...",
    },
    headers: {
      role: "Role",
      name: "Name",
      email: "Email",
      phone: "Phone Number",
      organization: "Organization",
    },
    filters: {
      role: "Role",
      options: {
        ALL: "All",
        ADMIN: "Admin",
        BILLING: "Billing",
        INTERNAL: "Internal",
        PROVIDER: "Provider",
        CLIENT: "Client",
        USER: "User",
        NONE: "None",
      },
    },
  },
  links: {
    users: "/app/admin/users/[id]",
  },
};

// Filters configuration
const filters = [
  {
    id: "role",
    label: i18n.en.filters.role,
    options: [
      { value: null, label: i18n.en.filters.options.ALL },
      { value: PersonRole.ADMIN, label: i18n.en.filters.options.ADMIN },
      { value: PersonRole.BILLING, label: i18n.en.filters.options.BILLING },
      { value: PersonRole.INTERNAL, label: i18n.en.filters.options.INTERNAL },
      { value: PersonRole.PROVIDER, label: i18n.en.filters.options.PROVIDER },
      { value: PersonRole.CLIENT, label: i18n.en.filters.options.CLIENT },
      { value: PersonRole.NONE, label: i18n.en.filters.options.NONE },
    ] satisfies { value: PersonRole | null; label: string }[],
  },
];

const tableConfig: TableConfig = {
  groupName,
  enableSelection: true,
  i18n: i18n.en,
};

interface ListUserProps extends PropsWithChildren {
  loading?: boolean;
  users?: UserQueryResult;
  defaultPageSize?: number;
  defaultPageIndex?: number;
}

export default function ListUser({
  loading = false,
  users,
  defaultPageSize = 10,
  defaultPageIndex = 0,
  children,
}: ListUserProps) {
  // Transform API data to PaginatedResponse format
  const data = useMemo(() => {
    if (!users) return undefined;
    return {
      items: users.users,
      total: users.total,
    };
  }, [users]);

  return (
    <Table
      loading={loading}
      data={data}
      defaultPageSize={defaultPageSize}
      defaultPageIndex={defaultPageIndex}
      config={tableConfig}
      filters={filters}
      header={
        <>
          <SearchText
            group={groupName}
            loading={loading}
            placeholder={i18n.en.actions.search}
          />
          <SearchOrganizations loading={loading} />
        </>
      }
      actions={useMemo(
        () => [
          // Typed CSV export with custom column mapping
          createTypedExportCSVAction<PersonType>(
            ["id", "firstName", "lastName", "email", "phone", "role"],
            {
              filename: "users_export.csv",
              label: "Export Users",
              resolvers: {
                role: (value) => value?.toUpperCase() ?? "NONE",
              },
            },
          ),
        ],
        [],
      )}
      columns={useMemo(
        () =>
          [
            {
              id: "avatar",
              accessorKey: "avatar",
              header: () => null,
              cell: ({ row }: any) => (
                <Avatar className="size-10">
                  <AvatarImage
                    asChild
                    src={row.original.avatar ?? undefined}
                    alt={`${row.original.firstName} ${row.original.lastName}`}
                  >
                    <Image
                      src={row.original.avatar ?? ""}
                      alt={`${row.original.firstName} ${row.original.lastName}`}
                      width={40}
                      height={40}
                      layout="fixed"
                    />
                  </AvatarImage>
                  <AvatarFallback>
                    {row.original.firstName.charAt(0)}
                    {row.original.lastName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              ),
            },
            {
              id: "name",
              header: ({ column }: any) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.name}
                />
              ),
              cell: ({ row }: any) => (
                <div className="flex w-full items-center gap-2">
                  <ContactName
                    className="font-semibold"
                    link={i18n.links.users.replace("[id]", row.original.id)}
                    name={`${row.original.firstName} ${row.original.lastName}`}
                  />
                </div>
              ),
              enableHiding: false,
            },
            {
              id: "email",
              accessorKey: "email",
              header: ({ column }: any) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.email}
                />
              ),
              cell: ({ row }: any) => (
                <ContactEmail email={row.getValue("email")} />
              ),
            },
            {
              id: "phone",
              accessorKey: "phone",
              header: ({ column }: any) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.phone}
                />
              ),
              cell: ({ row }: any) => (
                <ContactPhone phone={row.getValue("phone")} />
              ),
            },
            {
              id: "role",
              accessorKey: "role",
              header: ({ column }: any) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.role}
                />
              ),
              cell: ({ row }: any) => (
                <Badge variant="outline" className="w-fit text-nowrap text-xs">
                  {row.original.role}
                </Badge>
              ),
              filterFn: (row: any, id: string, value: string) => {
                return value.includes(row.original.role);
              },
            },
            {
              id: "organization",
              accessorKey: "organization.id",
              header: ({ column }: any) => (
                <DataTableColumnHeader
                  column={column}
                  title={i18n.en.headers.organization}
                />
              ),
              cell: ({ row }: any) =>
                row.original.organization ? (
                  <PreviewOrganization
                    organization={row.original.organization}
                  />
                ) : null,
              filterFn: (row: any, id: string, value: string) => {
                return value.includes(row.original.organization?.id ?? "");
              },
            },
          ] as ColumnDef<PersonType, PersonType[]>[],
        [],
      )}
    >
      {children}
    </Table>
  );
}
```

#### 2. Page Integration (`Users.tsx`)

Next, integrate the table into your page component:

```tsx
"use client";

import { useMemo, useState } from "react";
import { FolderSyncIcon } from "lucide-react";

import AppView from "@axa/ui/layouts/AppView";
import { Button } from "@axa/ui/primitives/button";
import { useSearchPagination } from "@axa/ui/search";

import type { RouterError, RouterOutputs } from "@/api";
import { ErrorFallback } from "@/components/common/Error";
import ListUsers from "@/components/ListUsers";

export type UsersQueryResult = NonNullable<RouterOutputs["user"]["getMany"]>;

export interface UsersViewProps {
  loading?: boolean;
  users: {
    data?: UsersQueryResult;
    error?: RouterError;
    isLoading: boolean;
  };
  syncPeopleMutation: ReturnType<
    typeof api.admin.clerk.syncClerkUsers.useMutation
  >;
  syncOrganizationsMutation: ReturnType<
    typeof api.admin.clerk.syncClerkOrganizations.useMutation
  >;
}

const i18n = {
  en: {
    title: "Users",
    description: "Users are the individuals that you work with.",
  },
};

export default function Users({
  users,
  syncPeopleMutation,
  syncOrganizationsMutation,
}: UsersViewProps) {
  return (
    <AppView title={i18n.en.title}>
      <div className="flex h-full flex-col gap-12">
        <div>
          <p className="text-muted-foreground">{i18n.en.description}</p>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <Button
            className="space-x-2"
            onClick={() => {
              syncPeopleMutation.mutate();
            }}
          >
            <FolderSyncIcon className="size-4" />
            <span>Sync Users</span>
          </Button>

          <Button
            className="space-x-2"
            onClick={async () => {
              await syncOrganizationsMutation.mutateAsync();
            }}
          >
            <FolderSyncIcon className="size-4" />
            <span>Sync Organizations</span>
          </Button>
        </div>

        <div className="space-y-6">
          {users.error && <ErrorFallback error={users.error} />}

          <ListUsers users={users.data} loading={users.isLoading} />
        </div>
      </div>
    </AppView>
  );
}
```

#### 3. Data Layer Connection (`index.tsx`)

Finally, connect your table to the data layer with search parameters and API integration:

```tsx
"use client";

import { Suspense, use } from "react";

import type { PersonRole } from "@axa/database-tech";
import { toast } from "@axa/ui/primitives/toast";
import {
  SearchParams,
  useSearchFilterValue,
  useSearchPaginationValue,
  useSearchTextValue,
  useSearchValueResult,
} from "@axa/ui/search";

import type { RouterOutputs } from "@/api";
import { api } from "@/api/client";
import Users from "./Users";

export type UsersQueryResult = NonNullable<RouterOutputs["user"]["getMany"]>;

export interface UsersViewProps {
  loading?: boolean;
  users?: Promise<UsersQueryResult>;
}

const userGroup = "user";

export function UsersView(props: UsersViewProps) {
  // Extract search parameters from URL
  const userPagination = useSearchPaginationValue(userGroup);
  const userOrganizations = useSearchValueResult<string>("", "organizations");
  const userRole = useSearchFilterValue<PersonRole>("role", userGroup);
  const userQuery = useSearchTextValue(userGroup);

  // Main users query with search parameters
  const users = api.user.getMany.useQuery(
    {
      query: userQuery,
      organizations: userOrganizations ? [userOrganizations] : undefined,
      pageNumber: userPagination.pageIndex,
      pageSize: userPagination.pageSize,
      roles: userRole ? [userRole] : undefined,
    },
    {
      enabled: !props.loading,
      initialData: props.users ? use(props.users) : undefined,
    },
  );

  // Mutations with proper error handling and toast notifications
  const syncPeopleMutation = api.admin.clerk.syncClerkUsers.useMutation({
    onSuccess: async () => {
      await users.refetch();
      toast.success("Users synced");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const syncOrganizationsMutation =
    api.admin.clerk.syncClerkOrganizations.useMutation({
      onSuccess: async () => {
        await users.refetch();
        toast.success("Organizations synced");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const loading = props.loading || users.isLoading;

  return (
    <Users
      loading={loading}
      users={users}
      syncPeopleMutation={syncPeopleMutation}
      syncOrganizationsMutation={syncOrganizationsMutation}
    />
  );
}

export default function UsersPage({ users }: UsersViewProps) {
  return (
    <Suspense fallback={<UsersView loading />}>
      <SearchParams>
        <UsersView users={users} />
      </SearchParams>
    </Suspense>
  );
}
```

#### Key Features Demonstrated

This production example showcases:

1. **Type Safety**: Complete TypeScript integration with API router outputs
2. **Search Integration**: URL-synced search, filters, and pagination
3. **Custom Components**: Rich cell content with AXA UI components
4. **Actions System**: Typed CSV export with custom resolvers
5. **Error Handling**: Proper error boundaries and toast notifications
6. **Loading States**: Comprehensive loading state management
7. **Data Transformation**: Converting API responses to Table component format
8. **Custom Header**: Multiple search components in header area
9. **Filter Configuration**: Real-world filter setup with enum values
10. **Mutation Handling**: Optimistic updates and cache invalidation

This pattern provides a complete, production-ready table implementation that can be adapted for any data type in your application.

### DataTable (For Custom Layouts)

Use `DataTable` when you need custom layouts or don't need built-in search/filters: