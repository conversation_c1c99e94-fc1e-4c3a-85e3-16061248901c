import type { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";

import { DownloadIcon, EditIcon, TrashIcon, UserIcon } from "lucide-react";

import type { TableAction, TableConfig } from "../../src/tables/table";
import type { Person } from "./TableUtils";

import Table, { ActionType } from "../../src/tables/table";
import { defaultPersonColumns, generatePeople } from "./TableUtils";

// Mock action handlers for stories
const mockHandlers = {
  bulkDelete: (people: Person[]) => {
    console.log(
      "Bulk delete:",
      people.map((p) => p.id),
    );
    alert(
      `Would delete ${people.length} people: ${people.map((p) => p.firstName + " " + p.lastName).join(", ")}`,
    );
  },
  bulkExport: (people: Person[]) => {
    console.log(
      "Bulk export:",
      people.map((p) => p.id),
    );
    alert(`Would export ${people.length} people`);
  },
  editPerson: (person: Person) => {
    console.log("Edit person:", person.id);
    alert(`Would edit: ${person.firstName} ${person.lastName}`);
  },
  viewProfile: (person: Person) => {
    console.log("View profile:", person.id);
    alert(`Would view profile: ${person.firstName} ${person.lastName}`);
  },
  deletePerson: (person: Person) => {
    console.log("Delete person:", person.id);
    alert(`Would delete: ${person.firstName} ${person.lastName}`);
  },
};

// Table configuration
const tableConfig: TableConfig = {
  groupName: "people",
  enableSelection: true,
  manualPagination: false,
  i18n: {
    noData: "No people found",
    selection: "Selected people",
    actions: {
      tableSettings: "Table settings",
      tableActions: "Bulk actions",
      search: "Search people...",
    },
  },
};

// Sample filter groups
const filterGroups = [
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All Statuses" },
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
      { value: "pending", label: "Pending" },
    ],
  },
  {
    id: "role",
    label: "Role",
    options: [
      { value: null, label: "All Roles" },
      { value: "Admin", label: "Admin" },
      { value: "User", label: "User" },
      { value: "Editor", label: "Editor" },
      { value: "Manager", label: "Manager" },
    ],
  },
];

interface TableActionsWrapperProps {
  data: Person[];
  actions: TableAction<Person>[];
  showBulkActions?: boolean;
  showRowActions?: boolean;
  enableSelection?: boolean;
}

function TableActionsWrapper({
  data,
  actions,
  enableSelection = true,
}: TableActionsWrapperProps) {
  return (
    <div className="p-4">
      <Table<Person>
        data={{ items: data, total: data.length }}
        columns={defaultPersonColumns}
        config={{
          ...tableConfig,
          enableSelection,
        }}
        filters={filterGroups}
        actions={actions}
      />
    </div>
  );
}

const meta = {
  title: "Tables/TableActions",
  component: TableActionsWrapper,
  parameters: {
    layout: "fullscreen",
    nextjs: { appDirectory: true },
  },
  tags: ["autodocs"],
  args: {
    data: generatePeople(10),
    enableSelection: true,
  },
} satisfies Meta<typeof TableActionsWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const SelectionActionsOnly: Story = {
  name: "Selection Actions Only",
  args: {
    actions: [
      {
        type: ActionType.SELECTION,
        label: "Delete Selected",
        icon: TrashIcon,
        variant: "destructive",
        disabled: (context) =>
          context.type === "selection" && context.selectedRows.length === 0,
        onClick: (context) => {
          if (context.type === "selection") {
            mockHandlers.bulkDelete(context.selectedRows);
          }
        },
      },
      {
        type: ActionType.SELECTION,
        label: "Export Selected",
        icon: DownloadIcon,
        disabled: (context) =>
          context.type === "selection" && context.selectedRows.length === 0,
        onClick: (context) => {
          if (context.type === "selection") {
            mockHandlers.bulkExport(context.selectedRows);
          }
        },
      },
    ],
  },
};

export const RowActionsOnly: Story = {
  name: "Row Actions Only",
  args: {
    enableSelection: false,
    actions: [
      {
        type: ActionType.ROW,
        label: "Edit",
        icon: EditIcon,
        onClick: (context) => {
          if (context.type === "row") {
            mockHandlers.editPerson(context.row);
          }
        },
      },
      {
        type: ActionType.ROW,
        label: "View Profile",
        icon: UserIcon,
        onClick: (context) => {
          if (context.type === "row") {
            mockHandlers.viewProfile(context.row);
          }
        },
      },
      {
        type: ActionType.ROW,
        label: "Delete",
        icon: TrashIcon,
        variant: "destructive",
        disabled: (context) =>
          context.type === "row" && context.row.status === "active",
        onClick: (context) => {
          if (context.type === "row") {
            mockHandlers.deletePerson(context.row);
          }
        },
      },
    ],
  },
};

export const BothActionTypes: Story = {
  name: "Selection + Row Actions",
  args: {
    actions: [
      // Selection Actions
      {
        type: ActionType.SELECTION,
        label: "Delete Selected",
        icon: TrashIcon,
        variant: "destructive",
        disabled: (context) =>
          context.type === "selection" && context.selectedRows.length === 0,
        onClick: (context) => {
          if (context.type === "selection") {
            mockHandlers.bulkDelete(context.selectedRows);
          }
        },
      },
      {
        type: ActionType.SELECTION,
        label: "Export Selected",
        icon: DownloadIcon,
        disabled: (context) =>
          context.type === "selection" && context.selectedRows.length === 0,
        onClick: (context) => {
          if (context.type === "selection") {
            mockHandlers.bulkExport(context.selectedRows);
          }
        },
      },
      // Row Actions
      {
        type: ActionType.ROW,
        label: "Edit",
        icon: EditIcon,
        onClick: (context) => {
          if (context.type === "row") {
            mockHandlers.editPerson(context.row);
          }
        },
      },
      {
        type: ActionType.ROW,
        label: "View Profile",
        icon: UserIcon,
        onClick: (context) => {
          if (context.type === "row") {
            mockHandlers.viewProfile(context.row);
          }
        },
      },
    ],
  },
};

export const WithCustomRowRender: Story = {
  name: "Custom Row Render",
  args: {
    enableSelection: false,
    actions: [
      {
        type: ActionType.ROW,
        label: "Custom Menu",
        render: (context) => {
          if (context.type === "row") {
            return (
              <div className="flex gap-1 p-1">
                <button
                  className="rounded px-2 py-1 text-xs hover:bg-gray-100"
                  onClick={() => mockHandlers.editPerson(context.row)}
                >
                  Edit
                </button>
                <button
                  className="rounded px-2 py-1 text-xs text-red-600 hover:bg-red-100"
                  onClick={() => mockHandlers.deletePerson(context.row)}
                >
                  Delete
                </button>
              </div>
            );
          }
          return null;
        },
      },
    ],
  },
};

export const NoActions: Story = {
  name: "No Actions",
  args: {
    actions: [],
  },
};
