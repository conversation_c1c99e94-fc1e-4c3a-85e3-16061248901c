import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import type { ColumnDef, Table } from "@tanstack/react-table"; // Import Table for explicit typing

// Make sure to import the actual DataTableProps from your project
// Assuming the second generic for DataTableProps is 'unknown' if not specified for value type
import type { DataTableProps } from "../../src/tables";
import type { Person } from "./TableUtils";

import { Card, CardContent } from "../../src/primitives/card";
import EmptyList from "../../src/shared/EmptyList";
import { DataTable } from "../../src/tables"; // The actual DataTable component
import {
  defaultPersonColumns,
  generatePeople,
  TableContext, // The decorator for layout
  TableProvider, // The context provider
} from "./TableUtils";

// This wrapper component becomes the 'component' for Storybook
interface DataTableStoryWrapperProps
  extends Omit<DataTableProps<Person, unknown>, "table"> {
  // Corrected Generic
  // Props to configure the TableProvider
  dataForProvider: Person[];
  columnsForProvider: ColumnDef<Person>[]; // Explicit prop for provider's columns
  children?: React.ReactNode; // Explicitly add children prop for the wrapper
}

const DataTableStoryWrapper = ({
  dataForProvider,
  columnsForProvider, // Use this for TableProvider
  children, // To support the Empty story's children pattern
  ...restDataTableProps // Other props for DataTable (e.g., loading, classNames)
}: DataTableStoryWrapperProps) => {
  return (
    <Card>
      <CardContent className="p-0">
        <TableProvider<Person>
          columns={columnsForProvider} // Pass provider-specific columns
          data={dataForProvider}
        >
          {/* Explicitly type the 'table' prop from the render prop context */}
          {({ table }: { table: Table<Person> }) => (
            <DataTable
              {...restDataTableProps} // Spread other DataTable props
              table={table} // Pass the created table instance
              // DO NOT pass 'columns' here if DataTable gets its column config from the 'table' instance
            >
              {children}{" "}
              {/* Pass through children for EmptyList case in Empty story */}
            </DataTable>
          )}
        </TableProvider>
      </CardContent>
    </Card>
  );
};

const meta = {
  title: "Tables/DataTable",
  component: DataTableStoryWrapper, // Storybook now targets this wrapper
  parameters: {
    layout: "padded",
    nextjs: { appDirectory: true },
  },
  tags: ["autodocs"],
  decorators: [TableContext.Decorator], // Existing layout decorator
  args: {
    // Default args for DataTableStoryWrapperProps, applied to all stories unless overridden
    columnsForProvider: defaultPersonColumns, // Use the new prop name
  },
} satisfies Meta<typeof DataTableStoryWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

// Stories are now much simpler and more type-safe

export const Default: Story = {
  args: {
    // Props for DataTableStoryWrapper
    dataForProvider: generatePeople(5),
    // 'columnsForProvider' will use the default from meta.args unless specified here
  },
};

export const Loading: Story = {
  args: {
    dataForProvider: generatePeople(0), // Provide empty data or a few items for skeleton
    loading: true, // This is a prop of DataTable, passed via ...restDataTableProps
    // 'columnsForProvider' will use the default from meta.args
  },
};

export const Empty: Story = {
  args: {
    dataForProvider: [],
    // 'columnsForProvider' will use the default from meta.args
    // Assuming DataTableProps accepts 'children' for the empty state content
    children: (
      <EmptyList title="No data available">
        <p>There are no items to display.</p>
      </EmptyList>
    ),
    // If DataTable uses a specific 'emptyState' prop instead of children:
    // emptyState: (
    //   <EmptyList title="No data available">
    //     <p>There are no items to display.</p>
    //   </EmptyList>
    // ),
  },
};

export const CustomStyling: Story = {
  args: {
    dataForProvider: generatePeople(3),
    tableClassName: "border-red-200",
    headerClassName: "bg-slate-100 text-slate-800",
    rowClassName: "hover:bg-blue-50",
    cellClassName: "p-4",
    // 'columnsForProvider' will use the default from meta.args
  },
};
