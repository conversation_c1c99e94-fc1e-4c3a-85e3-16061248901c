import type { Meta } from "@storybook/react";
import type { ColumnDef } from "@tanstack/react-table";

import { Card, CardContent } from "../../src/primitives/card";
import { DataTable } from "../../src/tables";
import { selectColumn } from "../../src/tables/columns";
import {
  defaultPersonColumns,
  generatePeople,
  TableContext,
  TableProvider,
} from "./TableUtils";

const meta = {
  title: "Tables/SelectColumn",
  parameters: {
    layout: "padded",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [TableContext.Decorator],
} satisfies Meta<typeof TableContext.Decorator>;

export default meta;

/**
 * Table with selection column
 */
export const Default = {
  render: () => {
    const data = generatePeople(5);
    // Add selection column to the start of columns
    const columnsWithSelection = [selectColumn, ...defaultPersonColumns];

    return (
      <Card>
        <CardContent className="p-0">
          <TableProvider
            columns={columnsWithSelection as ColumnDef<unknown>[]}
            data={data}
          >
            {({ table }) => {
              return <DataTable table={table} />;
            }}
          </TableProvider>
        </CardContent>
      </Card>
    );
  },
};

/**
 * Table with pre-selected rows
 */
export const WithPreselectedRows = {
  render: () => {
    const data = generatePeople(5);
    // Add selection column to the start of columns
    const columnsWithSelection = [selectColumn, ...defaultPersonColumns];

    return (
      <Card>
        <CardContent className="p-0">
          <TableProvider
            columns={columnsWithSelection as ColumnDef<unknown>[]}
            data={data}
          >
            {({ table }) => {
              // Pre-select the first and third rows
              if (table.getRowModel().rows.length > 0) {
                table.getRowModel().rows[0]?.toggleSelected(true);
              }
              if (table.getRowModel().rows.length > 2) {
                table.getRowModel().rows[2]?.toggleSelected(true);
              }

              return <DataTable table={table} />;
            }}
          </TableProvider>
        </CardContent>
      </Card>
    );
  },
};

/**
 * Table with all rows selected
 */
export const AllRowsSelected = {
  render: () => {
    const data = generatePeople(5);
    // Add selection column to the start of columns
    const columnsWithSelection = [selectColumn, ...defaultPersonColumns];

    return (
      <Card>
        <CardContent className="p-0">
          <TableProvider
            columns={columnsWithSelection as ColumnDef<unknown>[]}
            data={data}
          >
            {({ table }) => {
              // Select all rows
              table.toggleAllPageRowsSelected(true);

              return <DataTable table={table} />;
            }}
          </TableProvider>
        </CardContent>
      </Card>
    );
  },
};
