import type { ColumnDef, Table } from "@tanstack/react-table";
import type { ComponentType } from "react";
import type { Story<PERSON>ontext, StoryFn } from "storybook/internal/types";

import { createContext, useContext, useMemo, useState } from "react";

import { useDataTable } from "../../src/tables";

/**
 * Sample data type for table stories
 */
export interface Person {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  status: "active" | "inactive" | "pending";
  joinDate: string;
}

/**
 * Generate sample data for table stories
 */
export function generatePeople(count = 10): Person[] {
  const firstNames = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];
  const lastNames = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];
  const roles = ["<PERSON><PERSON>", "User", "Editor", "<PERSON>er", "Manager"];
  const statuses: Person["status"][] = ["active", "inactive", "pending"];

  return Array.from({ length: count }).map((_, i) => {
    const firstName =
      firstNames[Math.floor(Math.random() * firstNames.length)] ?? "<PERSON>";
    const lastName =
      lastNames[Math.floor(Math.random() * lastNames.length)] ?? "Doe";
    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`;
    const role = roles[Math.floor(Math.random() * roles.length)] ?? "User";
    const status =
      statuses[Math.floor(Math.random() * statuses.length)] ?? "active";
    const date = new Date();
    date.setMonth(date.getMonth() - Math.floor(Math.random() * 24));

    return {
      id: `person-${i}`,
      firstName,
      lastName,
      email,
      role,
      status,
      joinDate:
        date.toISOString().split("T")[0] ??
        new Date().toISOString().split("T")[0] ??
        "2000-01-01",
    };
  });
}

/**
 * Default columns for person data
 */
export const defaultPersonColumns: ColumnDef<Person>[] = [
  {
    accessorKey: "firstName",
    header: "First Name",
  },
  {
    accessorKey: "lastName",
    header: "Last Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "role",
    header: "Role",
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "joinDate",
    header: "Join Date",
  },
];

/**
 * Generate paginated response for list table
 */
export function generatePaginatedPeople(
  pageSize = 10,
  _pageIndex = 0,
  total = 100,
) {
  const items = generatePeople(pageSize);
  return {
    items,
    total,
  };
}

/**
 * Table context for stories to simplify table state management
 */
interface TableContextValue<TData = unknown> {
  table: Table<TData>;
}

const TableReactContext = createContext<TableContextValue | undefined>(
  undefined,
);

export function useStoryTableContext<TData>(): Table<TData> {
  const context = useContext(TableReactContext);
  if (context === undefined) {
    // Check for undefined, as context is initialized with undefined
    throw new Error("useStoryTableContext must be used within a TableProvider");
  }
  return context.table as Table<TData>; // context.table is Table<unknown>, cast to Table<TData>
}

export function TableProvider<TData>({
  children,
  columns,
  data,
  rowCount,
  pagination,
  manualPagination = false,
  // initialState removed as useDataTable does not directly accept it for now
}: {
  columns: ColumnDef<TData>[];
  data: TData[];
  rowCount?: number;
  pagination?: { pageIndex: number; pageSize: number };
  manualPagination?: boolean;
  // initialState?: Record<string, any>; // Removed for now
  children: (props: {
    table: Table<TData>;
  }) => React.ReactNode | React.ReactNode[] | undefined;
}) {
  const [paginationState, setPaginationState] = useState(
    pagination ?? { pageIndex: 0, pageSize: 10 },
  );

  // useDataTable returns an object like { table: TableInstance }
  const tableControl: { table: Table<TData> } = useDataTable<TData, any>({
    columns,
    data,
    rowCount,
    pagination: paginationState, // Pass the controlled pagination state
    setPagination: setPaginationState, // Pass the setter for pagination state
    manualPagination,
  });

  // Memoize the context value to prevent unnecessary re-renders of consumers
  // contextValue must be TableContextValue<unknown> to match TableReactContext
  const contextValue: TableContextValue<unknown> = useMemo(
    () => ({ table: tableControl.table as Table<unknown> }),
    [tableControl.table],
  );

  return (
    <TableReactContext.Provider value={contextValue}>
      {typeof children === "function"
        ? children({ table: tableControl.table })
        : children}
    </TableReactContext.Provider>
  );
}

// For compatibility with potential old usages if any, though direct use of TableProvider and useStoryTableContext is preferred.
export const TableContext = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Provider: TableProvider as unknown as ComponentType<any>,
  Consumer: ({
    children,
  }: {
    children: (value: TableContextValue<unknown>) => React.ReactNode;
  }) => {
    const tableInstance = useStoryTableContext<unknown>();
    // Children expects TableContextValue<unknown> which is { table: Table<unknown> }
    return children({ table: tableInstance });
  },
  Decorator: (Story: StoryFn<any>, context: StoryContext<any>) => (
    <div className="w-full max-w-4xl">
      {/* @ts-expect-error StoryFn<any> is not a valid JSX element type */}
      <Story args={context.args} />
    </div>
  ),
};
