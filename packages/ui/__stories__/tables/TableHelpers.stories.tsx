import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Card, CardContent } from "../../src/primitives/card";
import {
  DataTableBasicPagination,
  DataTablePagination,
  DataTableSettings,
  DataTableSimplePagination,
} from "../../src/tables/helpers";
import {
  defaultPersonColumns,
  generatePeople,
  TableContext,
  TableProvider,
} from "./TableUtils";

const meta = {
  title: "Tables/TableHelpers",
  parameters: {
    layout: "padded",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [TableContext.Decorator],
} satisfies Meta<typeof TableContext.Decorator>;

export default meta;

// Settings component stories
export const Settings = {
  render: () => {
    const data = generatePeople(20);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">Table Settings</h3>
        <TableProvider
          columns={defaultPersonColumns}
          data={data}
          pagination={{ pageIndex: 0, pageSize: 10 }}
        >
          {({ table }) => <DataTableSettings table={table} />}
        </TableProvider>
      </Card>
    );
  },
};

// Settings with custom sizes
export const SettingsWithCustomSizes = {
  render: () => {
    const data = generatePeople(20);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">
          Table Settings with Custom Sizes
        </h3>
        <TableProvider
          columns={defaultPersonColumns}
          data={data}
          pagination={{ pageIndex: 0, pageSize: 5 }}
        >
          {({ table }) => (
            <DataTableSettings
              table={table}
              sizes={[5, 10, 15, 20]}
              variant="primary"
              size="md"
            />
          )}
        </TableProvider>
      </Card>
    );
  },
};

// Basic pagination component story
export const BasicPagination = {
  render: () => {
    const data = generatePeople(30);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">Basic Pagination</h3>
        <TableProvider
          columns={defaultPersonColumns}
          data={data}
          pagination={{ pageIndex: 1, pageSize: 10 }}
        >
          {({ table }) => <DataTableBasicPagination table={table} />}
        </TableProvider>
      </Card>
    );
  },
};

// Simple pagination component story
export const SimplePagination = {
  render: () => {
    const data = generatePeople(30);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">Simple Pagination</h3>
        <TableProvider
          columns={defaultPersonColumns}
          data={data}
          pagination={{ pageIndex: 1, pageSize: 10 }}
        >
          {({ table }) => <DataTableSimplePagination table={table} />}
        </TableProvider>
      </Card>
    );
  },
};

// Full pagination component story
export const FullPagination = {
  render: () => {
    const data = generatePeople(100);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">Full Pagination</h3>
        <TableProvider
          columns={defaultPersonColumns}
          data={data}
          pagination={{ pageIndex: 2, pageSize: 10 }}
        >
          {({ table }) => <DataTablePagination table={table} />}
        </TableProvider>
      </Card>
    );
  },
};
