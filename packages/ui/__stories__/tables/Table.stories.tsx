import type { <PERSON><PERSON>, <PERSON><PERSON>b<PERSON> } from "@storybook/react";

import {
  DownloadIcon,
  EditIcon,
  PlusIcon,
  TrashIcon,
  UserIcon,
} from "lucide-react";

import type { TableAction, TableConfig } from "../../src/tables/table";
import type { Person } from "./TableUtils";

import { Button } from "../../src/primitives/button";
import Table, { ActionType } from "../../src/tables/table";
import { defaultPersonColumns, generatePeople } from "./TableUtils";

// Mock handlers for actions
const mockHandlers = {
  bulkDelete: (people: Person[]) => {
    alert(`Would delete ${people.length} people`);
  },
  bulkExport: (people: Person[]) => {
    alert(`Would export ${people.length} people`);
  },
  editPerson: (person: Person) => {
    alert(`Would edit: ${person.firstName} ${person.lastName}`);
  },
  viewProfile: (person: Person) => {
    alert(`Would view profile: ${person.firstName} ${person.lastName}`);
  },
  addPerson: () => {
    alert("Would open add person dialog");
  },
};

// Reusable table configuration
const tableConfig: TableConfig = {
  groupName: "people",
  enableSelection: true,
  manualPagination: false,
  i18n: {
    noData: "No people found",
    selection: "Selected people",
    actions: {
      tableSettings: "Table settings",
      tableActions: "Bulk actions",
      search: "Search people...",
    },
  },
};

// Sample filter groups
const filterGroups = [
  {
    id: "status",
    label: "Status",
    options: [
      { value: null, label: "All Statuses" },
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
      { value: "pending", label: "Pending" },
    ],
  },
  {
    id: "role",
    label: "Role",
    options: [
      { value: null, label: "All Roles" },
      { value: "Admin", label: "Admin" },
      { value: "User", label: "User" },
      { value: "Editor", label: "Editor" },
      { value: "Manager", label: "Manager" },
    ],
  },
];

// Common actions used across stories
const commonActions: TableAction<Person>[] = [
  {
    type: ActionType.SELECTION,
    label: "Delete Selected",
    icon: TrashIcon,
    variant: "destructive",
    disabled: (context) =>
      context.type === "selection" && context.selectedRows.length === 0,
    onClick: (context) => {
      if (context.type === "selection") {
        mockHandlers.bulkDelete(context.selectedRows);
      }
    },
  },
  {
    type: ActionType.SELECTION,
    label: "Export Selected",
    icon: DownloadIcon,
    disabled: (context) =>
      context.type === "selection" && context.selectedRows.length === 0,
    onClick: (context) => {
      if (context.type === "selection") {
        mockHandlers.bulkExport(context.selectedRows);
      }
    },
  },
  {
    type: ActionType.ROW,
    label: "Edit",
    icon: EditIcon,
    onClick: (context) => {
      if (context.type === "row") {
        mockHandlers.editPerson(context.row);
      }
    },
  },
  {
    type: ActionType.ROW,
    label: "View Profile",
    icon: UserIcon,
    onClick: (context) => {
      if (context.type === "row") {
        mockHandlers.viewProfile(context.row);
      }
    },
  },
];

interface TableWrapperProps {
  data: Person[];
  loading?: boolean;
  enableSelection?: boolean;
  showFilters?: boolean;
  showActions?: boolean;
  customHeader?: boolean;
  actions?: TableAction<Person>[];
}

function TableWrapper({
  data,
  loading = false,
  enableSelection = true,
  showFilters = false,
  showActions = false,
  customHeader = false,
  actions,
}: TableWrapperProps) {
  const config = {
    ...tableConfig,
    enableSelection,
  };

  return (
    <div className="p-4">
      <Table<Person>
        loading={loading}
        data={{ items: data, total: data.length }}
        columns={defaultPersonColumns}
        config={config}
        filters={showFilters ? filterGroups : undefined}
        actions={showActions ? actions || commonActions : []}
        header={
          customHeader ? (
            <div className="flex w-full items-center justify-between px-2">
              <input
                type="text"
                placeholder="Custom search..."
                className="rounded border border-gray-300 px-3 py-1 text-sm"
              />
              <Button size="sm" onClick={mockHandlers.addPerson}>
                <PlusIcon size="16" />
                Add Person
              </Button>
            </div>
          ) : undefined
        }
      />
    </div>
  );
}

const meta = {
  title: "Tables/Table",
  component: TableWrapper,
  parameters: {
    layout: "fullscreen",
    nextjs: { appDirectory: true },
  },
  tags: ["autodocs"],
  args: {
    data: generatePeople(10),
    loading: false,
    enableSelection: true,
    showFilters: false,
    showActions: false,
    customHeader: false,
  },
} satisfies Meta<typeof TableWrapper>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  name: "Basic Table",
  args: {
    data: generatePeople(5),
  },
};

export const Loading: Story = {
  name: "Loading State",
  args: {
    data: [],
    loading: true,
  },
};

export const Empty: Story = {
  name: "Empty State",
  args: {
    data: [],
  },
};

export const WithFilters: Story = {
  name: "With Filters",
  args: {
    data: generatePeople(15),
    showFilters: true,
  },
};

export const WithActions: Story = {
  name: "With Actions",
  args: {
    data: generatePeople(10),
    showActions: true,
  },
};

export const WithFiltersAndActions: Story = {
  name: "With Filters + Actions",
  args: {
    data: generatePeople(15),
    showFilters: true,
    showActions: true,
  },
};

export const WithCustomHeader: Story = {
  name: "Custom Header",
  args: {
    data: generatePeople(8),
    customHeader: true,
    showActions: true,
  },
};

export const NoSelection: Story = {
  name: "No Selection",
  args: {
    data: generatePeople(8),
    enableSelection: false,
    showActions: true,
    // Filter out selection actions since selection is disabled
    actions: commonActions.filter(
      (action) => action.type !== ActionType.SELECTION,
    ),
  },
};

export const LargeDataset: Story = {
  name: "Large Dataset",
  args: {
    data: generatePeople(50),
    showFilters: true,
    showActions: true,
  },
};
