import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { useState } from "react";
import { CalendarIcon, TagIcon, UserIcon } from "lucide-react";

import { Card, CardContent } from "../../src/primitives/card";
import {
  DataTableColumnHeader,
  DataTableFacetedFilter,
} from "../../src/tables/helpers";
import {
  defaultPersonColumns,
  generatePeople,
  TableContext,
  TableProvider,
} from "./TableUtils";

const meta = {
  title: "Tables/TableColumns",
  parameters: {
    layout: "padded",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [TableContext.Decorator],
} satisfies Meta<typeof TableContext.Decorator>;

export default meta;

// Column header stories
export const ColumnHeader = {
  render: () => {
    const data = generatePeople(10);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">Column Headers</h3>
        <div className="flex flex-col gap-4">
          <TableProvider columns={defaultPersonColumns} data={data}>
            {({ table }) => (
              <>
                <div className="flex items-center gap-4 rounded border p-4">
                  <DataTableColumnHeader
                    column={table.getColumn("firstName")!}
                    title="First Name"
                  />
                  <span className="text-sm text-muted-foreground">
                    Sortable, not sorted
                  </span>
                </div>

                <div className="flex items-center gap-4 rounded border p-4">
                  {/* Trigger sort by clicking column */}
                  <div
                    onClick={() =>
                      table.getColumn("lastName")?.toggleSorting(false)
                    }
                  >
                    <DataTableColumnHeader
                      column={table.getColumn("lastName")!}
                      title="Last Name"
                    />
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Sortable, sorted ascending
                  </span>
                </div>

                <div className="flex items-center gap-4 rounded border p-4">
                  {/* Trigger sort by clicking column */}
                  <div
                    onClick={() =>
                      table.getColumn("email")?.toggleSorting(true)
                    }
                  >
                    <DataTableColumnHeader
                      column={table.getColumn("email")!}
                      title="Email"
                    />
                  </div>
                  <span className="text-sm text-muted-foreground">
                    Sortable, sorted descending
                  </span>
                </div>
              </>
            )}
          </TableProvider>
        </div>
      </Card>
    );
  },
};

// Faceted filter component story
export const FacetedFilter = {
  render: () => {
    const data = generatePeople(30);

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">Faceted Filters</h3>

        <div className="flex flex-col gap-4">
          <TableProvider columns={defaultPersonColumns} data={data}>
            {({ table }) => (
              <>
                <div className="flex items-center gap-4">
                  <DataTableFacetedFilter
                    column={table.getColumn("status")}
                    title="Status"
                    options={[
                      { value: "active", label: "Active" },
                      { value: "inactive", label: "Inactive" },
                      { value: "pending", label: "Pending" },
                    ]}
                  />
                  <span className="text-sm text-muted-foreground">
                    Status filter
                  </span>
                </div>

                <div className="flex items-center gap-4">
                  <DataTableFacetedFilter
                    column={table.getColumn("role")}
                    title="Role"
                    options={[
                      { value: "Admin", label: "Admin", icon: UserIcon },
                      { value: "User", label: "User", icon: UserIcon },
                      { value: "Editor", label: "Editor", icon: TagIcon },
                      { value: "Manager", label: "Manager", icon: UserIcon },
                    ]}
                  />
                  <span className="text-sm text-muted-foreground">
                    Role filter with icons
                  </span>
                </div>
              </>
            )}
          </TableProvider>
        </div>
      </Card>
    );
  },
};

// Custom filter without column binding
export const CustomFilter = {
  render: () => {
    const [selectedValues, setSelectedValues] = useState<string[]>([]);

    const handleSelect = (values: string[]) => {
      setSelectedValues(values);
      console.log("Selected values:", values);
    };

    return (
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-medium">
          Custom Filter (Not Column-Bound)
        </h3>

        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-4">
            <DataTableFacetedFilter
              title="Date Range"
              options={[
                { value: "today", label: "Today", icon: CalendarIcon },
                { value: "yesterday", label: "Yesterday", icon: CalendarIcon },
                { value: "this-week", label: "This Week", icon: CalendarIcon },
                { value: "last-week", label: "Last Week", icon: CalendarIcon },
                {
                  value: "this-month",
                  label: "This Month",
                  icon: CalendarIcon,
                },
              ]}
              onSelect={handleSelect}
            />
            <span className="text-sm text-muted-foreground">
              Selected:{" "}
              {selectedValues.length ? selectedValues.join(", ") : "None"}
            </span>
          </div>
        </div>
      </Card>
    );
  },
};
