import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import type { ColumnDef } from "@tanstack/react-table";

import { Button } from "../../src/primitives/button";
import ListTable from "../../src/tables/ListTable";
import { withSearchParams } from "../search/SearchParamsDecorator";
import {
  defaultPersonColumns,
  generatePaginatedPeople,
  TableContext,
} from "./TableUtils";

const meta = {
  title: "Tables/ListTable",
  component: ListTable,
  parameters: {
    layout: "padded",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams, TableContext.Decorator],
} satisfies Meta<typeof ListTable>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default i18n configuration for all stories
const defaultI18n = {
  emptyText: "No items found",
  selection: "Selected items",
  actions: {
    tableSettings: "Table settings",
    tableActions: "Table actions",
    search: "Search...",
  },
  headers: {
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email",
    role: "Role",
    status: "Status",
    joinDate: "Join Date",
  },
};

// Default filter groups for filter stories
const defaultFilterGroups = [
  {
    id: "status",
    label: "Status",
    options: [
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
      { value: "pending", label: "Pending" },
    ],
  },
  {
    id: "role",
    label: "Role",
    options: [
      { value: "Admin", label: "Admin" },
      { value: "User", label: "User" },
      { value: "Editor", label: "Editor" },
      { value: "Viewer", label: "Viewer" },
      { value: "Manager", label: "Manager" },
    ],
  },
];

/**
 * Basic ListTable with default configuration
 */
export const Default: Story = {
  args: {
    data: generatePaginatedPeople(5),
    columns: () => defaultPersonColumns as ColumnDef<unknown>[],
    i18n: defaultI18n,
  },
};

/**
 * ListTable in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
    data: { items: [], total: 0 },
    columns: () => defaultPersonColumns as ColumnDef<unknown>[],
    i18n: defaultI18n,
  },
};

/**
 * ListTable with filter groups
 */
export const WithFilters: Story = {
  args: {
    data: generatePaginatedPeople(5),
    columns: () => defaultPersonColumns as ColumnDef<unknown>[],
    i18n: defaultI18n,
    groupName: "people",
    filterGroups: defaultFilterGroups,
  },
};

/**
 * ListTable with custom filters
 */
export const WithCustomFilters: Story = {
  args: {
    data: generatePaginatedPeople(5),
    columns: () => defaultPersonColumns as ColumnDef<unknown>[],
    i18n: defaultI18n,
    groupName: "people",
    filterGroups: defaultFilterGroups,
    filters: [
      <Button key="filter-today" size="sm" variant="outline">
        Today
      </Button>,
      <Button key="filter-yesterday" size="sm" variant="outline">
        Yesterday
      </Button>,
      <Button key="filter-week" size="sm" variant="outline">
        This Week
      </Button>,
    ],
  },
};

/**
 * Empty ListTable
 */
export const Empty: Story = {
  args: {
    data: { items: [], total: 0 },
    columns: () => defaultPersonColumns as ColumnDef<unknown>[],
    i18n: defaultI18n,
    groupName: "people",
    filterGroups: defaultFilterGroups,
  },
};
