import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { GenericNode } from "../../src/selectors/Selector";

import { Selector } from "../../src/selectors/Selector";

// Define a proper type for our test data that extends GenericNode
interface TestItem extends GenericNode {
  name: string;
  category: string;
}

const meta = {
  title: "Selectors/Selector",
  component: Selector<TestItem>,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    useDialog: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    variant: {
      control: { type: "select" },
      options: ["outline", "ghost", "secondary", "destructive"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
    showCheck: {
      control: { type: "boolean" },
    },
    closeOnSelect: {
      control: { type: "boolean" },
    },
  },
} satisfies Meta<typeof Selector<TestItem>>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for testing
const sampleItems: TestItem[] = [
  { id: "1", name: "Apple", category: "Fruit" },
  { id: "2", name: "Banana", category: "Fruit" },
  { id: "3", name: "Carrot", category: "Vegetable" },
  { id: "4", name: "Broccoli", category: "Vegetable" },
  { id: "5", name: "Chicken", category: "Protein" },
  { id: "6", name: "Salmon", category: "Protein" },
];

const manyItems: TestItem[] = Array.from({ length: 50 }, (_, i) => ({
  id: `item-${i + 1}`,
  name: `Item ${i + 1}`,
  category: `Category ${Math.floor(i / 10) + 1}`,
}));

/**
 * Default Selector component with basic configuration
 */
export const Default: Story = {
  args: {
    data: sampleItems,
    label: "Select an item",
    placeholder: "Search items...",
    onSelect: (item) => console.log("Selected:", item),
    onValueChange: (value) => console.log("Value changed:", value),
    renderItem: (item) => (
      <div className="flex flex-col">
        <span className="font-medium">{item.name}</span>
        <span className="text-xs text-muted-foreground">{item.category}</span>
      </div>
    ),
  },
};

/**
 * Selector with custom renderValue and renderSelection
 */
export const CustomRendering: Story = {
  args: {
    data: sampleItems,
    label: "Select food",
    renderValue: (item) => `${item.name} (${item.category})`,
    renderSelection: (item) =>
      item ? (
        <div className="flex items-center gap-2">
          <div className="size-2 rounded-full bg-green-500" />
          <span>{item.name}</span>
          <span className="text-xs text-muted-foreground">
            — {item.category}
          </span>
        </div>
      ) : null,
    renderItem: (item) => (
      <div className="flex w-full items-center justify-between">
        <div className="flex flex-col">
          <span className="font-medium">{item.name}</span>
          <span className="text-xs text-muted-foreground">{item.category}</span>
        </div>
        <div className="rounded bg-muted px-2 py-1 text-xs">
          {item.category}
        </div>
      </div>
    ),
    onSelect: (item) => console.log("Selected:", item),
  },
};

/**
 * Selector with checkboxes enabled
 */
export const WithCheckboxes: Story = {
  args: {
    data: sampleItems,
    label: "Select items",
    showCheck: true,
    renderItem: (item) => <span>{item.name}</span>,
    onSelect: (item) => console.log("Selected:", item),
  },
};

/**
 * Selector with custom checkbox rendering
 */
export const CustomCheckboxes: Story = {
  args: {
    data: sampleItems,
    label: "Select items",
    showCheck: true,
    renderCheck: (isSelected) => (
      <div className="mr-2 flex items-center">{isSelected ? "✓" : "○"}</div>
    ),
    renderItem: (item) => <span>{item.name}</span>,
    onSelect: (item) => console.log("Selected:", item),
  },
};

/**
 * Selector in loading state
 */
export const Loading: Story = {
  args: {
    data: [],
    loading: true,
    label: "Loading...",
  },
};

/**
 * Selector with custom loading component
 */
export const CustomLoading: Story = {
  args: {
    data: [],
    loading: true,
    label: "Loading...",
    renderLoading: () => (
      <div className="flex items-center justify-center p-4">
        <div className="size-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        <span className="ml-2">Loading items...</span>
      </div>
    ),
  },
};

/**
 * Selector with no results
 */
export const NoResults: Story = {
  args: {
    data: [],
    label: "No items available",
    placeholder: "Search for items...",
  },
};

/**
 * Selector with custom empty state
 */
export const CustomEmpty: Story = {
  args: {
    data: [],
    label: "Select items",
    placeholder: "Search for items...",
    renderEmpty: () => (
      <div className="flex flex-col items-center justify-center p-6 text-center">
        <div className="mb-2 text-4xl">🔍</div>
        <span className="font-medium">No items found</span>
        <span className="text-xs text-muted-foreground">
          Try adjusting your search
        </span>
      </div>
    ),
  },
};

/**
 * Selector as dialog (mobile-friendly)
 */
export const DialogMode: Story = {
  args: {
    data: sampleItems,
    label: "Open dialog",
    useDialog: true,
    renderItem: (item) => (
      <div className="flex flex-col">
        <span className="font-medium">{item.name}</span>
        <span className="text-xs text-muted-foreground">{item.category}</span>
      </div>
    ),
    onSelect: (item) => console.log("Selected:", item),
  },
};

/**
 * Selector with many items (performance test)
 */
export const ManyItems: Story = {
  args: {
    data: manyItems,
    label: "Select from many items",
    placeholder: "Search through 50 items...",
    renderItem: (item) => (
      <div className="flex flex-col">
        <span className="font-medium">{item.name}</span>
        <span className="text-xs text-muted-foreground">{item.category}</span>
      </div>
    ),
    onSelect: (item) => console.log("Selected:", item),
  },
};

/**
 * Selector with different sizes
 */
export const Sizes: Story = {
  parameters: {
    layout: "padded",
  },
  args: {
    data: sampleItems,
  },
  render: (args) => (
    <div className="space-y-4">
      <div>
        <label className="mb-2 block text-sm font-medium">Small</label>
        <Selector
          {...args}
          size="sm"
          label="Small selector"
          renderItem={(item) => <span>{item.name}</span>}
        />
      </div>
      <div>
        <label className="mb-2 block text-sm font-medium">
          Medium (Default)
        </label>
        <Selector
          {...args}
          size="md"
          label="Medium selector"
          renderItem={(item) => <span>{item.name}</span>}
        />
      </div>
      <div>
        <label className="mb-2 block text-sm font-medium">Large</label>
        <Selector
          {...args}
          size="lg"
          label="Large selector"
          renderItem={(item) => <span>{item.name}</span>}
        />
      </div>
    </div>
  ),
};

/**
 * Disabled selector
 */
export const Disabled: Story = {
  args: {
    data: sampleItems,
    disabled: true,
    label: "Disabled selector",
    renderItem: (item) => <span>{item.name}</span>,
  },
};

/**
 * Controlled selector with pre-selected value
 */
export const Controlled: Story = {
  args: {
    data: sampleItems,
    value: "2", // Pre-select Banana
    label: "Controlled selector",
    renderItem: (item) => <span>{item.name}</span>,
    onSelect: (item) => console.log("Selected:", item),
    onValueChange: (value) => console.log("Value changed:", value),
  },
};

/**
 * Selector with header and footer
 */
export const WithHeaderFooter: Story = {
  args: {
    data: sampleItems,
    label: "Select food",
    placeholder: "Search foods...",
    heading: "Available Foods",
    header: (
      <div className="px-2 py-1 text-xs text-muted-foreground">
        Choose from our selection
      </div>
    ),
    footer: (
      <div className="px-2 py-1">
        <button className="text-xs text-primary hover:underline">
          + Add new item
        </button>
      </div>
    ),
    renderItem: (item) => (
      <div className="flex flex-col">
        <span className="font-medium">{item.name}</span>
        <span className="text-xs text-muted-foreground">{item.category}</span>
      </div>
    ),
    onSelect: (item) => console.log("Selected:", item),
  },
};

/**
 * Multi-select mode with default rendering
 */
export const MultiSelect: Story = {
  args: {
    data: sampleItems,
    multiple: true,
    label: "Select multiple items",
    placeholder: "Search and select items...",
    defaultSelections: [sampleItems[0]!, sampleItems[2]!], // Pre-select Apple and Carrot
    renderItem: (item) => (
      <div className="flex flex-col">
        <span className="font-medium">{item.name}</span>
        <span className="text-xs text-muted-foreground">{item.category}</span>
      </div>
    ),
    onSelectionsChange: (selections) =>
      console.log(
        "Selections changed:",
        selections.map((s) => s.name),
      ),
  },
};

/**
 * Multi-select with maximum limit and custom rendering
 */
export const MultiSelectAdvanced: Story = {
  args: {
    data: sampleItems,
    multiple: true,
    maxSelections: 3,
    label: "Choose up to 3 favorites",
    placeholder: "Search for your favorites...",
    renderItem: (item) => (
      <div className="flex w-full items-center justify-between">
        <div className="flex flex-col">
          <span className="font-medium">{item.name}</span>
          <span className="text-xs text-muted-foreground">{item.category}</span>
        </div>
        <div className="rounded bg-muted px-2 py-1 text-xs">
          {item.category}
        </div>
      </div>
    ),
    renderMultipleSelection: (selections, onRemove) => (
      <div className="flex flex-wrap gap-1">
        {selections.slice(0, 2).map((item) => (
          <div
            key={item.id}
            className="flex items-center gap-1 rounded-md bg-primary/10 px-2 py-1 text-xs"
          >
            <span>{item.name}</span>
            {onRemove && (
              <button
                type="button"
                className="rounded-full hover:bg-primary/20"
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove(item);
                }}
              >
                ×
              </button>
            )}
          </div>
        ))}
        {selections.length > 2 && (
          <div className="rounded-md bg-muted px-2 py-1 text-xs">
            +{selections.length - 2} more
          </div>
        )}
      </div>
    ),
    onSelectionsChange: (selections) => {
      console.log(
        "Selections changed:",
        selections.map((s) => s.name),
      );
      console.log(`${selections.length}/3 items selected`);
    },
  },
};
