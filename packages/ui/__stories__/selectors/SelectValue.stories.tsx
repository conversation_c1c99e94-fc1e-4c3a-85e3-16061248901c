import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import type { PartialValue } from "../../src/selectors/SelectValue";

import { SelectValue } from "../../src/selectors/SelectValue";

const meta = {
  title: "Selectors/SelectValue",
  component: SelectValue,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    useDialog: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    variant: {
      control: { type: "select" },
      options: ["outline", "ghost", "secondary", "destructive"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
  },
} satisfies Meta<typeof SelectValue>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample value data
const values = [
  {
    id: "1",
    type: "ORDER_TYPE",
    value: "Standard Order",
  },
  {
    id: "2",
    type: "ORDER_TYPE",
    value: "Rush Order",
  },
  {
    id: "3",
    type: "ORDER_CATEGORY",
    value: "Medical Services",
  },
  {
    id: "4",
    type: "ORDER_CATEGORY",
    value: "Administrative",
  },
  {
    id: "5",
    type: "TAG",
    value: "Urgent",
  },
  {
    id: "6",
    type: "TAG",
    value: "Follow-up Required",
  },
] satisfies PartialValue[];

/**
 * Default SelectValue component with example data
 */
export const Default: Story = {
  args: {
    data: values,
  },
};

/**
 * SelectValue component in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
    data: [],
  },
};
