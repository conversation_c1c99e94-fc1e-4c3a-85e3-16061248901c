import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { SelectPerson } from "../../src/selectors/SelectPerson";

const meta = {
  title: "Selectors/SelectPerson",
  component: Select<PERSON>erson,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    useDialog: {
      control: { type: "boolean" },
    },
    loading: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg", "xl"],
    },
    variant: {
      control: { type: "select" },
      options: ["outline", "ghost", "secondary", "destructive"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
  },
} satisfies Meta<typeof SelectPerson>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample people data
const people = [
  {
    id: "1",
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+1234567890",
    avatar: null,
  },
  {
    id: "2",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1987654321",
    avatar: null,
  },
  {
    id: "3",
    firstName: "Michael",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "+1122334455",
    avatar: null,
  },
];

/**
 * Default SelectPerson component with example data
 */
export const Default: Story = {
  args: {
    data: people,
  },
};

/**
 * SelectPerson component in loading state
 */
export const Loading: Story = {
  args: {
    loading: true,
    data: [],
  },
};
