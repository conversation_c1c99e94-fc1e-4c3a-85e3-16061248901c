import { Meta } from "@storybook/addon-docs";

<Meta title="Selectors/Guide" />

# Selector Component

The `Selector` is a highly flexible, headless base component designed for creating custom select/search inputs. It provides the core logic for selection, searching, and state management, while allowing for complete control over rendering via props. It supports two primary operating modes: `popover` and `dialog`.

## Basic Usage

To use the `Selector`, you need to provide an array of `data` items, a `label` for the trigger button, and a `renderItem` function to display each item in the list.

```tsx
import { Selector } from "@/ui/selectors/Selector";

const items = [
  { id: "1", name: "Apple", category: "Fruit" },
  { id: "2", name: "Carrot", category: "Vegetable" },
];

function BasicSelector() {
  return (
    <Selector
      data={items}
      label="Select an item"
      onSelect={(item) => console.log("Selected:", item)}
      renderItem={(item) => <span>{item.name}</span>}
    />
  );
}
```

## Custom Rendering

The component's appearance can be fully customized using various render props.

- `renderItem`: Customizes the display of each item in the dropdown list.
- `renderSelection`: Customizes what is shown in the trigger button when an item is selected.
- `renderCheck`: Customizes the checkmark when `showCheck` is enabled.

```tsx
<Selector
  data={items}
  label="Select food"
  showCheck
  renderItem={(item) => (
    <div>
      <span>{item.name}</span>
      <span className="text-xs text-muted-foreground">({item.category})</span>
    </div>
  )}
  renderSelection={(item) =>
    item ? (
      <div className="flex items-center gap-2">
        <span className="font-bold">{item.name}</span>
        <span className="text-xs text-muted-foreground">{item.category}</span>
      </div>
    ) : null
  }
  renderCheck={(isSelected) => (
    <span className="mr-2">{isSelected ? "✅" : "◻️"}</span>
  )}
/>
```

## Operating Modes

The selector can operate in two modes, controlled by the `useDialog` prop.

### 1. Popover Mode (Default)

When `useDialog` is `false` (the default), the selector opens in a popover anchored to the trigger button. This is ideal for desktop applications where screen space is plentiful.

```tsx
<Selector
  data={items}
  label="Open Popover"
  useDialog={false} // This is the default value
  renderItem={(item) => <span>{item.name}</span>}
/>
```

### 2. Dialog Mode

When `useDialog` is `true`, the selector opens in a centered modal dialog. This is better for mobile devices or when the selection list is very long.

```tsx
<Selector
  data={items}
  label="Open Dialog"
  useDialog={true}
  renderItem={(item) => <span>{item.name}</span>}
/>
```

## Common Props

| Prop Name         | Type                                 | Description                                  |
| :---------------- | :----------------------------------- | :------------------------------------------- |
| `data`            | `T[]`                                | Array of data items to display.              |
| `label`           | `string`                             | Text to show in the button before selection. |
| `onSelect`        | `(item: T) => void`                  | Callback when an item is selected.           |
| `renderItem`      | `(item: T) => ReactNode`             | Renders each item in the list.               |
| `renderSelection` | `(item?: T) => ReactNode`            | Renders the selected value in the button.    |
| `renderCheck`     | `(isSelected: boolean) => ReactNode` | Renders a custom checkmark.                  |
| `showCheck`       | `boolean`                            | If true, shows a checkmark next to items.    |
| `useDialog`       | `boolean`                            | Toggles between popover and dialog mode.     |

> 💡 **Tip:** You can also customize loading, empty, header, and footer states with `renderLoading`, `renderEmpty`, `header`, and `footer` props to create a fully featured selection component.
