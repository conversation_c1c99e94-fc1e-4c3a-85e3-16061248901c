import type { <PERSON>a, StoryObj } from "@storybook/react";

import { useState } from "react";

import { SearchValue } from "../../src/search/SearchValue";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchValue",
  component: SearchValue,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchValue>;

export default meta;

type Story = StoryObj<typeof meta>;

// Sample options for the selector
const sampleOptions = [
  { value: "option1", label: "Option 1", id: "option1" },
  { value: "option2", label: "Option 2", id: "option2" },
  { value: "option3", label: "Option 3", id: "option3" },
  { value: "option4", label: "Option 4", id: "option4" },
];

export const Default: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const WithCustomGroup: Story = {
  args: {
    name: "value",
    group: "products",
    placeholder: "Select product",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const WithDefaultValue: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    defaultValue: "option2",
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const WithDialog: Story = {
  args: {
    name: "value",
    placeholder: "Select value",
    useDialog: true,
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};

export const Loading: Story = {
  args: {
    name: "value",
    placeholder: "Loading...",
    loading: true,
    data: sampleOptions,
  },
  render: function Render(args) {
    return (
      <div style={{ width: "300px" }}>
        <SearchValue {...args} />
      </div>
    );
  },
};
