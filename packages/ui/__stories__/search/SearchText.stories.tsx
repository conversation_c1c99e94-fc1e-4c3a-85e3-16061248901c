import type { <PERSON>a, StoryObj } from "@storybook/react";

import { SearchText } from "../../src/search/SearchText";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchText",
  component: SearchText,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchText>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Search...",
  },
};

export const WithCustomName: Story = {
  args: {
    name: "query",
    placeholder: "Search by name",
  },
};

export const WithGroup: Story = {
  args: {
    name: "query",
    group: "users",
    placeholder: "Search users",
  },
};

export const WithDefaultValue: Story = {
  args: {
    defaultValue: "John",
    placeholder: "Search...",
  },
};

export const SizeSmall: Story = {
  args: {
    size: "sm",
    placeholder: "Search (small)...",
  },
};

export const SizeLarge: Story = {
  args: {
    size: "lg",
    placeholder: "Search (large)...",
  },
};

export const SizeExtraLarge: Story = {
  args: {
    size: "xl",
    placeholder: "Search (XL)...",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    placeholder: "Loading...",
  },
};
