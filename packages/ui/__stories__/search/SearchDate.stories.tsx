import type { <PERSON>a, StoryObj } from "@storybook/react";

import { SearchDateRange } from "../../src/search/SearchDate";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchDate",
  component: SearchDateRange,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchDateRange>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: "Select date range...",
  },
};

export const WithCustomName: Story = {
  args: {
    name: "customDateRange",
    placeholder: "Custom date range...",
  },
};

export const WithCustomGroup: Story = {
  args: {
    group: "events",
    placeholder: "Event date range...",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    placeholder: "Loading date range...",
  },
};
