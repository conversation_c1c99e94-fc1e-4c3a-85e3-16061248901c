import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { ListFilterIcon } from "lucide-react";

import { Button } from "../../src/primitives/button";
import { SearchFilter } from "../../src/search/SearchFilter";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchFilter",
  component: SearchFilter,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchFilter>;

export default meta;

type Story = StoryObj<typeof meta>;

// Sample filter groups
const statusGroup = {
  id: "status",
  label: "Status",
  options: [
    { label: "All", value: null },
    { label: "Active", value: "active" },
    { label: "Pending", value: "pending" },
    { label: "Archived", value: "archived" },
  ],
};

const typeGroup = {
  id: "type",
  label: "Type",
  options: [
    { label: "All Types", value: null },
    { label: "Document", value: "document" },
    { label: "Agreement", value: "agreement" },
    { label: "Contract", value: "contract" },
  ],
};

export const Default: Story = {
  args: {
    groups: [statusGroup],
  },
};

export const MultipleGroups: Story = {
  args: {
    groups: [statusGroup, typeGroup],
  },
};

export const WithCustomButton: Story = {
  args: {
    groups: [statusGroup, typeGroup],
    children: (
      <Button variant="outline" size="sm" className="gap-2">
        <ListFilterIcon size={16} />
        <span>Filters</span>
      </Button>
    ),
  },
};

export const WithDifferentAlignment: Story = {
  args: {
    groups: [statusGroup, typeGroup],
    align: "end",
    side: "right",
  },
};

export const Disabled: Story = {
  args: {
    groups: [statusGroup, typeGroup],
    disabled: true,
  },
};
