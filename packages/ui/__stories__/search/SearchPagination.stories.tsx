import type { <PERSON>a, StoryObj } from "@storybook/react";

import { SearchPagination } from "../../src/search/SearchPagination";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchPagination",
  component: SearchPagination,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchPagination>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    totalItems: 100,
    namespace: "items",
  },
};

export const WithLargeDataset: Story = {
  args: {
    totalItems: 500,
    namespace: "items",
    defaultPageSize: 20,
  },
};

export const WithoutPageNumbers: Story = {
  args: {
    totalItems: 100,
    namespace: "items",
    showPageNumbers: false,
  },
};

export const WithCustomLabels: Story = {
  args: {
    totalItems: 100,
    namespace: "items",
    labels: {
      previous: "Go Back",
      previousShort: "Back",
      next: "Go Forward",
      nextShort: "Next",
      page: "Page {current} of {total}",
    },
  },
};

export const WithLargeSiblingCount: Story = {
  args: {
    totalItems: 100,
    namespace: "items",
    siblingCount: 3,
  },
};
