import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { SearchSelect } from "../../src/search/SearchSelect";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchSelect",
  component: SearchSelect,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
  argTypes: {
    size: {
      control: { type: "select" },
      options: ["sm", "md", "lg"],
    },
    variant: {
      control: { type: "select" },
      options: ["default", "compact"],
    },
    align: {
      control: { type: "select" },
      options: ["start", "center", "end"],
    },
    side: {
      control: { type: "select" },
      options: ["top", "right", "bottom", "left"],
    },
  },
} satisfies Meta<typeof SearchSelect>;

export default meta;

type Story = StoryObj<typeof meta>;

// Sample options
const statusOptions = [
  { value: "", label: "All statuses" },
  { value: "active", label: "Active" },
  { value: "pending", label: "Pending" },
  { value: "archived", label: "Archived" },
];

const sortOptions = [
  { value: "newest", label: "Newest first" },
  { value: "oldest", label: "Oldest first" },
  { value: "name_asc", label: "Name (A-Z)" },
  { value: "name_desc", label: "Name (Z-A)" },
];

export const Default: Story = {
  args: {
    options: statusOptions,
    placeholder: "Select status",
  },
};

export const WithCustomName: Story = {
  args: {
    name: "sort",
    options: sortOptions,
    placeholder: "Sort by",
  },
};

export const WithGroup: Story = {
  args: {
    name: "status",
    group: "users",
    options: statusOptions,
    placeholder: "User status",
  },
};

export const WithDefaultValue: Story = {
  args: {
    options: statusOptions,
    defaultValue: "active",
    placeholder: "Select status",
  },
};

export const WithCustomAlignment: Story = {
  args: {
    options: statusOptions,
    placeholder: "Select status",
    align: "end",
    side: "right",
  },
};

export const Loading: Story = {
  args: {
    options: statusOptions,
    placeholder: "Loading...",
    loading: true,
  },
};

// Size variants
export const SmallSize: Story = {
  args: {
    size: "sm",
    options: statusOptions,
    placeholder: "Small select",
  },
  name: "Size: Small",
};

export const MediumSize: Story = {
  args: {
    size: "md",
    options: statusOptions,
    placeholder: "Medium select",
  },
  name: "Size: Medium (Default)",
};

export const LargeSize: Story = {
  args: {
    size: "lg",
    options: statusOptions,
    placeholder: "Large select",
  },
  name: "Size: Large",
};

// Variant examples
export const CompactVariant: Story = {
  args: {
    variant: "compact",
    options: statusOptions,
    placeholder: "Compact select",
  },
  name: "Variant: Compact",
};

// Size comparison
export const SizeComparison: Story = {
  args: {
    options: statusOptions,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Small:</span>
        <SearchSelect
          size="sm"
          options={statusOptions}
          placeholder="Small select"
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Medium:</span>
        <SearchSelect
          size="md"
          options={statusOptions}
          placeholder="Medium select"
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Large:</span>
        <SearchSelect
          size="lg"
          options={statusOptions}
          placeholder="Large select"
        />
      </div>
    </div>
  ),
  name: "All Sizes",
};

// Variant comparison
export const VariantComparison: Story = {
  args: {
    options: statusOptions,
  },
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Default:</span>
        <SearchSelect
          variant="default"
          options={statusOptions}
          placeholder="Default variant"
        />
      </div>
      <div className="flex items-center gap-2">
        <span className="w-16 text-sm text-muted-foreground">Compact:</span>
        <SearchSelect
          variant="compact"
          options={statusOptions}
          placeholder="Compact variant"
        />
      </div>
    </div>
  ),
  name: "All Variants",
};

// With default value and clear functionality
export const WithDefaultAndClear: Story = {
  args: {
    options: statusOptions,
    defaultValue: "pending",
    placeholder: "Select status",
  },
  name: "With Default Value & Clear",
};

// Empty value handling
export const EmptyValueHandling: Story = {
  args: {
    options: [
      { value: "", label: "No filter" },
      { value: "option1", label: "Option 1" },
      { value: "option2", label: "Option 2" },
    ],
    defaultValue: "",
    placeholder: "Select option",
  },
  name: "Empty Value Handling",
};
