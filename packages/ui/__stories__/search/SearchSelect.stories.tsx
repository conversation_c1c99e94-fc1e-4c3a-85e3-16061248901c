import type { <PERSON>a, StoryObj } from "@storybook/react";

import { SearchSelect } from "../../src/search/SearchSelect";
import { withSearchParams } from "./SearchParamsDecorator";

const meta = {
  title: "Search/SearchSelect",
  component: SearchSelect,
  parameters: {
    layout: "centered",
    nextjs: {
      appDirectory: true,
    },
  },
  tags: ["autodocs"],
  decorators: [withSearchParams],
} satisfies Meta<typeof SearchSelect>;

export default meta;

type Story = StoryObj<typeof meta>;

// Sample options
const statusOptions = [
  { value: "", label: "All statuses" },
  { value: "active", label: "Active" },
  { value: "pending", label: "Pending" },
  { value: "archived", label: "Archived" },
];

const sortOptions = [
  { value: "newest", label: "Newest first" },
  { value: "oldest", label: "Oldest first" },
  { value: "name_asc", label: "Name (A-Z)" },
  { value: "name_desc", label: "Name (Z-A)" },
];

export const Default: Story = {
  args: {
    options: statusOptions,
    placeholder: "Select status",
  },
};

export const WithCustomName: Story = {
  args: {
    name: "sort",
    options: sortOptions,
    placeholder: "Sort by",
  },
};

export const WithGroup: Story = {
  args: {
    name: "status",
    group: "users",
    options: statusOptions,
    placeholder: "User status",
  },
};

export const WithDefaultValue: Story = {
  args: {
    options: statusOptions,
    defaultValue: "active",
    placeholder: "Select status",
  },
};

export const WithCustomAlignment: Story = {
  args: {
    options: statusOptions,
    placeholder: "Select status",
    align: "end",
    side: "right",
  },
};

export const Loading: Story = {
  args: {
    options: statusOptions,
    placeholder: "Loading...",
    loading: true,
  },
};
