import { Meta } from "@storybook/addon-docs";

<Meta title="Search/Guide" />

# Search Components

The search components are a collection of URL-synchronized UI components designed for building powerful search and filtering interfaces. Unlike traditional form components, these components automatically sync their state with URL query parameters, making search interfaces bookmarkable and shareable. All search components work together through a shared `SearchParams` context provider.

## Setup & Context

All search components require the `SearchParams` provider to manage URL state synchronization.

```tsx
import { SearchParams } from "@axa/ui/search"; // just "@/ui/search" for components in the ui lib

function AppPage() {
  return (
    <Suspense>
      <SearchParams>{/* Your search components go here */}</SearchParams>
    </Suspense>
  );
}
```

> Suspense is needed here because of Next.js and it's required to properly use the useSearch program, which is under the hood is what's being used.

## Basic Usage

Each search component automatically syncs its value with URL query parameters. Here's a simple search interface:

```tsx
import { SearchDate, SearchSelect, SearchText } from "@/ui/search";

const priorityOptions = [
  { value: "high", label: "High Priority" },
  { value: "medium", label: "Medium Priority" },
  { value: "low", label: "Low Priority" },
];

function BasicSearchInterface() {
  return (
    <div className="flex gap-4">
      <SearchText placeholder="Search orders..." />
      <SearchSelect options={priorityOptions} placeholder="Priority" />
      <SearchDate placeholder="Select date range" />
    </div>
  );
}
```

## Component Architecture

Each search component follows a consistent pattern:

- **UI Component** (`SearchText`, `SearchSelect`, etc.) - Handles rendering and user interaction
- **Hook** (`useSearchText`, `useSearchSelect`, etc.) - Manages state logic and URL synchronization
- **Shared Context** (`SearchParams`) - Coordinates URL state across all components

## Component Variants

### SearchText

A debounced text input that syncs search queries with URL parameters.

```tsx
<SearchText
  name="query"
  group="orders"
  placeholder="Search products..."
  size="lg"
/>
```

### SearchSelect

A dropdown selector with clear functionality for categorical filtering.

```tsx
<SearchSelect
  name="status"
  options={statusOptions}
  placeholder="Select status"
  variant="compact"
/>
```

### SearchDate

Calendar-based date range picker for temporal filtering.

```tsx
<SearchDate name="dateRange" placeholder="Select date range" size="md" />
```

### SearchValue

Multi-value selector for complex filtering scenarios.

```tsx
<SearchValue
  name="tags"
  options={tagOptions}
  placeholder="Select tags"
  multiple
/>
```

### SearchFilter

Advanced filtering component with grouped filter options.

```tsx
<SearchFilter groups={filterGroups} placeholder="Add filters" />
```

### SearchPagination

URL-synchronized pagination controls.

```tsx
<SearchPagination totalItems={1000} pageSize={20} />
```

## Size Variants

All search components support consistent sizing:

```tsx
// Small - Compact interfaces
<SearchText size="sm" />

// Medium - Default size
<SearchText size="md" />

// Large - Prominent search bars
<SearchText size="lg" />

// Extra Large - Hero search interfaces
<SearchText size="xl" />
```

## Styling Variants

Components support different visual styles:

```tsx
// Default - Standard bordered appearance
<SearchSelect variant="default" options={options} />

// Compact - Minimal styling for dense layouts
<SearchSelect variant="compact" options={options} />
```

## Naming & Grouping

Use `name` and `group` props to organize search parameters in the URL:

```tsx
// Creates URL param: ?search=query
<SearchText name="search" />

// Creates URL param: ?orders-search=query
<SearchText name="search" group="orders" />

// Creates URL param: ?products-category=electronics
<SearchSelect name="category" group="products" options={categories} />
```

## State Management

### URL Synchronization

Components automatically sync with URL parameters:

```tsx
// URL: /?search=laptop&category=electronics
// SearchText will show "laptop"
// SearchSelect will show "electronics"
<SearchText name="search" />
<SearchSelect name="category" options={categories} />
```

### Controlled vs Uncontrolled

Components can operate in controlled mode with external state:

```tsx
const [searchValue, setSearchValue] = useState("");

<SearchText value={searchValue} onChange={setSearchValue} name="search" />;
```

### Default Values

Set initial values that appear before user interaction:

```tsx
<SearchText name="search" defaultValue="initial query" />
```

## Hooks for Custom Components

Use the underlying hooks to build custom search components:

```tsx
import { useSearchSelect, useSearchText } from "@/ui/search";

function CustomSearchComponent() {
  const [value, setValue, clearValue] = useSearchText({
    name: "custom",
    group: "advanced",
    defaultValue: "",
    onChange: (val) => console.log("Search:", val),
  });

  return (
    <div>
      <input value={value} onChange={(e) => setValue(e.target.value)} />
      <button onClick={clearValue}>Clear</button>
    </div>
  );
}
```

## Common Props

| Prop Name      | Type                           | Description                                      |
| :------------- | :----------------------------- | :----------------------------------------------- |
| `name`         | `string`                       | URL parameter name (default varies by component) |
| `group`        | `string`                       | Groups related parameters with prefix            |
| `size`         | `"sm" \| "md" \| "lg" \| "xl"` | Visual size variant                              |
| `variant`      | `"default" \| "compact"`       | Styling variant                                  |
| `loading`      | `boolean`                      | Shows loading state                              |
| `className`    | `string`                       | Additional CSS classes                           |
| `defaultValue` | `string`                       | Initial value before user interaction            |
| `onChange`     | `(value: string) => void`      | Callback for controlled usage                    |

## Best Practices

### Performance

- Components use debounced input (500ms) to prevent excessive URL updates
- URL changes don't trigger page reloads, only component re-renders

### Accessibility

- All components include proper ARIA labels and keyboard navigation
- Clear buttons have descriptive labels for screen readers

### UX Patterns

- Clear buttons appear when components have values
- Loading states disable interaction appropriately
- Consistent visual feedback across all components

### URL Structure

- Use meaningful parameter names that describe the filter
- Group related filters with the `group` prop for organization
- Keep URLs readable and shareable

## Integration Examples

### Search Results Page

```tsx
function ProductSearchPage() {
  const searchParams = useSearchParamsValues();
  const { data, loading } = useProductSearch(searchParams);

  return (
    <SearchParams>
      <div className="space-y-4">
        <div className="flex gap-4">
          <SearchText name="q" placeholder="Search products..." />
          <SearchSelect name="category" options={categories} />
          <SearchDate name="dateRange" />
        </div>

        <SearchFilter groups={advancedFilters} />

        <ProductGrid products={data} loading={loading} />

        <SearchPagination totalItems={data?.total} />
      </div>
    </SearchParams>
  );
}
```

### Dashboard Filters

```tsx
function OrdersDashboard() {
  return (
    <SearchParams>
      <div className="space-y-6">
        <div className="flex gap-2">
          <SearchText
            name="search"
            group="orders"
            size="sm"
            variant="compact"
          />
          <SearchSelect
            name="status"
            group="orders"
            options={statusOptions}
            size="sm"
            variant="compact"
          />
        </div>

        <OrdersTable />
      </div>
    </SearchParams>
  );
}
```

> 💡 **Tip:** The search components work seamlessly with server-side frameworks like Next.js App Router, automatically handling SSR and hydration for URL-based state.
