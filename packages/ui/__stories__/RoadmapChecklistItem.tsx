import React from "react";

import { Checkbox } from "@/ui/primitives/checkbox";
import { Label } from "@/ui/primitives/label";

export interface RoadmapChecklistItemProps {
  checked?: boolean;
  children: React.ReactNode;
}

export const RoadmapChecklistItem: React.FC<RoadmapChecklistItemProps> = ({
  checked,
  children,
}) => {
  return (
    <Label className="flex items-center gap-2 text-sm font-normal">
      <Checkbox checked={checked} />
      <span>{children}</span>
    </Label>
  );
};
