<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1494.98 449.27">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        filter: url(#drop-shadow-1);
      }

      .cls-3 {
        fill: url(#Dégradé_sans_nom_7);
      }

      .cls-4 {
        fill: url(#Dégradé_sans_nom_7-2);
      }

      .cls-5 {
        fill: url(#Dégradé_sans_nom_7-4);
      }

      .cls-6 {
        fill: url(#Dégradé_sans_nom_7-3);
      }

      .cls-7 {
        fill: url(#Dégradé_sans_nom_7-6);
      }

      .cls-8 {
        fill: url(#Dégradé_sans_nom_7-5);
      }

      .cls-9 {
        fill: url(#Dégradé_sans_nom_10);
      }

      .cls-10 {
        <!-- fill: #1c353d; -->
        fill: #ffffff;
      }

      .cls-11 {
        fill: url(#Dégradé_sans_nom_10-3);
      }

      .cls-12 {
        fill: url(#Dégradé_sans_nom_10-2);
      }
    </style>
    <filter id="drop-shadow-1" x="-209.98" y="-209.56" width="884" height="870" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur" stdDeviation="69.85"/>
      <feFlood flood-color="#0e3030" flood-opacity=".1"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="Dégradé_sans_nom_10" data-name="Dégradé sans nom 10" x1="275.48" y1="511.97" x2="411.53" y2="318.47" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4eaaa0"/>
      <stop offset="1" stop-color="#39ead9"/>
    </linearGradient>
    <linearGradient id="Dégradé_sans_nom_10-2" data-name="Dégradé sans nom 10" x1="391.97" y1="23.59" x2="194.64" y2="203.58" xlink:href="#Dégradé_sans_nom_10"/>
    <linearGradient id="Dégradé_sans_nom_7" data-name="Dégradé sans nom 7" x1="189.93" y1="310.83" x2="195.15" y2="303.84" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#4eaaa0"/>
      <stop offset="1" stop-color="#30d5c8"/>
    </linearGradient>
    <linearGradient id="Dégradé_sans_nom_7-2" data-name="Dégradé sans nom 7" x1="191.47" y1="322.99" x2="194.85" y2="318.45" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-3" data-name="Dégradé sans nom 7" x1="190.73" y1="338.23" x2="195.56" y2="331.76" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-4" data-name="Dégradé sans nom 7" x1="194.18" y1="306.66" x2="187.7" y2="311.33" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-5" data-name="Dégradé sans nom 7" x1="194.92" y1="334.18" x2="188.23" y2="339.01" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_7-6" data-name="Dégradé sans nom 7" x1="193.7" y1="323.31" x2="191.05" y2="325.22" xlink:href="#Dégradé_sans_nom_7"/>
    <linearGradient id="Dégradé_sans_nom_10-3" data-name="Dégradé sans nom 10" x1="179.1" y1="235.34" x2="-4.98" y2="403.41" xlink:href="#Dégradé_sans_nom_10"/>
  </defs>
  <g id="Calque_1">
    <g>
      <g>
        <g>
          <path class="cls-10" d="M799.8,263.34h-139.57l-24.75,48.83h-56.42l122.41-230.97h57.74l122.41,230.97h-57.08l-24.75-48.83ZM777.69,220.45l-47.84-94.04-47.84,94.04h95.69Z"/>
          <path class="cls-10" d="M1068.37,194.71l106.24,117.46h-64.67l-74.57-81.83-74.57,81.83h-61.37l105.92-116.8-104.27-114.16h64.67l72.92,79.85,72.26-79.85h61.7l-104.27,113.5Z"/>
          <path class="cls-10" d="M1413.16,263.34h-139.57l-24.75,48.83h-56.42l122.41-230.97h57.74l122.41,230.97h-57.08l-24.75-48.83ZM1391.05,220.45l-47.84-94.04-47.84,94.04h95.69Z"/>
        </g>
        <g>
          <path class="cls-10" d="M634.11,383.06c0,10.71-8.23,16.68-20.61,16.68h-25.71v17.62h-8.74v-50.98h34.45c12.38,0,20.61,6.04,20.61,16.68ZM625.16,383.13c0-6.55-4.88-9.1-12.38-9.1h-24.98v18.21h24.98c7.5,0,12.38-2.62,12.38-9.1Z"/>
          <path class="cls-10" d="M700.76,417.36l-15.8-18.5h-21.48v18.5h-8.74v-50.98h34.67c11.87,0,20.61,5.68,20.61,16.24,0,8.81-6.12,14.2-15,15.8l16.53,18.94h-10.78ZM688.81,391.36c7.28,0,12.24-2.33,12.24-8.67s-4.95-8.67-12.24-8.67h-25.42v17.33h25.42Z"/>
          <path class="cls-10" d="M728.51,391.87c0-16.82,12.09-27.02,31.68-27.02s31.68,10.19,31.68,27.02-12.09,27.02-31.68,27.02-31.68-10.19-31.68-27.02ZM782.91,391.87c0-12.38-8.88-19.3-22.72-19.3s-22.79,6.92-22.79,19.3,8.89,19.3,22.79,19.3,22.72-6.92,22.72-19.3Z"/>
          <path class="cls-10" d="M822.68,374.1v15h34.81v7.79h-34.81v20.46h-8.74v-50.98h49.89v7.72h-41.15Z"/>
          <path class="cls-10" d="M934.84,409.72v7.65h-50.76v-50.98h50.03v7.65h-41.36v13.62h34.81v7.65h-34.81v14.42h42.09Z"/>
          <path class="cls-10" d="M1008.76,402.29c0,9.68-9.54,16.6-26.87,16.6-11.94,0-22.5-3.5-30.44-10.34l5.46-6.19c6.92,6.19,15.07,8.96,25.34,8.96,11.36,0,17.62-2.99,17.62-8.3s-6.41-6.63-19.23-7.72c-14.05-1.16-27.31-4.3-27.31-14.71s11.73-15.73,26.36-15.73c11.07,0,20.46,3.28,26.58,8.45l-5.53,5.97c-5.24-4.59-12.45-6.77-20.9-6.85-8.08-.07-17.7,1.89-17.7,7.57s8.52,6.34,19.66,7.21c15.88,1.24,26.95,4.44,26.95,15.08Z"/>
          <path class="cls-10" d="M1082.17,402.29c0,9.68-9.54,16.6-26.87,16.6-11.94,0-22.5-3.5-30.44-10.34l5.46-6.19c6.92,6.19,15.07,8.96,25.34,8.96,11.36,0,17.62-2.99,17.62-8.3s-6.41-6.63-19.23-7.72c-14.05-1.16-27.31-4.3-27.31-14.71s11.73-15.73,26.36-15.73c11.07,0,20.46,3.28,26.58,8.45l-5.53,5.97c-5.24-4.59-12.45-6.77-20.9-6.85-8.08-.07-17.7,1.89-17.7,7.57s8.52,6.34,19.66,7.21c15.88,1.24,26.95,4.44,26.95,15.08Z"/>
          <path class="cls-10" d="M1103.15,417.36v-50.98h8.74v50.98h-8.74Z"/>
          <path class="cls-10" d="M1133.89,391.87c0-16.82,12.09-27.02,31.68-27.02s31.68,10.19,31.68,27.02-12.09,27.02-31.68,27.02-31.68-10.19-31.68-27.02ZM1188.29,391.87c0-12.38-8.88-19.3-22.72-19.3s-22.79,6.92-22.79,19.3,8.89,19.3,22.79,19.3,22.72-6.92,22.72-19.3Z"/>
          <path class="cls-10" d="M1275.32,366.38v50.98h-7.72l-39.84-40.2v40.2h-8.45v-50.98h9.32l38.16,38.67v-38.67h8.52Z"/>
          <path class="cls-10" d="M1342.48,405.05h-32.7l-6.48,12.31h-9.47l27.6-50.98h9.61l27.53,50.98h-9.54l-6.55-12.31ZM1338.62,397.63l-12.45-23.6-12.45,23.6h24.91Z"/>
          <path class="cls-10" d="M1423.47,409.64v7.72h-46.32v-50.98h8.74v43.26h37.58Z"/>
          <path class="cls-10" d="M1494.98,402.29c0,9.68-9.54,16.6-26.87,16.6-11.94,0-22.5-3.5-30.44-10.34l5.46-6.19c6.92,6.19,15.07,8.96,25.34,8.96,11.36,0,17.62-2.99,17.62-8.3s-6.41-6.63-19.23-7.72c-14.05-1.16-27.31-4.3-27.31-14.71s11.73-15.73,26.36-15.73c11.07,0,20.46,3.28,26.58,8.45l-5.53,5.97c-5.24-4.59-12.45-6.77-20.9-6.85-8.08-.07-17.7,1.89-17.7,7.57s8.52,6.34,19.66,7.21c15.88,1.24,26.95,4.44,26.95,15.08Z"/>
        </g>
      </g>
      <g class="cls-2">
        <path class="cls-9" d="M391.29,207.07l-139.84,242.2h118.28c71.74,0,116.58-77.66,80.7-139.79l-59.14-102.41Z"/>
        <path class="cls-12" d="M319.32,131.97c0,21.96-5.59,44.47-17.86,65.71l-84.33,146.08-1.68,2.98-14.95,25.87-44.26,76.65c6.59-12.06,12.18-23.66,16.83-34.79,1-2.4,1.98-4.77,2.86-7.13,1.77-4.49,3.38-8.92,4.82-13.25,1.21-3.59,2.3-7.13,3.31-10.59.19-.65.37-1.3.56-1.96,1-3.56,1.91-7.08,2.7-10.52.54-2.28,1.02-4.52,1.44-6.75.09-.4.19-.79.26-1.19.68-3.45,1.26-6.85,1.72-10.17.35-2.35.65-4.68.88-6.96.21-1.84.37-3.66.51-5.45.23-3.05.42-6.05.49-8.99.07-1.86.09-3.7.09-5.54,0-1.3-.02-2.61-.05-3.89-.02-1.35-.07-2.68-.14-4-.02-1.26-.09-2.49-.21-3.73,0-.16,0-.3-.02-.44-.26-3.75-.65-7.4-1.16-10.97-.14-1.14-.33-2.26-.51-3.38-.23-1.42-.46-2.79-.74-4.17-.4-2.12-.84-4.21-1.35-6.26-.02-.23-.07-.44-.14-.65-.26-1.02-.51-2.03-.77-3.05,0-.05,0-.09-.02-.12-.3-1.09-.58-2.17-.88-3.24-.65-2.24-1.33-4.42-2.05-6.57-.37-1.07-.74-2.12-1.12-3.17-.37-1.02-.74-2.03-1.12-3.03-.05-.09-.07-.16-.09-.23-1.19-3.05-2.44-6.01-3.8-8.87-.37-.82-.77-1.63-1.14-2.42-.16-.3-.3-.58-.44-.86-.4-.82-.79-1.63-1.23-2.44-1.49-2.89-3.05-5.68-4.68-8.38-.42-.72-.84-1.42-1.28-2.12-1.23-1.98-2.49-3.91-3.77-5.8-.72-1.07-1.47-2.12-2.21-3.14-.47-.65-.93-1.3-1.4-1.91-.42-.61-.86-1.19-1.33-1.77-1.37-1.82-2.77-3.59-4.19-5.31-34.95-43.38-40.72-103.33-12.95-151.65l24.57-42.54L186.9,0h.42c76.95,0,132,63.15,132,131.97Z"/>
        <path class="cls-3" d="M192.3,313.91c-.28-3.75-.68-7.4-1.16-10.97.51,3.56.91,7.22,1.16,10.97Z"/>
        <path class="cls-4" d="M192.72,324.78c-.02-2.26-.08-4.5-.19-6.7.1,2.2.17,4.44.19,6.7Z"/>
        <path class="cls-6" d="M192.63,331.52c-.07,2.96-.23,5.94-.49,8.99.23-3.05.42-6.05.49-8.99Z"/>
        <path class="cls-5" d="M192.3,313.91c-.28-3.75-.68-7.4-1.16-10.97.51,3.56.91,7.22,1.16,10.97Z"/>
        <path class="cls-8" d="M192.14,340.5c.23-3.05.42-6.05.49-8.99-.07,2.96-.23,5.94-.49,8.99Z"/>
        <path class="cls-7" d="M192.72,324.79v1.19c0-1.3-.02-2.61-.05-3.89.02.91.05,1.79.05,2.7Z"/>
        <path class="cls-11" d="M192.72,325.98c0,1.84-.02,3.68-.09,5.54-.07,2.93-.26,5.94-.49,8.99-.14,1.79-.3,3.61-.51,5.45-.23,2.28-.54,4.61-.88,6.96-.47,3.33-1.05,6.73-1.72,10.17-.07.4-.16.79-.26,1.19-.42,2.24-.91,4.47-1.44,6.75-.79,3.45-1.7,6.96-2.7,10.52-.19.65-.37,1.3-.56,1.96-1,3.47-2.1,7.01-3.31,10.59-1.44,4.33-3.05,8.75-4.82,13.25-.88,2.35-1.86,4.73-2.86,7.13-4.66,11.13-10.24,22.73-16.83,34.79h-57.18c-17.7,0-34.72-4.73-49.53-13.25-14.81-8.57-27.4-20.93-36.25-36.25-17.7-30.64-17.7-68.41,0-99.07l73.48-127.27,57.16-99c-27.78,48.31-22,108.27,12.95,151.65,1.42,1.72,2.82,3.49,4.19,5.31.46.58.91,1.16,1.33,1.77.47.6.93,1.26,1.4,1.91.74,1.02,1.49,2.07,2.21,3.14,1.28,1.89,2.54,3.82,3.77,5.8.44.7.86,1.4,1.28,2.12,1.63,2.7,3.19,5.5,4.68,8.38.42.82.84,1.63,1.23,2.44.14.28.28.56.44.86.37.79.77,1.61,1.14,2.42,1.35,2.86,2.61,5.82,3.8,8.87.02.07.05.14.09.23.37,1,.75,2,1.12,3.03.37,1.05.75,2.1,1.12,3.17.72,2.14,1.4,4.33,2.05,6.57.3,1.07.58,2.14.88,3.24.02.02.02.07.02.12.26,1.02.51,2.03.77,3.05.07.21.12.42.14.65.51,2.05.96,4.14,1.35,6.26.28,1.37.51,2.75.74,4.17.19,1.12.35,2.26.51,3.38.49,3.56.88,7.22,1.16,10.97.02.14.02.28.02.44.09,1.23.16,2.47.21,3.73.05,1.33.09,2.65.14,4,.02,1.28.05,2.58.05,3.89Z"/>
      </g>
      <line class="cls-1" x1="143.92" y1="74.42" x2="168.49" y2="31.88"/>
    </g>
  </g>
</svg>