# AXA UI – Shared Component Library

A core library of reusable, accessible, and themeable React components for all AXA Professionals web frontends.

- ✨ Built with [Tailwind CSS](https://tailwindcss.com/)
- 🧩 Includes forms, fields, primitives, layouts, and more
- 🗂️ Used across all apps for a consistent, modern UI

## 🚀 Quick Start (Monorepo)

> **Note:** No need to install dependencies here—handled at the workspace root!

### 🏗️ Develop & Preview Components

```sh
pnpm storybook
# or
npm run storybook
```

- Launches Storybook at [localhost:6006](http://localhost:6006) for live component previews and documentation.

### 🧪 Run Tests

```sh
pnpm test
# or
npm run test
```

- Unit and integration tests powered by [Vitest](https://vitest.dev/)
- Test files live in `__tests__/`

### 🧹 Lint & Format

```sh
pnpm lint
pnpm format
```

---

- Components live in `src/` (see `primitives/`, `fields/`, `forms/`, `shared/`, etc.)
- Storybook stories in `__stories__/`
- Tailwind config in `tailwind.config.ts`

## 📦 Usage Example

```tsx
import { Button } from "@axa/ui/primitives/button";
import { Card } from "@axa/ui/primitives/card";
import { Form } from "@axa/ui/primitives/forms";
import { Input } from "@axa/ui/primitives/input";

// Use components in your app
export default function Example() {
  return (
    <Card>
      <Form>
        <Input label="Name" />
        <Button type="submit">Submit</Button>
      </Form>
    </Card>
  );
}
```

## 🛠️ Features

- **Primitives:** Buttons, dialogs, modals, tooltips, etc.
- **Forms & Fields:** Ready-to-use form layouts and field types (text, date, select, etc.)
- **Layouts:** Cards, tables, navigation, and more
- **Themeable:** Built with Tailwind and supports dark mode
- **Accessible:** Follows best practices for accessibility

## 🤝 Contributing

- All changes to this package affect every app—review carefully!
- Follow the code style and conventions enforced by linting and formatting scripts.
- Preview changes in Storybook before submitting a PR.

## 🛡️ License

This software is the proprietary property of AXA Professionals.  
See the [LICENSE](../../LICENSE) file for details.
