{"name": "@axa/ui", "private": true, "version": "0.1.0", "type": "module", "exports": {"./*": {"types": "./dist/*.d.ts", "default": "./src/*.tsx"}, "./hooks/*": {"types": "./dist/hooks/*.d.ts", "default": "./src/hooks/*.ts"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "default": "./src/hooks/index.ts"}, "./lib/masks/*": {"types": "./dist/lib/masks/*.d.ts", "default": "./src/lib/masks/*.ts"}, "./lib/*": {"types": "./dist/lib/*.d.ts", "default": "./src/lib/*.ts"}, "./lib": {"types": "./dist/lib/index.d.ts", "default": "./src/lib/index.ts"}, "./brand/*": {"types": "./dist/brand/*.d.ts", "default": "./src/brand/*.tsx"}, "./maps/*": {"types": "./dist/maps/*.d.ts", "default": "./src/maps/*.tsx"}, "./maps": {"types": "./dist/maps/index.d.ts", "default": "./src/maps/index.ts"}, "./authentication/*": {"types": "./dist/authentication/*.d.ts", "default": "./src/authentication/*.tsx"}, "./authentication": {"types": "./dist/authentication/index.d.ts", "default": "./src/authentication/index.tsx"}, "./fields/files/*": {"types": "./dist/fields/files/*.d.ts", "default": "./src/fields/files/*.tsx"}, "./fields/company/*": {"types": "./dist/fields/company/*.d.ts", "default": "./src/fields/company/*.tsx"}, "./fields/*": {"types": "./dist/fields/*.d.ts", "default": "./src/fields/*.tsx"}, "./forms/organizations/*": {"types": "./dist/forms/organizations/*.d.ts", "default": "./src/forms/organizations/*.tsx"}, "./forms/public/*": {"types": "./dist/forms/public/*.d.ts", "default": "./src/forms/public/*.tsx"}, "./forms/users/*": {"types": "./dist/forms/users/*.d.ts", "default": "./src/forms/users/*.tsx"}, "./forms/*": {"types": "./dist/forms/*.d.ts", "default": "./src/forms/*.tsx"}, "./shared/*": {"types": "./dist/shared/*.d.ts", "default": "./src/shared/*.tsx"}, "./shared": {"types": "./dist/shared/index.d.ts", "default": "./src/shared/index.tsx"}, "./pages/*": {"types": "./dist/pages/*.d.ts", "default": "./src/pages/*.tsx"}, "./pages": {"types": "./dist/pages/index.d.ts", "default": "./src/pages/index.tsx"}, "./search/*": {"types": "./dist/search/*.d.ts", "default": "./src/search/*.tsx"}, "./search": {"types": "./dist/search/index.d.ts", "default": "./src/search/index.tsx"}, "./tables/*": {"types": "./dist/tables/*.d.ts", "default": "./src/tables/*.tsx"}, "./tables": {"types": "./dist/tables/index.d.ts", "default": "./src/tables/index.tsx"}, "./styles/*": {"default": "./src/styles/*.css"}, "./styles": {"default": "./src/styles/index.css"}, "./env": {"types": "./dist/env.d.ts", "default": "./src/env.ts"}}, "license": "SEE LICENSE", "scripts": {"build": "tsc", "dev": "tsc --watch", "add": "pnpm dlx shadcn-ui add", "clean": "rm -rf .turbo node_modules dist", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "test": "vitest run --coverage", "test:watch": "vitest --watch", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "ui-add": "pnpm dlx shadcn-ui add && prettier src --write --list-different", "storybook": "storybook dev -p 6006", "storybook:ci": "storybook dev -p 6006 --no-open --ci", "storybook:build": "storybook build"}, "dependencies": {"@clerk/elements": "^0.23.31", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/markerclusterer": "^2.5.3", "@hookform/resolvers": "^4.1.3", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@mantine/rte": "^5.10.5", "@maskito/core": "^2.5.0", "@maskito/kit": "^2.5.0", "@maskito/react": "^2.5.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.6", "@tabler/icons-react": "^3.33.0", "@tanstack/react-table": "^8.21.3", "@uidotdev/usehooks": "^2.4.1", "@vis.gl/react-google-maps": "^1.5.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "cobe": "^0.6.3", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "input-otp": "^1.4.2", "jotai": "^2.12.5", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lucide-react": "^0.511.0", "luxon": "^3.6.1", "next-themes": "^0.4.6", "react-aria": "^3.40.0", "react-aria-components": "^1.9.0", "react-day-picker": "^8.10.1", "react-hook-form": "^7.56.4", "react-icon-cloud": "^4.1.7", "react-merge-refs": "^2.1.1", "react-resizable-panels": "^2.1.9", "react-spring": "^9.7.5", "react-stately": "^3.38.0", "react-tweet": "^3.2.2", "recharts": "^2.15.3", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "timescape": "^0.7.1", "vaul": "^1.1.2"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/storybook-config": "workspace:*", "@axa/tailwind-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@clerk/nextjs": "^6.20.2", "@faker-js/faker": "^9.8.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/canvas-confetti": "^1.9.0", "@types/google.maps": "^3.58.1", "@types/lodash.debounce": "^4.0.9", "@types/lodash.throttle": "^4.1.9", "@types/react": "^19.1.6", "eslint": "^9.28.0", "geist": "^1.4.2", "happy-dom": "^17.5.6", "next": "^15.3.3", "next-router-mock": "^1.0.2", "prettier": "^3.5.3", "react": "19.1.0", "react-dom": "19.1.0", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vitest": "^3.1.4", "zod": "^3.25.42"}, "peerDependencies": {"@clerk/nextjs": "^6.20.2", "geist": "^1.4.2", "next": "^15.3.3", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^3.25.42"}, "prettier": "@axa/prettier-config"}