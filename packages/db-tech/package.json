{"name": "@axa/database-tech", "private": true, "version": "0.1.0", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./prisma/index.ts"}, "./edge": {"types": "./dist/edge.d.ts", "default": "./prisma/edge.ts"}, "./seed": {"types": "./dist/seed.d.ts", "default": "./prisma/seed.ts"}, "./env": {"types": "./dist/env.d.ts", "default": "./prisma/env.ts"}}, "license": "SEE LICENSE", "scripts": {"dev": "pnpm db:generate --watch", "build": "pnpm db:generate && pnpm db:deploy && tsc", "clean": "rm -rf .turbo node_modules prisma/generated dist", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "test": "vitest run --coverage", "test:watch": "vitest --watch", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "db:studio": "prisma studio --port 5557", "db:generate": "prisma generate", "db:push": "prisma db push --preview-feature --skip-generate", "db:seed": "tsx src/seed.ts", "db:reset": "prisma migrate reset --preview-feature", "db:migrate": "prisma migrate dev --preview-feature", "db:deploy": "prisma migrate deploy --preview-feature"}, "dependencies": {"@prisma/client": "^6.8.2", "@t3-oss/env-nextjs": "^0.13.6", "prisma-extension-bark": "^0.2.2", "zod": "^3.25.42"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@types/node": "^22.15.29", "eslint": "^9.28.0", "pg-mem": "^3.0.5", "prettier": "^3.5.3", "prisma": "^6.8.2", "prisma-json-types-generator": "^3.4.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "prettier": "@axa/prettier-config"}