{"name": "@axa/api-medical", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/src/index.d.ts", "default": "./src/index.ts"}, "./actions/*": {"types": "./dist/src/actions/*.d.ts", "default": "./src/actions/*.ts"}, "./functions/*": {"types": "./dist/src/functions/*.d.ts", "default": "./src/functions/*.ts"}, "./hooks/*": {"types": "./dist/src/hooks/*.d.ts", "default": "./src/hooks/*.ts"}, "./jobs/*": {"types": "./dist/src/jobs/*.d.ts", "default": "./src/jobs/*.ts"}, "./utils/*": {"types": "./dist/src/utils/*.d.ts", "default": "./src/utils/*.ts"}, "./constants": {"types": "./dist/src/constants/index.d.ts", "default": "./src/constants/index.ts"}, "./constants/*": {"types": "./dist/src/constants/*.d.ts", "default": "./src/constants/*.ts"}, "./env": {"types": "./dist/src/env.d.ts", "default": "./src/env.ts"}, "./types": {"types": "./src/types.d.ts", "default": "./src/types.ts"}}, "license": "SEE LICENSE", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf .turbo node_modules dist", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../apps/web-med/.env --", "script": "pnpm with-env tsx --", "hot-fix": "pnpm with-env tsx scripts/hot-fix.ts", "test": "pnpm with-env vitest run --coverage", "test:watch": "pnpm with-env vitest --watch"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@axa/database-medical": "workspace:*", "@axa/emails": "workspace:*", "@axa/lib": "workspace:*", "@clerk/nextjs": "^6.20.2", "@documenso/sdk-typescript": "^0.1.3", "@supabase/supabase-js": "^2.49.8", "@t3-oss/env-nextjs": "^0.13.6", "@trpc/server": "^11.1.4", "@turf/turf": "^7.2.0", "@vercel/functions": "^2.1.0", "date-fns": "^4.1.0", "luxon": "^3.6.1", "openai": "^4.104.0", "pdfreader": "^3.0.7", "pino": "^9.7.0", "stripe": "^17.7.0", "superjson": "^2.2.2", "svix": "^1.66.0", "zod": "^3.25.42", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@axa/constants": "workspace:*", "@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@clerk/backend": "^1.34.0", "@clerk/types": "^4.59.3", "eslint": "^9.28.0", "next": "^15.3.3", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "peerDependencies": {"next": "^15.2.4"}, "prettier": "@axa/prettier-config"}