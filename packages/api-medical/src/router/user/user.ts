import { captureException } from "@sentry/nextjs";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Prisma } from "@axa/database-medical";
import { calculateSkip } from "@axa/lib/utils/index";

import type { ProcedureResult } from "../../types/select";

import { roles } from "../../constants";
import { logger } from "../../lib/log";
import {
  adminProcedure,
  authorizedProcedure,
  createTRPCRouter,
  protectedProcedure,
} from "../../trpc";

export type UserRole = (typeof roles)[number];

const selection = {
  person: {
    id: true,
    role: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
    type: true,
    status: true,
    customerId: true,
  },
  provider: {
    id: true,
    status: true,
    verificationStatus: true,
    accountId: true,
    verification: {
      select: {
        status: true,
        backgroundCheckStatus: true,
        i9VerificationStatus: true,
        identityVerificationStatus: true,
      },
    },
  },
  settings: {
    notifications: true,
    appNotifications: true,
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: true,
    contracts: true,
    reports: true,
    messages: true,
    shifts: true,
    jobs: true,
    invoices: true,
    payments: true,
  },
} satisfies {
  person: Prisma.PersonSelect;
  organization: Prisma.OrganizationSelect;
  provider: Prisma.ProviderSelect;
  settings: Prisma.PersonSettingSelect;
};

export const userRouter = createTRPCRouter({
  me: authorizedProcedure.query(async ({ ctx }) => {
    return {
      role: ctx.role,
      id: ctx.user.id,
      firstName: ctx.user.firstName,
      lastName: ctx.user.lastName,
      avatar: ctx.user.imageUrl,
      hasImage: ctx.user.hasImage,
      hasPassword: ctx.user.passwordEnabled,
      mode: ctx.user.publicMetadata.mode as "PROVIDER" | "ORGANIZATION" | null,
      onboarded: ctx.user.publicMetadata.onboarded as boolean | null,
      acceptedTerms: ctx.user.publicMetadata.acceptedTerms as Date | null,
      providerId: ctx.user.publicMetadata.providerId as string | null,
      organizationId: ctx.user.publicMetadata.organizationId as string | null,
      email: ctx.user.emailAddresses
        .map((e) => ({
          id: e.id,
          emailAddress: e.emailAddress,
          status: e.verification?.status,
        }))
        .find((e) => e.id === ctx.user.primaryEmailAddressId),
      phone: ctx.user.phoneNumbers
        .map((e) => ({
          id: e.id,
          phoneNumber: e.phoneNumber,
          status: e.verification?.status,
        }))
        .find((p) => p.id === ctx.user.primaryPhoneNumberId),
      settings: await ctx.prisma.personSetting.findUnique({
        where: { personId: ctx.user.id },
        select: selection.settings,
      }),
      provider: ctx.provider,
      organization: ctx.organization,
    };
  }),

  setRole: authorizedProcedure
    .input(z.object({ role: z.enum(roles) }))
    .mutation(async ({ ctx, input }) => {
      await Promise.all([
        ctx.clerk.users.updateUserMetadata(ctx.user.id, {
          publicMetadata: { role: input.role },
        }),
        ctx.prisma.person.update({
          where: { id: ctx.user.id },
          data: { role: input.role },
        }),
      ]);

      return { id: ctx.user.id, role: input.role };
    }),

  switchMode: authorizedProcedure
    .input(z.object({ mode: z.enum(["PROVIDER", "ORGANIZATION"]) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.clerk.users.updateUserMetadata(ctx.user.id, {
        publicMetadata: { mode: input.mode },
      });

      return { id: ctx.user.id, mode: input.mode };
    }),

  get: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const select = {
        ...selection.person,
        organization: { select: selection.organization },
        provider: { select: selection.provider },
      } satisfies Prisma.PersonSelect;

      const user = await ctx.prisma.person.findUnique({
        where: { id: input.id },
        select,
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return user;
    }),

  getMany: adminProcedure
    .input(
      z.object({
        roles: z.array(z.enum(roles)).optional(),
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const where: Prisma.PersonWhereInput = {
        deletedAt: null,
        role:
          input.roles && input.roles.length > 0
            ? { in: input.roles, not: "NONE" }
            : { not: "NONE" },
        OR: input.query
          ? [
              { firstName: { contains: input.query, mode: "insensitive" } },
              { lastName: { contains: input.query, mode: "insensitive" } },
            ]
          : undefined,
        organizationId:
          (input.organizationId ?? input.organizations)
            ? { in: input.organizations }
            : undefined,
      };

      const select = {
        ...selection.person,
        provider: { select: selection.provider },
        organization: { select: selection.organization },
      };

      const [total, items] = await Promise.all([
        ctx.prisma.person.count({ where }),
        ctx.prisma.person.findMany({
          where,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          select,
        }),
      ]);

      return {
        total,
        items,
      };
    }),

  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        role: z.enum(roles).optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const person = await ctx.prisma.person.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!person) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      const update = {
        role: input.role,
        organizationId: input.organizationId,
      };

      await ctx.prisma.person.update({
        where: {
          id: input.id,
        },
        data: update,
      });

      if (input.organizationId) {
        await ctx.clerk.users.updateUserMetadata(input.id, {
          publicMetadata: {
            organizationId: input.organizationId,
          },
        });
        await ctx.clerk.organizations.createOrganizationMembership({
          userId: input.id,
          organizationId: input.organizationId,
          role: "org:member",
        });
      }

      return { id: input.id };
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      await ctx.clerk.users.deleteUser(input.id);
      await ctx.prisma.person.update({
        where: { id: input.id },
        data: { deletedAt: new Date() },
      });
      return { id: input.id };
    }),

  updateNotificationSettings: protectedProcedure
    .input(
      z.object({
        notifications: z.boolean().optional(),
        appNotifications: z.boolean().optional(),
        emailNotifications: z.boolean().optional(),
        pushNotifications: z.boolean().optional(),
        smsNotifications: z.boolean().optional(),
        contracts: z.boolean().optional(),
        reports: z.boolean().optional(),
        messages: z.boolean().optional(),
        shifts: z.boolean().optional(),
        jobs: z.boolean().optional(),
        invoices: z.boolean().optional(),
        payments: z.boolean().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { user, prisma } = ctx;

      try {
        await prisma.personSetting.upsert({
          where: { personId: user.id },
          update: input,
          create: {
            ...input,
            person: {
              connect: {
                id: user.id,
              },
            },
          },
          select: {
            id: true,
          },
        });

        return { success: true };
      } catch (error) {
        logger.error("Failed to update notification settings:", error);

        // eslint-disable-next-line no-restricted-properties
        if (process.env.NODE_ENV === "production") {
          captureException(error);
        }

        return { success: false };
      }
    }),
});
