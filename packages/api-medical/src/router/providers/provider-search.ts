import { z } from "zod";

import type { Prisma } from "@axa/database-medical";
import { ProviderStatus, VerificationStatus } from "@axa/database-medical";

import { createTRPCRouter, protectedProcedure } from "../../trpc";

const ProviderSearchInputSchema = z.object({
  // Text search across multiple fields
  query: z.string().optional(),

  // Filter by professional role/position
  role: z.string().optional(),

  // Filter by join date (days ago)
  joinDate: z.enum(["30", "90", "180"]).optional(),

  // Additional criteria filters
  criteria: z.enum(["verified", "available", "high-rated"]).optional(),

  // Pagination
  pageNumber: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(50).default(9), // 3-column layout default

  // Sorting options
  sortBy: z.enum(["createdAt", "score", "name"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const providerSearchRouter = createTRPCRouter({
  search: protectedProcedure
    .input(ProviderSearchInputSchema)
    .query(async ({ input, ctx }) => {
      const {
        query,
        role,
        joinDate,
        criteria,
        pageNumber,
        pageSize,
        sortBy,
        sortOrder,
      } = input;

      // Build the where clause for comprehensive filtering
      const where: Prisma.ProviderWhereInput = {
        // Remove hardcoded status filter - show all providers by default
        // Only filter by status if explicitly needed for specific criteria
        AND: [], // Use AND array to combine multiple filters properly
      };

      // Text search across provider names, titles, and specialties
      if (query?.trim()) {
        const searchTerm = query.trim();
        (where.AND as any[]).push({
          OR: [
            // Search in person's first and last name
            {
              person: {
                OR: [
                  { firstName: { contains: searchTerm, mode: "insensitive" } },
                  { lastName: { contains: searchTerm, mode: "insensitive" } },
                ],
              },
            },
            // Search in provider title
            {
              title: { contains: searchTerm, mode: "insensitive" },
            },
            // Search in specialties
            {
              specialties: {
                some: {
                  name: { contains: searchTerm, mode: "insensitive" },
                },
              },
            },
            // Search in experience roles for broader coverage
            {
              experiences: {
                some: {
                  role: { contains: searchTerm, mode: "insensitive" },
                },
              },
            },
          ],
        });
      }

      // Filter by professional role/position - search in both title and experience roles
      if (role?.trim()) {
        (where.AND as any[]).push({
          OR: [
            // Search in provider title
            { title: { contains: role, mode: "insensitive" } },
            // Also search in experience roles for broader matching
            {
              experiences: {
                some: {
                  role: { contains: role, mode: "insensitive" },
                },
              },
            },
          ],
        });
      }

      // Filter by join date (providers who joined within specified days)
      if (joinDate) {
        const daysAgo = parseInt(joinDate);
        const dateThreshold = new Date();
        dateThreshold.setDate(dateThreshold.getDate() - daysAgo);

        (where.AND as any[]).push({
          createdAt: {
            gte: dateThreshold,
          },
        });
      }

      // Additional criteria filters - only apply when explicitly requested
      if (criteria) {
        switch (criteria) {
          case "verified":
            // Only filter for verified providers when explicitly requested
            (where.AND as any[]).push({
              verification: {
                status: VerificationStatus.APPROVED,
              },
            });
            break;
          case "available":
            // Only filter for available providers when explicitly requested
            (where.AND as any[]).push({
              settings: {
                openToWork: true,
              },
            });
            break;
          case "high-rated":
            // Only filter for high-rated providers when explicitly requested
            (where.AND as any[]).push({
              score: {
                gte: 80, // Providers with score >= 80 considered high-rated
              },
            });
            break;
        }
      }

      // If no filters were added, remove the empty AND array
      if ((where.AND as any[]).length === 0) {
        delete where.AND;
      }

      // Build the orderBy clause
      let orderBy: any;
      switch (sortBy) {
        case "createdAt":
          orderBy = { createdAt: sortOrder };
          break;
        case "score":
          orderBy = { score: sortOrder };
          break;
        case "name":
          orderBy = { person: { firstName: sortOrder } };
          break;
        default:
          orderBy = { createdAt: "desc" };
      }

      // Calculate pagination
      const skip = (pageNumber - 1) * pageSize;

      // Execute the queries in parallel for performance
      const [total, providers] = await Promise.all([
        // Count total matching providers
        ctx.prisma.provider.count({
          where,
        }),

        // Fetch the providers with all necessary data
        ctx.prisma.provider.findMany({
          where,
          include: {
            // Basic person information (name, avatar)
            person: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                email: false, // Privacy compliance - no contact info
                phone: false, // Privacy compliance - no contact info
              },
            },
            // Location information (city/state only for privacy)
            address: {
              select: {
                id: true,
                city: true, // Individual city field for flexible formatting
                state: true, // Individual state field for flexible formatting
                latitude: false, // Privacy compliance - no exact coordinates
                longitude: false, // Privacy compliance - no exact coordinates
                timeZone: true,
              },
            },
            // Provider specialties
            specialties: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            // Work experiences for years of experience calculation
            experiences: {
              select: {
                id: true,
                role: true,
                company: true,
                startDate: true,
                endDate: true,
                description: false, // Not needed for card view
              },
              orderBy: {
                startDate: "desc",
              },
              take: 5, // Limit to recent experiences for performance
            },
            // Verification status - include all verification records
            verification: {
              select: {
                id: true,
                status: true,
                verifiedAt: true,
                // Don't include detailed verification fields for privacy
                backgroundCheckStatus: false,
                i9VerificationStatus: false,
                identityVerificationStatus: false,
              },
            },
            // Provider settings (availability) - include all settings
            settings: {
              select: {
                id: true,
                openToWork: true,
                openToOnCall: true,
                // Don't include other private settings
                openToRelocate: false,
                openToTravel: false,
              },
            },
            // Don't include sensitive data
            qualifications: false,
            reviews: false,
            shifts: false,
            schedules: false,
            calendar: false,
          },
          orderBy,
          skip,
          take: pageSize,
        }),
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / pageSize);
      const hasNextPage = pageNumber < totalPages;
      const hasPreviousPage = pageNumber > 1;

      // Transform the data to include calculated fields
      const transformedProviders = providers.map((provider) => {
        // Calculate years of experience
        const yearsOfExperience = provider.experiences.reduce((years, exp) => {
          const startDate = new Date(exp.startDate);
          const endDate = exp.endDate ? new Date(exp.endDate) : new Date();
          const experienceYears =
            (endDate.getTime() - startDate.getTime()) /
            (1000 * 60 * 60 * 24 * 365.25);
          return years + Math.max(0, experienceYears);
        }, 0);

        // Get the most recent experience
        const latestExperience = provider.experiences[0];

        return {
          ...provider,
          // Add calculated fields
          yearsOfExperience: Math.round(yearsOfExperience * 10) / 10, // Round to 1 decimal
          latestExperience: latestExperience
            ? {
                role: latestExperience.role,
                company: latestExperience.company,
              }
            : null,
          // Format join date for display
          joinedDate: provider.createdAt,
          // Calculate if provider is recently joined (within 90 days)
          isRecentlyJoined:
            (new Date().getTime() - provider.createdAt.getTime()) /
              (1000 * 60 * 60 * 24) <=
            90,
        };
      });

      return {
        items: transformedProviders,
        pagination: {
          total,
          totalPages,
          currentPage: pageNumber,
          pageSize,
          hasNextPage,
          hasPreviousPage,
        },
        // Provide search metadata
        searchMetadata: {
          query: query || null,
          role: role || null,
          joinDate: joinDate || null,
          criteria: criteria || null,
          sortBy,
          sortOrder,
          resultCount: total,
        },
      };
    }),
});
