import type { Organization, PersonRole } from "@axa/database-medical";
import { OrganizationType } from "@axa/database-medical";

import { adminProcedure, createTRPCRouter } from "../../trpc";

export const clerkRouter = createTRPCRouter({
  loadClerk: adminProcedure.mutation(async ({ ctx }) => {
    const users = await ctx.clerk.users.getUserList();
    const organizations = await ctx.clerk.organizations.getOrganizationList();

    return { users: users.data, organizations: organizations.data };
  }),
  syncClerkUsers: adminProcedure.mutation(async ({ ctx }) => {
    const users = await ctx.clerk.users.getUserList();

    for (const user of users.data) {
      const primaryEmail = user.primaryEmailAddress?.emailAddress;
      const primaryPhone = user.primaryPhoneNumber?.phoneNumber;
      const role = user.publicMetadata.role as PersonRole;
      const userId = user.id;
      const organizationId = user.publicMetadata.organizationId as
        | string
        | undefined;
      const providerId = user.publicMetadata.providerId as string | undefined;

      const provider = providerId
        ? await ctx.prisma.provider.findUnique({
            where: {
              id: providerId,
            },
            select: {
              id: true,
            },
          })
        : undefined;
      const organization = organizationId
        ? await ctx.prisma.organization.findUnique({
            where: {
              id: organizationId,
            },
            select: {
              id: true,
            },
          })
        : undefined;

      await ctx.prisma.person.upsert({
        where: {
          email: primaryEmail,
        },
        update: {
          id: userId,
          role,
          firstName: user.firstName ?? "",
          lastName: user.lastName ?? "",
          email: primaryEmail,
          phone: primaryPhone,
          avatar: user.imageUrl,
          organization: organization
            ? {
                connect: {
                  id: organization.id,
                },
              }
            : undefined,
          provider: provider
            ? {
                connect: {
                  id: provider.id,
                },
              }
            : undefined,
        },
        create: {
          id: userId,
          role,
          firstName: user.firstName ?? "",
          lastName: user.lastName ?? "",
          email: primaryEmail,
          phone: primaryPhone,
          avatar: user.imageUrl,
          organization: organization
            ? {
                connect: {
                  id: organization.id,
                },
              }
            : undefined,
          provider: provider
            ? {
                connect: {
                  id: provider.id,
                },
              }
            : undefined,
        },
      });
    }

    return true;
  }),
  syncClerkOrganizations: adminProcedure.mutation(async ({ ctx }) => {
    const organizations = await ctx.clerk.organizations.getOrganizationList();

    const orgs = organizations.data.map((org) => ({
      id: org.id,
      managerId: org.publicMetadata?.managerId,
      parentId: org.publicMetadata?.parentId,
      type: org.publicMetadata?.type,
    }));
    // order organizations by depth
    const clients = organizations.data.filter(
      (org) => org.publicMetadata?.managerId === null,
    );
    const accounts = organizations.data
      .filter((org) => org.publicMetadata?.managerId !== null)
      .sort((a, b) => {
        const aParent = orgs.find((o) => o.id === a.publicMetadata?.parentId);
        const bParent = orgs.find((o) => o.id === b.publicMetadata?.parentId);
        if (!aParent || !bParent) {
          return 0;
        }
        if (a.id === bParent.id) {
          return -1;
        } else if (a.id === aParent.id) {
          return -1;
        } else if (aParent.id === b.id) {
          return -1;
        } else if (b.id === bParent.id) {
          return -1;
        }

        if (aParent.id === bParent.id) {
          return 0;
        }

        return 1;
      });

    for (const organization of [...clients, ...accounts]) {
      const managerId = organization.publicMetadata?.managerId as
        | string
        | undefined;
      const parentId = organization.publicMetadata?.parentId as
        | string
        | undefined;
      const type =
        (organization.publicMetadata?.type as OrganizationType | undefined) ??
        (managerId ? OrganizationType.ACCOUNT : OrganizationType.CLIENT);

      let org = (await ctx.prisma.organization.findUnique({
        where: {
          id: organization.id,
        },
      })) as Organization | null;

      if (org) {
        org = (await ctx.prisma.organization.update({
          where: {
            id: organization.id,
          },
          data: {
            name: organization.name,
            avatar: organization.imageUrl,
            type,
            manager: managerId
              ? {
                  connect: {
                    id: managerId,
                  },
                }
              : undefined,
          },
        })) as unknown as Organization;
      } else {
        if (parentId) {
          org = (await ctx.prisma.organization.createChild({
            where: {
              id: parentId,
            },
            data: {
              id: organization.id,
              name: organization.name,
              avatar: organization.imageUrl,
              type,
              manager: managerId
                ? {
                    connect: {
                      id: managerId,
                    },
                  }
                : undefined,
            },
          })) as Organization;
        } else {
          org = (await ctx.prisma.organization.createRoot({
            data: {
              id: organization.id,
              name: organization.name,
              avatar: organization.imageUrl,
              type,
              manager: managerId
                ? {
                    connect: {
                      id: managerId,
                    },
                  }
                : undefined,
            },
          })) as Organization;
        }
      }

      // finally link the organization to the users
      const users = await ctx.prisma.person.findMany({
        where: {
          organizationId: org.id,
        },
      });

      const members =
        await ctx.clerk.organizations.getOrganizationMembershipList({
          organizationId: org.id,
        });

      for (const member of members.data) {
        const userId = member.publicUserData?.userId;
        if (!userId) {
          continue;
        }

        const user = users.find((u) => u.id === userId);
        if (!user) {
          continue;
        }
      }

      for (const user of users) {
        const member = members.data.find(
          (m) => m.publicUserData?.userId === user.id,
        );
        if (!member) {
          await ctx.clerk.organizations.createOrganizationMembership({
            organizationId: org.id,
            userId: user.id,
            role: "org:admin",
          });
        }
      }
    }

    return true;
  }),
});
