import medicalRoles from "@axa/constants/medical-roles";
import medicalSpecialties from "@axa/constants/medical-specialties";

import { adminProcedure, createTRPCRouter } from "../../trpc";

export const systemRouter = createTRPCRouter({
  initialize: adminProcedure.mutation(async ({ ctx }) => {
    await ctx.prisma.$transaction(
      Object.keys(medicalRoles).map((name) =>
        ctx.prisma.value.upsert({
          where: {
            id: `medical-role-${name.toLowerCase().replace(/ /g, "-")}`,
          },
          update: {
            type: "MEDICAL_ROLE",
            value: name,
          },
          create: {
            id: `medical-role-${name.toLowerCase().replace(/ /g, "-")}`,
            type: "MEDICAL_ROLE",
            value: name,
          },
        }),
      ),
    );

    await ctx.prisma.$transaction(
      Object.values(medicalSpecialties).map(({ name, description }) =>
        ctx.prisma.specialty.upsert({
          where: {
            name,
          },
          update: {
            name,
            description,
          },
          create: {
            name,
            description,
          },
        }),
      ),
    );
  }),
  purge: adminProcedure.mutation(async ({ ctx }) => {
    await ctx.prisma.value.deleteMany();
    await ctx.prisma.specialty.deleteMany();
  }),
});
