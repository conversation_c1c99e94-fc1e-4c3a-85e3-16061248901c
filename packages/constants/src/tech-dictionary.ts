export interface ValueStore {
  type: string;
  key: string;
  value: string;
}

export const contactTypes = [
  "Contact",
  "On-Site",
  "Account Manager",
  "Technical",
  "Billing",
].map((type) => ({
  type: "CONTACT",
  key: `contact-${type}`,
  value: type,
})) as ValueStore[];

export const expenseTypes = ["Dispatch", "Travel", "Cleaning Supplies"].map(
  (type) => ({
    type: "EXPENSE",
    key: `expense-${type}`,
    value: type,
  }),
) as ValueStore[];
