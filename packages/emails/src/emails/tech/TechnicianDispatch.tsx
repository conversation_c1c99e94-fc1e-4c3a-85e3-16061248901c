import {
  Container,
  Heading,
  Link,
  Section,
  Text,
} from "@react-email/components";

// import { transformPhoneNumber } from "@/ui/fields/numeric/PhoneNumber";

import EmailLayout, {
  <PERSON><PERSON><PERSON><PERSON>,
  EmailFooter,
} from "../../templates/EmailLayout";

/*
  usage:
  
  const email = renderEmail<TechnicianDispatchEmailProps>(
    TechnicianDispatchEmail,
    {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      baseUrl: env.PUBLIC_URL!,
      organization: order.organization,
      firstName: provider.firstName!,
      lastName: provider.lastName!,
      address: order.location?.address.formatted!,
      emergency: order.emergency,
      category: order.category,
      type: order.type,
      summary: order.summary,
      scope: order.scope,
      hours: shift.hours,
      startTime: shiftTime.toFormat("PPP 'at' t"),
      contacts,
      orderId: order.id,
    },
  );
 */

export interface TechnicianDispatchEmailProps {
  baseUrl: string;
  summary: string;
  scope: string;
  firstName: string;
  lastName: string;
  address: string;
  category: string;
  type: string;
  emergency: boolean;
  hours: number;
  startTime: string;
  date: Date;
  dateLink?: string;
  contacts?: {
    role: string;
    name: string;
    phone?: string;
    email?: string;
  }[];
  organization?: {
    name: string;
    avatar?: string | null;
  };
  body?: string;
}

export default function TechnicianDispatchEmail({
  baseUrl = "https://tech.axapro.com",
  firstName,
  lastName,
  startTime,
  date,
  dateLink,
  summary,
  scope,
  address,
  contacts,
  organization,
  body,
}: TechnicianDispatchEmailProps) {
  const previewText = `You have been assigned to a work order for ${organization?.name}.`;
  return (
    <EmailLayout preview={previewText}>
      <Container className="mx-auto my-10 max-w-[465px] rounded-lg border border-solid border-[#eaeaea] p-5">
        <EmailBrand baseUrl={baseUrl} />

        <Heading className="mx-0 my-[30px] p-0 text-center text-[24px] font-normal text-black">
          Work Order for <strong>{organization?.name}</strong>
        </Heading>
        <Text className="text-[14px] leading-[24px] text-black">
          Hi {firstName} {lastName},
        </Text>

        {body ? (
          <div className="my-4" dangerouslySetInnerHTML={{ __html: body }} />
        ) : (
          <>
            <Text>
              Thank you for accepting this work order. Attached to this email is
              your time sheet if you would kindly fill it out and attach it to
              your work order at the end of the service.
            </Text>

            <Text>
              Upon arrival at the client location, please contact your work
              order manager and let them know you have arrived onsite.
            </Text>

            <ul>
              <li>
                <Text>
                  Valid drivers license or government issued ID - electronic IDs
                  are <strong>NOT ACCEPTED</strong> onsite
                </Text>
              </li>
            </ul>
          </>
        )}

        <Section className="my-4">
          <Heading className="mx-0 my-5 p-0 text-[16px] text-black">
            Date
          </Heading>
          <Text>
            {dateLink ? (
              <Link className="text-[#299487]" href={dateLink}>
                <time dateTime={date.toISOString()}>{startTime}</time>
              </Link>
            ) : (
              <time dateTime={date.toISOString()}>{startTime}</time>
            )}
          </Text>

          <Heading className="mx-0 my-5 p-0 text-[16px] text-black">
            Location
          </Heading>
          <Link
            className="text-[#299487]"
            href={`https://maps.google.com/?q=${encodeURIComponent(address)}`}
          >
            <Text>{address}</Text>
          </Link>

          <Heading className="mx-0 my-5 p-0 text-[16px] text-black">
            Contacts
          </Heading>
          {contacts?.map((contact, i) => (
            <Text key={i}>
              {contact.role}: {contact.name}{" "}
              {/* {contact.phone && (
                <Link href={`tel:${contact.phone}`}>
                  {transformPhoneNumber(contact.phone)}
                </Link>
              )} */}
            </Text>
          ))}

          <Heading className="mx-0 my-5 p-0 text-[16px] text-black">
            Scope of Work
          </Heading>
          <Text className="font-bold">{summary}</Text>
          <Text>{scope}</Text>
        </Section>

        <Section className="my-4">
          <Heading className="mx-0 my-5 p-0 text-[16px] text-black">
            Code of Conduct
          </Heading>
          <Text>
            By accepting this work order you agree to the terms below. Failure
            to comply will result in immediate termination of your contract.
          </Text>
          <Text>
            <strong>NO</strong> weapons of any kind are permitted onsite, even
            concealed.
          </Text>
          <Text>
            <strong>DO NOT</strong> under any circumstances take any pictures
            onsite - even if you are asked to, we are not permitted to take any
            pictures on the premise.
          </Text>
          <Text>
            <strong>DO NOT</strong> bring or substitute any other person onsite
            without prior written approval or they will be{" "}
            <strong>considered trespassing</strong>.
          </Text>
          <Text>
            Please be <strong>professional at all times</strong>, as client
            locations are under constant video surveillance.
          </Text>
        </Section>

        <Text>
          <span>Thank you,</span>
          <br />
          <span>AXA Professionals</span>
        </Text>

        <EmailFooter firstName={firstName} lastName={lastName} />
      </Container>
    </EmailLayout>
  );
}
