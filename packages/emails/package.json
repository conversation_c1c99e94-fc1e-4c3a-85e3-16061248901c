{"name": "@axa/emails", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/src/index.d.ts", "default": "./src/index.ts"}, "./emails/*": {"types": "./dist/src/emails/*.d.ts", "default": "./src/emails/*.tsx"}, "./emails/tech/*": {"types": "./dist/src/emails/tech/*.d.ts", "default": "./src/emails/tech/*.tsx"}, "./emails/med/*": {"types": "./dist/src/emails/med/*.d.ts", "default": "./src/emails/med/*.tsx"}, "./pdfs": {"types": "./dist/src/pdfs/index.d.ts", "default": "./src/pdfs/index.tsx"}, "./pdfs/*": {"types": "./dist/src/pdfs/*.d.ts", "default": "./src/pdfs/*.tsx"}, "./templates/*": {"types": "./dist/src/templates/*.d.ts", "default": "./src/templates/*.tsx"}}, "license": "SEE LICENSE", "scripts": {"build": "tsc", "build:preview": "email build", "dev:preview": "email dev --port 3005", "export": "email export", "clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "storybook": "storybook dev -p 6007", "storybook:ci": "storybook dev -p 6007 --no-open --ci", "storybook:build": "storybook build", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@axa/ui": "workspace:*", "@react-email/components": "0.0.41", "@react-email/tailwind": "^1.0.5", "@react-pdf/layout": "^4.4.0", "@react-pdf/renderer": "^4.3.0", "calendar-link": "^2.10.0", "class-variance-authority": "^0.7.1", "react-pdf-tailwind": "^2.3.0"}, "devDependencies": {"@axa/constants": "workspace:*", "@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/storybook-config": "workspace:*", "@axa/tailwind-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@storybook/react": "^9.0.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "eslint": "^9.28.0", "jest": "^29.7.0", "prettier": "^3.5.3", "react": "19.1.0", "react-dom": "19.1.0", "storybook": "^9.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "zod": "^3.25.42"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1", "zod": "^3.23.7"}, "prettier": "@axa/prettier-config"}