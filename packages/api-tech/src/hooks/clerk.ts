import type { Email<PERSON><PERSON><PERSON>, WebhookEvent } from "@clerk/nextjs/server";
import type { NextRequest } from "next/server";

import { clerkClient } from "@clerk/nextjs/server";
import { captureException } from "@sentry/nextjs";
import { Webhook } from "svix";

import { resend } from "@axa/lib/resend";
import { sendTextMessage } from "@axa/lib/twilio";

import type { UserRole } from "../constants";

import { roles } from "../constants";
import { env } from "../env";
import { prisma } from "../lib/prisma";

export async function POST(req: NextRequest) {
  const WEBHOOK_SECRET = env.CLERK_WEBHOOK_SECRET;

  if (!WEBHOOK_SECRET) {
    throw new Error(
      "Please add WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local",
    );
  }

  const svix_id = req.headers.get("svix-id");
  const svix_timestamp = req.headers.get("svix-timestamp");
  const svix_signature = req.headers.get("svix-signature");

  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occurred -- no svix headers", {
      status: 400,
    });
  }

  const payload = await req.json();
  const body = JSON.stringify(payload);

  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    captureException(err);
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  try {
    await handleClerkEvent(evt);
  } catch (err) {
    captureException(err);
    console.error("Error handling clerk event:", err);
  }

  return new Response("", { status: 200 });
}

async function handleClerkEvent(evt: WebhookEvent) {
  const client = await clerkClient();

  switch (evt.type) {
    default:
      break;
    case "sms.created": {
      if (evt.data.delivered_by_clerk !== true) {
        await sendTextMessage(evt.data.to_phone_number, evt.data.message);
      }
      break;
    }
    case "email.created": {
      const data: EmailJSON = evt.data;
      if (evt.data.delivered_by_clerk !== true) {
        await resend.emails.send({
          from: "AXA Tech <<EMAIL>>",
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          to: data.to_email_address!,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          html: data.body!,
          subject: `[AXA Tech] ${data.subject ?? ""}`,
        });
      }
      break;
    }
    case "organizationMembership.created": {
      const user = await client.users.getUser(
        evt.data.public_user_data.user_id,
      );
      const role = user.publicMetadata.role as UserRole | undefined;

      if (roles.includes(role! ?? "") === false) {
        await client.users.updateUserMetadata(
          evt.data.public_user_data.user_id,
          {
            publicMetadata: {
              role: "CLIENT",
            },
          },
        );
      }
      break;
    }
    case "user.created": {
      const email = evt.data.email_addresses[0]?.email_address;

      let phone = evt.data.phone_numbers[0]?.phone_number;
      let role = evt.data.public_metadata.role as UserRole | undefined;
      let organizationId = evt.data.public_metadata.organizationId as
        | string
        | null;

      const memberships = await client.users.getOrganizationMembershipList({
        userId: evt.data.id,
      });

      if (
        email?.endsWith("@axapro.com") ||
        ["ADMIN", "BILLING", "INTERNAL"].includes(role! ?? "")
      ) {
        role ??= "INTERNAL";
        organizationId = null;
      } else {
        if (memberships.data.length > 0) {
          role = "CLIENT";
          organizationId = memberships.data[0]?.organization.id ?? null;
        }
      }

      if (role === "CLIENT") {
        if (!organizationId) {
          throw new Error("User created without organization");
        }
      }

      const existingPerson = await prisma.person.findFirst({
        where: {
          OR: [{ email }],
        },
      });

      if (existingPerson?.isUser && existingPerson.email === email) {
        throw new Error("User already exists");
      }

      if (existingPerson?.isUser && existingPerson.phone === phone) {
        phone = undefined;
      } else if (existingPerson && phone && existingPerson.phone === phone) {
        await prisma.person.update({
          where: {
            id: existingPerson.id,
          },
          data: {
            phone: null,
          },
        });
      }

      await prisma.person.upsert({
        where: {
          email,
          isUser: false,
        },
        update: {
          id: evt.data.id,
          role,
          email,
          phone,
          isUser: true,
          avatar: evt.data.image_url,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          firstName: evt.data.first_name!,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          lastName: evt.data.last_name!,
          preferences: {
            notification: {
              orders: true,
              messages: true,
              reports: true,
            },
          },
          organization: organizationId
            ? {
                connect: {
                  id: organizationId,
                },
              }
            : undefined,
        },
        create: {
          role,
          email,
          phone,
          isUser: true,
          id: evt.data.id,
          avatar: evt.data.image_url,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          firstName: evt.data.first_name!,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          lastName: evt.data.last_name!,
          preferences: {
            notification: {
              orders: true,
              messages: true,
              reports: true,
            },
          },
          organization: organizationId
            ? {
                connect: {
                  id: organizationId,
                },
              }
            : undefined,
        },
      });

      await client.users.updateUserMetadata(evt.data.id, {
        publicMetadata: {
          role,
          organizationId,
          acceptedTerms: null,
          onboarded: false,
        },
      });

      break;
    }
    case "user.updated": {
      await prisma.person.update({
        where: {
          id: evt.data.id,
        },
        data: {
          avatar: evt.data.image_url,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          firstName: evt.data.first_name!,
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          lastName: evt.data.last_name!,
          email: evt.data.email_addresses[0]?.email_address,
          phone: evt.data.phone_numbers[0]?.phone_number,
        },
      });
      break;
    }
    case "user.deleted": {
      await prisma.person.update({
        where: {
          id: evt.data.id,
        },
        data: {
          role: "NONE",
          isUser: false,
          deletedAt: new Date(),
        },
      });
      break;
    }
  }
}
