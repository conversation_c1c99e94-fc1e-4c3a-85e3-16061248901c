import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Location, Prisma, Schedule, Shift } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";
import { getTimeFromInteger } from "@axa/lib/utils/time";

import onWorkOrderEvent from "../jobs/work-order-event";
import { createShiftDateAndTime } from "../lib/dates";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const zScheduleType = z.enum(["SHORT_TERM", "LONG_TERM", "ONE_TIME"]);
export const zShiftType = z.enum([
  "SERVICE_WINDOW",
  "SERVICE_DATE",
  "SERVICE_TERM",
]);
export const zShiftStatus = z.enum(["OPEN", "CLOSED", "ASSIGNED", "COMPLETED"]);

const selections = {
  schedule: {
    id: true,
    type: true,
    name: true,
    description: true,
    timeZone: true,
  },
  shift: {
    id: true,
    hours: true,
    type: true,
    status: true,
    date: true,
    startDate: true,
    endDate: true,
    startTime: true,
    endTime: true,
    timeZone: true,
    scheduleId: true,
  },
} satisfies {
  schedule: Partial<Record<keyof Schedule, boolean>>;
  shift: Partial<Record<keyof Shift, boolean>>;
};

export const shiftsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        include: z
          .object({
            schedule: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const shift = await ctx.prisma.shift.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.shift,
          schedule:
            input.include?.schedule === true
              ? {
                  select: selections.schedule,
                }
              : undefined,
        },
      });

      if (!shift) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Shift not found",
        });
      }

      return {
        ...shift,
        startTime: getTimeFromInteger(shift.startTime),
        endTime: getTimeFromInteger(shift.endTime),
      };
    }),
  getMany: protectedProcedure
    .input(
      z
        .object({
          scheduleId: z.string(),
          pageSize: z.number().optional(),
          pageNumber: z.number().optional(),
        })
        .optional(),
    )
    .query(async ({ input = {}, ctx }) => {
      const query = {
        scheduleId: input.scheduleId,
      } as Prisma.ShiftFindManyArgs["where"];

      const [count, shifts] = await Promise.all([
        ctx.prisma.shift.count({
          where: query,
        }),
        ctx.prisma.shift.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          where: query,
          select: selections.shift,
        }),
      ]);

      return {
        shifts: shifts.map((shift) => ({
          ...shift,
          startTime: getTimeFromInteger(shift.startTime),
          endTime: getTimeFromInteger(shift.endTime),
        })),
        total: count,
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        type: zShiftType.optional(),
        timeZone: z.string().optional(),
        status: zShiftStatus.optional(),
        hours: z.number().int().positive().optional(),
        date: z.date().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        startTime: z
          .object({
            hour: z.number(),
            minute: z.number(),
            second: z.number().optional(),
            millisecond: z.number().optional(),
          })
          .optional(),
        endTime: z
          .object({
            hour: z.number(),
            minute: z.number(),
            second: z.number().optional(),
            millisecond: z.number().optional(),
          })
          .optional(),
        scheduleId: z.string(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const shift = await ctx.prisma.shift.create({
        data: {
          ...createShiftDateAndTime({
            timeZone: input.timeZone,
            type: input.type,
            date: input.date,
            startDate: input.startDate,
            endDate: input.endDate,
            startTime: input.startTime,
            endTime: input.endTime,
            hours: input.hours,
          }),
          schedule: {
            connect: {
              id: input.scheduleId,
            },
          },
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          project: input.projectId
            ? {
                connect: {
                  id: input.projectId,
                },
              }
            : undefined,
          template: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
        },
        select: selections.shift,
      });

      const transactions = [];

      if (input.orderId) {
        const order = await ctx.prisma.workOrder.findUniqueOrThrow({
          where: {
            id: input.orderId,
          },
          select: {
            id: true,
            invoiceId: true,
            technicians: {
              select: {
                id: true,
                paymentRate: true,
                paymentType: true,
                billingRate: true,
                billingType: true,
              },
            },
          },
        });

        if (order.technicians.length > 0) {
          transactions.push(
            ...order.technicians.map((technician) =>
              ctx.prisma.technician.update({
                where: {
                  id: technician.id,
                },
                data: {
                  shifts: {
                    connect: {
                      id: shift.id,
                    },
                  },
                  timeSheets: {
                    create: {
                      order: {
                        connect: {
                          id: order.id,
                        },
                      },
                      invoice: {
                        connect: {
                          id: order.invoiceId,
                        },
                      },
                      shift: {
                        connect: {
                          id: shift.id,
                        },
                      },
                      hours: shift.hours,
                      billingRate: technician.billingRate,
                      billingType: technician.billingType,
                      paymentRate: technician.paymentRate,
                      paymentType: technician.paymentType,
                      total:
                        technician.billingType === "HOURLY"
                          ? technician.billingRate * shift.hours
                          : technician.billingRate,
                    },
                  },
                },
              }),
            ),
          );
        }
      }

      await ctx.prisma.$transaction(transactions);

      if (input.orderId) {
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "SCHEDULE",
          meta: {
            id: shift.id,
            mode: "ADD",
          },
        });
      }

      return {
        id: shift.id,
      };
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        type: zShiftType.optional(),
        status: zShiftStatus.optional(),
        hours: z.number().int().positive().optional(),
        timeZone: z.string().optional(),
        date: z.date().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        startTime: z
          .object({
            hour: z.number(),
            minute: z.number(),
            second: z.number().optional(),
            millisecond: z.number().optional(),
          })
          .optional(),
        endTime: z
          .object({
            hour: z.number(),
            minute: z.number(),
            second: z.number().optional(),
            millisecond: z.number().optional(),
          })
          .optional(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      let location: Pick<Location, "timeZone"> | null = null;

      console.log("shift input", input);

      if (input.orderId) {
        const order = await ctx.prisma.workOrder.findUniqueOrThrow({
          where: {
            id: input.orderId,
          },
          select: {
            location: {
              select: {
                timeZone: true,
              },
            },
          },
        });
        location = order.location;
      }

      console.log(
        "shift date",
        createShiftDateAndTime({
          timeZone: location?.timeZone ?? input.timeZone ?? "",
          type: (input.type ?? input.date) ? "SERVICE_DATE" : "SERVICE_WINDOW",
          date: input.date,
          startDate: input.startDate,
          endDate: input.endDate,
          startTime: input.startTime,
          endTime: input.endTime,
          hours: input.hours,
        }),
      );

      const shift = await ctx.prisma.shift.update({
        where: {
          id: input.id,
        },
        data: createShiftDateAndTime({
          timeZone: location?.timeZone ?? input.timeZone ?? "",
          type: (input.type ?? input.date) ? "SERVICE_DATE" : "SERVICE_WINDOW",
          date: input.date,
          startDate: input.startDate,
          endDate: input.endDate,
          startTime: input.startTime,
          endTime: input.endTime,
          hours: input.hours,
        }),
        select: {
          ...selections.shift,
          timeSheets: {
            select: {
              id: true,
              billingRate: true,
              billingType: true,
              hours: true,
              expenses: {
                select: {
                  amount: true,
                },
              },
            },
          },
        },
      });

      const transactions = [];

      if (input.orderId) {
        if (shift.timeSheets.length > 0) {
          transactions.push(
            ...shift.timeSheets.map((timeSheet) =>
              ctx.prisma.timeSheet.update({
                where: {
                  id: timeSheet.id,
                },
                data: {
                  total:
                    (timeSheet.billingType === "HOURLY"
                      ? timeSheet.billingRate * shift.hours
                      : timeSheet.billingRate) +
                    timeSheet.expenses.reduce(
                      (acc, expense) => acc + expense.amount,
                      0,
                    ),
                  hours: shift.hours,
                },
              }),
            ),
          );
        }
      }

      await ctx.prisma.$transaction(transactions);

      if (input.orderId) {
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "SCHEDULE",
          meta: {
            id: shift.id,
            mode: "UPDATE",
          },
        });
      }

      return {
        id: shift.id,
      };
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.shift.update({
        where: {
          id: input.id,
        },
        data: {
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          project: input.projectId
            ? {
                connect: {
                  id: input.projectId,
                },
              }
            : undefined,
          template: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
        },
        select: selections.shift,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.shift.update({
        where: {
          id: input.id,
        },
        data: {
          order: input.orderId
            ? {
                disconnect: true,
              }
            : undefined,
          project: input.projectId
            ? {
                disconnect: true,
              }
            : undefined,
          template: input.templateId
            ? {
                disconnect: true,
              }
            : undefined,
        },
        select: selections.shift,
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.shift.delete({
          where: {
            id: input.id,
          },
          select: selections.shift,
        });
      }

      return ctx.prisma.shift.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.shift,
      });
    }),
});

export const schedulesRouter = createTRPCRouter({
  shifts: shiftsRouter,
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        include: z
          .object({
            shifts: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const schedule = await ctx.prisma.schedule.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.schedule,
          shifts: input.include?.shifts
            ? {
                select: selections.shift,
              }
            : undefined,
        },
      });

      if (!schedule) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Schedule not found",
        });
      }

      return {
        ...schedule,
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        shifts: schedule.shifts?.map((shift) => ({
          ...shift,
          startTime: getTimeFromInteger(shift.startTime),
          endTime: getTimeFromInteger(shift.endTime),
        })),
      };
    }),
  getMany: protectedProcedure
    .input(
      z
        .object({
          query: z.string().optional(),
          pageSize: z.number().optional(),
          pageNumber: z.number().optional(),
          include: z
            .object({
              shifts: z.boolean().optional(),
            })
            .optional(),
        })
        .optional(),
    )
    .query(async ({ input = {}, ctx }) => {
      const query = {
        name: input.query
          ? {
              contains: input.query,
            }
          : undefined,
      } as Prisma.ScheduleFindManyArgs["where"];

      const [count, schedules] = await Promise.all([
        ctx.prisma.schedule.count({
          where: query,
        }),
        ctx.prisma.schedule.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          where: query,
          select: {
            ...selections.schedule,
            shifts: input.include?.shifts
              ? {
                  select: selections.shift,
                }
              : undefined,
          },
        }),
      ]);

      return {
        schedules: schedules.map((schedule) => ({
          ...schedule,
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
          shifts: schedule.shifts?.map((shift) => ({
            ...shift,
            startTime:
              typeof shift.startTime === "number"
                ? {
                    hour: Math.floor(shift.startTime / 60),
                    minute: shift.startTime % 60,
                  }
                : undefined,
            endTime:
              typeof shift.endTime === "number"
                ? {
                    hour: Math.floor(shift.endTime / 60),
                    minute: shift.endTime % 60,
                  }
                : undefined,
          })),
        })),
        total: count,
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        type: zScheduleType.optional(),
        name: z.string().optional(),
        description: z.string().optional(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.schedule.create({
        data: {
          type: input.type,
          name: input.name,
          description: input.description,
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          project: input.projectId
            ? {
                connect: {
                  id: input.projectId,
                },
              }
            : undefined,
          template: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
        },
        select: selections.schedule,
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        type: zScheduleType.optional(),
        name: z.string().optional(),
        description: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.schedule.update({
        where: {
          id: input.id,
        },
        data: {
          type: input.type,
          name: input.name,
          description: input.description,
        },
        select: selections.schedule,
      });
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.schedule.update({
        where: {
          id: input.id,
        },
        data: {
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          project: input.projectId
            ? {
                connect: {
                  id: input.projectId,
                },
              }
            : undefined,
          template: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
        },
        select: selections.schedule,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        orderId: z.string().optional(),
        projectId: z.string().optional(),
        templateId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.schedule.update({
        where: {
          id: input.id,
        },
        data: {
          order: input.orderId
            ? {
                disconnect: true,
              }
            : undefined,
          project: input.projectId
            ? {
                disconnect: true,
              }
            : undefined,
          template: input.templateId
            ? {
                disconnect: true,
              }
            : undefined,
        },
        select: selections.schedule,
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.schedule.delete({
          where: {
            id: input.id,
          },
          select: selections.schedule,
        });
      }

      return ctx.prisma.schedule.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.schedule,
      });
    }),
});
