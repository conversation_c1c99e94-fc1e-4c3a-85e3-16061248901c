import { captureException } from "@sentry/nextjs";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { WorkOrderStatus } from "@axa/database-tech";

import {
  getNotification,
  getNotifications,
  updateNotification,
} from "../../lib/queries";
import { createTRPCRouter, protectedProcedure } from "../../trpc";

export const userNotificationsRouter = createTRPCRouter({
  getMany: protectedProcedure
    .input(
      z
        .object({
          types: z.array(z.enum(["ORDER", "MESSAGE", "ALERT"])).optional(),
          read: z.boolean().optional(),
        })
        .optional(),
    )
    .query(async ({ input, ctx }) => {
      try {
        const notifications = (
          await Promise.all(
            (input?.types ?? ["ORDER", "MESSAGE", "ALERT"]).map((type) =>
              getNotifications(ctx.user.id, type, input?.read),
            ),
          )
        )
          .flat()
          .map((notification) => {
            return {
              id: notification.id,
              type: notification.data.type,
              message: notification.data.message,
              read: notification.data.read,
              created: notification.data.created,
              meta: {
                status: notification.data.status as WorkOrderStatus | undefined,
                link: notification.data.link as string | undefined,
                orderId: notification.data.orderId as string | undefined,
                messageId: notification.data.messageId as string | undefined,
              },
            };
          });

        return notifications;
      } catch (error) {
        console.error(error);
        captureException(
          new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to get notifications",
          }),
        );
        return [];
      }
    }),
  markRead: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const notification = await getNotification(input.id);

      if (!notification) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Notification not found",
        });
      }

      await updateNotification(input.id, {
        ...notification.data,
        read: true,
      });

      return true;
    }),
  markAllAsRead: protectedProcedure
    .input(
      z
        .object({
          types: z.array(z.enum(["ORDER", "MESSAGE", "ALERT"])).optional(),
        })
        .optional(),
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const notifications = (
          await Promise.all(
            (input?.types ?? ["ORDER", "MESSAGE", "ALERT"]).map((type) =>
              getNotifications(ctx.user.id, type, false),
            ),
          )
        ).flat();

        await Promise.all(
          notifications.map((notification) =>
            updateNotification(notification.id, {
              ...notification.data,
              read: true,
            }),
          ),
        );

        return true;
      } catch (e) {
        console.error(e);
        captureException(
          new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to mark all notifications as read",
          }),
        );
        return false;
      }
    }),
});
