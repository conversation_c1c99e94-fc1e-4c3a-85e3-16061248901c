import { z } from "zod";

import { createTRPCRouter, protectedProcedure } from "../../trpc";

export const userMembershipsRouter = createTRPCRouter({
  getMany: protectedProcedure.query(async ({ ctx }) => {
    const memberships = await ctx.clerk.users.getOrganizationMembershipList({
      userId: ctx.user.id,
    });

    return memberships.data.map((membership) => ({
      id: membership.id,
      role: membership.role,
      organization: {
        id: membership.organization.id,
        name: membership.organization.name,
        avatar: membership.organization.imageUrl,
      },
    }));
  }),
  changeOrganization: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.person.update({
        where: { id: ctx.user.id },
        data: {
          organization: {
            connect: {
              id: input.organizationId,
            },
          },
        },
      });

      await ctx.clerk.users.updateUserMetadata(ctx.user.id, {
        publicMetadata: {
          organizationId: input.organizationId,
        },
      });
      return true;
    }),
});
