import { mergeRouters } from "@trpc/server/unstable-core-do-not-import";

import { createTRPCRouter } from "../../trpc";
import { internalUserRouter } from "./internal";
import { userInvitationsRouter } from "./invitations";
import { userMembershipsRouter } from "./memberships";
import { userNotificationsRouter } from "./notifications";
import { userOnboardingRouter } from "./onboarding";
import { userRouter as user } from "./user";

export const userRouter = mergeRouters(
  mergeRouters(user, internalUserRouter),
  createTRPCRouter({
    memberships: userMembershipsRouter,
    invitations: userInvitationsRouter,
    notifications: userNotificationsRouter,
    onboarding: userOnboardingRouter,
  }),
);
