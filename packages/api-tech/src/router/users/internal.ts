import { captureException } from "@sentry/nextjs";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils/index";

import { roles } from "../../constants";
import {
  adminProcedure,
  createTRPCRouter,
  internalProcedure,
  protectedProcedure,
} from "../../trpc";

export const internalUserRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const user = await ctx.prisma.person.findUnique({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          role: true,
          isUser: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          avatar: true,
          organization: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          accounts: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return {
        ...user,
        organization:
          user.organization ??
          (["ADMIN", "BILLING", "INTERNAL"].includes(user.role)
            ? {
                id: "AXA",
                name: "AXA Professionals",
                avatar: null,
              }
            : {
                id: "AXA-UNKNOWN",
                name: "Unknown",
                avatar: null,
              }),
      };
    }),
  getMany: internalProcedure
    .input(
      z.object({
        roles: z.array(z.enum(roles)).optional(),
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const search = input.query?.replace(/\s+/g, " ").trim() ?? "";
      const searchQuery: Prisma.ProviderWhereInput = {};

      if (search) {
        const splitQuery = search.split(" ");

        if (splitQuery.length > 1) {
          searchQuery.AND = splitQuery.map((word) => ({
            OR: [
              {
                firstName: {
                  contains: word,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: word,
                  mode: "insensitive",
                },
              },
              {
                email: {
                  contains: word,
                  mode: "insensitive",
                },
              },
            ],
          }));
        } else {
          searchQuery.OR = [
            {
              firstName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              lastName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              email: {
                contains: search,
                mode: "insensitive",
              },
            },
          ];
        }
      }

      const query = {
        deletedAt: null,
        isUser: true,
        ...searchQuery,
        role:
          input.roles && input.roles.length > 0
            ? {
                in: input.roles,
              }
            : undefined,
        organizationId:
          input.organizationId ??
          (input.organizations
            ? {
                in: input.organizations,
              }
            : undefined),
      } as Prisma.PersonFindManyArgs["where"];

      const [users, total] = await Promise.all([
        await ctx.prisma.person.findMany({
          where: query,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          select: {
            id: true,
            isUser: true,
            role: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            avatar: true,
            organization: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
        }),
        ctx.prisma.person.count({
          where: query,
        }),
      ]);

      return {
        total,
        users: users.map((person) => ({
          ...person,
          organization:
            person.organization ??
            (["ADMIN", "BILLING", "INTERNAL"].includes(person.role)
              ? {
                  id: "AXA",
                  name: "AXA Professionals",
                  avatar: null,
                }
              : {
                  id: "AXA-UNKNOWN",
                  name: "Unknown",
                  avatar: null,
                }),
        })),
      };
    }),
  delete: internalProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.clerk.users.deleteUser(input.id);
      await ctx.prisma.person.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
          isUser: false,
          role: "NONE",
        },
      });
      return true;
    }),
  linkAccountManager: adminProcedure
    .input(
      z.object({
        id: z.string(),
        accountId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.person.update({
        where: {
          id: input.id,
        },
        data: {
          accounts: {
            connect: {
              id: input.accountId,
            },
          },
        },
      });
    }),
  unlinkAccountManager: adminProcedure
    .input(
      z.object({
        id: z.string(),
        accountId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.person.update({
        where: {
          id: input.id,
        },
        data: {
          accounts: {
            disconnect: {
              id: input.accountId,
            },
          },
        },
      });
    }),
  changeUserRole: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        role: z.enum(roles),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const isInternal = ["INTERNAL", "BILLING", "ADMIN"].includes(input.role);
      const isClient = input.role === "CLIENT";

      if (isClient) {
        if (!input.organizationId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Organization ID is required for AXA clients",
          });
        }

        const person = await ctx.prisma.person.findUnique({
          where: { id: input.userId },
        });

        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain
        if (!person || !person.organizationId) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Person not found",
          });
        }

        try {
          const membership =
            await ctx.clerk.organizations.deleteOrganizationMembership({
              organizationId: person.organizationId,
              userId: input.userId,
            });

          await ctx.clerk.organizations.createOrganizationMembership({
            organizationId: input.organizationId,
            userId: input.userId,
            role: membership.role,
          });
        } catch (error) {
          captureException(error);
        }

        await ctx.prisma.person.update({
          where: { id: input.userId },
          data: {
            role: input.role,
            organization: input.organizationId
              ? {
                  connect: {
                    id: input.organizationId,
                  },
                }
              : undefined,
          },
        });
      } else {
        await ctx.prisma.person.update({
          where: {
            id: input.userId,
          },
          data: {
            role: input.role,
            organization: isInternal
              ? {
                  disconnect: true,
                }
              : undefined,
          },
        });
      }

      await ctx.clerk.users.updateUserMetadata(input.userId, {
        publicMetadata: {
          role: input.role,
          organizationId: isInternal ? null : input.organizationId,
        },
      });

      return true;
    }),
  changeUserOrganization: adminProcedure
    .input(
      z.object({
        userId: z.string(),
        organizationId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      await ctx.clerk.users.updateUserMetadata(input.userId, {
        publicMetadata: {
          organizationId: input.organizationId,
        },
      });
      return true;
    }),
});
