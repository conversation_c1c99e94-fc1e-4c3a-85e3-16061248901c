import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Person, Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import {
  accountsProcedure,
  createTRPCRouter,
  internalProcedure,
} from "../../trpc";

export const billingRouter = createTRPCRouter({
  get: accountsProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!input.organizationId && !ctx.organization?.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      const orgId = input.organizationId ?? ctx.organization?.id;

      if (ctx.accounts && ctx.accounts.includes(orgId ?? "") === false) {
        if (ctx.isBilling) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message:
              "Organization is not part of your managed accounts - Please contact the administrators if you need access to this organization",
          });
        }

        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Organization not found",
        });
      }

      const organization = await ctx.prisma.organization.findUniqueOrThrow({
        where: {
          id: orgId,
        },
        select: {
          id: true,
          balance: true,
          threshold: true,
          manager: {
            select: {
              id: true,
              name: true,
              avatar: true,
              balance: true,
            },
          },
        },
      });

      return {
        manager: organization.manager
          ? {
              id: organization.manager.id,
              name: organization.manager.name,
              avatar: organization.manager.avatar,
            }
          : null,
        threshold: organization.threshold ?? 0,
        balance: organization.balance ?? 0,
      };
    }),
  getAccounts: accountsProcedure
    .input(
      z.object({
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const accounts = await ctx.prisma.organization.findMany({
        skip: calculateSkip({
          pageSize: input.pageSize,
          pageNumber: input.pageNumber,
        }),
        take: input.pageSize ?? 10,
        where: {
          deletedAt: null,
          name: input.query
            ? {
                contains: input.query,
                mode: "insensitive",
              }
            : undefined,
          id: ctx.accounts ? { in: ctx.accounts } : undefined,
        },
        select: {
          id: true,
          type: true,
          name: true,
          avatar: true,
          managerId: true,
        },
      });
      return { accounts };
    }),
  getStatements: accountsProcedure
    .input(
      z.object({
        organizationId: z.string(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        type: z.enum(["FUNDING", "PAYMENT", "FEE"]).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const orgId = input.organizationId ?? ctx.organization?.id;

      if (!orgId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      if (ctx.accounts && ctx.accounts.includes(orgId) === false) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Organization not found",
        });
      }

      const query = {
        deletedAt: null,
        organizationId: input.organizationId,
        type: input.type,
        period: {
          gte: input.startDate,
          lte: input.endDate,
        },
      } as Prisma.StatementFindManyArgs["where"];

      const [count, statements] = await Promise.all([
        ctx.prisma.statement.count({
          where: query,
        }),
        ctx.prisma.statement.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          select: {
            id: true,
            type: true,
            createdAt: true,
            createdBy: true,
            balance: true,
            period: true,
            actor: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
              },
            },
          },
        }),
      ]);

      const organization = await ctx.prisma.organization.findUniqueOrThrow({
        where: {
          id: input.organizationId,
        },
        select: {
          id: true,
          name: true,
          avatar: true,
        },
      });

      type UserPartial = Pick<
        Person,
        "id" | "firstName" | "lastName" | "avatar"
      >;
      const users = new Map<string, UserPartial>([
        [
          "SYSTEM",
          {
            id: "SYSTEM",
            firstName: "SYSTEM",
            lastName: "",
            avatar: "",
          },
        ],
      ]);

      return {
        organization,
        statements: await Promise.all(
          statements.map(async (statement) => {
            let user: UserPartial;

            if (statement.actor) {
              user = {
                id: statement.actor.id,
                firstName: statement.actor.firstName,
                lastName: statement.actor.lastName,
                avatar: statement.actor.avatar ?? "",
              };
            } else if (users.has(statement.createdBy ?? "")) {
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              user = users.get(statement.createdBy ?? "")!;
            } else {
              user = await ctx.prisma.person.findUniqueOrThrow({
                where: {
                  id: statement.createdBy!,
                },
              });
              users.set(statement.createdBy!, user);
            }
            return {
              ...statement,
              createdBy: {
                id: user.id,
                name: user.firstName + " " + user.lastName,
                avatar: user.avatar,
              },
            };
          }),
        ),
        total: count,
      };
    }),
  addStatement: accountsProcedure
    .input(
      z.object({
        organizationId: z.string(),
        balance: z.number(),
        type: z.enum(["FUNDING", "PAYMENT", "FEE"]),
        date: z.date(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const orgId = input.organizationId ?? ctx.organization?.id;

      if (!orgId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      if (ctx.accounts && ctx.accounts.includes(orgId) === false) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Organization not found",
        });
      }

      const statement = await ctx.prisma.statement.create({
        data: {
          createdBy: ctx.user.id,
          balance: input.balance,
          type: input.type,
          period: input.date,
          notes: input.notes,
          organization: {
            connect: {
              id: input.organizationId,
            },
          },
          actor: {
            connect: {
              id: ctx.user.id,
            },
          },
        },
      });

      if (["FUNDING", "PAYMENT"].includes(input.type)) {
        await ctx.prisma.organization.update({
          where: {
            id: input.organizationId,
          },
          data: {
            balance:
              input.type === "FUNDING"
                ? {
                    increment: input.balance,
                  }
                : {
                    decrement: input.balance,
                  },
          },
        });
      }

      return statement;
    }),
  updateBillingSettings: accountsProcedure
    .input(
      z.object({
        organizationId: z.string(),
        threshold: z.number(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.organization.update({
        where: {
          id: input.organizationId,
        },
        data: {
          threshold: input.threshold,
        },
      });
    }),
  createCancellationFee: internalProcedure
    .input(
      z.object({
        orderId: z.string(),
        amount: z.number(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUniqueOrThrow({
        where: {
          id: input.orderId,
        },
        select: {
          id: true,
          organizationId: true,
          invoiceId: true,
        },
      });

      const statement = await ctx.prisma.statement.create({
        data: {
          balance: input.amount,
          type: "FEE",
          period: new Date(),
          notes: input.notes ?? "cancelation fee",
          actor: {
            connect: {
              id: ctx.user.id,
            },
          },
          organization: {
            connect: {
              id: order.organizationId,
            },
          },
          invoice: {
            connect: {
              id: order.invoiceId,
            },
          },
          order: {
            connect: {
              id: order.id,
            },
          },
        },
      });

      return statement;
    }),
});
