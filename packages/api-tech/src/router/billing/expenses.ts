import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Expense, Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import {
  adminProcedure,
  billingProcedure,
  createTRPCRouter,
  protectedProcedure,
} from "../../trpc";

const selections = {
  expense: {
    id: true,
    type: true,
    amount: true,
    notes: true,
  },
} satisfies {
  expense: Partial<Record<keyof Expense, boolean>>;
};

export const expensesRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const expense = await ctx.prisma.expense.findUnique({
        where: {
          id: input.id,
        },
        select: selections.expense,
      });

      if (!expense) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Expense not found",
        });
      }

      return expense;
    }),
  getMany: protectedProcedure
    .input(
      z.object({
        timeSheetId: z.string().nullable().optional(),
        orderId: z.string().nullable().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const query = {
        deletedAt: null,
        timeSheetId: input.timeSheetId,
        orderId: input.orderId,
      } as Prisma.ExpenseFindManyArgs["where"];

      const [count, expenses] = await Promise.all([
        ctx.prisma.expense.count({
          where: query,
        }),
        ctx.prisma.expense.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          select: {
            ...selections.expense,
          },
        }),
      ]);

      return { expenses, total: count };
    }),
  create: billingProcedure
    .input(
      z.object({
        orderId: z.string().optional(),
        timeSheetId: z.string().optional(),
        type: z.string(),
        amount: z.number(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const expense = await ctx.prisma.expense.create({
        data: {
          createdBy: ctx.user.id,
          type: input.type,
          amount: input.amount,
          notes: input.notes,
          actor: {
            connect: {
              id: ctx.user.id,
            },
          },
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          timeSheet: input.timeSheetId
            ? {
                connect: {
                  id: input.timeSheetId,
                },
              }
            : undefined,
        },
        select: selections.expense,
      });

      if (input.timeSheetId) {
        await ctx.prisma.timeSheet.update({
          where: {
            id: input.timeSheetId,
          },
          data: {
            total: {
              increment: input.amount,
            },
          },
        });
      }

      return expense;
    }),
  update: billingProcedure
    .input(
      z.object({
        id: z.string(),
        type: z.string().optional(),
        amount: z.number().optional(),
        notes: z.string().optional(),
        orderId: z.string().optional(),
        timeSheetId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { amount: previousAmount } =
        await ctx.prisma.expense.findUniqueOrThrow({
          where: {
            id: input.id,
          },
          select: {
            amount: true,
          },
        });

      const expense = await ctx.prisma.expense.update({
        where: {
          id: input.id,
        },
        data: {
          type: input.type,
          amount: input.amount,
          notes: input.notes,
          order: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          timeSheet: input.timeSheetId
            ? {
                connect: {
                  id: input.timeSheetId,
                },
              }
            : undefined,
        },
        select: {
          ...selections.expense,
          timeSheetId: true,
        },
      });

      if (previousAmount !== expense.amount) {
        if (expense.timeSheetId) {
          let increment = undefined;
          let decrement = undefined;

          const difference = expense.amount - previousAmount;

          if (difference > 0) {
            increment = difference;
          } else {
            decrement = Math.abs(difference);
          }

          await ctx.prisma.timeSheet.update({
            where: {
              id: expense.timeSheetId,
            },
            data: {
              total: {
                increment,
                decrement,
              },
            },
          });
        }
      }

      return {
        id: expense.id,
      };
    }),
  delete: adminProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const expense = await ctx.prisma.expense.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          amount: true,
          timeSheetId: true,
        },
      });

      const transactions = [];

      if (expense.timeSheetId) {
        transactions.push(
          ctx.prisma.timeSheet.update({
            where: {
              id: expense.timeSheetId,
            },
            data: {
              total: {
                decrement: expense.amount,
              },
            },
          }),
        );
      }

      if (ctx.options.permanentDelete) {
        transactions.push(
          ctx.prisma.expense.delete({
            where: {
              id: input.id,
            },
            select: selections.expense,
          }),
        );
      } else {
        transactions.push(
          ctx.prisma.expense.update({
            where: {
              id: input.id,
            },
            data: {
              deletedAt: new Date(),
            },
            select: selections.expense,
          }),
        );
      }

      await ctx.prisma.$transaction(transactions);

      return {
        id: expense.id,
      };
    }),
});
