import { createTRPCRouter } from "../trpc";
import { adminRouter } from "./admin";
import {
  billingRouter,
  expensesRouter,
  invoicesRouter,
  statementsRouter,
  timeSheetsRouter,
} from "./billing";
import {
  contactsRouter,
  documentsRouter,
  locationsRouter,
  peopleRouter,
  valuesRouter,
} from "./core";
import { ordersRouter, projectsRouter, templatesRouter } from "./orders";
import {
  invitationsRouter,
  membersRouter,
  organizationsRouter,
} from "./organizations";
import { providerRouter, techniciansRouter } from "./providers";
import { schedulesRouter } from "./scheduling";
import { userRouter } from "./users";

export const appRouter = createTRPCRouter({
  admin: adminRouter,
  schedule: schedulesRouter,
  providers: providerRouter,
  technicians: techniciansRouter,
  organizations: organizationsRouter,
  members: membersRouter,
  invitations: invitationsRouter,
  billing: billingRouter,
  statements: statementsRouter,
  invoices: invoicesRouter,
  timeSheets: timeSheetsRouter,
  expenses: expensesRouter,
  documents: documentsRouter,
  orders: ordersRouter,
  templates: templatesRouter,
  projects: projectsRouter,
  people: peopleRouter,
  contacts: contactsRouter,
  locations: locationsRouter,
  user: userRouter,
  values: valuesRouter,
});

export type AppRouter = typeof appRouter;
