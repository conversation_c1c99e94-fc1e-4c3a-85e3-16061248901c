import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

export const membersRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        limit: z.number().optional(),
        offset: z.number().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const members =
        await ctx.clerk.organizations.getOrganizationMembershipList({
          organizationId: input.organizationId,
          limit: input.limit,
          offset: input.offset,
        });

      return {
        totalCount: members.totalCount,
        data: members.data.map((member) => ({
          id: member.id,
          role: member.role,
          user: {
            id: member.publicUserData?.userId,
            avatar: member.publicUserData?.imageUrl,
            firstName: member.publicUserData?.firstName,
            lastName: member.publicUserData?.lastName,
          },
        })),
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        userId: z.string(),
        role: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { user, isInternal } = ctx;

      if (!isInternal) {
        const members =
          await ctx.clerk.organizations.getOrganizationMembershipList({
            organizationId: input.organizationId,
          });

        const member = members.data.find(
          (member) => member.publicUserData?.userId === user.id,
        );

        if (!member && !ctx.organizations.includes(input.organizationId)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "You are not authorized to create members in this organization",
          });
        } else if (member?.role !== "org:admin") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "You are not authorized to create members in this organization",
          });
        }
      }

      const member = await ctx.clerk.organizations.createOrganizationMembership(
        {
          organizationId: input.organizationId,
          userId: input.userId,
          role: input.role,
        },
      );

      return {
        id: member.id,
        role: member.role,
        user: {
          id: member.publicUserData?.userId,
          avatar: member.publicUserData?.imageUrl,
          firstName: member.publicUserData?.firstName,
          lastName: member.publicUserData?.lastName,
        },
      };
    }),
  update: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        userId: z.string(),
        role: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { user, isInternal } = ctx;

      if (!isInternal) {
        const members =
          await ctx.clerk.organizations.getOrganizationMembershipList({
            organizationId: input.organizationId,
          });

        const member = members.data.find(
          (member) => member.publicUserData?.userId === user.id,
        );

        if (!member && !ctx.organizations.includes(input.organizationId)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You are not authorized to update this membership",
          });
        } else if (member?.role !== "org:admin") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You are not authorized to update this membership",
          });
        }
      }

      const member = await ctx.clerk.organizations.updateOrganizationMembership(
        {
          organizationId: input.organizationId,
          userId: input.userId,
          role: input.role,
        },
      );

      return {
        id: member.id,
        role: member.role,
        user: {
          id: member.publicUserData?.userId,
          avatar: member.publicUserData?.imageUrl,
          firstName: member.publicUserData?.firstName,
          lastName: member.publicUserData?.lastName,
        },
      };
    }),
  delete: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        userId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { user, isInternal } = ctx;

      if (!isInternal) {
        const members =
          await ctx.clerk.organizations.getOrganizationMembershipList({
            organizationId: input.organizationId,
          });

        const member = members.data.find(
          (member) => member.publicUserData?.userId === user.id,
        );

        if (!member && !ctx.organizations.includes(input.organizationId)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You are not authorized to delete this membership",
          });
        } else if (member?.role !== "org:admin") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You are not authorized to delete this membership",
          });
        }
      }

      const member = await ctx.clerk.organizations.deleteOrganizationMembership(
        {
          userId: input.userId,
          organizationId: input.organizationId,
        },
      );

      return {
        id: member.id,
      };
    }),
});
