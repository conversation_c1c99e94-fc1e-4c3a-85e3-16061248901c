import type { OrganizationInvitationStatus } from "@clerk/types";

import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { createURL } from "@axa/lib/utils/url";

import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

export const invitationsRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        limit: z.number().optional(),
        offset: z.number().optional(),
        status: z
          .enum(["accepted", "pending", "revoked", "expired"])
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const invitations =
        await ctx.clerk.organizations.getOrganizationInvitationList({
          organizationId: input.organizationId,
          limit: input.limit,
          offset: input.offset,
          status: (input.status
            ? [input.status]
            : ["pending"]) as OrganizationInvitationStatus[],
        });
      return {
        count: invitations.totalCount,
        data: invitations.data.map((invitation) => ({
          id: invitation.id,
          emailAddress: invitation.emailAddress,
          role: invitation.role,
          status: invitation.status as string,
          createdAt: invitation.createdAt,
          updatedAt: invitation.updatedAt,
        })),
      };
    }),
  invite: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        email: z.string().email(),
        role: z.string(),
        message: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { user, isInternal } = ctx;

      const members =
        await ctx.clerk.organizations.getOrganizationMembershipList({
          organizationId: input.organizationId,
        });

      const isMember = members.data.find(
        (member) => member.publicUserData?.userId === user.id,
      );
      if (!isInternal) {
        if (!isMember && !ctx.organizations.includes(input.organizationId)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "You are not authorized to invite users in this organization",
          });
        } else if (isMember?.role !== "org:admin") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "You are not authorized to invite users in this organization",
          });
        }
      } else {
        if (!isMember) {
          await ctx.clerk.organizations.createOrganizationMembership({
            organizationId: input.organizationId,
            userId: ctx.user.id,
            role: "org:admin",
          });
        }
      }

      const invitation =
        await ctx.clerk.organizations.createOrganizationInvitation({
          organizationId: input.organizationId,
          emailAddress: input.email,
          role: input.role,
          inviterUserId: ctx.user.id,
          redirectUrl: createURL("sign-up").toString(),
          publicMetadata: {
            role: "CLIENT",
            organizationId: input.organizationId,
            invitedBy: ctx.user.id,
            message: input.message,
          },
        });

      return {
        id: invitation.id,
        emailAddress: invitation.emailAddress,
        role: invitation.role,
        status: invitation.status as string,
        createdAt: invitation.createdAt,
        updatedAt: invitation.updatedAt,
      };
    }),
  revoke: organizationProcedure
    .input(
      z.object({
        organizationId: z.string(),
        invitationId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { user, isInternal } = ctx;

      const members =
        await ctx.clerk.organizations.getOrganizationMembershipList({
          organizationId: input.organizationId,
        });

      const isMember = members.data.find(
        (member) => member.publicUserData?.userId === user.id,
      );
      if (!isInternal) {
        if (!isMember && !ctx.organizations.includes(input.organizationId)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "You are not authorized to revoke invitations in this organization",
          });
        } else if (isMember?.role !== "org:admin") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message:
              "You are not authorized to revoke invitations in this organization",
          });
        }
      } else {
        if (!isMember) {
          await ctx.clerk.organizations.createOrganizationMembership({
            organizationId: input.organizationId,
            userId: ctx.user.id,
            role: "org:admin",
          });
        }
      }

      const invitation =
        await ctx.clerk.organizations.revokeOrganizationInvitation({
          organizationId: input.organizationId,
          invitationId: input.invitationId,
          requestingUserId: ctx.user.id,
        });

      return {
        id: invitation.id,
      };
    }),
});
