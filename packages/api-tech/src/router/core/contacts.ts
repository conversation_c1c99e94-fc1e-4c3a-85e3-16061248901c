import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Contact, Organization, Person, Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import onWorkOrderEvent from "../../jobs/work-order-event";
import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

const selections = {
  person: {
    id: true,
    role: true,
    isUser: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
  contact: {
    id: true,
    role: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
} satisfies {
  person: Partial<Record<keyof Person, boolean>>;
  contact: Partial<Record<keyof Contact, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const contactsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const contact = await ctx.prisma.contact.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.contact,
          person: {
            select: selections.person,
          },
          organization: {
            select: selections.organization,
          },
        },
      });

      if (!contact) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Contact not found",
        });
      }

      return {
        ...contact,
        person: {
          ...contact.person,
        },
      };
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        showDeleted: z.boolean().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const search = input.query?.replace(/\s+/g, " ").trim() ?? "";
      const searchQuery: Prisma.ProviderWhereInput = {};

      if (search) {
        const splitQuery = search.split(" ");

        if (splitQuery.length > 1) {
          searchQuery.AND = splitQuery.map((word) => ({
            OR: [
              {
                firstName: {
                  contains: word,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: word,
                  mode: "insensitive",
                },
              },
              {
                email: {
                  contains: word,
                  mode: "insensitive",
                },
              },
            ],
          }));
        } else {
          searchQuery.OR = [
            {
              firstName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              lastName: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              email: {
                contains: search,
                mode: "insensitive",
              },
            },
          ];
        }
      }

      const query = {
        deletedAt,
        ...searchQuery,
        templates: input.templateId
          ? {
              equals: input.templateId,
            }
          : undefined,
        orders: input.orderId
          ? {
              equals: input.orderId,
            }
          : undefined,
        organizationId:
          (!input.templateId && !input.orderId) ||
          (organizations && organizations.length > 0)
            ? {
                in: organizations,
              }
            : undefined,
      } as Prisma.ContactFindManyArgs["where"];

      const [total, contacts] = await Promise.all([
        ctx.prisma.contact.count({
          where: query,
        }),
        ctx.prisma.contact.findMany({
          where: query,
          take: input.pageSize,
          skip: calculateSkip({
            pageNumber: input.pageNumber,
            pageSize: input.pageSize,
          }),
          select: {
            ...selections.contact,
            person: {
              select: selections.person,
            },
            organization: {
              select: selections.organization,
            },
          },
        }),
      ]);

      return {
        contacts: contacts.map((contact) => ({
          ...contact,
          person: {
            ...contact.person,
          },
        })),
        total,
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        personId: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
        role: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId = input.organizationId ?? ctx.organization?.id ?? "";

      const contact = await ctx.prisma.contact.create({
        select: selections.contact,
        data: {
          role: input.role,
          person: {
            connect: {
              id: input.personId,
            },
          },
          organization: input.organizationId
            ? {
                connect: {
                  id: organizationId,
                },
              }
            : undefined,
          templates: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
      });

      if (input.orderId) {
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "CONTACT",
          meta: {
            id: contact.id,
            role: contact.role ?? "",
            mode: "ADD",
          },
        });
      }

      return contact;
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        role: z.string().optional(),
        personId: z.string().optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const contact = await ctx.prisma.contact.update({
        where: {
          id: input.id,
        },
        data: {
          role: input.role,
          person: input.personId
            ? {
                connect: {
                  id: input.personId,
                },
              }
            : undefined,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: {
          ...selections.contact,
          orders: {
            select: {
              id: true,
            },
          },
        },
      });

      if (contact.orders.length > 0) {
        for (const order of contact.orders) {
          await onWorkOrderEvent({
            actorId: ctx.user.id,
            orderId: order.id,
            action: "UPDATE",
            resource: "CONTACT",
            meta: {
              id: contact.id,
              role: contact.role ?? "",
              mode: "UPDATE",
            },
          });
        }
      }

      return contact;
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.contact.update({
        where: {
          id: input.id,
        },
        data: {
          templates: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
        select: selections.contact,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.contact.update({
        where: {
          id: input.id,
        },
        data: {
          templates: input.templateId
            ? {
                disconnect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                disconnect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
        select: selections.contact,
      });
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const contact = await ctx.prisma.contact.delete({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          role: true,
          orders: {
            select: {
              id: true,
            },
          },
        },
      });

      if (contact.orders.length > 0) {
        for (const order of contact.orders) {
          await onWorkOrderEvent({
            actorId: ctx.user.id,
            orderId: order.id,
            action: "UPDATE",
            resource: "CONTACT",
            meta: {
              id: contact.id,
              role: contact.role ?? "",
              mode: "REMOVE",
            },
          });
        }
      }

      return {
        id: contact.id,
      };
    }),
});
