import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { Document, Organization, Prisma } from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import onWorkOrderEvent from "../../jobs/work-order-event";
import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

const selections = {
  document: {
    id: true,
    name: true,
    description: true,
    type: true,
    size: true,
    url: true,
    fileId: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
} satisfies {
  document: Partial<Record<keyof Document, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const documentsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const document = await ctx.prisma.document.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.document,
          organization: {
            select: selections.organization,
          },
        },
      });

      if (!document) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Document not found",
        });
      }

      return document;
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        query: z.string().optional(),
        showDeleted: z.boolean().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const query = {
        deletedAt,
        name: input.query
          ? {
              contains: input.query,
              mode: "insensitive",
            }
          : undefined,
        organizationId:
          organizations && organizations.length > 0
            ? {
                in: organizations,
              }
            : undefined,
      } as Prisma.DocumentFindManyArgs["where"];

      const [count, documents] = await Promise.all([
        ctx.prisma.document.count({
          where: query,
        }),
        ctx.prisma.document.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          select: {
            ...selections.document,
            organization: {
              select: selections.organization,
            },
          },
        }),
      ]);

      return { documents, total: count };
    }),
  create: organizationProcedure
    .input(
      z.object({
        fileId: z.string(),
        name: z.string(),
        type: z.string(),
        size: z.number(),
        url: z.string(),
        description: z.string().optional(),
        organizationId: z.string().optional(),
        orderId: z.string().optional(),
        templateId: z.string().optional(),
        locationId: z.string().optional(),
        invoiceId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId = input.organizationId ?? ctx.organization?.id ?? "";

      const document = await ctx.prisma.document.create({
        data: {
          fileId: input.fileId,
          description: input.description,
          name: input.name,
          type: input.type,
          size: input.size,
          url: input.url,
          userId: ctx.user.id,
          organization: {
            connect: {
              id: organizationId,
            },
          },
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          templates: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
          locations: input.locationId
            ? {
                connect: {
                  id: input.locationId,
                },
              }
            : undefined,
          invoice: input.invoiceId
            ? {
                connect: {
                  id: input.invoiceId,
                },
              }
            : undefined,
        },
        select: {
          ...selections.document,
        },
      });

      return document;
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const document = await ctx.prisma.document.update({
        where: {
          id: input.id,
        },
        data: {
          name: input.name,
          description: input.description,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: selections.document,
      });

      if (input.name) {
        try {
          const extension = document.url.split(".").pop();
          await ctx.utapi.renameFiles([
            {
              fileKey: document.fileId!,
              newName: `${document.name}.${extension}`,
            },
          ]);
        } catch (error) {
          console.error("Failed to rename file", error);
        }
      }

      return document;
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const document = await ctx.prisma.document.update({
        where: {
          id: input.id,
        },
        data: {
          templates: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
        select: selections.document,
      });

      if (input.orderId) {
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "DOCUMENT",
          meta: {
            id: document.id,
            name: document.name,
            mode: "LINK",
          },
        });
      }

      return document;
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const document = await ctx.prisma.document.update({
        where: {
          id: input.id,
        },
        data: {
          templates: input.templateId
            ? {
                disconnect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                disconnect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
        select: selections.document,
      });

      if (input.orderId) {
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "DOCUMENT",
          meta: {
            id: document.id,
            name: document.name,
            mode: "UNLINK",
          },
        });
      }

      return document;
    }),
  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        const document = await ctx.prisma.document.delete({
          where: {
            id: input.id,
          },
          select: {
            id: true,
            fileId: true,
          },
        });

        try {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          await ctx.utapi.deleteFiles([document.fileId!]);
        } catch (error) {
          console.error("Failed to delete file", error);
        }

        return document;
      }

      const document = await ctx.prisma.document.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: {
          ...selections.document,
        },
      });

      return document;
    }),
});
