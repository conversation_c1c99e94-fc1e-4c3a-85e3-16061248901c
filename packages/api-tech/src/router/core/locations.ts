import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  Address,
  Location,
  Organization,
  Prisma,
} from "@axa/database-tech";
import { getTimeZone } from "@axa/lib/google-maps";
import { calculateSkip } from "@axa/lib/utils";

import onWorkOrderEvent from "../../jobs/work-order-event";
import { createShiftDateAndTime } from "../../lib/dates";
import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

const zAddress = z.object({
  formatted: z.string().optional(),
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal: z.string().optional(),
  country: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
});

const selections = {
  location: {
    id: true,
    name: true,
    type: true,
    description: true,
    timeZone: true,
  },
  address: {
    formatted: true,
    street: true,
    city: true,
    state: true,
    postal: true,
    country: true,
    latitude: true,
    longitude: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
} satisfies {
  location: Partial<Record<keyof Location, boolean>>;
  address: Partial<Record<keyof Address, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const locationsRouter = createTRPCRouter({
  getTimeZone: protectedProcedure
    .input(
      z.object({
        latitude: z.number(),
        longitude: z.number(),
      }),
    )
    .query(async ({ input }) => {
      try {
        const tz = await getTimeZone(input.latitude, input.longitude);

        return tz.data.timeZoneId;
      } catch (e) {
        console.error(e);
      }

      return null;
    }),
  find: organizationProcedure
    .input(z.object({ query: z.string() }))
    .query(async ({ input, ctx }) => {
      return ctx.prisma.location.findMany({
        where: {
          name: { contains: input.query, mode: "insensitive" },
          description: { contains: input.query, mode: "insensitive" },
        },
      });
    }),
  search: organizationProcedure
    .input(
      z.object({
        query: z.string(),
        organizationId: z.string().optional(),
        organizations: z.array(z.string()).optional(),
        showDeleted: z.boolean().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const locations = await ctx.prisma.location.findMany({
        where: {
          deletedAt,
          organizationId:
            organizations && organizations.length > 0
              ? {
                  in: organizations,
                }
              : undefined,
          OR: [
            {
              name: {
                contains: input.query,
                mode: "insensitive",
              },
            },
            {
              description: {
                contains: input.query,
                mode: "insensitive",
              },
            },
          ],
        },
        select: {
          ...selections.location,
          organizationId: true,
          address: {
            select: {
              ...selections.address,
            },
          },
        },
      });

      return locations;
    }),
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const location = await ctx.prisma.location.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.location,
          fieldNationId: ctx.isInternal,
          address: {
            select: selections.address,
          },
          organization: {
            select: selections.organization,
          },
        },
      });

      if (!location) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Location not found",
        });
      }

      return location;
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        projectId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        showDeleted: z.boolean().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const query = {
        OR: input.query
          ? [
              {
                name: {
                  contains: input.query,
                  mode: "insensitive",
                },
              },
              {
                address: {
                  formatted: {
                    contains: input.query,
                    mode: "insensitive",
                  },
                },
              },
            ].concat(
              ctx.isInternal && !Number.isNaN(parseInt(input.query, 10))
                ? [
                    {
                      // @ts-expect-error type lost in concat
                      fieldNationId: {
                        equals: parseInt(input.query, 10),
                      },
                    },
                  ]
                : [],
            )
          : undefined,
        projects: input.projectId
          ? {
              some: {
                id: input.projectId,
              },
            }
          : undefined,
        organizationId:
          organizations && organizations.length > 0
            ? {
                in: organizations,
              }
            : undefined,
        deletedAt,
      } as Prisma.LocationFindManyArgs["where"];

      const [count, locations] = await Promise.all([
        ctx.prisma.location.count({
          where: query,
        }),
        ctx.prisma.location.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          where: query,
          select: {
            ...selections.location,
            fieldNationId: ctx.isInternal,
            organization: {
              select: selections.organization,
            },
            address: {
              select: selections.address,
            },
          },
        }),
      ]);

      return {
        locations,
        total: count,
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        type: z.string(),
        name: z.string(),
        description: z.string().optional(),
        address: zAddress,
        fieldNationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId = input.organizationId ?? ctx.organization?.id ?? "";

      const tz = await getTimeZone(
        input.address.latitude ?? 0,
        input.address.longitude ?? 0,
      );

      return ctx.prisma.location.create({
        data: {
          timeZone: tz.data.timeZoneId,
          type: input.type,
          name: input.name,
          description: input.description,
          fieldNationId: ctx.isInternal
            ? input.fieldNationId
              ? parseInt(input.fieldNationId)
              : null
            : undefined,
          address: {
            create: {
              formatted: input.address.formatted,
              street: input.address.street,
              city: input.address.city,
              state: input.address.state,
              postal: input.address.postal,
              country: input.address.country,
              latitude: input.address.latitude,
              longitude: input.address.longitude,
            },
          },
          organization: {
            connect: {
              id: organizationId,
            },
          },
        },
        select: {
          ...selections.location,
          fieldNationId: ctx.isInternal,
          address: {
            select: {
              ...selections.address,
            },
          },
        },
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        type: z.string().optional(),
        description: z.string().optional(),
        address: zAddress,
        organizationId: z.string().optional(),
        fieldNationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      let tz;

      if (input.address.latitude && input.address.longitude) {
        tz = await getTimeZone(input.address.latitude, input.address.longitude);
      }

      return ctx.prisma.location.update({
        where: {
          id: input.id,
        },
        data: {
          name: input.name,
          type: input.type,
          description: input.description,
          timeZone: tz?.data.timeZoneId,
          fieldNationId: ctx.isInternal
            ? input.fieldNationId
              ? parseInt(input.fieldNationId)
              : null
            : undefined,
          address: {
            update: {
              formatted: input.address.formatted,
              street: input.address.street,
              city: input.address.city,
              state: input.address.state,
              postal: input.address.postal,
              country: input.address.country,
              latitude: input.address.latitude,
              longitude: input.address.longitude,
            },
          },
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: {
          ...selections.location,
          fieldNationId: ctx.isInternal,
          address: {
            select: {
              ...selections.address,
            },
          },
        },
      });
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const location = await ctx.prisma.location.update({
        where: {
          id: input.id,
        },
        data: {
          templates: input.templateId
            ? {
                connect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
        select: selections.location,
      });

      if (input.orderId) {
        const schedule = await ctx.prisma.schedule.findFirst({
          where: {
            order: {
              id: input.orderId,
            },
          },
        });
        const shifts = await ctx.prisma.shift.findMany({
          where: {
            orderId: input.orderId,
          },
        });

        await ctx.prisma.$transaction(
          shifts.map((shift) =>
            ctx.prisma.shift.update({
              where: {
                id: shift.id,
              },
              data: createShiftDateAndTime({
                timeZone: location.timeZone ?? "",
                type: shift.type,
                date: shift.date,
                startDate: shift.startDate,
                endDate: shift.endDate,
                startTime: shift.startTime,
                endTime: shift.endTime,
                hours: shift.hours,
              }),
            }),
          ),
        );
        await ctx.prisma.schedule.update({
          where: {
            id: schedule?.id,
          },
          data: {
            timeZone: location.timeZone,
          },
        });
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "LOCATION",
          meta: {
            id: location.id,
            name: location.name,
            mode: "LINK",
          },
        });
      }

      return location;
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        templateId: z.string().optional(),
        orderId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const location = await ctx.prisma.location.update({
        where: {
          id: input.id,
        },
        data: {
          templates: input.templateId
            ? {
                disconnect: {
                  id: input.templateId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                disconnect: {
                  id: input.orderId,
                },
              }
            : undefined,
        },
        select: selections.location,
      });

      if (input.orderId) {
        await ctx.prisma.shift.updateMany({
          where: {
            orderId: input.orderId,
          },
          data: {
            timeZone: null,
          },
        });
        await onWorkOrderEvent({
          actorId: ctx.user.id,
          orderId: input.orderId,
          action: "UPDATE",
          resource: "LOCATION",
          meta: {
            id: location.id,
            name: location.name,
            mode: "UNLINK",
          },
        });
      }

      return location;
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.location.delete({
          where: {
            id: input.id,
          },
          select: {
            ...selections.location,
            address: {
              select: {
                ...selections.address,
              },
            },
          },
        });
      }

      return ctx.prisma.location.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: {
          ...selections.location,
          address: {
            select: {
              ...selections.address,
            },
          },
        },
      });
    }),
});
