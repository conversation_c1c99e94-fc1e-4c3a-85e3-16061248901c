import { contactTypes, expenseTypes } from "@axa/constants/tech-dictionary";

import { adminProcedure, createTR<PERSON>Router } from "../../trpc";

export const systemRouter = createTRPCRouter({
  clearAll: adminProcedure.mutation(async ({ ctx }) => {
    // await ctx.utapi.getFileUrls()
    await ctx.prisma.valueStore.deleteMany();
  }),
  initialize: adminProcedure.mutation(async ({ ctx }) => {
    await ctx.prisma.valueStore.deleteMany();
    const types = await ctx.fn.orders.types.load({
      latest: true,
    });
    const categories = [
      ...new Set(
        types
          .map((type) => type.services)
          .flat()
          .map(({ text }) => text),
      ),
    ].map((text) => ({
      type: "ORDER_CATEGORY",
      key: `order-category-${text}`,
      value: text,
    }));
    await ctx.prisma.valueStore.createMany({
      // @ts-expect-error sub type not defined
      data: [
        ...types.map((type) => ({
          type: "ORDER_TYPE",
          key: `work-order-type-${type.id}`,
          value: type.name,
          fnId: type.id,
          subtypes: type.services,
        })),
        ...categories,
        // ...workspaceTypes,
        // ...orderTypes,
        // ...categoryTypes,
        ...contactTypes,
        ...expenseTypes,
      ],
    });
  }),
});
