import { z } from "zod";

import type { Person } from "@axa/database-tech";

import { createTRPCRouter, protectedProcedure } from "../../trpc";

const selectors = {
  person: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
    isUser: true,
  },
} satisfies {
  person: Partial<Record<keyof Person, boolean>>;
};

export const transactionsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const transactions = await ctx.prisma.transaction.findMany({
        where: {
          orderId: input.orderId,
        },
        orderBy: {
          createdAt: "desc",
        },
        select: {
          id: true,
          type: true,
          field: true,
          value: true,
          reason: true,
          createdAt: true,
          actor: {
            select: selectors.person,
          },
        },
      });

      return {
        transactions,
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        type: z.enum(["CREATE", "UPDATE", "DELETE"]),
        field: z.string(),
        value: z.string(),
        reason: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.transaction.create({
        data: {
          type: input.type,
          field: input.field,
          value: input.value,
          reason: input.reason,
          order: {
            connect: {
              id: input.orderId,
            },
          },
          actor: {
            connect: {
              id: ctx.user.id,
            },
          },
        },
      });
    }),
});
