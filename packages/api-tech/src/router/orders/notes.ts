import { isEqual } from "date-fns";
import { z } from "zod";

import type { Person } from "@axa/database-tech";

import onWorkOrderMessage from "../../jobs/work-order-message";
import { createTRPCRouter, protectedProcedure } from "../../trpc";

const selectors = {
  person: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
    isUser: true,
  },
} satisfies {
  person: Partial<Record<keyof Person, boolean>>;
};

export const notesRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const notes = await ctx.prisma.note.findMany({
        where: {
          orderId: input.orderId,
          type: ctx.isInternal ? undefined : "PUBLIC",
        },
        orderBy: {
          createdAt: "desc",
        },
        select: {
          id: true,
          type: true,
          note: true,
          createdAt: true,
          updatedAt: true,
          createdBy: true,
          author: {
            select: selectors.person,
          },
        },
      });

      return {
        notes: notes.map((note) => {
          return {
            id: note.id,
            type: note.type,
            message: note.note,
            createdAt: note.createdAt,
            updatedAt: isEqual(note.updatedAt, note.createdAt)
              ? null
              : note.updatedAt,
            user: note.author,
            canEdit: ctx.isInternal || note.author.id === ctx.user.id,
          };
        }),
      };
    }),
  create: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        message: z.string(),
        type: z.enum(["INTERNAL", "PUBLIC"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const note = await ctx.prisma.note.create({
        data: {
          note: input.message,
          type: input.type,
          order: {
            connect: {
              id: input.orderId,
            },
          },
          author: {
            connect: {
              id: ctx.user.id,
            },
          },
        },
      });

      await onWorkOrderMessage({
        actorId: ctx.user.id,
        orderId: input.orderId,
        messageId: note.id,
      });

      return note;
    }),
  update: protectedProcedure
    .input(
      z.object({
        noteId: z.string(),
        message: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.note.update({
        where: {
          id: input.noteId,
        },
        data: {
          note: input.message,
        },
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        noteId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.note.delete({
        where: {
          id: input.noteId,
        },
      });
    }),
});
