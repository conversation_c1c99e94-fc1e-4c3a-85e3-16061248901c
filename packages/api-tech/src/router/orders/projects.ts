import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  Organization,
  Prisma,
  Project,
  Schedule,
  Shift,
  Template,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import triggerGenerateProject from "../../jobs/generate-project";
import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

const selections = {
  project: {
    id: true,
    name: true,
    description: true,
    status: true,
  },
  template: {
    id: true,
    name: true,
    description: true,
  },
  schedule: {
    id: true,
  },
  shift: {
    id: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
} satisfies {
  project: Partial<Record<keyof Project, boolean>>;
  template: Partial<Record<keyof Template, boolean>>;
  schedule: Partial<Record<keyof Schedule, boolean>>;
  shift: Partial<Record<keyof Shift, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
};

export const projectsRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const project = await ctx.prisma.project.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.project,
          organization: {
            select: selections.organization,
          },
          templates: {
            select: selections.template,
          },
          schedule: {
            select: {
              ...selections.schedule,
              shifts: {
                select: selections.shift,
              },
            },
          },
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      return project;
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        showDeleted: z.boolean().optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const query = {
        deletedAt,
        name: input.query
          ? {
              contains: input.query,
            }
          : undefined,
        organizationId:
          organizations && organizations.length > 0
            ? {
                in: organizations,
              }
            : undefined,
      } as Prisma.ProjectFindManyArgs["where"];

      const [count, projects] = await Promise.all([
        ctx.prisma.project.count({
          where: query,
        }),
        ctx.prisma.project.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          where: query,
          select: {
            ...selections.project,
            organization: {
              select: selections.organization,
            },
          },
        }),
      ]);

      return {
        projects,
        total: count,
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        name: z.string(),
        description: z.string().optional(),
        schedule: z
          .object({
            name: z.string().optional(),
            description: z.string().optional(),
            type: z.enum(["LONG_TERM", "SHORT_TERM", "ONE_TIME"]),
            dates: z
              .array(
                z.object({
                  date: z.date(),
                }),
              )
              .optional(),
          })
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId = input.organizationId ?? ctx.organization?.id ?? "";

      return ctx.prisma.project.create({
        data: {
          name: input.name,
          description: input.description,
          schedule: {
            create: {
              name: input.schedule?.name,
              description: input.schedule?.description,
              type: input.schedule?.type,
            },
          },
          organization: {
            connect: {
              id: organizationId,
            },
          },
        },
        select: selections.project,
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.project.update({
        where: {
          id: input.id,
        },
        data: {
          name: input.name,
          description: input.description,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: selections.project,
      });
    }),
  generate: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      await triggerGenerateProject({
        projectId: input.id,
        actorId: ctx.user.id,
      });

      return ctx.prisma.project.update({
        where: {
          id: input.id,
        },
        data: {
          status: "POSTED",
        },
        select: selections.project,
      });
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string().optional(),
        orderId: z.string().optional(),
        locationId: z.string().optional(),
        contactId: z.string().optional(),
        documentId: z.string().optional(),
        shiftId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.project.update({
        where: {
          id: input.id,
        },
        data: {
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
          orders: input.orderId
            ? {
                connect: {
                  id: input.orderId,
                },
              }
            : undefined,
          locations: input.locationId
            ? {
                connect: {
                  id: input.locationId,
                },
              }
            : undefined,
          contacts: input.contactId
            ? {
                connect: {
                  id: input.contactId,
                },
              }
            : undefined,
          shifts: input.shiftId
            ? {
                connect: {
                  id: input.shiftId,
                },
              }
            : undefined,
          documents: input.documentId
            ? {
                connect: {
                  id: input.documentId,
                },
              }
            : undefined,
        },
        select: selections.project,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string().optional(),
        orderId: z.string().optional(),
        locationId: z.string().optional(),
        contactId: z.string().optional(),
        documentId: z.string().optional(),
        shiftId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.project.update({
        where: {
          id: input.id,
        },
        data: {
          orders: input.orderId
            ? {
                disconnect: [
                  {
                    id: input.orderId,
                  },
                ],
              }
            : undefined,
          locations: input.locationId
            ? {
                disconnect: [
                  {
                    id: input.locationId,
                  },
                ],
              }
            : undefined,
          contacts: input.contactId
            ? {
                disconnect: [
                  {
                    id: input.contactId,
                  },
                ],
              }
            : undefined,
          shifts: input.shiftId
            ? {
                disconnect: [
                  {
                    id: input.shiftId,
                  },
                ],
              }
            : undefined,
          documents: input.documentId
            ? {
                disconnect: [
                  {
                    id: input.documentId,
                  },
                ],
              }
            : undefined,
        },
        select: selections.project,
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.project.delete({
          where: {
            id: input.id,
          },
          select: selections.project,
        });
      }

      return ctx.prisma.project.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.project,
      });
    }),
});
