import { z } from "zod";

import onWorkOrderRemoved from "../../jobs/work-order-removed";
import {
  createWorkOrder as createFieldNationWorkOrder,
  syncFieldNationWorkOrder,
} from "../../lib/fieldnation";
import { createTRPCRouter, internalProcedure } from "../../trpc";

export const fieldNationRouter = createTRPCRouter({
  syncWorkOrder: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return await syncFieldNationWorkOrder({ id: input.id });
    }),
  createWorkOrder: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return await createFieldNationWorkOrder({ id: input.id });
    }),
  publishWorkOrder: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          fieldNationId: true,
        },
      });

      if (order.fieldNationId !== null) {
        await ctx.fn.orders.publish(order.fieldNationId.toString());
      }

      return true;
    }),
  approveWorkOrder: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          fieldNationId: true,
        },
      });

      if (order.fieldNationId !== null) {
        await ctx.fn.orders.approve(order.fieldNationId.toString());
      }

      return true;
    }),
  cancelWorkOrder: internalProcedure
    .input(
      z.object({
        id: z.string(),
        dispatchEmailToTechs: z.boolean().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const order = await ctx.prisma.workOrder.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          fieldNationId: true,
        },
      });

      if (order.fieldNationId !== null) {
        await ctx.fn.orders.cancel(order.fieldNationId.toString(), {
          notesToSupport: "Cancelled by the customer",
          messageToProvider: "Cancelled by the customer",
          cancel_reason: 46,
        });
      }

      if (input.dispatchEmailToTechs) {
        const order = await ctx.prisma.workOrder.findUniqueOrThrow({
          where: {
            id: input.id,
          },
          select: {
            id: true,
            timeSheets: {
              select: {
                providerId: true,
              },
            },
          },
        });

        await Promise.all(
          order.timeSheets.map(async (ts) => {
            if (ts.providerId) {
              await onWorkOrderRemoved({
                orderId: order.id,
                providerId: ts.providerId,
              });
            }
          }),
        );
      }

      return true;
    }),
});
