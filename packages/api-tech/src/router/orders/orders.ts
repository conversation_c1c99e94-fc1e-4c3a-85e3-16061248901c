import { captureException } from "@sentry/nextjs";
import { TRPCError } from "@trpc/server";
import { endOfDay, startOfDay } from "date-fns";
import { DateTime, Interval } from "luxon";
import { z } from "zod";

import type {
  Address,
  Contact,
  Document,
  Invoice,
  Location,
  Organization,
  Person,
  Prisma,
  Project,
  Provider,
  Schedule,
  Shift,
  Technician,
  WorkOrder,
  WorkOrderPriority,
  WorkOrderStatus,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import { getActiveInvoice } from "../../jobs/cycle-invoices";
import onWorkOrderDispatcher from "../../jobs/work-order-dispatcher";
import onWorkOrderEvent, { workOrderEvent } from "../../jobs/work-order-event";
import onWorkOrderRemoved from "../../jobs/work-order-removed";
import { createShiftDateAndTime } from "../../lib/dates";
import { createWorkOrder as createFieldNationWorkOrder } from "../../lib/fieldnation";
import {
  createTRPCRouter,
  internalProcedure,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";
import { fieldNationRouter } from "./field-nation";
import { notesRouter } from "./notes";
import { transactionsRouter } from "./transactions";

const selectors = {
  order: {
    id: true,
    number: true,
    emergency: true,
    status: true,
    type: true,
    category: true,
    priority: true,
    summary: true,
    scope: true,
    fieldNationId: true,
    fieldNationURL: true,
    customId: true,
    customUrl: true,
    PO: true,
    createdAt: true,
  },
  location: {
    id: true,
    name: true,
    type: true,
    description: true,
    organizationId: true,
  },
  address: {
    formatted: true,
    street: true,
    city: true,
    state: true,
    postal: true,
    country: true,
    latitude: true,
    longitude: true,
  },
  contact: {
    id: true,
    role: true,
  },
  person: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
  },
  document: {
    id: true,
    name: true,
    type: true,
    url: true,
    size: true,
  },
  provider: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    level: true,
  },
  technician: {
    id: true,
    level: true,
    billingRate: true,
    billingType: true,
    paymentRate: true,
    paymentType: true,
  },
  invoice: {
    id: true,
    name: true,
    timePeriodStart: true,
    timePeriodEnd: true,
    dueDate: true,
  },
  project: {
    id: true,
    name: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  schedule: {
    id: true,
  },
  shift: {
    id: true,
    type: true,
    status: true,
    date: true,
    startDate: true,
    endDate: true,
    startTime: true,
    endTime: true,
    timeZone: true,
    scheduleId: true,
  },
} satisfies {
  order: Partial<Record<keyof WorkOrder, boolean>>;
  location: Partial<Record<keyof Location, boolean>>;
  address: Partial<Record<keyof Address, boolean>>;
  contact: Partial<Record<keyof Contact, boolean>>;
  person: Partial<Record<keyof Person, boolean>>;
  document: Partial<Record<keyof Document, boolean>>;
  technician: Partial<Record<keyof Technician, boolean>>;
  provider: Partial<Record<keyof Provider, boolean>>;
  invoice: Partial<Record<keyof Invoice, boolean>>;
  project: Partial<Record<keyof Project, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
  schedule: Partial<Record<keyof Schedule, boolean>>;
  shift: Partial<Record<keyof Shift, boolean>>;
};

export const zWorkOrderStatus = z.enum([
  "DRAFT",
  "PENDING",
  "PUBLISHED",
  "ASSIGNED",
  "SCHEDULED",
  "ACTIVE",
  "COMPLETED",
  "APPROVED",
  "CANCELLED",
]);
export const zWorkOrderPriority = z.enum(["LOW", "MEDIUM", "HIGH"]);
export const zWorkOrderType = z.string();
export const zWorkOrderCategory = z.string();

export const ordersRouter = createTRPCRouter({
  notes: notesRouter,
  transactions: transactionsRouter,
  fieldNation: fieldNationRouter,
  metadata: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const order = await ctx.prisma.workOrder.findUnique({
        where: {
          id: input.id,
        },
        select: {
          number: true,
          customId: true,
          summary: true,
          scope: true,
          type: true,
          category: true,
        },
      });

      const id = order?.customId ?? order?.number ?? null;

      return {
        title:
          `${id ? `${id} | ` : ""} ${order?.summary ?? "Work Order"}`.trim(),
        description: `${[order?.type ?? "", order?.category ?? ""].join(" | ").trim()} ${order?.scope ?? ""}`,
      };
    }),
  analytics: organizationProcedure
    .input(
      z
        .object({
          organization: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input?.organization) {
          organizations.push(input.organization);
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input?.organization &&
          ctx.organizations.includes(input.organization)
        ) {
          organizations.push(input.organization);
        } else {
          organizations = ctx.organizations;
        }
      }

      const today = DateTime.now().setZone("utc").startOf("day");
      const nextWeek = today
        .plus({ week: 1, day: 1 })
        .setZone("utc")
        .startOf("day");
      const lastWeek = today.minus({ week: 1 }).setZone("utc").endOf("day");
      const interval = Interval.fromDateTimes(lastWeek, nextWeek).splitBy({
        days: 1,
      });

      const shifts = (
        await ctx.prisma.shift.groupBy({
          by: ["date", "startDate", "endDate"],
          where: {
            order: {
              is: {
                status: {
                  not: {
                    in: ["CANCELLED", "DRAFT"],
                  },
                },
                organizationId: {
                  in: organizations,
                },
              },
            },
            OR: [
              {
                date: {
                  lte: nextWeek.toJSDate(),
                  gte: lastWeek.toJSDate(),
                },
              },
              {
                startDate: {
                  lte: nextWeek.toJSDate(),
                  gte: lastWeek.toJSDate(),
                },
              },
              {
                endDate: {
                  lte: nextWeek.toJSDate(),
                  gte: lastWeek.toJSDate(),
                },
              },
            ],
          },
          _count: true,
        })
      ).reduce(
        (acc, count) => {
          const date = DateTime.fromJSDate(
            count.date ?? count.startDate ?? count.endDate ?? new Date(),
          ).toFormat("yyyy-MM-dd");

          const currentCount = (acc[date] as number | undefined) ?? 0;
          return {
            ...acc,
            [date]: count._count + currentCount,
          };
        },
        {} as Record<string, number>,
      );

      const volume = interval.map((day) => {
        const date = day.start?.toFormat("yyyy-MM-dd") ?? "";
        return {
          date,
          volume: shifts[date] ?? 0,
        };
      });

      const status = (
        await ctx.prisma.workOrder.groupBy({
          by: ["status"],
          where: {
            organizationId: {
              in: organizations,
            },
          },
          _count: {
            id: true,
          },
        })
      ).reduce(
        (acc, count) => ({
          ...acc,
          [count.status]: count._count.id,
        }),
        {
          DRAFT: 0,
          PENDING: 0,
          PUBLISHED: 0,
          SCHEDULED: 0,
          ASSIGNED: 0,
          ACTIVE: 0,
          COMPLETED: 0,
          APPROVED: 0,
          CANCELLED: 0,
        },
      );

      return {
        volume,
        status: {
          reviewed: {
            total: status.PUBLISHED + status.PENDING,
            pending: status.PUBLISHED,
          },
          assigned: {
            total: status.ASSIGNED + status.PUBLISHED,
            pending: status.ASSIGNED,
          },
          scheduled: {
            total: status.SCHEDULED + status.ASSIGNED,
            pending: status.SCHEDULED,
          },
          completed: {
            total: status.ACTIVE + status.COMPLETED,
            pending: status.COMPLETED,
          },
        },
      };
    }),
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        include: z
          .object({
            organization: z.boolean().optional(),
            invoice: z.boolean().optional(),
            project: z.boolean().optional(),
            location: z.boolean().optional(),
            contacts: z.boolean().optional(),
            documents: z.boolean().optional(),
            technicians: z.boolean().optional(),
            providers: z.boolean().optional(),
            notes: z.boolean().optional(),
            transactions: z.boolean().optional(),
            expenses: z.boolean().optional(),
            schedule: z.boolean().optional(),
            shifts: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selectors.order,
          fieldNationId: ctx.isInternal,
          organization: input.include?.organization
            ? {
                select: {
                  ...selectors.organization,
                  fieldNationId: ctx.isInternal,
                },
              }
            : undefined,
          location: input.include?.location
            ? {
                select: {
                  ...selectors.location,
                  fieldNationId: ctx.isInternal,
                  address: {
                    select: selectors.address,
                  },
                },
              }
            : undefined,
          contacts: input.include?.contacts
            ? {
                select: {
                  ...selectors.contact,
                  person: {
                    select: selectors.person,
                  },
                },
              }
            : undefined,
          documents: input.include?.documents
            ? {
                select: selectors.document,
              }
            : undefined,
          technicians: input.include?.technicians
            ? {
                select: selectors.technician,
              }
            : undefined,
          providers: input.include?.providers
            ? {
                select: selectors.provider,
              }
            : undefined,
          invoice: input.include?.invoice
            ? {
                select: selectors.invoice,
              }
            : undefined,
          project: input.include?.project
            ? {
                select: selectors.project,
              }
            : undefined,
          schedule: input.include?.schedule
            ? {
                select: {
                  ...selectors.schedule,
                  shifts: {
                    select: input.include.shifts ? selectors.shift : undefined,
                  },
                },
              }
            : undefined,
          notes: input.include?.notes && true,
          transactions: input.include?.transactions && true,
          expenses: input.include?.expenses && true,
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      return order;
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        projectId: z.string().optional(),
        query: z.string().optional(),
        type: z.array(zWorkOrderType).optional(),
        category: z.array(zWorkOrderCategory).optional(),
        status: z.array(zWorkOrderStatus).optional(),
        priority: z.array(zWorkOrderPriority).optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        showDeleted: z.boolean().optional(),
        showArchived: z.boolean().optional(),
        include: z
          .object({
            organization: z.boolean().optional(),
            invoice: z.boolean().optional(),
            project: z.boolean().optional(),
            location: z.boolean().optional(),
            contacts: z.boolean().optional(),
            documents: z.boolean().optional(),
            technicians: z.boolean().optional(),
            providers: z.boolean().optional(),
            notes: z.boolean().optional(),
            transactions: z.boolean().optional(),
            expenses: z.boolean().optional(),
            schedule: z.boolean().optional(),
            shifts: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const search = input.query?.trim() ?? "";
      const searchQuery: Prisma.WorkOrderWhereInput["OR"] = input.query
        ? [
            {
              summary: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              scope: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              type: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              category: {
                contains: search,
                mode: "insensitive",
              },
            },
            {
              customId: {
                contains: search,
                mode: "insensitive",
              },
            },
          ]
        : [];

      if (ctx.isInternal) {
        if (!Number.isNaN(parseInt(search, 10))) {
          searchQuery.push({
            fieldNationId: {
              equals: parseInt(search, 10),
            },
          });
        }
      }

      const query:
        | Prisma.WorkOrderWhereInput
        | Prisma.WorkOrderGroupByArgs["where"] = {
        deletedAt,
        archivedAt: null,
        OR: searchQuery.length > 0 ? searchQuery : undefined,
        status: {
          in:
            input.status && input.status.length > 0 ? input.status : undefined,
        },
        type: {
          in: input.type && input.type.length > 0 ? input.type : undefined,
        },
        category: {
          in:
            input.category && input.category.length > 0
              ? input.category
              : undefined,
        },
        priority: {
          in:
            input.priority && input.priority.length > 0
              ? input.priority
              : undefined,
        },
        schedule:
          input.startDate || input.endDate
            ? {
                is: {
                  shifts: {
                    some: {
                      OR: [
                        {
                          date: {
                            lte:
                              input.endDate?.toISOString() ??
                              (input.startDate
                                ? endOfDay(input.startDate)
                                : undefined
                              )?.toISOString(),
                            gte: (input.startDate
                              ? startOfDay(input.startDate)
                              : undefined
                            )?.toISOString(),
                          },
                        },
                        {
                          startDate: {
                            gte: input.startDate,
                            lte: input.endDate,
                          },
                        },
                        {
                          endDate: {
                            gte: input.startDate,
                            lte: input.endDate,
                          },
                        },
                      ],
                    },
                  },
                },
              }
            : undefined,
        project: input.projectId
          ? {
              is: {
                id: input.projectId,
              },
            }
          : undefined,
        organizationId:
          organizations && organizations.length > 0
            ? {
                in: organizations,
              }
            : undefined,
      };

      const [orders, total, groups] = await Promise.all([
        ctx.prisma.workOrder.findMany({
          where: query,
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize ?? 10,
          orderBy: {
            createdAt: "desc",
          },
          select: {
            ...selectors.order,
            fieldNationId: ctx.isInternal,
            organization: input.include?.organization
              ? {
                  select: {
                    ...selectors.organization,
                    fieldNationId: ctx.isInternal,
                  },
                }
              : undefined,
            location: input.include?.location
              ? {
                  select: {
                    ...selectors.location,
                    fieldNationId: ctx.isInternal,
                    address: {
                      select: selectors.address,
                    },
                  },
                }
              : undefined,
            contacts: input.include?.contacts
              ? {
                  select: {
                    ...selectors.contact,
                    person: {
                      select: selectors.person,
                    },
                  },
                }
              : undefined,
            documents: input.include?.documents
              ? {
                  select: selectors.document,
                }
              : undefined,
            technicians: input.include?.technicians
              ? {
                  select: selectors.technician,
                }
              : undefined,
            providers: input.include?.providers
              ? {
                  select: selectors.provider,
                }
              : undefined,
            invoice: input.include?.invoice
              ? {
                  select: selectors.invoice,
                }
              : undefined,
            project: input.include?.project
              ? {
                  select: selectors.project,
                }
              : undefined,
            schedule: input.include?.schedule
              ? {
                  select: {
                    ...selectors.schedule,
                    shifts: {
                      select: input.include.shifts
                        ? selectors.shift
                        : undefined,
                    },
                  },
                }
              : undefined,
            notes: input.include?.notes && true,
            transactions: input.include?.transactions && true,
            expenses: input.include?.expenses && true,
          },
        }),
        ctx.prisma.workOrder.count({
          where: query,
        }),
        ctx.prisma.workOrder.groupBy({
          by: ["status"],
          where: {
            ...query,
            status: undefined,
          },
          _count: {
            id: true,
          },
        }),
      ]);

      const countByStatus = groups.reduce<Record<WorkOrderStatus, number>>(
        (acc, group) => ({
          ...acc,
          [group.status]: group._count.id,
        }),
        {
          DRAFT: 0,
          PENDING: 0,
          PUBLISHED: 0,
          SCHEDULED: 0,
          ASSIGNED: 0,
          ACTIVE: 0,
          COMPLETED: 0,
          APPROVED: 0,
          CANCELLED: 0,
        },
      );

      return {
        orders,
        total,
        count: countByStatus,
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        emergency: z.boolean().optional(),
        organizationId: z.string().optional(),
        status: z.enum(["DRAFT", "PENDING"]),
        summary: z.string(),
        scope: z.string(),
        shift: z.object({
          type: z.enum(["SERVICE_WINDOW", "SERVICE_DATE", "SERVICE_TERM"]),
          date: z.date().optional(),
          startDate: z.date().optional(),
          endDate: z.date().optional(),
          hours: z.number(),
          startTime: z.object({
            hour: z.number(),
            minute: z.number(),
          }),
          endTime: z
            .object({
              hour: z.number(),
              minute: z.number(),
            })
            .optional(),
        }),
        type: zWorkOrderType,
        category: zWorkOrderCategory,
        priority: zWorkOrderPriority,
        customId: z.string().optional(),
        customUrl: z.string().optional(),
        PO: z.string().optional(),
        templateId: z.string().optional(),
        projectId: z.string().optional(),
        locationId: z.string().optional(),
        documents: z.array(z.string()).optional(),
        contacts: z
          .array(
            z.object({
              role: z.string(),
              personId: z.string(),
            }),
          )
          .optional(),
        technicians: z
          .array(
            z.object({
              level: z.number(),
              billingRate: z.number(),
              billingType: z.enum(["HOURLY", "FIXED"]),
              paymentRate: z.number().optional(),
              paymentType: z.enum(["HOURLY", "FIXED"]).optional(),
            }),
          )
          .optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId = input.organizationId ?? ctx.organization?.id ?? "";

      const { invoice: currentInvoice } = await getActiveInvoice({
        organizationId,
      });

      const location = input.locationId
        ? await ctx.prisma.location.findUnique({
            where: {
              id: input.locationId,
            },
            select: {
              timeZone: true,
            },
          })
        : undefined;
      const timeZone = location?.timeZone ?? undefined;

      const schedule = await ctx.prisma.schedule.create({
        data: {
          type: "ONE_TIME",
          timeZone,
          shifts: {
            create: [
              createShiftDateAndTime({
                timeZone,
                type: input.shift.type,
                date: input.shift.date,
                startDate: input.shift.startDate,
                endDate: input.shift.endDate,
                startTime: input.shift.startTime,
                endTime: input.shift.endTime,
                hours: input.shift.hours,
              }),
            ],
          },
        },
        select: {
          id: true,
          shifts: {
            select: {
              id: true,
              type: true,
              hours: true,
            },
          },
        },
      });

      const order = await ctx.prisma.workOrder.create({
        data: {
          emergency: input.emergency,
          timeZone,
          summary: input.summary,
          scope: input.scope,
          status: input.status,
          type: input.type,
          category: input.category,
          priority: input.priority,
          customId: input.customId,
          customUrl: input.customUrl,
          PO: input.PO,
          organization: {
            connect: {
              id: organizationId,
            },
          },
          invoice: {
            connect: {
              id: currentInvoice.id,
            },
          },
          schedule: {
            connect: {
              id: schedule.id,
            },
          },
          shifts: {
            connect: schedule.shifts.map((shift) => ({
              id: shift.id,
            })),
          },
          location: input.locationId
            ? {
                connect: {
                  id: input.locationId,
                },
              }
            : undefined,
          documents: input.documents
            ? {
                connect: input.documents.map((documentId) => ({
                  id: documentId,
                })),
              }
            : undefined,
          contacts: input.contacts
            ? {
                create: input.contacts.map((contact) => ({
                  role: contact.role,
                  person: {
                    connect: {
                      id: contact.personId,
                    },
                  },
                })),
              }
            : undefined,
        },
      });

      if (input.technicians && input.technicians.length > 0) {
        await ctx.prisma.workOrder.update({
          where: {
            id: order.id,
          },
          data: {
            technicians: {
              create: input.technicians.map((technician) => {
                const paymentType = technician.paymentType ?? "HOURLY";
                const paymentRate = technician.paymentRate ?? 20;
                const billingType = technician.billingType;
                const billingRate = technician.billingRate;
                return {
                  level: technician.level,
                  billingRate,
                  billingType,
                  paymentRate,
                  paymentType,
                  shifts: {
                    connect: schedule.shifts.map((shift) => ({
                      id: shift.id,
                    })),
                  },
                  timeSheets: {
                    create: schedule.shifts.map((shift) => ({
                      total:
                        billingType === "HOURLY"
                          ? billingRate * shift.hours
                          : billingRate,
                      billingRate,
                      billingType,
                      paymentRate,
                      paymentType,
                      hours: shift.hours,
                      status: "PENDING",
                      shift: {
                        connect: {
                          id: shift.id,
                        },
                      },
                      invoice: {
                        connect: {
                          id: currentInvoice.id,
                        },
                      },
                      order: {
                        connect: {
                          id: order.id,
                        },
                      },
                    })),
                  },
                };
              }),
            },
          },
        });
      }

      await onWorkOrderEvent({
        actorId: ctx.user.id,
        orderId: order.id,
        action: "CREATE",
        resource: "ORDER",
        emergency: input.emergency,
        meta: {
          status: order.status,
        },
      });

      const warnings = [];
      try {
        if (order.status !== "DRAFT") {
          await createFieldNationWorkOrder({
            id: order.id,
          });
        }
      } catch (error) {
        warnings.push({
          code: "FIELD_NATION_ERROR",
          message: (error as Error).message,
        });
      }

      return {
        order,
        warnings,
      };
    }),
  duplicate: organizationProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUnique({
        where: {
          id: input.id,
        },
        include: {
          schedule: {
            include: {
              shifts: true,
            },
          },
          technicians: {
            include: {
              timeSheets: true,
            },
          },
          contacts: true,
          documents: true,
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      const { invoice: currentInvoice } = await getActiveInvoice({
        organizationId: order.organizationId,
      });

      const schedule = await ctx.prisma.schedule.create({
        data: {
          type: "ONE_TIME",
          timeZone: order.timeZone,
          shifts: {
            create: order.schedule.shifts.map((shift) => ({
              type: shift.type,
              date: shift.date,
              startDate: shift.startDate,
              endDate: shift.endDate,
              hours: shift.hours,
              startTime: shift.startTime,
              endTime: shift.endTime,
              timeZone: order.timeZone,
            })),
          },
        },
        select: {
          id: true,
          shifts: {
            select: {
              id: true,
              type: true,
              hours: true,
            },
          },
        },
      });

      const nextOrder = await ctx.prisma.workOrder.create({
        data: {
          status: "DRAFT",
          emergency: order.emergency,
          timeZone: order.timeZone,
          summary: order.summary,
          scope: order.scope,
          type: order.type,
          category: order.category,
          priority: order.priority,
          customId: order.customId,
          customUrl: order.customUrl,
          PO: order.PO,
          organization: {
            connect: {
              id: order.organizationId,
            },
          },
          invoice: {
            connect: {
              id: currentInvoice.id,
            },
          },
          schedule: {
            connect: {
              id: schedule.id,
            },
          },
          shifts: {
            connect: schedule.shifts.map((shift) => ({
              id: shift.id,
            })),
          },
          location: order.locationId
            ? {
                connect: {
                  id: order.locationId,
                },
              }
            : undefined,
          documents: {
            connect: order.documents.map((document) => ({
              id: document.id,
            })),
          },
          contacts: {
            create: order.contacts.map((contact) => ({
              role: contact.role,
              person: {
                connect: {
                  id: contact.personId,
                },
              },
            })),
          },
        },
      });

      if (order.technicians.length > 0) {
        await ctx.prisma.workOrder.update({
          where: {
            id: nextOrder.id,
          },
          data: {
            technicians: {
              create: order.technicians.map((technician) => ({
                level: technician.level,
                paymentRate: technician.paymentRate,
                paymentType: technician.paymentType,
                billingRate: technician.billingRate,
                billingType: technician.billingType,
                shifts: {
                  connect: schedule.shifts.map((shift) => ({
                    id: shift.id,
                  })),
                },
                timeSheets: {
                  create: schedule.shifts.map((shift) => ({
                    total:
                      technician.billingType === "HOURLY"
                        ? technician.billingRate * shift.hours
                        : technician.billingRate,
                    billingRate: technician.billingRate,
                    billingType: technician.billingType,
                    paymentRate: technician.paymentRate,
                    paymentType: technician.paymentType,
                    hours: shift.hours,
                    status: "PENDING",
                    shift: {
                      connect: {
                        id: shift.id,
                      },
                    },
                    invoice: {
                      connect: {
                        id: currentInvoice.id,
                      },
                    },
                    order: {
                      connect: {
                        id: nextOrder.id,
                      },
                    },
                  })),
                },
              })),
            },
          },
        });
      }

      await onWorkOrderEvent({
        actorId: ctx.user.id,
        orderId: nextOrder.id,
        action: "CREATE",
        resource: "ORDER",
        emergency: nextOrder.emergency,
        meta: {
          status: nextOrder.status,
        },
      });

      return {
        id: nextOrder.id,
      };
    }),
  update: organizationProcedure
    .input(
      z.object({
        id: z.string(),
        priority: zWorkOrderPriority.optional(),
        category: zWorkOrderCategory.optional(),
        type: zWorkOrderType.optional(),
        summary: z.string().optional(),
        scope: z.string().optional(),
        customId: z.string().optional(),
        customUrl: z.string().optional(),
        PO: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUnique({
        where: {
          id: input.id,
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      const updates: {
        type?: string;
        category?: string;
        summary?: string;
        scope?: string;
        priority?: WorkOrderPriority;
        customId?: string;
        customUrl?: string;
        PO?: string;
      } = {};

      if (input.type && order.type !== input.type) {
        updates.type = input.type;
      }

      if (input.category && order.category !== input.category) {
        updates.category = input.category;
      }

      if (input.summary && order.summary !== input.summary) {
        updates.summary = input.summary;
      }

      if (input.scope && order.scope !== input.scope) {
        updates.scope = input.scope;
      }

      if (input.priority && order.priority !== input.priority) {
        updates.priority = input.priority;
      }

      if (input.customId && order.customId !== input.customId) {
        updates.customId = input.customId;
      }

      if (input.customUrl && order.customUrl !== input.customUrl) {
        updates.customUrl = input.customUrl;
      }

      if (input.PO && order.PO !== input.PO) {
        updates.PO = input.PO;
      }

      const nextOrder = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: updates,
      });

      await onWorkOrderEvent({
        actorId: ctx.user.id,
        orderId: order.id,
        action: "UPDATE",
        resource: "ORDER",
        meta: {
          ...updates,
        },
      });

      return nextOrder;
    }),
  delete: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
  post: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "PENDING",
        },
      });

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "UPDATE",
        resource: "ORDER",
        emergency: false,
        meta: {
          status: order.status,
        },
      });

      if (order.fieldNationId === null) {
        try {
          await createFieldNationWorkOrder({
            id: order.id,
          });
        } catch (error) {
          captureException(error, {
            tags: {
              path: "orders.post",
            },
          });
        }
      }

      return {
        order: {
          id: order.id,
          status: order.status,
        },
      };
    }),
  markReviewed: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "PUBLISHED",
          publishedAt: new Date(),
        },
        select: {
          id: true,
          status: true,
          fieldNationId: true,
        },
      });

      const warnings = [];

      if (order.fieldNationId !== null) {
        try {
          await ctx.fn.orders.publish(order.fieldNationId.toString());
        } catch (error) {
          warnings.push({
            message: "Failed to publish to Field Nation",
            error,
          });
          captureException(error, {
            tags: {
              path: "orders.publish",
            },
          });
        }
      }

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "UPDATE",
        resource: "ORDER",
        emergency: false,
        meta: {
          status: order.status,
        },
      });

      return { order, warnings };
    }),
  markScheduled: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "SCHEDULED",
        },
      });

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "UPDATE",
        resource: "ORDER",
        emergency: false,
        meta: {
          status: order.status,
        },
      });

      return { order };
    }),
  markInProgress: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "ACTIVE",
        },
        select: {
          id: true,
          status: true,
        },
      });

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "UPDATE",
        resource: "ORDER",
        emergency: false,
        meta: {
          status: order.status,
        },
      });

      return {
        order: {
          id: order.id,
          status: order.status,
        },
      };
    }),
  markCompleted: internalProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "COMPLETED",
        },
        select: {
          id: true,
          status: true,
        },
      });

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "UPDATE",
        resource: "ORDER",
        emergency: false,
        meta: {
          status: order.status,
        },
      });

      return {
        order: {
          id: order.id,
          status: order.status,
        },
      };
    }),
  approve: organizationProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "APPROVED",
          approvedAt: new Date(),
        },
        select: {
          id: true,
          status: true,
          fieldNationId: true,
          invoiceId: true,
          timeSheets: {
            select: {
              id: true,
            },
          },
        },
      });

      const transactions = [];

      for (const ts of order.timeSheets) {
        transactions.push(
          ctx.prisma.timeSheet.update({
            where: {
              id: ts.id,
            },
            data: {
              status: "APPROVED",
            },
          }),
        );
      }

      await ctx.prisma.$transaction(transactions);

      const warnings = [];

      if (order.fieldNationId !== null) {
        try {
          await ctx.fn.orders.approve(order.fieldNationId.toString());
        } catch (error) {
          warnings.push({
            message: "Failed to approve on Field Nation",
            error,
          });
          captureException(error, {
            tags: {
              path: "orders.approve",
            },
          });
        }
      }

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "APPROVE",
        resource: "ORDER",
        emergency: false,
        meta: {
          status: order.status,
        },
      });

      return {
        order: {
          id: order.id,
          status: order.status,
        },
        warnings,
      };
    }),
  cancel: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.update({
        where: {
          id: input.id,
        },
        data: {
          status: "CANCELLED",
          cancelledAt: new Date(),
        },
        select: {
          id: true,
          status: true,
          invoiceId: true,
          timeSheets: {
            select: {
              id: true,
            },
          },
        },
      });

      const transactions = [];

      for (const ts of order.timeSheets) {
        transactions.push(
          ctx.prisma.timeSheet.update({
            where: {
              id: ts.id,
            },
            data: {
              status: "REJECTED",
            },
          }),
        );
      }

      await ctx.prisma.$transaction(transactions);

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "CANCEL",
        resource: "ORDER",
        emergency: true,
        meta: {
          status: order.status,
        },
      });

      return {
        order: {
          id: order.id,
          status: order.status,
        },
      };
    }),
  reopen: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUniqueOrThrow({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          status: true,
          publishedAt: true,
          statements: {
            select: {
              id: true,
              type: true,
            },
          },
          timeSheets: {
            select: {
              id: true,
              providerId: true,
            },
          },
        },
      });

      if (order.status !== "CANCELLED") {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Order is not canceled",
        });
      }

      const transactions = [];

      for (const statement of order.statements) {
        // delete any cancellation feels
        if (statement.type === "FEE") {
          transactions.push(
            ctx.prisma.statement.delete({
              where: {
                id: statement.id,
              },
            }),
          );
        }
      }

      for (const ts of order.timeSheets) {
        transactions.push(
          ctx.prisma.timeSheet.update({
            where: {
              id: ts.id,
            },
            data: {
              status: ts.providerId ? "ASSIGNED" : "PENDING",
            },
          }),
        );
      }

      let status: WorkOrderStatus =
        order.publishedAt !== null ? "PUBLISHED" : "PENDING";

      if (
        order.timeSheets.filter((ts) => ts.providerId === null).length === 0
      ) {
        status = "ASSIGNED";
      }

      transactions.push(
        ctx.prisma.workOrder.update({
          where: {
            id: order.id,
          },
          data: {
            status,
            cancelledAt: null,
          },
        }),
      );

      await ctx.prisma.$transaction(transactions);

      await workOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "REOPEN",
        resource: "ORDER",
        emergency: true,
        meta: {
          status: order.status,
        },
      });

      return {
        order: {
          id: order.id,
          status: order.status,
        },
      };
    }),
  assign: internalProcedure
    .input(
      z.object({
        id: z.string(),
        providerId: z.string(),
        technicianId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUnique({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          status: true,
          schedule: {
            select: {
              shifts: {
                select: {
                  id: true,
                },
              },
            },
          },
          technicians: {
            select: {
              id: true,
              providerId: true,
              shifts: {
                select: {
                  id: true,
                },
              },
              timeSheets: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      const tech = order.technicians.find(
        (tech) => tech.id === input.technicianId,
      );

      if (!tech) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Technician not found",
        });
      }

      const transactions = [];

      const { shifts: _shifts, timeSheets, providerId } = tech;
      const shifts = _shifts.length > 0 ? _shifts : order.schedule.shifts;

      const providers = order.technicians.map((tech) => tech.providerId);

      if (providers.includes(input.providerId)) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Provider already assigned",
        });
      }

      transactions.push(
        ctx.prisma.technician.update({
          where: {
            id: input.technicianId,
          },
          data: {
            shifts: {
              connect: shifts,
            },
          },
        }),
        ctx.prisma.provider.update({
          where: {
            id: input.providerId,
          },
          data: {
            shifts: {
              connect: shifts,
            },
            technicians: {
              connect: {
                id: input.technicianId,
              },
            },
            orders: {
              connect: {
                id: order.id,
              },
            },
          },
        }),
      );

      for (const ts of timeSheets) {
        transactions.push(
          ctx.prisma.timeSheet.update({
            where: {
              id: ts.id,
            },
            data: {
              status: "ASSIGNED",
              provider: {
                connect: {
                  id: input.providerId,
                },
              },
            },
          }),
        );
      }

      if (providerId) {
        transactions.push(
          ctx.prisma.provider.update({
            where: {
              id: providerId,
            },
            data: {
              shifts: {
                disconnect: shifts,
              },
              timeSheets: {
                disconnect: timeSheets,
              },
              orders: {
                disconnect: {
                  id: order.id,
                },
              },
            },
          }),
        );

        await onWorkOrderRemoved({
          orderId: order.id,
          providerId,
        });
      }

      await ctx.prisma.$transaction(transactions);

      const techsToAssign = order.technicians
        .filter((tech) => tech.id !== input.technicianId)
        .filter((tech) => tech.providerId === null);

      if (techsToAssign.length === 0) {
        await ctx.prisma.workOrder.update({
          where: {
            id: input.id,
          },
          data: {
            status: "ASSIGNED",
          },
        });

        await onWorkOrderEvent({
          orderId: order.id,
          actorId: ctx.user.id,
          action: "UPDATE",
          resource: "ORDER",
          emergency: false,
          meta: {
            status: "ASSIGNED",
          },
        });

        await onWorkOrderDispatcher({ orderId: order.id });
      } else {
        await onWorkOrderEvent({
          orderId: order.id,
          actorId: ctx.user.id,
          action: "UPDATE",
          resource: "PROVIDER",
          emergency: false,
          meta: {
            mode: providerId ? "REASSIGN" : "ASSIGN",
          },
        });
      }

      return {
        id: order.id,
      };
    }),
  unAssign: internalProcedure
    .input(
      z.object({
        id: z.string(),
        technicianId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const order = await ctx.prisma.workOrder.findUnique({
        where: {
          id: input.id,
        },
        select: {
          id: true,
          status: true,
          schedule: {
            select: {
              shifts: {
                select: {
                  id: true,
                },
              },
            },
          },
          technicians: {
            select: {
              id: true,
              providerId: true,
              shifts: {
                select: {
                  id: true,
                },
              },
              timeSheets: {
                select: {
                  id: true,
                },
              },
            },
          },
        },
      });

      if (!order) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      } else if (["ACTIVE", "COMPLETED", "APPROVED"].includes(order.status)) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message:
            "Cannot unassign technician for work orders in motion or completed",
        });
      }

      const tech = order.technicians.find(
        (tech) => tech.id === input.technicianId,
      );

      if (!tech) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Technician not found",
        });
      }

      const transactions = [];

      const { shifts: _shifts, timeSheets, providerId } = tech;
      const shifts = _shifts.length > 0 ? _shifts : order.schedule.shifts;

      if (!providerId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Technician not assigned",
        });
      }

      transactions.push(
        ctx.prisma.provider.update({
          where: {
            id: providerId,
          },
          data: {
            shifts: {
              disconnect: shifts,
            },
            technicians: {
              disconnect: {
                id: tech.id,
              },
            },
            orders: {
              disconnect: {
                id: order.id,
              },
            },
          },
        }),
        ...timeSheets.map((ts) =>
          ctx.prisma.timeSheet.update({
            where: {
              id: ts.id,
            },
            data: {
              status: "PENDING",
              provider: {
                disconnect: {
                  id: providerId,
                },
              },
            },
          }),
        ),
      );

      await ctx.prisma.$transaction(transactions);

      await onWorkOrderEvent({
        orderId: order.id,
        actorId: ctx.user.id,
        action: "UPDATE",
        resource: "PROVIDER",
        emergency: false,
        meta: {
          mode: "UNASSIGN",
        },
      });

      await onWorkOrderRemoved({
        orderId: order.id,
        providerId,
      });

      if (["ASSIGNED", "SCHEDULED"].includes(order.status)) {
        await ctx.prisma.workOrder.update({
          where: {
            id: input.id,
          },
          data: {
            status: "PUBLISHED",
          },
        });

        await onWorkOrderEvent({
          orderId: order.id,
          actorId: ctx.user.id,
          action: "UPDATE",
          resource: "ORDER",
          emergency: false,
          meta: {
            status: "PUBLISHED",
          },
        });
      }

      return {
        id: order.id,
      };
    }),
});
