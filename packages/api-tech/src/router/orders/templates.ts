import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type {
  Address,
  Contact,
  Document,
  EmailTemplate,
  Location,
  Organization,
  Person,
  Prisma,
  Technician,
  Template,
} from "@axa/database-tech";
import { calculateSkip } from "@axa/lib/utils";

import {
  createTRPCRouter,
  organizationProcedure,
  protectedProcedure,
} from "../../trpc";

const selections = {
  template: {
    id: true,
    name: true,
    description: true,
    summary: true,
    scope: true,
    type: true,
    category: true,
  },
  organization: {
    id: true,
    name: true,
    avatar: true,
  },
  location: {
    id: true,
    name: true,
    type: true,
    description: true,
    organizationId: true,
  },
  address: {
    formatted: true,
    street: true,
    city: true,
    state: true,
    postal: true,
    country: true,
    latitude: true,
    longitude: true,
  },
  contact: {
    id: true,
    role: true,
  },
  person: {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    avatar: true,
    isUser: true,
  },
  document: {
    id: true,
    name: true,
    description: true,
    type: true,
    size: true,
    url: true,
    organizationId: true,
  },
  technician: {
    id: true,
    level: true,
    billingRate: true,
    billingType: true,
    paymentRate: true,
    paymentType: true,
  },
  email: {
    id: true,
    name: true,
    description: true,
    body: true,
    createdAt: true,
    updatedAt: true,
  },
} satisfies {
  template: Partial<Record<keyof Template, boolean>>;
  organization: Partial<Record<keyof Organization, boolean>>;
  location: Partial<Record<keyof Location, boolean>>;
  address: Partial<Record<keyof Address, boolean>>;
  contact: Partial<Record<keyof Contact, boolean>>;
  person: Partial<Record<keyof Person, boolean>>;
  document: Partial<Record<keyof Document, boolean>>;
  technician: Partial<Record<keyof Technician, boolean>>;
  email: Partial<Record<keyof EmailTemplate, boolean>>;
};

export const templatesRouter = createTRPCRouter({
  get: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const template = await ctx.prisma.template.findUnique({
        where: {
          id: input.id,
        },
        select: {
          ...selections.template,
          organization: {
            select: selections.organization,
          },
          location: {
            select: {
              ...selections.location,
              fieldNationId: ctx.isInternal,
              address: {
                select: selections.address,
              },
            },
          },
          contacts: {
            select: selections.contact,
          },
          documents: {
            select: selections.document,
          },
          technicians: {
            select: selections.technician,
          },
        },
      });

      if (!template) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Template not found",
        });
      }

      return template;
    }),
  getMany: organizationProcedure
    .input(
      z.object({
        organizations: z.array(z.string()).optional(),
        organizationId: z.string().optional(),
        query: z.string().optional(),
        pageSize: z.number().optional(),
        pageNumber: z.number().optional(),
        showDeleted: z.boolean().optional(),
        include: z
          .object({
            location: z.boolean().optional(),
            contacts: z.boolean().optional(),
            documents: z.boolean().optional(),
            technicians: z.boolean().optional(),
          })
          .optional(),
      }),
    )
    .query(async ({ input, ctx }) => {
      let deletedAt: Date | null | undefined = null;
      let organizations: string[] | undefined = [];

      if (ctx.isInternal && ctx.organizations.length === 0) {
        if (input.showDeleted) {
          deletedAt = undefined;
        }

        if (input.organizationId) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations;
        } else {
          organizations = undefined;
        }
      } else {
        if (
          input.organizationId &&
          ctx.organizations.includes(input.organizationId)
        ) {
          organizations.push(input.organizationId);
        } else if (input.organizations && input.organizations.length > 0) {
          organizations = input.organizations.filter((id) =>
            ctx.organizations.includes(id),
          );
        } else {
          organizations = ctx.organizations;
        }
      }

      const query = {
        deletedAt,
        name: input.query
          ? {
              contains: input.query,
            }
          : undefined,
        organizationId:
          organizations && organizations.length > 0
            ? {
                in: organizations,
              }
            : undefined,
      } as Prisma.TemplateFindManyArgs["where"];

      const [count, templates] = await Promise.all([
        ctx.prisma.template.count({
          where: query,
        }),
        ctx.prisma.template.findMany({
          skip: calculateSkip({
            pageSize: input.pageSize,
            pageNumber: input.pageNumber,
          }),
          take: input.pageSize,
          where: query,
          select: {
            ...selections.template,
            organization: {
              select: {
                ...selections.organization,
                fieldNationId: ctx.isInternal,
              },
            },
            location: input.include?.location
              ? {
                  select: {
                    ...selections.location,
                    fieldNationId: ctx.isInternal,
                    address: {
                      select: selections.address,
                    },
                  },
                }
              : undefined,
            contacts: input.include?.contacts
              ? {
                  select: {
                    ...selections.contact,
                    person: {
                      select: selections.person,
                    },
                  },
                }
              : undefined,
            documents: input.include?.documents
              ? {
                  select: selections.document,
                }
              : undefined,
            technicians: input.include?.technicians
              ? {
                  select: selections.technician,
                }
              : undefined,
          },
        }),
      ]);

      return {
        templates,
        total: count,
      };
    }),
  create: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        templateId: z.string().optional(),
        name: z.string(),
        description: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId =
        input.organizationId ?? ctx.organization?.id ?? undefined;

      if (!organizationId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      return ctx.prisma.template.create({
        data: {
          organization: {
            connect: {
              id: organizationId,
            },
          },
          name: input.name,
          description: input.description,
        },
        select: selections.template,
      });
    }),
  duplicate: organizationProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        templateId: z.string(),
        name: z.string(),
        description: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const organizationId =
        input.organizationId ?? ctx.organization?.id ?? undefined;

      if (!organizationId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Organization ID is required",
        });
      }

      const template = await ctx.prisma.template.findUniqueOrThrow({
        where: {
          id: input.templateId,
        },
        select: {
          locationId: true,
          summary: true,
          scope: true,
          type: true,
          category: true,
          contacts: {
            select: {
              id: true,
              role: true,
              person: {
                select: {
                  id: true,
                },
              },
            },
          },
          technicians: {
            select: {
              level: true,
              paymentRate: true,
              paymentType: true,
              billingRate: true,
              billingType: true,
            },
          },
          documents: {
            select: {
              id: true,
            },
          },
        },
      });

      // create a duplicate
      return ctx.prisma.template.create({
        data: {
          summary: template.summary,
          scope: template.scope,
          type: template.type,
          category: template.category,
          location: template.locationId
            ? {
                connect: {
                  id: template.locationId,
                },
              }
            : undefined,
          contacts:
            template.contacts.length > 0
              ? {
                  create: template.contacts.map((contact) => ({
                    role: contact.role,
                    person: {
                      connect: {
                        id: contact.person?.id,
                      },
                    },
                  })),
                }
              : undefined,
          technicians:
            template.technicians.length > 0
              ? {
                  create: template.technicians,
                }
              : undefined,
          documents:
            template.documents.length > 0
              ? {
                  connect: template.documents.map((document) => ({
                    id: document.id,
                  })),
                }
              : undefined,
          organization: {
            connect: {
              id: organizationId,
            },
          },
          name: input.name,
          description: input.description,
        },
        select: selections.template,
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        description: z.string().optional(),
        summary: z.string().optional(),
        scope: z.string().optional(),
        type: z.string().optional(),
        category: z.string().optional(),
        organizationId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.template.update({
        where: {
          id: input.id,
        },
        data: {
          name: input.name,
          description: input.description,
          summary: input.summary,
          scope: input.scope,
          type: input.type,
          category: input.category,
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
        },
        select: selections.template,
      });
    }),
  link: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        locationId: z.string().optional(),
        organizationId: z.string().optional(),
        contactId: z.string().optional(),
        technicianId: z.string().optional(),
        documentId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.template.update({
        where: {
          id: input.id,
        },
        data: {
          organization: input.organizationId
            ? {
                connect: {
                  id: input.organizationId,
                },
              }
            : undefined,
          location: input.locationId
            ? {
                connect: {
                  id: input.locationId,
                },
              }
            : undefined,
          contacts: input.contactId
            ? {
                connect: {
                  id: input.contactId,
                },
              }
            : undefined,
          technicians: input.technicianId
            ? {
                connect: {
                  id: input.technicianId,
                },
              }
            : undefined,
          documents: input.documentId
            ? {
                connect: {
                  id: input.documentId,
                },
              }
            : undefined,
        },
        select: selections.template,
      });
    }),
  unlink: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        locationId: z.string().optional(),
        organizationId: z.string().optional(),
        contactId: z.string().optional(),
        technicianId: z.string().optional(),
        documentId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.template.update({
        where: {
          id: input.id,
        },
        data: {
          location: input.locationId
            ? {
                disconnect: true,
              }
            : undefined,
          contacts: input.contactId
            ? {
                disconnect: [
                  {
                    id: input.contactId,
                  },
                ],
              }
            : undefined,
          technicians: input.technicianId
            ? {
                disconnect: [
                  {
                    id: input.technicianId,
                  },
                ],
              }
            : undefined,
          documents: input.documentId
            ? {
                disconnect: [
                  {
                    id: input.documentId,
                  },
                ],
              }
            : undefined,
        },
        select: selections.template,
      });
    }),
  delete: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.options.permanentDelete) {
        return ctx.prisma.template.delete({
          where: {
            id: input.id,
          },
          select: selections.template,
        });
      }

      return ctx.prisma.template.update({
        where: {
          id: input.id,
        },
        data: {
          deletedAt: new Date(),
        },
        select: selections.template,
      });
    }),

  emails: createTRPCRouter({
    get: protectedProcedure
      .input(
        z.object({
          id: z.string(),
        }),
      )
      .query(async ({ ctx, input }) => {
        const template = await ctx.prisma.emailTemplate.findUnique({
          where: {
            id: input.id,
          },
          select: {
            ...selections.email,
            organization: {
              select: selections.organization,
            },
          },
        });

        if (!template) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Template not found",
          });
        }

        return template;
      }),
    getMany: organizationProcedure
      .input(
        z
          .object({
            organizations: z.array(z.string()).optional(),
            organizationId: z.string().optional(),
            query: z.string().optional(),
            pageSize: z.number().optional(),
            pageNumber: z.number().optional(),
            showDeleted: z.boolean().optional(),
          })
          .optional(),
      )
      .query(async ({ input, ctx }) => {
        let deletedAt: Date | null | undefined = null;
        let organizations: string[] | undefined = [];

        if (ctx.isInternal && ctx.organizations.length === 0) {
          if (input?.showDeleted) {
            deletedAt = undefined;
          }

          if (input?.organizationId) {
            organizations.push(input.organizationId);
          } else if (input?.organizations && input.organizations.length > 0) {
            organizations = input.organizations;
          } else {
            organizations = undefined;
          }
        } else {
          if (
            input?.organizationId &&
            ctx.organizations.includes(input.organizationId)
          ) {
            organizations.push(input.organizationId);
          } else if (input?.organizations && input.organizations.length > 0) {
            organizations = input.organizations.filter((id) =>
              ctx.organizations.includes(id),
            );
          } else {
            organizations = ctx.organizations;
          }
        }

        const query = {
          name: input?.query
            ? {
                contains: input.query,
              }
            : undefined,
          organizationId: {
            in: organizations,
          },
        } as Prisma.EmailTemplateFindManyArgs["where"];

        const [count, emails] = await Promise.all([
          ctx.prisma.emailTemplate.count({
            where: query,
          }),
          ctx.prisma.emailTemplate.findMany({
            skip: calculateSkip({
              pageSize: input?.pageSize,
              pageNumber: input?.pageNumber,
            }),
            take: input?.pageSize,
            where: query,
            select: {
              ...selections.email,
              organization: {
                select: selections.organization,
              },
            },
          }),
        ]);

        return {
          emails,
          total: count,
        };
      }),
    create: organizationProcedure
      .input(
        z.object({
          organizationId: z.string(),
          name: z.string(),
          description: z.string().optional(),
          body: z.string(),
        }),
      )
      .mutation(async ({ ctx, input }) => {
        return ctx.prisma.emailTemplate.create({
          data: {
            organization: {
              connect: {
                id: input.organizationId,
              },
            },
            name: input.name,
            description: input.description,
            body: input.body,
          },
          select: selections.email,
        });
      }),

    update: protectedProcedure
      .input(
        z.object({
          id: z.string(),
          name: z.string().optional(),
          description: z.string().optional(),
          body: z.string().optional(),
          organizationId: z.string().optional(),
        }),
      )
      .mutation(async ({ ctx, input }) => {
        return ctx.prisma.emailTemplate.update({
          where: {
            id: input.id,
          },
          data: {
            name: input.name,
            description: input.description,
            body: input.body,
            organization: input.organizationId
              ? {
                  connect: {
                    id: input.organizationId,
                  },
                }
              : undefined,
          },
          select: selections.email,
        });
      }),

    delete: protectedProcedure
      .input(
        z.object({
          id: z.string(),
        }),
      )
      .mutation(async ({ ctx, input }) => {
        if (ctx.options.permanentDelete) {
          return ctx.prisma.emailTemplate.delete({
            where: {
              id: input.id,
            },
            select: selections.email,
          });
        }

        return ctx.prisma.emailTemplate.update({
          where: {
            id: input.id,
          },
          data: {
            deletedAt: new Date(),
          },
          select: selections.email,
        });
      }),
  }),
});
