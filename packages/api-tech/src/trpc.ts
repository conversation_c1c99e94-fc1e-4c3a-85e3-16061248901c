import type { AuthObject } from "@clerk/backend";

import { clerkClient, currentUser } from "@clerk/nextjs/server";
import { initTRPC, TRPCError } from "@trpc/server";
import superjson from "superjson";
import { ZodError } from "zod";

import type { Organization, PersonRole } from "@axa/database-tech";

import fn from "./lib/fieldnation/field-nation";
import { prisma } from "./lib/prisma";
import { utapi } from "./lib/uploadthing";

export type { AuthObject };
export interface ContextOptions {
  headers: Headers;
  auth: AuthObject;
}
export async function createTRPCContext(opts: ContextOptions) {
  // eslint-disable-next-line no-restricted-properties
  if (process.env.NODE_ENV === "development") {
    const source = opts.headers.get("x-trpc-source") ?? "unknown";
    console.log(
      ">>> tRPC Request from",
      source,
      "by",
      opts.auth.userId ?? "anonymous",
    );
  }

  return {
    user: null,
    session: opts.auth,
    prisma,
    clerk: await clerk<PERSON>lient(),
    utapi,
    fn,
  };
}

export type Context = typeof createTRPCContext;
export interface Meta {
  permanentDelete: boolean;
  roles: PersonRole[];
}

const t = initTRPC
  .context<Context>()
  .meta<Meta>()
  .create({
    defaultMeta: {
      permanentDelete: process.env.NODE_ENV !== "production",
      roles: ["ADMIN", "BILLING", "INTERNAL", "CLIENT"],
    },
    transformer: superjson,
    errorFormatter: ({ shape, error }) => ({
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    }),
  });

export const createCallerFactory = t.createCallerFactory;
export const createTRPCRouter = t.router;

export const publicProcedure = t.procedure;
export const protectedProcedure = t.procedure.use(
  async ({ ctx, meta, next }) => {
    if (!ctx.session.userId) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }

    const user = await currentUser();

    if (!user) {
      if (ctx.session.sessionId) {
        // if this happens, then the user is authenticated but not found
        await ctx.clerk.sessions.revokeSession(ctx.session.sessionId);
      }
      throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
    }

    const userRole = user.publicMetadata.role as string;

    return next({
      ctx: {
        ...ctx,
        user,
        role: userRole,
        isInternal:
          ["ADMIN", "BILLING", "INTERNAL"].includes(userRole) === true,
        isBilling: ["ADMIN", "BILLING"].includes(userRole) === true,
        isAdmin: ["ADMIN"].includes(userRole) === true,
        isClient: ["CLIENT"].includes(userRole) === true,
        options: {
          roles: meta?.roles,
          permanentDelete: meta?.permanentDelete,
        },
      },
    });
  },
);

export const organizationProcedure = protectedProcedure.use(
  async ({ ctx, next }) => {
    let organization: Pick<Organization, "id"> | null;
    let organizations: string[] = [];

    const primaryOrganizationId =
      (ctx.user.publicMetadata.organizationId as string) || "";
    const organizationId = primaryOrganizationId;

    if (ctx.isInternal === true && !organizationId) {
      organization = null;
      organizations = [];
    } else {
      organization = await ctx.prisma.organization.findUnique({
        where: {
          id: organizationId,
        },
        select: {
          id: true,
        },
      });

      if (!organization) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      const descendants =
        ((await ctx.prisma.organization.findDescendants({
          where: {
            id: organizationId,
          },
          select: {
            id: true,
          },
        })) as Organization[] | null) ?? [];

      organizations = [organizationId, ...descendants.map((org) => org.id)];
    }

    return next({
      ctx: {
        ...ctx,
        organization,
        organizations,
      },
    });
  },
);

export const accountsProcedure = organizationProcedure.use(
  async ({ ctx, next }) => {
    let accounts: string[] | undefined = [];

    // TODO: support client mode

    if (ctx.isClient) {
      accounts = ctx.organizations;
    } else if (ctx.isInternal) {
      if (ctx.isAdmin) {
        if (ctx.organization !== null) {
          accounts = ctx.organizations;
        } else {
          accounts = undefined;
        }
      } else if (ctx.isBilling) {
        const managingAccounts = await ctx.prisma.organization.findMany({
          where: {
            managers: {
              some: {
                id: ctx.user.id,
              },
            },
          },
          select: {
            id: true,
          },
        });

        if (managingAccounts.length > 0) {
          accounts = managingAccounts.map((org) => org.id);

          if (ctx.organization !== null) {
            accounts = accounts.filter((id) => ctx.organizations.includes(id));
          }
        } else {
          if (ctx.organization !== null) {
            accounts = ctx.organizations;
          }
          // treat as billing admin
          accounts = undefined;
        }
      } else {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }
    }

    return next({
      ctx: {
        ...ctx,
        accounts,
      },
    });
  },
);

export const internalProcedure = protectedProcedure.use(({ ctx, next }) => {
  if (ctx.isInternal === false) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({ ctx });
});

export const billingProcedure = protectedProcedure.use(({ ctx, next }) => {
  if (ctx.isBilling === false) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({ ctx });
});

export const adminProcedure = protectedProcedure.use(({ ctx, next }) => {
  if (ctx.isAdmin === false) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({ ctx });
});
