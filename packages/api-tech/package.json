{"name": "@axa/api-tech", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./functions/*": {"types": "./dist/functions/*.d.ts", "default": "./src/functions/*.ts"}, "./hooks/*": {"types": "./dist/hooks/*.d.ts", "default": "./src/hooks/*.ts"}, "./jobs/*": {"types": "./dist/jobs/*.d.ts", "default": "./src/jobs/*.ts"}, "./lib/*": {"types": "./dist/lib/*.d.ts", "default": "./src/lib/*.ts"}, "./utils/*": {"types": "./dist/utils/*.d.ts", "default": "./src/utils/*.ts"}, "./env": {"types": "./dist/env.d.ts", "default": "./src/env.ts"}}, "license": "SEE LICENSE", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf .turbo node_modules dist", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../apps/web-tech/.env.local --", "test:fn": "pnpm with-env pnpm tsx scripts/test-fieldnation.ts", "test:migration": "pnpm with-env pnpm tsx scripts/process-migration.tsx", "clear:all": "pnpm with-env pnpm tsx scripts/clear-notifications.ts", "test": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@axa/constants": "workspace:*", "@axa/database-tech": "workspace:*", "@axa/emails": "workspace:*", "@axa/lib": "workspace:*", "@clerk/nextjs": "^6.20.2", "@t3-oss/env-nextjs": "^0.13.6", "@trpc/server": "^11.1.4", "@vercel/functions": "^2.1.0", "luxon": "^3.6.1", "superjson": "^2.2.2", "svix": "^1.66.0", "uploadthing": "^6.13.3", "zod": "^3.25.42"}, "devDependencies": {"@axa/eslint-config": "workspace:*", "@axa/prettier-config": "workspace:*", "@axa/tsconfig": "workspace:*", "@clerk/backend": "^1.34.0", "@clerk/types": "^4.59.3", "@sentry/nextjs": "^9.24.0", "@types/luxon": "^3.6.2", "eslint": "^9.28.0", "next": "^15.3.3", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "peerDependencies": {"@sentry/nextjs": "^8.24.0", "next": "^15.2.4"}, "prettier": "@axa/prettier-config"}